// Mozilla User Preferences

// DO NOT EDIT THIS FILE.
//
// If you make changes to this file while the application is running,
// the changes will be overwritten when the application exits.
//
// To change a preference value, you can either:
// - modify it via the UI (e.g. via about:config in the browser); or
// - set it within a user.js file in your profile.

user_pref("app.normandy.first_run", false);
user_pref("app.normandy.migrationsApplied", 12);
user_pref("app.normandy.user_id", "fb51c415-7936-489a-8bcd-f511e40dbd78");
user_pref("app.update.lastUpdateTime.addon-background-update-timer", 1748169760);
user_pref("app.update.lastUpdateTime.background-update-timer", 1748169880);
user_pref("app.update.lastUpdateTime.browser-cleanup-thumbnails", 1748170000);
user_pref("app.update.lastUpdateTime.recipe-client-addon-run", 1748169880);
user_pref("app.update.lastUpdateTime.region-update-timer", 1748108816);
user_pref("app.update.lastUpdateTime.rs-experiment-loader-timer", 1748083504);
user_pref("app.update.lastUpdateTime.services-settings-poll-changes", 1748169760);
user_pref("app.update.lastUpdateTime.telemetry_modules_ping", 1748119265);
user_pref("app.update.lastUpdateTime.xpi-signature-verification", 1748169760);
user_pref("browser.aboutwelcome.didSeeFinalScreen", true);
user_pref("browser.bookmarks.addedImportButton", true);
user_pref("browser.bookmarks.restore_default_bookmarks", false);
user_pref("browser.bookmarks.showMobileBookmarks", false);
user_pref("browser.contentblocking.category", "standard");
user_pref("browser.contextual-services.contextId", "{7bd60dd5-2a80-4a7d-942a-9f4a8a5d5853}");
user_pref("browser.download.panel.shown", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.avif", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.webp", true);
user_pref("browser.engagement.fxa-toolbar-menu-button.has-used", true);
user_pref("browser.laterrun.bookkeeping.profileCreationTime", 1746276848);
user_pref("browser.laterrun.bookkeeping.sessionCount", 1);
user_pref("browser.laterrun.enabled", true);
user_pref("browser.migration.version", 153);
user_pref("browser.ml.chat.nimbus", "simplified-chatbot-onboarding:treatment-c-short-copy-1-step");
user_pref("browser.newtabpage.activity-stream.impressionId", "{90a9e9db-8068-4c86-ac3d-7a4ddf381bba}");
user_pref("browser.newtabpage.activity-stream.newtabShortcuts.refresh", true);
user_pref("browser.newtabpage.pinned", "[null,null,null,{\"url\":\"https://www.tradingview.com/chart/?symbol=BITSTAMP%3ABTCUSD\",\"label\":\"tradingview\"}]");
user_pref("browser.newtabpage.storageVersion", 1);
user_pref("browser.pageActions.persistedActions", "{\"ids\":[\"bookmark\",\"_036a55b4-5e72-4d05-a06c-cba2dfcc134a_\",\"_************************************_\"],\"idsInUrlbar\":[\"_036a55b4-5e72-4d05-a06c-cba2dfcc134a_\",\"_************************************_\",\"bookmark\"],\"idsInUrlbarPreProton\":[],\"version\":1}");
user_pref("browser.pagethumbnails.storage_version", 3);
user_pref("browser.policies.applied", true);
user_pref("browser.proton.toolbar.version", 3);
user_pref("browser.region.update.updated", **********);
user_pref("browser.safebrowsing.provider.google4.lastupdatetime", "1748170395869");
user_pref("browser.safebrowsing.provider.google4.nextupdatetime", "1748172205869");
user_pref("browser.safebrowsing.provider.mozilla.lastupdatetime", "1748166800787");
user_pref("browser.safebrowsing.provider.mozilla.nextupdatetime", "*************");
user_pref("browser.search.region", "CZ");
user_pref("browser.search.serpEventTelemetryCategorization.regionEnabled", false);
user_pref("browser.search.totalSearches", 4);
user_pref("browser.sessionstore.upgradeBackup.latestBuildID", "**************");
user_pref("browser.shell.mostRecentDateSetAsDefault", "**********");
user_pref("browser.startup.couldRestoreSession.count", 2);
user_pref("browser.startup.homepage", "https://myaccount.google.com/security-checkup?continue=https://www.youtube.com/signin?action_handle_signin%3Dtrue%26app%3Ddesktop%26hl%3Den%26next%3D%252Fwatch%253Fv%253De67eJvfd9SE%2526pp%253DygUKa2FsaSB0b29scw%25253D%25253D&flowType=1&hl=en&rapt=AEjHL4NCnu3AfzFzNYp-Fe5K8zjanpa1yF0ZJvfgic7SvNILfH55taIq18AlYJh25fwePH3crGUb1G41LsvWgFp7cgOH4U46hg&hl=en&continue=https://accounts.google.com/ServiceLogin?continue%3Dhttps%253A%252F%252Fwww.youtube.com%252Fsignin%253Faction_handle_signin%253Dtrue%2526app%253Ddesktop%2526hl%253Den%2526next%253D%25252Fwatch%25253Fv%25253De67eJvfd9SE%252526pp%25253DygUKa2FsaSB0b29scw%2525253D%2525253D%26service%3Dyoutube%26hl%3Den%26authuser%3D0%26passive%3Dtrue%26sarp%3D1%26aodrpl%3D1%26checkedDomains%3Dyoutube%26checkConnection%3Dyoutube%253A1329%26pstMsg%3D1&service=youtube&rapt=AEjHL4NCnu3AfzFzNYp-Fe5K8zjanpa1yF0ZJvfgic7SvNILfH55taIq18AlYJh25fwePH3crGUb1G41LsvWgFp7cgOH4U46hg&pli=1|https://accounts.firefox.com/pair?entrypoint=fxa_app_menu|moz-extension://b91a9fab-7da1-42b6-a318-99f4d6605bb1/options/options.html|https://accounts.firefox.com/oauth/signin?client_id=9ebfe2c2f9ea3c58&redirect_uri=https%3A%2F%2Frelay.firefox.com%2Faccounts%2Ffxa%2Flogin%2Fcallback%2F&scope=profile%2Bhttps%3A%2F%2Fidentity.mozilla.com%2Faccount%2Fsubscriptions&response_type=code&state=JNPXLnnmKIUHs3oP&access_type=offline|https://platform.avast.com/sp/onboarding?utm_medium=link&utm_source=safeprice&utm_campaign=safeprice-onboarding&language=en|moz-extension://********-2535-4877-bd48-960919825f2a/home.html#onboarding/welcome");
user_pref("browser.startup.homepage_override.buildID", "**************");
user_pref("browser.startup.homepage_override.mstone", "138.0");
user_pref("browser.startup.lastColdStartupCheck", **********);
user_pref("browser.startup.page", 3);
user_pref("browser.tabs.groups.enabled", true);
user_pref("browser.toolbarbuttons.introduced.sidebar-button", true);
user_pref("browser.topsites.contile.cacheValidFor", 10800);
user_pref("browser.topsites.contile.lastFetch", **********);
user_pref("browser.translations.panelShown", true);
user_pref("browser.uiCustomization.horizontalTabsBackup", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"unified-extensions-area\":[],\"nav-bar\":[\"sidebar-button\",\"back-button\",\"forward-button\",\"stop-reload-button\",\"customizableui-special-spring1\",\"vertical-spacer\",\"urlbar-container\",\"customizableui-special-spring2\",\"save-to-pocket-button\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"unified-extensions-button\"],\"toolbar-menubar\":[\"menubar-items\"],\"TabsToolbar\":[\"firefox-view-button\",\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"vertical-tabs\":[],\"PersonalToolbar\":[\"import-button\",\"personal-bookmarks\"]},\"seen\":[\"save-to-pocket-button\",\"developer-button\"],\"dirtyAreaCache\":[\"nav-bar\",\"vertical-tabs\",\"PersonalToolbar\",\"toolbar-menubar\",\"TabsToolbar\"],\"currentVersion\":22,\"newElementCount\":2}");
user_pref("browser.uiCustomization.navBarWhenVerticalTabs", "[\"sidebar-button\",\"back-button\",\"forward-button\",\"stop-reload-button\",\"customizableui-special-spring1\",\"vertical-spacer\",\"urlbar-container\",\"customizableui-special-spring2\",\"save-to-pocket-button\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"unified-extensions-button\",\"firefox-view-button\",\"alltabs-button\"]");
user_pref("browser.uiCustomization.state", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"unified-extensions-area\":[\"_506e023c-7f2b-40a3-8066-bc5deb40aebe_-browser-action\",\"_036a55b4-5e72-4d05-a06c-cba2dfcc134a_-browser-action\",\"keplr-extension_keplr_app-browser-action\",\"customscrollbars_computerwhiz-browser-action\",\"webextension_metamask_io-browser-action\",\"jid1-qofqdk4qzufgwq_jetpack-browser-action\"],\"nav-bar\":[\"sidebar-button\",\"back-button\",\"forward-button\",\"stop-reload-button\",\"customizableui-special-spring1\",\"vertical-spacer\",\"urlbar-container\",\"customizableui-special-spring2\",\"save-to-pocket-button\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"unified-extensions-button\",\"_************************************_-browser-action\",\"private-relay_firefox_com-browser-action\"],\"toolbar-menubar\":[\"menubar-items\"],\"TabsToolbar\":[\"firefox-view-button\",\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"vertical-tabs\":[],\"PersonalToolbar\":[\"import-button\",\"personal-bookmarks\"]},\"seen\":[\"save-to-pocket-button\",\"developer-button\",\"_506e023c-7f2b-40a3-8066-bc5deb40aebe_-browser-action\",\"_036a55b4-5e72-4d05-a06c-cba2dfcc134a_-browser-action\",\"_************************************_-browser-action\",\"private-relay_firefox_com-browser-action\",\"keplr-extension_keplr_app-browser-action\",\"customscrollbars_computerwhiz-browser-action\",\"webextension_metamask_io-browser-action\",\"jid1-qofqdk4qzufgwq_jetpack-browser-action\"],\"dirtyAreaCache\":[\"nav-bar\",\"vertical-tabs\",\"PersonalToolbar\",\"toolbar-menubar\",\"TabsToolbar\",\"unified-extensions-area\"],\"currentVersion\":22,\"newElementCount\":2}");
user_pref("browser.urlbar.placeholderName", "Google");
user_pref("browser.urlbar.quicksuggest.migrationVersion", 2);
user_pref("browser.urlbar.tipShownCount.searchTip_onboard", 4);
user_pref("captchadetection.lastSubmission", 1746276);
user_pref("datareporting.dau.cachedUsageProfileID", "d96701a4-195a-4287-8d60-ee239fe58dc5");
user_pref("datareporting.policy.dataSubmissionPolicyAcceptedVersion", 2);
user_pref("datareporting.policy.dataSubmissionPolicyNotifiedTime", "1746276850968");
user_pref("distribution.iniFile.exists.appversion", "138.0");
user_pref("distribution.iniFile.exists.value", true);
user_pref("distribution.mint-001.bookmarksProcessed", true);
user_pref("doh-rollout.doneFirstRun", true);
user_pref("doh-rollout.home-region", "CZ");
user_pref("dom.push.userAgentID", "ef3c7e87fc024037b8b388cb2ed9b34b");
user_pref("extensions.activeThemeID", "<EMAIL>");
user_pref("extensions.blocklist.pingCountVersion", 0);
user_pref("extensions.databaseSchema", 37);
user_pref("extensions.formautofill.creditCards.reauth.optout", "MDIEEPgAAAAAAAAAAAAAAAAAAAEwFAYIKoZIhvcNAwcECJ/MgVWa/xGABAg4VearTFFwLQ==");
user_pref("extensions.getAddons.cache.lastUpdate", **********);
user_pref("extensions.getAddons.databaseSchema", 6);
user_pref("<EMAIL>", true);
user_pref("<EMAIL>", true);
user_pref("extensions.lastAppBuildId", "**************");
user_pref("extensions.lastAppVersion", "138.0");
user_pref("extensions.lastPlatformVersion", "138.0");
user_pref("extensions.pendingOperations", false);
user_pref("extensions.pictureinpicture.enable_picture_in_picture_overrides", true);
user_pref("extensions.quarantinedDomains.list", "autoatendimento.bb.com.br,ibpf.sicredi.com.br,ibpj.sicredi.com.br,internetbanking.caixa.gov.br,www.ib12.bradesco.com.br,www2.bancobrasil.com.br");
user_pref("extensions.signatureCheckpoint", 1);
user_pref("extensions.systemAddonSet", "{\"schema\":1,\"addons\":{}}");
user_pref("extensions.webcompat.enable_shims", true);
user_pref("extensions.webcompat.perform_injections", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.customscrollbars@computerwhiz", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.jid1-QoFqdK4qzUfGWQ@jetpack", true);
user_pref("<EMAIL>", true);
user_pref("<EMAIL>", true);
user_pref("<EMAIL>", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.{036a55b4-5e72-4d05-a06c-cba2dfcc134a}", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.{************************************}", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.{506e023c-7f2b-40a3-8066-bc5deb40aebe}", true);
user_pref("extensions.webextensions.uuids", "{\"<EMAIL>\":\"0abe06ff-a3ca-4977-ab0c-3ae6efbf53d2\",\"<EMAIL>\":\"d3f095b9-98a8-4b76-b5d2-397f5cac17c2\",\"<EMAIL>\":\"4b7a79e3-8b64-4235-9f9c-42e2071fa1bf\",\"<EMAIL>\":\"b277af0a-f650-4923-892b-f85a430d1494\",\"<EMAIL>\":\"5b59214e-75ef-4525-8369-a85f5dfe13e6\",\"<EMAIL>\":\"4f78213a-719c-4067-a970-2ce660c5206d\",\"{506e023c-7f2b-40a3-8066-bc5deb40aebe}\":\"530911e9-553e-4cb5-ac3b-b4e66f5aa604\",\"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}\":\"cfb6007d-ab06-4d36-b484-bc26c68017d2\",\"{************************************}\":\"00bd2ddf-b75c-4022-8063-4cc88e35dc1a\",\"<EMAIL>\":\"bc27f079-a449-4bbf-9d17-26154b45cdbf\",\"<EMAIL>\":\"cfc20317-d6cd-48df-867a-21e57737b8e2\",\"{b57b832e-f614-4bc0-b98f-1b6c720bec75}\":\"6e4f168c-dc80-473d-91ea-228fa300af65\",\"{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}\":\"8e358374-64be-4fcd-993f-f6f16861fa1f\",\"customscrollbars@computerwhiz\":\"af739c0f-756e-4f88-b868-83df4d264dc6\",\"<EMAIL>\":\"56a66393-22b7-4567-a584-da91fc8f2cd6\",\"jid1-QoFqdK4qzUfGWQ@jetpack\":\"84daf55a-73bd-480d-a7f9-72ba55522a92\"}");
user_pref("gecko.handlerService.defaultHandlersVersion", 1);
user_pref("identity.fxaccounts.account.device.name", "Firefox tv na tv-HP-t640-Thin-Client");
user_pref("identity.fxaccounts.account.telemetry.sanitized_uid", "a0259c8abec953172ad97f9f50b0e803");
user_pref("identity.fxaccounts.commands.missed.last_fetch", **********);
user_pref("identity.fxaccounts.lastSignedInUserHash", "DX0BXK31HZqPKQPeBm+mAiSYi/0DuWOnSB6aHXz1a4c=");
user_pref("identity.fxaccounts.toolbar.accessed", true);
user_pref("idle.lastDailyNotification", **********);
user_pref("intl.accept_languages", "cs,en-us,en");
user_pref("intl.locale.requested", "cs,en-US");
user_pref("media.gmp-gmpopenh264.abi", "x86_64-gcc3");
user_pref("media.gmp-gmpopenh264.hashValue", "f5246bf14d038adf4ce0c4360262ab722bc3de4220f047c3d542b4c564074b4877dc8659e3125c5171c749e7ce93f20cc63777eb5e1539e960670cbc5f30ac85");
user_pref("media.gmp-gmpopenh264.lastDownload", **********);
user_pref("media.gmp-gmpopenh264.lastInstallStart", **********);
user_pref("media.gmp-gmpopenh264.lastUpdate", **********);
user_pref("media.gmp-gmpopenh264.version", "2.6.0");
user_pref("media.gmp-manager.buildID", "**************");
user_pref("media.gmp-manager.lastCheck", **********);
user_pref("media.gmp-manager.lastEmptyCheck", **********);
user_pref("media.gmp.storage.version.observed", 1);
user_pref("media.videocontrols.picture-in-picture.video-toggle.first-seen-secs", **********);
user_pref("nimbus.migrations.latest", 0);
user_pref("nimbus.syncdefaultsstore.upgradeDialog", "{\"slug\":\"upgrade-spotlight-rollout\",\"branch\":{\"slug\":\"treatment\",\"ratio\":1,\"feature\":{\"value\":null,\"enabled\":true,\"featureId\":\"upgradeDialog\"},\"features\":null},\"active\":true,\"experimentType\":\"rollout\",\"source\":\"rs-loader\",\"userFacingName\":\"Upgrade Spotlight Rollout\",\"userFacingDescription\":\"Experimenting on onboarding content when you upgrade Firefox.\",\"lastSeen\":\"2025-05-03T12:54:11.874Z\",\"featureIds\":[\"upgradeDialog\"],\"prefs\":[],\"isRollout\":true}");
user_pref("nimbus.syncdefaultsstore.upgradeDialog.enabled", false);
user_pref("pdfjs.enabledCache.state", true);
user_pref("pdfjs.migrationVersion", 2);
user_pref("places.database.lastMaintenance", **********);
user_pref("pref.privacy.disable_button.view_passwords", false);
user_pref("privacy.bounceTrackingProtection.hasMigratedUserActivationData", true);
user_pref("privacy.clearOnShutdown_v2.formdata", true);
user_pref("privacy.purge_trackers.date_in_cookie_database", "0");
user_pref("privacy.purge_trackers.last_purge", "**********882");
user_pref("privacy.sanitize.clearOnShutdown.hasMigratedToNewPrefs3", true);
user_pref("privacy.sanitize.pending", "[{\"id\":\"newtab-container\",\"itemsToClear\":[],\"options\":{}}]");
user_pref("privacy.userContext.enabled", true);
user_pref("privacy.userContext.extension", "{************************************}");
user_pref("privacy.userContext.ui.enabled", true);
user_pref("services.settings.blocklists.addons-bloomfilters.last_check", **********);
user_pref("services.settings.blocklists.gfx.last_check", **********);
user_pref("services.settings.clock_skew_seconds", 0);
user_pref("services.settings.last_etag", "\"1748141828985\"");
user_pref("services.settings.last_update_seconds", 1748169760);
user_pref("services.settings.main.addons-manager-settings.last_check", **********);
user_pref("services.settings.main.anti-tracking-url-decoration.last_check", **********);
user_pref("services.settings.main.bounce-tracking-protection-exceptions.last_check", **********);
user_pref("services.settings.main.cfr.last_check", **********);
user_pref("services.settings.main.cookie-banner-rules-list.last_check", **********);
user_pref("services.settings.main.devtools-compatibility-browsers.last_check", **********);
user_pref("services.settings.main.devtools-devices.last_check", **********);
user_pref("services.settings.main.doh-config.last_check", **********);
user_pref("services.settings.main.doh-providers.last_check", **********);
user_pref("services.settings.main.fingerprinting-protection-overrides.last_check", **********);
user_pref("services.settings.main.fxmonitor-breaches.last_check", **********);
user_pref("services.settings.main.hijack-blocklists.last_check", **********);
user_pref("services.settings.main.language-dictionaries.last_check", **********);
user_pref("services.settings.main.message-groups.last_check", **********);
user_pref("services.settings.main.moz-essential-domain-fallbacks.last_check", **********);
user_pref("services.settings.main.newtab-wallpapers-v2.last_check", **********);
user_pref("services.settings.main.nimbus-desktop-experiments.last_check", **********);
user_pref("services.settings.main.nimbus-secure-experiments.last_check", **********);
user_pref("services.settings.main.normandy-recipes-capabilities.last_check", **********);
user_pref("services.settings.main.partitioning-exempt-urls.last_check", **********);
user_pref("services.settings.main.password-recipes.last_check", **********);
user_pref("services.settings.main.password-rules.last_check", **********);
user_pref("services.settings.main.query-stripping.last_check", **********);
user_pref("services.settings.main.remote-permissions.last_check", **********);
user_pref("services.settings.main.search-categorization.last_check", **********);
user_pref("services.settings.main.search-config-icons.last_check", **********);
user_pref("services.settings.main.search-config-overrides-v2.last_check", **********);
user_pref("services.settings.main.search-config-v2.last_check", **********);
user_pref("services.settings.main.search-default-override-allowlist.last_check", **********);
user_pref("services.settings.main.search-telemetry-v2.last_check", **********);
user_pref("services.settings.main.sites-classification.last_check", **********);
user_pref("services.settings.main.third-party-cookie-blocking-exempt-urls.last_check", **********);
user_pref("services.settings.main.tippytop.last_check", **********);
user_pref("services.settings.main.top-sites.last_check", **********);
user_pref("services.settings.main.tracking-protection-lists.last_check", **********);
user_pref("services.settings.main.translations-models.last_check", **********);
user_pref("services.settings.main.translations-wasm.last_check", **********);
user_pref("services.settings.main.url-classifier-skip-urls.last_check", **********);
user_pref("services.settings.main.url-parser-default-unknown-schemes-interventions.last_check", **********);
user_pref("services.settings.main.urlbar-persisted-search-terms.last_check", **********);
user_pref("services.settings.main.websites-with-shared-credential-backends.last_check", **********);
user_pref("services.settings.security-state.cert-revocations.last_check", **********);
user_pref("services.settings.security-state.intermediates.last_check", **********);
user_pref("services.settings.security-state.onecrl.last_check", **********);
user_pref("services.sync.addons.lastSync", "1746288402.34");
user_pref("services.sync.addons.syncID", "kegYTkNhRNOE");
user_pref("services.sync.client.GUID", "yaG4qkE_HULF");
user_pref("services.sync.client.syncID", "sHY7TQ_9wFTY");
user_pref("services.sync.clients.devices.desktop", 1);
user_pref("services.sync.clients.devices.mobile", 0);
user_pref("services.sync.clients.lastRecordUpload", 1748083507);
user_pref("services.sync.clients.lastSync", "1748083507.55");
user_pref("services.sync.clients.syncID", "0kVsz9qP7gwZ");
user_pref("services.sync.creditcards.lastSync", "0");
user_pref("services.sync.declinedEngines", "");
user_pref("services.sync.engine.prefs.modified", false);
user_pref("services.sync.forms.lastSync", "1748169956.80");
user_pref("services.sync.forms.syncID", "2bXshV6QV8lT");
user_pref("services.sync.globalScore", 2);
user_pref("services.sync.lastPing", 1748169954);
user_pref("services.sync.lastSync", "Sun May 25 2025 12:45:57 GMT+0200 (středoevropský letní čas)");
user_pref("services.sync.lastTabFetch", 1748169956);
user_pref("services.sync.nextSync", 1748173557);
user_pref("services.sync.prefs.lastSync", "1746288401.50");
user_pref("services.sync.prefs.sync-seen.browser.contentblocking.category", true);
user_pref("services.sync.prefs.sync-seen.browser.newtabpage.activity-stream.section.highlights.includePocket", true);
user_pref("services.sync.prefs.sync-seen.browser.newtabpage.pinned", true);
user_pref("services.sync.prefs.sync-seen.browser.startup.homepage", true);
user_pref("services.sync.prefs.sync-seen.browser.startup.page", true);
user_pref("services.sync.prefs.sync-seen.extensions.activeThemeID", true);
user_pref("services.sync.prefs.sync-seen.general.autoScroll", true);
user_pref("services.sync.prefs.sync-seen.intl.accept_languages", true);
user_pref("services.sync.prefs.sync-seen.media.eme.enabled", true);
user_pref("services.sync.prefs.sync-seen.privacy.clearOnShutdown_v2.formdata", true);
user_pref("services.sync.prefs.sync-seen.privacy.userContext.enabled", true);
user_pref("services.sync.prefs.syncID", "1JFrxWgaCG57");
user_pref("services.sync.username", "<EMAIL>");
user_pref("sidebar.backupState", "{\"panelOpen\":false,\"launcherWidth\":53,\"launcherExpanded\":false,\"launcherVisible\":true}");
user_pref("sidebar.main.tools", "aichat,syncedtabs,history,bookmarks");
user_pref("sidebar.new-sidebar.has-used", true);
user_pref("sidebar.nimbus", "upgraded-sidebar-138-broad-rollout:rollout-treatment");
user_pref("sidebar.revamp", true);
user_pref("sidebar.visibility", "hide-sidebar");
user_pref("signon.management.page.os-auth.optout", "MDIEEPgAAAAAAAAAAAAAAAAAAAEwFAYIKoZIhvcNAwcECJ+2lzAZP1EOBAj7VkifW5OveQ==");
user_pref("startup.homepage_override_nimbus_maxVersion", "139.9");
user_pref("startup.homepage_override_nimbus_minVersion", "138.0");
user_pref("startup.homepage_override_url_nimbus", "https://www.mozilla.org/%LOCALE%/firefox/138.0/whatsnew/?branch=wnp-seasonal-spring&v=1&newversion=%VERSION%&oldversion=%OLD_VERSION%&utm_medium=firefox-desktop&utm_source=update&utm_campaign=spring-whats-new-page");
user_pref("storage.vacuum.last.content-prefs.sqlite", 1747511013);
user_pref("storage.vacuum.last.index", 2);
user_pref("storage.vacuum.last.places.sqlite", 1746278765);
user_pref("toolkit.startup.last_success", 1748083500);
user_pref("toolkit.telemetry.cachedClientID", "d8c6c0f0-848b-4220-8c58-d154075c592b");
user_pref("toolkit.telemetry.cachedProfileGroupID", "51893314-bb6a-4618-9d27-875969894d39");
user_pref("toolkit.telemetry.previousBuildID", "**************");
user_pref("toolkit.telemetry.reportingpolicy.firstRun", false);
user_pref("trailhead.firstrun.didSeeAboutWelcome", true);
user_pref("ui.context_menus.after_mouseup", true);
