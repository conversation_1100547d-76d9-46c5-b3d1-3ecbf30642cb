{"type": "main", "id": "2006a734-2177-4570-a81d-9e061040befb", "creationDate": "2025-05-25T10:18:22.048Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 84801, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 34}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 8, "browser.engagement.tab_open_event_count": 2, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 2, "dom.contentprocess.os_priority_raised": 279, "browser.engagement.unique_domains_count": 2, "dom.contentprocess.os_priority_lowered": 25, "urlbar.zeroprefix.abandonment": 1, "dom.contentprocess.os_priority_change_considered": 52, "browser.engagement.active_ticks": 34, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 8, "power.total_thread_wakeups": 1976309, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 84801378, "browser.engagement.total_uri_count": 8, "browser.engagement.max_concurrent_window_count": 1, "browser.engagement.session_time_excluding_suspend": 84801378, "power.total_cpu_time_ms": 1163665}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 4}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1131438, "parent.active": 32227}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 2}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "power.wakeups_per_process_type": {"parent.inactive": 1897092, "parent.active": 79217}, "networking.data_transferred_v3_kb": {"Y1_N1": 4999, "Y0_N1Sys": 415, "Y2_N3Oth": 5455}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 1213, "successful": 372}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 15791, "range": [1, 10000], "values": {"0": 0, "1": 21, "2": 331, "3": 248, "4": 12, "5": 6, "6": 1, "7": 1, "8": 362, "10": 6, "12": 2, "14": 17, "17": 155, "20": 33, "24": 281, "29": 7, "34": 1, "48": 1, "57": 1, "68": 1, "81": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 682, "range": [1, 100], "values": {"2": 0, "3": 184, "4": 8, "5": 6, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 497, "range": [1, 100], "values": {"0": 1, "1": 70, "2": 17, "3": 106, "4": 5, "5": 3, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 38303, "range": [1, 10000], "values": {"3": 0, "4": 2, "7": 1, "8": 2, "12": 2, "14": 2, "17": 1, "24": 1, "29": 6, "34": 2, "40": 6, "48": 1, "57": 5, "68": 3, "96": 1, "114": 3, "135": 1, "160": 28, "190": 129, "226": 3, "268": 2, "318": 1, "449": 1, "533": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 55120, "range": [1, 10000], "values": {"29": 0, "34": 1, "40": 5, "48": 2, "57": 2, "68": 6, "81": 8, "96": 3, "114": 4, "135": 2, "190": 1, "226": 70, "268": 82, "318": 10, "378": 4, "533": 1, "633": 1, "752": 1, "894": 1, "1262": 1, "1500": 1, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 1989, "1": 1, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 17, "range": [1, 2], "values": {"0": 1973, "1": 17, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 9666, "range": [1, 10000], "values": {"3": 0, "4": 3, "8": 2, "12": 3, "14": 6, "17": 4, "24": 2, "29": 5, "34": 1, "40": 6, "48": 168, "57": 3, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 182, "range": [1, 1000], "values": {"0": 75, "1": 113, "2": 11, "3": 3, "4": 1, "6": 1, "7": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 30697, "range": [1, 10000], "values": {"0": 2, "1": 2, "5": 4, "8": 3, "14": 1, "17": 1, "20": 5, "24": 6, "34": 5, "40": 2, "48": 3, "57": 1, "68": 2, "81": 1, "96": 1, "135": 19, "160": 139, "190": 3, "226": 1, "268": 2, "378": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 15362, "range": [1, 10000], "values": {"1": 0, "2": 5, "3": 3, "4": 1, "5": 1, "6": 3, "7": 3, "8": 6, "10": 3, "12": 4, "17": 3, "20": 1, "24": 2, "29": 1, "68": 71, "81": 84, "96": 7, "114": 4, "135": 1, "190": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 288, "range": [1, 10000], "values": {"1": 0, "2": 1, "17": 1, "226": 1, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 38039, "range": [1, 10000], "values": {"0": 853, "1": 15, "2": 34, "3": 16, "4": 13, "5": 18, "6": 15, "7": 13, "8": 28, "10": 15, "12": 22, "14": 94, "17": 51, "20": 93, "24": 57, "29": 26, "34": 55, "40": 38, "48": 526, "57": 5, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 1301, "range": [1, 100], "values": {"0": 174, "1": 2, "7": 3, "12": 3, "18": 3, "23": 1, "29": 2, "34": 5, "40": 3, "56": 1, "67": 3, "73": 1, "78": 2, "84": 1, "89": 2, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 16214335, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 2, "5": 3, "6": 1, "9": 1, "10": 6, "11": 30, "13": 93, "15": 220, "17": 199, "19": 337, "22": 127, "25": 15, "28": 4, "32": 1, "36": 2, "41": 6, "47": 6, "53": 16, "60": 23, "68": 14, "77": 52, "88": 17, "100": 18, "114": 43, "130": 77, "148": 172, "168": 284, "191": 771, "217": 626, "247": 464, "281": 296, "320": 223, "364": 351, "414": 629, "471": 724, "536": 953, "610": 1098, "695": 1110, "791": 1924, "901": 2538, "1026": 3648, "1168": 2519, "1330": 334, "1514": 87, "1724": 52, "1963": 19, "2235": 6, "2545": 7, "2898": 12, "3300": 4, "3758": 12, "4279": 8, "4872": 9, "5548": 6, "6317": 4, "7193": 11, "8190": 4, "9326": 2, "10619": 1, "12092": 3, "13769": 2, "17852": 2, "20328": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 16424, "range": [1, 2000], "values": {"0": 826, "1": 30, "2": 21, "3": 32, "4": 12, "5": 14, "6": 23, "7": 13, "8": 7, "9": 4, "10": 2, "11": 19, "13": 23, "15": 705, "17": 5, "19": 4, "22": 3, "25": 2, "29": 3, "33": 5, "38": 5, "44": 5, "50": 1, "57": 2, "65": 1, "75": 1, "99": 7, "113": 2, "196": 2, "225": 4, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 1989, "range": [1, 200], "values": {"2": 0, "3": 3, "4": 17, "5": 4, "6": 4, "7": 1, "8": 10, "9": 89, "10": 17, "11": 6, "12": 25, "13": 11, "14": 9, "15": 3, "16": 1, "18": 1, "19": 1, "21": 1, "25": 1, "29": 1, "31": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 104455168, "range": [16, 2147483646], "values": {"0": 0, "16": 57113, "23": 4947, "28": 86, "34": 23, "41": 95, "50": 15, "61": 53, "74": 32, "90": 51, "109": 109, "132": 40, "160": 257, "194": 61, "235": 43, "284": 55622, "344": 44570, "416": 35, "503": 303, "609": 55889, "737": 498, "892": 1157, "1080": 286, "1307": 281, "1582": 556, "1915": 559, "2318": 283, "2805": 376, "3395": 6, "4109": 290, "4973": 569, "6019": 8, "7284": 101, "8815": 384, "10668": 36, "12911": 15, "15625": 2, "18910": 8, "27698": 12, "33521": 3, "40569": 2, "49098": 281, "71914": 4, "87033": 1, "105331": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 3979252, "range": [1, 2147483646], "values": {"0": 0, "1": 62237, "2": 11, "3": 179, "5": 190, "8": 330, "12": 111579, "19": 44738, "30": 2041, "47": 650, "73": 1126, "113": 288, "176": 1246, "274": 48, "426": 103, "662": 8, "1029": 5, "1599": 3, "2485": 279, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2903830, "range": [1, 150000000], "values": {"0": 3023, "1": 13315, "2": 18966, "3": 5883, "4": 3296, "5": 2304, "6": 4947, "7": 10242, "8": 35320, "10": 34638, "12": 32236, "14": 24938, "17": 7488, "20": 5201, "24": 10751, "29": 5670, "35": 3414, "42": 988, "50": 601, "60": 380, "72": 217, "87": 203, "105": 244, "126": 196, "151": 118, "182": 265, "219": 51, "263": 58, "316": 56, "380": 4, "457": 6, "549": 6, "660": 5, "793": 2, "1146": 6, "1378": 4, "1657": 1, "1992": 2, "2395": 1, "2879": 2, "3461": 2, "4161": 2, "6013": 3, "7228": 7, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1360318960, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 3, "103055": 5, "117047": 2, "124740": 4, "132939": 10, "141677": 9, "150989": 9, "160913": 8, "171489": 5, "182760": 7, "194772": 245, "207574": 472, "221217": 7, "235757": 413, "251252": 315, "267766": 1, "368115": 1, "392310": 11, "418095": 7, "474861": 22, "506072": 92, "539334": 96, "574782": 103, "612560": 117, "652821": 130, "695728": 394, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1654983084, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 14, "267766": 733, "285365": 736, "324110": 1, "345412": 2, "368115": 16, "418095": 1, "445575": 1, "506072": 113, "539334": 96, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 68, "1019325": 734, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1041228084, "range": [32768, 16777216], "values": {"0": 13, "34922": 2, "37217": 5, "39663": 5, "42270": 3, "45048": 2, "48009": 1, "51164": 5, "54527": 4, "58111": 2, "61930": 6, "66000": 1, "70338": 7, "74961": 1, "85139": 7, "90735": 3, "96699": 241, "103055": 240, "109828": 232, "117047": 2, "124740": 26, "132939": 381, "141677": 327, "160913": 1, "285365": 1, "304121": 16, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 96, "539334": 106, "574782": 297, "612560": 387, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 608050176, "range": [1024, 16777216], "values": {"0": 0, "1024": 5, "8848": 16, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 79, "33230": 204, "34899": 101, "36652": 205, "38493": 202, "40427": 202, "42458": 204, "44591": 180, "46831": 82, "49183": 9, "59836": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 709, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 48, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 2980, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 6, "range": [1, 2], "values": {"0": 0, "1": 6, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 9542, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "862": 2, "969": 1, "1223": 1, "1456": 1, "1733": 1, "1837": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 32, "range": [1, 30000], "values": {"0": 1, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 30000], "values": {"0": 1, "1": 2, "13": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 233, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 1, "140": 1, "171": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 273, "range": [1, 30000], "values": {"0": 2, "1": 1, "9": 1, "13": 1, "16": 1, "20": 1, "43": 1, "52": 1, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 142, "range": [1, 50], "values": {"0": 2586, "2": 71, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 13435, "range": [1, 10000], "values": {"0": 209, "1": 184, "2": 497, "3": 130, "4": 100, "5": 82, "6": 82, "7": 83, "8": 167, "10": 125, "12": 141, "14": 161, "17": 83, "20": 6, "24": 12, "29": 4, "40": 2, "48": 2, "57": 2, "68": 2, "81": 4, "96": 1, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 7137, "range": [1, 10000], "values": {"0": 180, "1": 86, "2": 244, "3": 66, "4": 54, "5": 37, "6": 41, "7": 49, "8": 71, "10": 62, "12": 73, "14": 70, "17": 21, "20": 7, "24": 18, "29": 8, "34": 1, "40": 5, "48": 1, "57": 1, "68": 1, "81": 4, "135": 1, "160": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"0": 0, "1": 52, "2": 6, "3": 6, "4": 2, "5": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 2, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 830, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "485": 1, "535": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 37, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 2, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 292, "range": [1, 10000], "values": {"0": 2, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5109, "range": [1, 1000], "values": {"0": 294, "1": 524, "2": 370, "3": 298, "4": 188, "5": 93, "6": 87, "7": 54, "8": 40, "9": 8, "10": 13, "11": 6, "12": 5, "14": 6, "16": 1, "20": 1, "26": 1, "33": 1, "37": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2198, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "909": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2335, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "909": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 57, "range": [1, 5000], "values": {"1": 0, "2": 7, "3": 7, "5": 1, "17": 1, "18": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 15, "range": [1, 5000], "values": {"0": 21, "1": 5, "2": 2, "6": 1, "7": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 624, "range": [1, 50000], "values": {"4": 0, "5": 1, "131": 1, "144": 1, "158": 1, "174": 1, "192": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3098, "range": [1, 50000], "values": {"15": 0, "17": 1, "666": 1, "733": 2, "807": 1, "888": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4136, "range": [1, 50000], "values": {"25": 0, "28": 1, "666": 1, "733": 1, "807": 1, "1736": 1, "1911": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4192, "range": [1, 50000], "values": {"45": 0, "50": 1, "666": 1, "733": 1, "807": 1, "1736": 1, "1911": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4604, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4605, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4634, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 263, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "158": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2320, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "888": 1, "977": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1175, "range": [1, 50000], "values": {"454": 0, "500": 1, "605": 1, "666": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2200, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "888": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1807, "range": [1, 50000], "values": {"412": 0, "454": 1, "605": 1, "666": 1, "733": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2706, "range": [1, 50000], "values": {"0": 38, "1": 7, "2": 18, "3": 4, "4": 6, "5": 9, "6": 2, "7": 1, "8": 2, "10": 4, "11": 3, "12": 4, "13": 4, "14": 3, "15": 4, "17": 3, "19": 1, "21": 1, "25": 1, "37": 1, "41": 1, "131": 1, "174": 1, "192": 3, "211": 1, "255": 1, "281": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1852, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "23": 1, "25": 1, "28": 2, "31": 2, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "74": 1, "108": 1, "119": 1, "158": 1, "232": 1, "255": 2, "281": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 514, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "61": 1, "67": 1, "119": 1, "158": 1, "174": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 142, "range": [1, 50], "values": {"0": 71, "1": 53, "2": 27, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3005, "range": [1, 2000], "values": {"1": 0, "2": 5, "3": 6, "4": 32, "5": 39, "6": 13, "7": 8, "8": 4, "9": 2, "10": 4, "11": 6, "13": 4, "15": 2, "17": 2, "19": 4, "22": 2, "29": 1, "33": 8, "38": 9, "50": 4, "57": 1, "65": 2, "75": 3, "113": 1, "130": 1, "149": 1, "171": 2, "196": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 393, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "99": 1, "113": 1, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 2016, "range": [1, 2000], "values": {"1": 0, "2": 5, "3": 7, "4": 4, "6": 3, "8": 15, "9": 9, "10": 10, "11": 5, "33": 8, "38": 11, "75": 1, "86": 2, "99": 1, "149": 1, "171": 2, "196": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 7742, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 6, "5": 3, "6": 6, "7": 17, "8": 9, "9": 7, "10": 8, "11": 7, "13": 4, "15": 4, "17": 3, "19": 6, "22": 10, "25": 10, "29": 11, "33": 38, "38": 14, "44": 5, "50": 3, "57": 2, "65": 8, "75": 1, "113": 5, "130": 7, "196": 7, "225": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 22, "range": [1, 2000], "values": {"19": 0, "22": 1, "25": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 52, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 2, "4": 2, "7": 1, "11": 1, "15": 1, "17": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2643, "range": [1, 2000], "values": {"0": 4, "1": 10, "2": 6, "3": 38, "4": 63, "5": 15, "6": 2, "8": 1, "9": 1, "11": 1, "13": 1, "15": 3, "19": 2, "22": 2, "25": 2, "29": 1, "33": 7, "38": 2, "44": 6, "50": 3, "57": 3, "65": 3, "99": 3, "171": 1, "258": 1, "296": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 338, "range": [1, 2000], "values": {"4": 0, "5": 2, "7": 1, "17": 1, "19": 1, "258": 1, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 1169, "range": [1, 2000], "values": {"0": 2, "1": 128, "2": 45, "4": 4, "5": 1, "6": 1, "8": 2, "10": 2, "13": 1, "15": 1, "17": 1, "19": 1, "22": 1, "38": 1, "50": 2, "57": 1, "99": 1, "130": 1, "339": 1, "389": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 4075, "range": [1, 2000], "values": {"0": 1, "1": 15, "2": 60, "3": 83, "4": 10, "5": 9, "6": 4, "7": 7, "8": 2, "9": 2, "11": 6, "13": 4, "15": 2, "17": 6, "19": 6, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "99": 1, "149": 2, "258": 1, "296": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 81, "range": [1, 2000], "values": {"4": 0, "5": 1, "15": 1, "22": 1, "38": 1, "44": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 529, "range": [1, 2000], "values": {"0": 1, "2": 2, "9": 1, "10": 1, "25": 2, "33": 1, "171": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3951, "range": [1, 2000], "values": {"0": 17, "1": 9, "2": 7, "3": 27, "4": 26, "5": 52, "6": 1, "10": 1, "11": 1, "15": 3, "22": 3, "25": 1, "33": 5, "38": 2, "44": 4, "50": 6, "57": 3, "65": 2, "75": 1, "86": 1, "99": 2, "113": 1, "171": 2, "258": 2, "339": 1, "389": 1, "446": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 375, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "5": 1, "19": 1, "25": 1, "296": 1, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 1108, "range": [1, 2000], "values": {"0": 17, "1": 135, "2": 26, "4": 3, "5": 1, "6": 1, "8": 2, "10": 1, "15": 1, "17": 1, "19": 1, "22": 1, "38": 1, "50": 2, "57": 1, "99": 1, "130": 1, "339": 1, "389": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 5803, "range": [1, 2000], "values": {"0": 6, "1": 11, "2": 53, "3": 98, "4": 7, "5": 4, "6": 2, "7": 1, "8": 2, "10": 1, "11": 4, "13": 4, "15": 4, "17": 3, "19": 4, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 2, "99": 2, "130": 1, "149": 2, "171": 1, "258": 1, "339": 2, "389": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 296, "range": [1, 2000], "values": {"22": 0, "25": 1, "50": 2, "149": 1, "171": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 589, "range": [1, 2000], "values": {"0": 0, "1": 1, "2": 1, "6": 1, "9": 1, "11": 1, "15": 1, "44": 1, "113": 1, "149": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 358, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "148": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 108, "range": [1, 50000], "values": {"3": 0, "4": 2, "5": 3, "11": 1, "12": 2, "13": 1, "17": 1, "19": 1, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 4, "range": [1, 50000], "values": {"0": 0, "1": 4, "2": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 2067, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 1, "255": 1, "281": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 83, "range": [1, 50000], "values": {"0": 9, "2": 5, "3": 1, "6": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 61, "range": [1, 50000], "values": {"0": 25, "2": 6, "4": 1, "5": 1, "10": 1, "12": 1, "17": 1, "19": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 178, "range": [1, 50000], "values": {"0": 4, "1": 3, "2": 7, "3": 2, "4": 2, "6": 1, "8": 1, "10": 3, "12": 1, "13": 1, "14": 1, "15": 3, "17": 1, "19": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "61": 1, "108": 1, "232": 1, "255": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 772, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "255": 2, "281": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 416, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "74": 1, "119": 1, "158": 1, "174": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 514, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "61": 1, "67": 1, "119": 1, "158": 1, "174": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 285, "power.total_thread_wakeups": 3057046, "power.total_cpu_time_ms": 1149878}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 325799, "web.foreground": 724947, "prealloc": 96753, "privilegedabout": 2379}, "power.wakeups_per_process_type": {"web.background": 1232871, "web.foreground": 977351, "prealloc": 838242, "privilegedabout": 8582}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3144, "range": [1, 10000], "values": {"0": 0, "1": 214, "2": 1401, "3": 40, "4": 2, "5": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 24705, "range": [1, 100], "values": {"11": 0, "14": 1647, "17": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 11265, "range": [1, 100], "values": {"0": 0, "1": 960, "14": 687, "17": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 69420, "range": [1, 10000], "values": {"8": 0, "10": 13, "12": 15, "14": 814, "17": 112, "20": 6, "57": 3, "68": 513, "81": 158, "96": 12, "135": 1, "160": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 153022, "range": [1, 10000], "values": {"24": 0, "29": 1, "40": 6, "48": 109, "57": 776, "68": 35, "81": 17, "96": 22, "114": 609, "135": 45, "160": 9, "190": 5, "226": 4, "268": 2, "318": 4, "378": 3, "449": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8512, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8512, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 47152, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 15, "6": 19, "7": 13, "8": 33, "10": 31, "12": 20, "14": 751, "17": 69, "20": 5, "24": 2, "29": 1, "34": 20, "40": 18, "48": 646, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 1000], "values": {"0": 1620, "1": 27, "2": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 54955, "range": [1, 10000], "values": {"5": 0, "6": 6, "7": 11, "8": 526, "10": 410, "12": 4, "14": 3, "48": 3, "57": 462, "68": 209, "81": 10, "96": 2, "114": 1, "135": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12753, "range": [1, 10000], "values": {"2": 0, "3": 20, "4": 231, "5": 650, "6": 53, "7": 5, "8": 75, "10": 294, "12": 65, "14": 247, "17": 3, "20": 3, "34": 1, "40": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 68685, "range": [1, 10000], "values": {"0": 5179, "1": 24, "2": 38, "3": 48, "4": 75, "5": 104, "6": 30, "7": 21, "8": 123, "10": 336, "12": 100, "14": 1412, "17": 209, "20": 85, "24": 19, "29": 14, "34": 26, "40": 20, "48": 649, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 65139, "range": [1, 100], "values": {"0": 638, "1": 42, "7": 5, "12": 2, "51": 2, "56": 8, "62": 217, "67": 704, "73": 10, "78": 11, "84": 8, "89": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4066429, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "28": 1, "32": 18, "36": 60, "41": 80, "47": 78, "53": 47, "60": 61, "68": 305, "77": 410, "88": 917, "100": 597, "114": 260, "130": 220, "148": 144, "168": 152, "191": 123, "217": 50, "247": 79, "281": 216, "320": 396, "364": 563, "414": 710, "471": 1228, "536": 1137, "610": 373, "695": 446, "791": 561, "901": 310, "1026": 170, "1168": 87, "1330": 21, "1724": 1, "1963": 2, "2235": 2, "2898": 1, "3300": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 81144, "range": [1, 2000], "values": {"0": 1709, "1": 76, "2": 21, "3": 22, "4": 33, "5": 40, "6": 36, "7": 28, "8": 34, "9": 35, "10": 46, "11": 72, "13": 41, "15": 4535, "17": 44, "19": 33, "22": 8, "25": 12, "29": 7, "33": 2, "38": 7, "50": 13, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 8508, "range": [1, 200], "values": {"2": 0, "3": 53, "4": 600, "5": 215, "6": 618, "7": 144, "8": 7, "9": 3, "10": 2, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 12515425464, "range": [16, 2147483646], "values": {"0": 0, "16": 9112, "23": 123, "34": 261, "41": 3711, "50": 18, "61": 61, "74": 23, "90": 9, "109": 52, "132": 2919, "160": 22, "194": 23, "235": 3711, "284": 2238, "344": 747, "416": 3704, "503": 3746, "609": 268, "737": 2988, "892": 526, "1080": 293, "1307": 537, "1582": 730, "1915": 550, "2318": 259, "2805": 352, "3395": 159, "4109": 76, "4973": 1021, "6019": 75, "7284": 97, "8815": 30, "10668": 6, "12911": 56, "15625": 1, "18910": 6, "40569": 2, "49098": 2, "225968": 13, "3267857": 3698, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 9857695, "range": [1, 2147483646], "values": {"0": 0, "1": 9235, "3": 4071, "5": 33, "8": 3005, "12": 5529, "19": 9986, "30": 4330, "47": 877, "73": 297, "113": 338, "176": 826, "1599": 3698, "2485": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 21839861, "range": [1, 150000000], "values": {"0": 153, "1": 435, "2": 229, "3": 105, "4": 554, "5": 1783, "6": 1873, "7": 1800, "8": 3673, "10": 3905, "12": 1999, "14": 2455, "17": 1937, "20": 5005, "24": 2421, "29": 2165, "35": 2171, "42": 1688, "50": 1066, "60": 543, "72": 240, "87": 109, "105": 83, "126": 70, "151": 122, "182": 672, "219": 521, "263": 496, "316": 173, "380": 29, "457": 1, "549": 4, "660": 30, "793": 12, "953": 2, "1657": 3, "2395": 9, "2879": 38, "3461": 18, "4161": 9, "5002": 3271, "6013": 342, "7228": 7, "8689": 1, "10445": 1, "12556": 1, "31521": 1, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 261730648, "range": [32768, 16777216], "values": {"207574": 0, "221217": 1, "235757": 4, "267766": 3, "285365": 8, "304121": 10, "324110": 249, "345412": 273, "368115": 189, "392310": 1, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 391491288, "range": [32768, 16777216], "values": {"474861": 0, "506072": 738, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 186321668, "range": [32768, 16777216], "values": {"141677": 0, "150989": 1, "160913": 4, "171489": 2, "182760": 5, "194772": 4, "207574": 3, "221217": 180, "235757": 109, "251252": 249, "267766": 180, "285365": 1, "304121": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 76999680, "range": [1024, 16777216], "values": {"97683": 0, "102590": 738, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 738, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 2, "1": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 39, "range": [1, 50000], "values": {"0": 6, "1": 13, "2": 3, "3": 2, "4": 2, "6": 1, "7": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 713951, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "131": 6, "144": 47, "158": 656, "174": 960, "192": 1521, "211": 442, "232": 60, "309": 1, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 7647, "range": [1, 2000], "values": {"0": 0, "1": 42, "2": 87, "3": 42, "4": 33, "5": 25, "6": 27, "7": 15, "8": 27, "9": 13, "10": 19, "11": 24, "13": 21, "15": 24, "17": 12, "19": 7, "22": 7, "25": 8, "29": 48, "33": 4, "38": 13, "44": 28, "50": 22, "57": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4427, "range": [1, 2000], "values": {"0": 37, "1": 163, "2": 80, "3": 62, "4": 36, "5": 35, "6": 31, "7": 13, "8": 15, "9": 10, "10": 5, "11": 13, "13": 6, "15": 8, "17": 4, "19": 2, "22": 2, "33": 7, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "225": 2, "258": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3713, "range": [1, 2000], "values": {"0": 21, "1": 159, "2": 93, "3": 68, "4": 43, "5": 31, "6": 32, "7": 17, "8": 17, "9": 10, "10": 9, "11": 14, "13": 5, "15": 6, "17": 5, "19": 3, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 39, "range": [1, 50000], "values": {"0": 6, "1": 13, "2": 3, "3": 2, "4": 2, "6": 1, "7": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 713868, "range": [1, 50000], "values": {"119": 0, "131": 6, "144": 47, "158": 656, "174": 960, "192": 1522, "211": 441, "232": 60, "309": 1, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1055915, "power.total_cpu_time_ms": 919647}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 919647}, "power.wakeups_per_process_type": {"extension": 1055915}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 72, "power.total_cpu_time_ms": 6}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 6}, "power.wakeups_per_process_type": {"socket": 72}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1542302, "power.total_cpu_time_ms": 86175}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 86175}, "power.wakeups_per_process_type": {"utility": 1542302}}}}, "histograms": {"CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10136, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 5, "31": 86, "34": 70, "38": 35, "42": 61, "46": 10, "51": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7987, "range": [1, 1000], "values": {"0": 196, "1": 485, "2": 1237, "3": 407, "4": 165, "5": 101, "6": 42, "7": 52, "8": 27, "9": 19, "10": 17, "11": 11, "12": 21, "14": 5, "16": 8, "18": 5, "20": 7, "23": 5, "26": 1, "29": 4, "33": 2, "37": 1, "42": 1, "47": 3, "75": 1, "84": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2042, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 13, "6": 34, "7": 117, "8": 91, "9": 7, "10": 3, "12": 2, "13": 3, "15": 1, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12647, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 67, "42": 83, "46": 57, "51": 59, "56": 3, "62": 1, "68": 1, "75": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 277, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2248, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 21, "6": 10, "7": 130, "8": 115, "10": 8, "12": 5, "14": 1, "17": 1, "20": 1, "24": 1, "29": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 20, "range": [1, 100], "values": {"1": 0, "2": 10, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 13, "range": [1, 100], "values": {"0": 0, "1": 7, "2": 3, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 457, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 4, "40": 3, "48": 1, "81": 1, "96": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1364, "range": [1, 10000], "values": {"57": 0, "68": 1, "81": 4, "96": 1, "114": 1, "190": 1, "226": 2, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 65, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 65, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 378, "range": [1, 10000], "values": {"12": 0, "14": 1, "29": 2, "34": 3, "40": 2, "48": 2, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 1000], "values": {"0": 6, "1": 1, "2": 1, "4": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 284, "range": [1, 10000], "values": {"17": 0, "20": 1, "24": 6, "34": 3, "40": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 136, "range": [1, 10000], "values": {"5": 0, "6": 2, "8": 2, "12": 2, "14": 1, "20": 2, "24": 1, "29": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 26, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 445, "range": [1, 10000], "values": {"0": 47, "1": 1, "3": 1, "4": 3, "8": 1, "14": 1, "17": 1, "24": 1, "29": 2, "34": 3, "40": 2, "48": 2, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 201, "range": [1, 100], "values": {"0": 1, "1": 1, "7": 2, "23": 3, "29": 1, "34": 1, "40": 1, "45": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 920079, "range": [1, 1000000], "values": {"3": 0, "4": 1, "6": 1, "13": 1, "15": 1, "17": 3, "19": 6, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 4, "47": 8, "53": 38, "60": 45, "68": 70, "77": 175, "88": 386, "100": 771, "114": 956, "130": 731, "148": 355, "168": 118, "191": 81, "217": 103, "247": 73, "281": 51, "320": 36, "364": 36, "414": 45, "471": 65, "536": 98, "610": 73, "695": 15, "791": 12, "901": 9, "1026": 6, "1168": 1, "1330": 8, "1514": 5, "1724": 4, "1963": 5, "2235": 4, "2545": 3, "2898": 10, "3300": 4, "3758": 1, "4279": 7, "4872": 2, "5548": 5, "6317": 1, "7193": 1, "8190": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 887, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 1, "4": 1, "9": 1, "11": 1, "13": 1, "15": 24, "17": 1, "22": 2, "50": 1, "75": 1, "99": 1, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 65, "range": [1, 200], "values": {"3": 0, "4": 5, "5": 2, "6": 2, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 62717584, "range": [16, 2147483646], "values": {"0": 0, "16": 65180, "23": 1678, "34": 26, "41": 9, "50": 9, "61": 66, "74": 119, "90": 22, "109": 45, "132": 3, "160": 4, "194": 134, "235": 1835, "284": 1588, "344": 185, "416": 359, "503": 55746, "609": 3367, "737": 29, "892": 7632, "1080": 11988, "1307": 287, "1582": 39, "1915": 2, "2318": 14, "2805": 2, "3395": 4, "4109": 5, "4973": 7, "6019": 9, "7284": 26, "18910": 6, "22886": 24, "40569": 5, "71914": 9, "87033": 2, "105331": 1, "154277": 3, "225968": 3, "273476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 1626971, "range": [1, 2147483646], "values": {"0": 0, "1": 66858, "3": 1623, "5": 281, "8": 57743, "12": 322, "19": 3573, "30": 19886, "47": 98, "73": 36, "113": 7, "176": 2, "1029": 24, "2485": 1, "6002": 11, "9328": 4, "14498": 3, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2430932, "range": [1, 150000000], "values": {"0": 239, "1": 3082, "2": 4996, "3": 6909, "4": 8118, "5": 12212, "6": 9307, "7": 5446, "8": 15844, "10": 11393, "12": 10305, "14": 18221, "17": 14315, "20": 13174, "24": 6679, "29": 2556, "35": 1887, "42": 1830, "50": 1381, "60": 783, "72": 548, "87": 359, "105": 208, "126": 123, "151": 150, "182": 143, "219": 41, "263": 31, "316": 16, "380": 14, "457": 13, "549": 26, "660": 15, "793": 13, "953": 14, "1146": 8, "1378": 12, "1657": 13, "1992": 10, "2395": 4, "2879": 12, "3461": 7, "4161": 8, "5002": 2, "6013": 4, "7228": 1, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 489844544, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 4, "418095": 1, "445575": 6, "539334": 60, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 602499292, "range": [32768, 16777216], "values": {"741455": 0, "790188": 735, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1757696804, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 7, "1696203": 1, "1807688": 1, "1926500": 6, "2053121": 21, "2188065": 143, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 325641628, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 3, "221217": 1, "235757": 6, "324110": 38, "345412": 107, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 38612992, "range": [1024, 16777216], "values": {"46831": 0, "49183": 89, "51654": 477, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 735, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 9785, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 224, "33": 37, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 228, "range": [1, 200000], "values": {"6": 0, "8": 2, "10": 4, "17": 6, "22": 1, "28": 1, "36": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 822, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 4, "22": 5, "28": 2, "36": 1, "46": 1, "74": 3, "151": 1, "192": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 4, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 644, "range": [1, 100000], "values": {"0": 14, "1": 5, "5": 1, "16": 2, "20": 1, "25": 3, "31": 5, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 1467, "range": [1, 2], "values": {"0": 51, "1": 1467, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 1, "1": 3, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 380, "range": [1, 3], "values": {"0": 280, "2": 190, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 190, "range": [1, 2], "values": {"0": 0, "1": 190, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 905, "range": [1, 60000], "values": {"0": 189, "874": 1, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 1937, "range": [1, 16], "values": {"2": 0, "3": 19, "4": 470, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 839, "range": [1, 16], "values": {"1": 0, "2": 19, "3": 267, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 476, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 461, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 16, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 460, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 71600, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 9, "39": 10, "41": 13, "43": 21, "45": 11, "47": 21, "49": 18, "51": 8, "53": 19, "55": 17, "58": 20, "61": 9, "64": 10, "67": 11, "70": 2, "73": 11, "76": 13, "80": 8, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 12, "248": 8, "259": 8, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 69743, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 8, "39": 7, "41": 10, "43": 20, "45": 10, "47": 20, "49": 18, "51": 8, "53": 19, "55": 17, "58": 20, "61": 9, "64": 10, "67": 10, "70": 2, "73": 11, "76": 13, "80": 8, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 12, "248": 8, "259": 7, "271": 3, "283": 2, "296": 2, "310": 6, "324": 2, "339": 8, "355": 3, "371": 4, "388": 7, "406": 6, "425": 12, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1111, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 3, "37": 1, "41": 2, "45": 1, "47": 2, "53": 1, "115": 1, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 70489, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 8, "39": 10, "41": 11, "43": 21, "45": 10, "47": 19, "49": 18, "51": 8, "53": 18, "55": 17, "58": 20, "61": 9, "64": 10, "67": 11, "70": 2, "73": 11, "76": 13, "80": 8, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 4, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 6, "158": 3, "165": 2, "173": 4, "181": 3, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 12, "248": 8, "259": 8, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1052669, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 8, "3314": 73, "3855": 17, "4484": 2, "5216": 117, "6067": 4, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 266, "range": [1, 2], "values": {"0": 223, "1": 266, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 936, "range": [1, 16], "values": {"3": 0, "4": 212, "8": 11, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 1836, "range": [1, 16], "values": {"3": 0, "4": 73, "8": 193, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 659, "range": [1, 100000], "values": {"0": 183, "1": 109, "2": 28, "3": 6, "4": 2, "5": 3, "6": 1, "8": 19, "25": 6, "39": 1, "61": 1, "76": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 14961, "range": [1, 16], "values": {"0": 1, "1": 1, "2": 51, "8": 181, "9": 56, "10": 1285, "11": 4, "12": 1, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 1679, "range": [1, 2], "values": {"0": 5, "1": 1679, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 5399, "range": [1, 5000], "values": {"0": 191, "1": 6, "2": 2, "3": 4, "7": 4, "10": 2, "11": 3, "12": 2, "13": 1, "14": 3, "15": 6, "16": 3, "17": 5, "18": 2, "19": 5, "20": 2, "21": 4, "23": 5, "25": 4, "27": 2, "29": 3, "35": 3, "38": 3, "41": 1, "47": 3, "54": 2, "58": 1, "66": 1, "87": 2, "93": 1, "115": 1, "123": 1, "132": 2, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 7527, "range": [1, 5000], "values": {"0": 122, "1": 3, "2": 2, "3": 5, "10": 1, "11": 2, "12": 5, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 7, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "71": 1, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 1, "142": 1, "163": 3, "188": 1, "202": 2, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 15193, "range": [1, 16], "values": {"0": 0, "1": 2797, "2": 150, "6": 1820, "7": 168, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 4114, "range": [1, 60000], "values": {"0": 2, "1": 15, "2": 10, "3": 5, "4": 1, "5": 1, "6": 2, "9": 2, "11": 3, "14": 3, "17": 6, "21": 8, "26": 13, "32": 7, "40": 8, "50": 6, "62": 3, "77": 8, "95": 5, "118": 3, "146": 3, "181": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 77708, "range": [1, 60000], "values": {"0": 227, "1": 325, "2": 42, "3": 13, "4": 1, "5": 1, "6": 5, "7": 12, "9": 11, "11": 42, "14": 144, "17": 133, "21": 144, "26": 169, "32": 212, "40": 96, "50": 63, "62": 45, "77": 45, "95": 59, "118": 42, "146": 36, "181": 27, "224": 27, "278": 26, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 498, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 250, "range": [1, 60000], "values": {"0": 2396, "1": 41, "2": 14, "3": 8, "7": 1, "11": 2, "14": 1, "17": 2, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 2569, "range": [1, 60000], "values": {"17": 0, "21": 1, "26": 1, "40": 1, "77": 1, "146": 2, "181": 4, "531": 2, "658": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 557, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 8639, "range": [1, 50], "values": {"0": 39, "3": 13, "4": 267, "6": 14, "8": 931, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 280, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 6, "range": [1, 1000], "values": {"5": 0, "6": 1, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 6, "range": [1, 1000], "values": {"5": 0, "6": 1, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 10, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 3, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 14, "range": [1, 50], "values": {"1": 0, "2": 7, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 4, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 500], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 63, "range": [1, 1000], "values": {"0": 1, "1": 2, "29": 1, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 58, "range": [1, 5000], "values": {"0": 3, "55": 1, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 5, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1273, "range": [50, 1000], "values": {"69": 0, "77": 1, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 13, "range": [50, 10000], "values": {"0": 2, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 74, "range": [1, 50], "values": {"36": 0, "37": 2, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 462, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 3, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 169, "range": [1, 1000], "values": {"9": 0, "13": 1, "19": 3, "27": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 80, "range": [1, 1000], "values": {"0": 0, "1": 1, "2": 1, "6": 1, "13": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 379, "range": [1, 1000], "values": {"39": 0, "56": 2, "115": 2, "165": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 6, "range": [1, 100], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 6, "range": [1, 100], "values": {"0": 0, "1": 2, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 4430, "range": [1, 10000], "values": {"708": 0, "759": 1, "860": 1, "1011": 1, "1718": 1, "1769": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 6441, "range": [1, 10000], "values": {"0": 283, "1": 2991, "2": 565, "3": 66, "4": 25, "5": 21, "6": 21, "7": 7, "8": 21, "10": 11, "12": 11, "14": 9, "17": 1, "20": 7, "24": 3, "40": 6, "48": 2, "96": 3, "114": 2, "135": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 3446, "range": [1, 10000], "values": {"0": 238, "1": 1412, "2": 363, "3": 44, "4": 17, "5": 14, "6": 14, "7": 3, "8": 10, "10": 7, "12": 7, "14": 6, "17": 1, "20": 3, "24": 1, "29": 1, "40": 3, "48": 2, "96": 1, "114": 1, "135": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 76, "range": [1, 2000], "values": {"0": 159, "1": 8, "2": 1, "3": 2, "5": 1, "7": 2, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"1": 0, "2": 45, "3": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 239, "range": [1, 10000], "values": {"0": 198, "1": 239, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 253, "range": [1, 10000], "values": {"0": 446, "1": 19, "2": 14, "3": 9, "4": 7, "5": 1, "7": 3, "8": 14, "10": 1, "12": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 8731, "range": [1, 64], "values": {"13": 0, "14": 19, "18": 465, "19": 5, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 6142, "range": [1, 36], "values": {"22": 0, "23": 1, "29": 211, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1306, "range": [1, 16], "values": {"3": 0, "4": 85, "7": 138, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 1656, "range": [1, 24], "values": {"11": 0, "12": 138, "13": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 1955, "range": [1, 36], "values": {"22": 0, "23": 85, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 7, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1144, "range": [1, 8], "values": {"0": 0, "1": 266, "2": 7, "4": 216, "5": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 242, "range": [1, 24], "values": {"0": 0, "1": 242, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 692, "range": [1, 10], "values": {"0": 0, "1": 692, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 692, "range": [1, 10], "values": {"1": 0, "2": 34, "3": 208, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 748, "range": [1, 10], "values": {"0": 0, "1": 678, "5": 14, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 242, "range": [1, 10], "values": {"0": 0, "1": 242, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 10127, "range": [1, 256], "values": {"14": 0, "15": 73, "20": 114, "89": 9, "116": 23, "119": 2, "145": 21, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 24, "range": [1, 2], "values": {"0": 0, "1": 24, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 91, "range": [1, 512], "values": {"12": 0, "13": 7, "14": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 242, "range": [1, 4], "values": {"0": 0, "1": 242, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 12, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 12, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 12, "1": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 10875, "range": [1, 30000], "values": {"12": 0, "19": 110, "29": 143, "45": 1, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 12, "range": [1, 10], "values": {"0": 0, "1": 12, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 12, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 9178, "range": [1, 1000], "values": {"0": 4, "1": 227, "2": 359, "3": 381, "4": 231, "5": 261, "6": 121, "7": 80, "8": 88, "9": 55, "10": 30, "11": 28, "12": 32, "14": 20, "16": 7, "18": 8, "20": 9, "26": 3, "29": 2, "33": 1, "42": 2, "53": 1, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 411463798, "range": [1, 5000], "values": {"13": 0, "15": 1, "18": 3, "21": 5, "25": 7, "29": 12, "34": 13, "40": 24, "47": 22, "55": 28, "64": 27, "75": 54, "88": 203, "103": 1323, "120": 112, "140": 42, "164": 29, "192": 23, "224": 4, "262": 8, "306": 5, "357": 1, "417": 2, "487": 2, "569": 1, "777": 1, "1059": 1, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 412382282, "range": [8, 792], "values": {"8": 0, "16": 1, "32": 2, "48": 6, "56": 1, "64": 3, "72": 3, "80": 1, "88": 1, "96": 15, "104": 416, "112": 912, "120": 224, "128": 56, "136": 28, "144": 18, "152": 10, "160": 7, "168": 7, "176": 2, "184": 4, "200": 2, "208": 10, "216": 9, "224": 14, "232": 9, "240": 7, "248": 4, "256": 5, "264": 4, "272": 2, "280": 2, "288": 1, "296": 3, "304": 2, "312": 1, "320": 3, "328": 4, "336": 1, "344": 1, "360": 3, "384": 1, "424": 1, "432": 1, "464": 1, "480": 1, "488": 2, "504": 1, "552": 1, "560": 1, "600": 1, "624": 1, "792": 5}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 645378, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 1, "47": 4, "55": 3, "64": 5, "75": 2, "88": 3, "103": 1086, "120": 242, "140": 28, "164": 8, "192": 20, "224": 36, "262": 11, "306": 9, "357": 4, "417": 4, "487": 5, "569": 2, "777": 1, "5000": 1}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 89, "range": [1, 5000], "values": {"0": 0, "1": 6, "2": 26, "9": 1, "21": 1, "23": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 60, "1": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 15, "range": [1, 50], "values": {"1": 0, "2": 2, "11": 1, "12": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 7735, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 376, "9": 4, "12": 1, "14": 2, "17": 361, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 12, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1076, "range": [1, 50], "values": {"1": 0, "2": 12, "6": 167, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 3584, "range": [1, 50], "values": {"11": 0, "12": 12, "20": 172, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 4, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 728, "range": [1, 50], "values": {"0": 0, "1": 728, "2": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 3, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 74, "range": [1, 2000], "values": {"0": 88, "1": 7, "2": 3, "3": 1, "8": 5, "9": 2, "10": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"0": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 131, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 1, "1": 2, "2": 3, "3": 1, "4": 1, "6": 2, "7": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 144, "range": [1, 2000], "values": {"0": 1, "1": 91, "2": 14, "3": 3, "4": 4, "5": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2049, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "41": 1, "45": 1, "47": 2, "53": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 25628, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 6, "41": 4, "43": 15, "45": 6, "47": 10, "49": 8, "51": 5, "53": 10, "55": 13, "58": 11, "61": 5, "64": 6, "67": 5, "73": 6, "76": 4, "80": 2, "84": 5, "88": 5, "92": 1, "96": 3, "100": 1, "105": 2, "110": 1, "120": 5, "126": 1, "132": 2, "138": 1, "151": 4, "158": 2, "165": 1, "173": 2, "181": 3, "189": 2, "217": 2, "227": 3, "248": 4, "259": 3, "271": 1, "310": 4, "324": 1, "339": 3, "355": 1, "371": 2, "388": 1, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 43576, "range": [1, 60000], "values": {"32": 0, "33": 1, "37": 1, "39": 4, "41": 7, "43": 7, "45": 5, "47": 9, "49": 10, "51": 3, "53": 7, "55": 6, "58": 10, "61": 4, "64": 4, "67": 6, "70": 2, "73": 5, "76": 9, "80": 6, "84": 6, "88": 11, "92": 7, "96": 4, "100": 6, "105": 6, "110": 1, "115": 4, "120": 1, "126": 5, "132": 1, "138": 1, "144": 1, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 2, "217": 13, "227": 24, "237": 12, "248": 4, "259": 5, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 3, "371": 2, "388": 6, "406": 4, "425": 10, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 549, "range": [1, 60000], "values": {"508": 0, "531": 1, "555": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 1, "range": [1, 32], "values": {"0": 306, "1": 1, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 2563, "range": [1, 50], "values": {"0": 181, "1": 43, "2": 1252, "3": 4, "4": 1, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 103, "range": [1, 50], "values": {"0": 1, "1": 1, "2": 51, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 79, "range": [1, 50], "values": {"0": 0, "1": 13, "2": 33, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 198, "range": [1, 100], "values": {"17": 0, "18": 11, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 296, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 462, "range": [1, 100], "values": {"41": 0, "42": 11, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 442, "range": [1, 2], "values": {"0": 0, "1": 442, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1076, "range": [1, 2], "values": {"0": 5, "1": 1076, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 2976, "range": [1, 50], "values": {"5": 0, "6": 496, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 11826, "range": [1, 50], "values": {"5": 0, "6": 1957, "7": 12, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 1064, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 436, "range": [1, 100000], "values": {"229": 0, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 814, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 2, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 4, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 2, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 2, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 187, "range": [1, 60000], "values": {"0": 8, "1": 1, "2": 1, "5": 3, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11150, "range": [1, 60000], "values": {"0": 1, "1": 69, "2": 6, "5": 1, "8": 2, "21": 194, "34": 60, "137": 16, "219": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11337, "range": [1, 60000], "values": {"0": 9, "1": 70, "2": 7, "5": 4, "8": 2, "21": 194, "34": 60, "137": 17, "219": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 10848, "range": [1, 5000], "values": {"0": 67, "1": 9, "2": 1, "4": 1, "6": 1, "18": 18, "26": 223, "37": 13, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 10999, "range": [1, 5000], "values": {"0": 77, "1": 9, "2": 2, "3": 2, "4": 1, "6": 1, "18": 18, "26": 223, "37": 13, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 151, "range": [1, 5000], "values": {"0": 10, "2": 1, "3": 2, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 6, "range": [1, 5000], "values": {"0": 347, "1": 1, "4": 1, "6": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 36, "range": [1, 5000], "values": {"0": 355, "1": 6, "4": 1, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 30, "range": [1, 5000], "values": {"0": 8, "1": 5, "18": 1, "26": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 855, "range": [1, 50], "values": {"0": 101, "1": 261, "2": 297, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 2208, "range": [1, 50], "values": {"0": 173, "1": 542, "2": 833, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 47, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 18, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 14, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 141, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 16, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2214, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 15, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 27, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4406, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3050, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 216, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 149, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 152, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7058, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 73, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 283, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 443, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 14, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1828, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 880, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 91656, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 15, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 459, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 168, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 40, "range": [1, 50], "values": {"4": 0, "5": 8, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 912, "range": [1, 50], "values": {"16": 0, "17": 19, "19": 31, "20": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 399, "range": [1, 50], "values": {"16": 0, "17": 15, "18": 8, "19": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 12, "range": [1, 50], "values": {"1": 0, "2": 6, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 354, "range": [1, 50], "values": {"1": 0, "2": 177, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 76, "range": [1, 50], "values": {"1": 0, "2": 38, "3": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 158, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 9, "17": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 211, "range": [1, 50], "values": {"15": 0, "16": 12, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 16, "range": [1, 50], "values": {"1": 0, "2": 8, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 5, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3425, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 174, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1388, "range": [1, 50], "values": {"15": 0, "16": 6, "19": 68, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 46, "range": [1, 50], "values": {"1": 0, "2": 23, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1138, "range": [1, 50], "values": {"3": 0, "4": 102, "16": 18, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 84801, "subsessionLength": 44301, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}