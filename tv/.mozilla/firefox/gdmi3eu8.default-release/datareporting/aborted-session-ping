{"type": "main", "id": "1053dfba-ab9c-420a-9099-e2e2d2e91d22", "creationDate": "2025-05-25T10:03:21.163Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 83901, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 30}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 8, "browser.engagement.tab_open_event_count": 2, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 2, "dom.contentprocess.os_priority_raised": 267, "browser.engagement.unique_domains_count": 2, "dom.contentprocess.os_priority_lowered": 19, "urlbar.zeroprefix.abandonment": 1, "dom.contentprocess.os_priority_change_considered": 41, "browser.engagement.active_ticks": 30, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 8, "power.total_thread_wakeups": 1933202, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 83900484, "browser.engagement.total_uri_count": 8, "browser.engagement.max_concurrent_window_count": 1, "browser.engagement.session_time_excluding_suspend": 83900484, "power.total_cpu_time_ms": 1141992}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 3}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1112922, "parent.active": 29070}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 2}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "power.wakeups_per_process_type": {"parent.inactive": 1860577, "parent.active": 72625}, "networking.data_transferred_v3_kb": {"Y1_N1": 4987, "Y0_N1Sys": 394, "Y2_N3Oth": 5446}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 1193, "successful": 361}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 15409, "range": [1, 10000], "values": {"0": 0, "1": 15, "2": 323, "3": 241, "4": 11, "5": 6, "6": 1, "7": 1, "8": 356, "10": 5, "12": 2, "14": 17, "17": 150, "20": 30, "24": 279, "29": 6, "48": 1, "57": 1, "68": 1, "81": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 658, "range": [1, 100], "values": {"2": 0, "3": 176, "4": 8, "5": 6, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 479, "range": [1, 100], "values": {"0": 1, "1": 67, "2": 17, "3": 101, "4": 5, "5": 3, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 36576, "range": [1, 10000], "values": {"3": 0, "4": 2, "8": 2, "12": 2, "14": 2, "17": 1, "24": 1, "29": 5, "34": 2, "40": 6, "48": 1, "57": 5, "68": 3, "96": 1, "114": 3, "135": 1, "160": 28, "190": 126, "226": 2, "268": 1, "318": 1, "533": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 52946, "range": [1, 10000], "values": {"29": 0, "34": 1, "40": 4, "48": 2, "57": 2, "68": 5, "81": 8, "96": 3, "114": 4, "135": 2, "190": 1, "226": 70, "268": 78, "318": 9, "378": 4, "633": 1, "752": 1, "894": 1, "1262": 1, "1500": 1, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 1911, "1": 1, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 15, "range": [1, 2], "values": {"0": 1897, "1": 15, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 9311, "range": [1, 10000], "values": {"3": 0, "4": 2, "8": 2, "12": 3, "14": 6, "17": 4, "24": 2, "29": 4, "34": 1, "40": 6, "48": 164, "57": 1, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 179, "range": [1, 1000], "values": {"0": 70, "1": 110, "2": 11, "3": 3, "4": 1, "6": 1, "7": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 29271, "range": [1, 10000], "values": {"0": 2, "1": 1, "5": 4, "8": 3, "14": 1, "17": 1, "20": 5, "24": 5, "34": 5, "40": 2, "48": 3, "57": 1, "68": 2, "81": 1, "96": 1, "135": 19, "160": 136, "190": 2, "268": 2, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 14690, "range": [1, 10000], "values": {"1": 0, "2": 4, "3": 3, "5": 1, "6": 3, "7": 3, "8": 6, "10": 3, "12": 4, "17": 3, "20": 1, "24": 2, "29": 1, "68": 69, "81": 83, "96": 6, "114": 3, "135": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 286, "range": [1, 10000], "values": {"14": 0, "17": 1, "226": 1, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 36328, "range": [1, 10000], "values": {"0": 826, "1": 15, "2": 33, "3": 16, "4": 12, "5": 17, "6": 14, "7": 12, "8": 26, "10": 14, "12": 21, "14": 86, "17": 50, "20": 93, "24": 55, "29": 21, "34": 55, "40": 37, "48": 503, "57": 3, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 1180, "range": [1, 100], "values": {"0": 168, "1": 2, "7": 3, "12": 3, "18": 3, "23": 1, "29": 2, "34": 4, "40": 3, "56": 1, "67": 3, "73": 1, "78": 2, "89": 2, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 15856064, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 2, "5": 3, "6": 1, "9": 1, "10": 4, "11": 28, "13": 89, "15": 219, "17": 194, "19": 332, "22": 127, "25": 15, "28": 4, "36": 2, "41": 6, "47": 6, "53": 15, "60": 23, "68": 14, "77": 51, "88": 15, "100": 14, "114": 39, "130": 72, "148": 166, "168": 269, "191": 748, "217": 604, "247": 445, "281": 276, "320": 195, "364": 316, "414": 609, "471": 713, "536": 935, "610": 1075, "695": 1091, "791": 1883, "901": 2487, "1026": 3620, "1168": 2508, "1330": 328, "1514": 86, "1724": 49, "1963": 19, "2235": 5, "2545": 6, "2898": 11, "3300": 3, "3758": 11, "4279": 6, "4872": 7, "5548": 6, "6317": 4, "7193": 4, "8190": 3, "9326": 1, "10619": 1, "12092": 3, "13769": 2, "17852": 2, "20328": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 15996, "range": [1, 2000], "values": {"0": 786, "1": 29, "2": 21, "3": 31, "4": 11, "5": 14, "6": 23, "7": 12, "8": 7, "9": 4, "10": 2, "11": 19, "13": 22, "15": 680, "17": 5, "19": 4, "22": 3, "25": 2, "29": 3, "33": 5, "38": 5, "44": 5, "50": 1, "57": 2, "65": 1, "75": 1, "99": 7, "113": 2, "196": 2, "225": 4, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 1911, "range": [1, 200], "values": {"2": 0, "3": 3, "4": 15, "5": 4, "6": 4, "7": 1, "8": 10, "9": 87, "10": 16, "11": 6, "12": 25, "13": 9, "14": 9, "15": 3, "18": 1, "19": 1, "21": 1, "25": 1, "29": 1, "31": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 102096032, "range": [16, 2147483646], "values": {"0": 0, "16": 55842, "23": 4851, "28": 86, "34": 23, "41": 69, "50": 15, "61": 50, "74": 28, "90": 51, "109": 109, "132": 40, "160": 257, "194": 60, "235": 43, "284": 54428, "344": 43595, "416": 35, "503": 297, "609": 54689, "737": 492, "892": 1126, "1080": 280, "1307": 275, "1582": 544, "1915": 547, "2318": 277, "2805": 368, "3395": 6, "4109": 284, "4973": 557, "6019": 8, "7284": 99, "8815": 371, "10668": 36, "12911": 15, "15625": 2, "18910": 8, "27698": 8, "33521": 3, "40569": 2, "49098": 275, "71914": 4, "87033": 1, "105331": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 3892280, "range": [1, 2147483646], "values": {"0": 0, "1": 60870, "2": 11, "3": 146, "5": 190, "8": 322, "12": 109185, "19": 43770, "30": 1992, "47": 636, "73": 1102, "113": 282, "176": 1219, "274": 40, "426": 101, "662": 8, "1029": 5, "1599": 3, "2485": 273, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2754735, "range": [1, 150000000], "values": {"0": 2910, "1": 13046, "2": 18611, "3": 5770, "4": 3208, "5": 2196, "6": 4772, "7": 9994, "8": 34660, "10": 33940, "12": 31669, "14": 24414, "17": 7304, "20": 5010, "24": 10543, "29": 5512, "35": 3316, "42": 936, "50": 583, "60": 372, "72": 212, "87": 195, "105": 232, "126": 189, "151": 115, "182": 258, "219": 46, "263": 56, "316": 53, "380": 3, "457": 6, "549": 4, "660": 3, "793": 2, "1146": 5, "1378": 4, "1992": 1, "2395": 1, "2879": 2, "3461": 1, "4161": 1, "6013": 1, "7228": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1337117120, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 3, "132939": 4, "141677": 3, "150989": 1, "171489": 5, "182760": 1, "194772": 245, "207574": 471, "221217": 1, "235757": 411, "251252": 309, "267766": 1, "392310": 5, "418095": 7, "474861": 22, "506072": 92, "539334": 96, "574782": 103, "612560": 113, "652821": 123, "695728": 390, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1613359388, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 3, "267766": 718, "285365": 721, "324110": 1, "345412": 2, "368115": 5, "418095": 1, "445575": 1, "506072": 113, "539334": 96, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 53, "1019325": 719, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1025983728, "range": [32768, 16777216], "values": {"0": 5, "34922": 2, "37217": 1, "39663": 1, "42270": 1, "45048": 1, "48009": 1, "51164": 1, "70338": 5, "85139": 1, "90735": 3, "96699": 241, "103055": 240, "109828": 232, "117047": 1, "124740": 26, "132939": 380, "141677": 314, "160913": 1, "304121": 10, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 92, "539334": 99, "574782": 294, "612560": 386, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 593541120, "range": [1024, 16777216], "values": {"0": 0, "1024": 5, "8848": 5, "9759": 3, "20356": 4, "22453": 1, "31641": 79, "33230": 204, "34899": 101, "36652": 205, "38493": 202, "40427": 202, "42458": 204, "44591": 165, "46831": 69, "49183": 7, "59836": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 694, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 33, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 2898, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 6, "range": [1, 2], "values": {"0": 0, "1": 6, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 9542, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "862": 2, "969": 1, "1223": 1, "1456": 1, "1733": 1, "1837": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 32, "range": [1, 30000], "values": {"0": 1, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 30000], "values": {"0": 1, "1": 2, "13": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 233, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 1, "140": 1, "171": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 273, "range": [1, 30000], "values": {"0": 2, "1": 1, "9": 1, "13": 1, "16": 1, "20": 1, "43": 1, "52": 1, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 140, "range": [1, 50], "values": {"0": 2532, "2": 70, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12370, "range": [1, 10000], "values": {"0": 201, "1": 179, "2": 484, "3": 120, "4": 94, "5": 77, "6": 79, "7": 77, "8": 154, "10": 111, "12": 128, "14": 147, "17": 73, "20": 6, "24": 8, "29": 4, "40": 2, "48": 2, "57": 2, "68": 2, "81": 4, "96": 1, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 6583, "range": [1, 10000], "values": {"0": 176, "1": 84, "2": 237, "3": 63, "4": 51, "5": 35, "6": 40, "7": 45, "8": 68, "10": 55, "12": 66, "14": 63, "17": 20, "20": 6, "24": 15, "29": 7, "34": 1, "40": 4, "48": 1, "57": 1, "68": 1, "81": 4, "135": 1, "160": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"0": 0, "1": 52, "2": 6, "3": 6, "4": 2, "5": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 2, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 830, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "485": 1, "535": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 37, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 2, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 292, "range": [1, 10000], "values": {"0": 2, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 4246, "range": [1, 1000], "values": {"0": 292, "1": 519, "2": 318, "3": 226, "4": 145, "5": 59, "6": 85, "7": 52, "8": 40, "9": 6, "10": 11, "11": 5, "12": 3, "14": 6, "16": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2198, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "909": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2335, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "909": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 57, "range": [1, 5000], "values": {"1": 0, "2": 7, "3": 7, "5": 1, "17": 1, "18": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 15, "range": [1, 5000], "values": {"0": 21, "1": 5, "2": 2, "6": 1, "7": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 624, "range": [1, 50000], "values": {"4": 0, "5": 1, "131": 1, "144": 1, "158": 1, "174": 1, "192": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3098, "range": [1, 50000], "values": {"15": 0, "17": 1, "666": 1, "733": 2, "807": 1, "888": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4136, "range": [1, 50000], "values": {"25": 0, "28": 1, "666": 1, "733": 1, "807": 1, "1736": 1, "1911": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4192, "range": [1, 50000], "values": {"45": 0, "50": 1, "666": 1, "733": 1, "807": 1, "1736": 1, "1911": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4604, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4605, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4634, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1736": 1, "1911": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 263, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "158": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2320, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "888": 1, "977": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1175, "range": [1, 50000], "values": {"454": 0, "500": 1, "605": 1, "666": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1437, "range": [1, 50000], "values": {"550": 0, "605": 1, "807": 1, "888": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1173, "range": [1, 50000], "values": {"412": 0, "454": 1, "666": 1, "733": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2706, "range": [1, 50000], "values": {"0": 38, "1": 7, "2": 18, "3": 4, "4": 6, "5": 9, "6": 2, "7": 1, "8": 2, "10": 4, "11": 3, "12": 4, "13": 4, "14": 3, "15": 4, "17": 3, "19": 1, "21": 1, "25": 1, "37": 1, "41": 1, "131": 1, "174": 1, "192": 3, "211": 1, "255": 1, "281": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1852, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "23": 1, "25": 1, "28": 2, "31": 2, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "74": 1, "108": 1, "119": 1, "158": 1, "232": 1, "255": 2, "281": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 514, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "61": 1, "67": 1, "119": 1, "158": 1, "174": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 7, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 142, "range": [1, 50], "values": {"0": 71, "1": 53, "2": 27, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2994, "range": [1, 2000], "values": {"1": 0, "2": 5, "3": 6, "4": 32, "5": 38, "6": 12, "7": 8, "8": 4, "9": 2, "10": 4, "11": 6, "13": 4, "15": 2, "17": 2, "19": 4, "22": 2, "29": 1, "33": 8, "38": 9, "50": 4, "57": 1, "65": 2, "75": 3, "113": 1, "130": 1, "149": 1, "171": 2, "196": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 393, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "99": 1, "113": 1, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 1979, "range": [1, 2000], "values": {"1": 0, "2": 5, "3": 7, "4": 4, "6": 3, "8": 15, "9": 9, "10": 10, "11": 5, "33": 7, "38": 11, "75": 1, "86": 2, "99": 1, "149": 1, "171": 2, "196": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 6487, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 16, "4": 6, "5": 2, "6": 5, "7": 17, "8": 8, "9": 7, "10": 8, "11": 3, "13": 4, "15": 4, "17": 3, "19": 6, "22": 10, "25": 10, "29": 11, "33": 37, "38": 13, "44": 4, "50": 3, "57": 2, "65": 8, "75": 1, "113": 3, "130": 1, "196": 7, "225": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 22, "range": [1, 2000], "values": {"19": 0, "22": 1, "25": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 52, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 2, "4": 2, "7": 1, "11": 1, "15": 1, "17": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2631, "range": [1, 2000], "values": {"0": 4, "1": 10, "2": 6, "3": 37, "4": 62, "5": 14, "6": 2, "8": 1, "9": 1, "11": 1, "13": 1, "15": 3, "19": 2, "22": 2, "25": 2, "29": 1, "33": 7, "38": 2, "44": 6, "50": 3, "57": 3, "65": 3, "99": 3, "171": 1, "258": 1, "296": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 338, "range": [1, 2000], "values": {"4": 0, "5": 2, "7": 1, "17": 1, "19": 1, "258": 1, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 1164, "range": [1, 2000], "values": {"0": 2, "1": 127, "2": 43, "4": 4, "5": 1, "6": 1, "8": 2, "10": 2, "13": 1, "15": 1, "17": 1, "19": 1, "22": 1, "38": 1, "50": 2, "57": 1, "99": 1, "130": 1, "339": 1, "389": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 3528, "range": [1, 2000], "values": {"0": 1, "1": 15, "2": 59, "3": 82, "4": 10, "5": 9, "6": 3, "7": 7, "8": 1, "9": 1, "11": 5, "13": 4, "15": 2, "17": 5, "19": 6, "22": 6, "25": 8, "29": 7, "33": 10, "38": 7, "44": 5, "50": 1, "57": 7, "65": 1, "99": 1, "149": 2, "258": 1, "296": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 81, "range": [1, 2000], "values": {"4": 0, "5": 1, "15": 1, "22": 1, "38": 1, "44": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 529, "range": [1, 2000], "values": {"0": 1, "2": 2, "9": 1, "10": 1, "25": 2, "33": 1, "171": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3938, "range": [1, 2000], "values": {"0": 17, "1": 9, "2": 7, "3": 27, "4": 24, "5": 51, "6": 1, "10": 1, "11": 1, "15": 3, "22": 3, "25": 1, "33": 5, "38": 2, "44": 4, "50": 6, "57": 3, "65": 2, "75": 1, "86": 1, "99": 2, "113": 1, "171": 2, "258": 2, "339": 1, "389": 1, "446": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 375, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "5": 1, "19": 1, "25": 1, "296": 1, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 1104, "range": [1, 2000], "values": {"0": 17, "1": 133, "2": 25, "4": 3, "5": 1, "6": 1, "8": 2, "10": 1, "15": 1, "17": 1, "19": 1, "22": 1, "38": 1, "50": 2, "57": 1, "99": 1, "130": 1, "339": 1, "389": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 5191, "range": [1, 2000], "values": {"0": 6, "1": 11, "2": 53, "3": 96, "4": 7, "5": 4, "6": 1, "7": 1, "8": 2, "10": 1, "11": 4, "13": 2, "15": 3, "17": 2, "19": 4, "22": 4, "25": 6, "29": 8, "33": 8, "38": 11, "44": 3, "50": 2, "57": 11, "65": 5, "86": 2, "99": 2, "130": 1, "149": 2, "171": 1, "258": 1, "339": 2, "389": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 296, "range": [1, 2000], "values": {"22": 0, "25": 1, "50": 2, "149": 1, "171": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 589, "range": [1, 2000], "values": {"0": 0, "1": 1, "2": 1, "6": 1, "9": 1, "11": 1, "15": 1, "44": 1, "113": 1, "149": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 358, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "148": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 108, "range": [1, 50000], "values": {"3": 0, "4": 2, "5": 3, "11": 1, "12": 2, "13": 1, "17": 1, "19": 1, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 4, "range": [1, 50000], "values": {"0": 0, "1": 4, "2": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 2067, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 1, "255": 1, "281": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 83, "range": [1, 50000], "values": {"0": 9, "2": 5, "3": 1, "6": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 61, "range": [1, 50000], "values": {"0": 25, "2": 6, "4": 1, "5": 1, "10": 1, "12": 1, "17": 1, "19": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 178, "range": [1, 50000], "values": {"0": 4, "1": 3, "2": 7, "3": 2, "4": 2, "6": 1, "8": 1, "10": 3, "12": 1, "13": 1, "14": 1, "15": 3, "17": 1, "19": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "61": 1, "108": 1, "232": 1, "255": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 772, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "255": 2, "281": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 416, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "74": 1, "119": 1, "158": 1, "174": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 514, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "61": 1, "67": 1, "119": 1, "158": 1, "174": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 279, "power.total_thread_wakeups": 2987629, "power.total_cpu_time_ms": 1124235}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 306286, "web.foreground": 720771, "prealloc": 95500, "privilegedabout": 1678}, "power.wakeups_per_process_type": {"web.background": 1196713, "web.foreground": 959591, "prealloc": 827801, "privilegedabout": 3524}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3078, "range": [1, 10000], "values": {"0": 0, "1": 209, "2": 1374, "3": 39, "4": 1, "5": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 24210, "range": [1, 100], "values": {"11": 0, "14": 1614, "17": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 11050, "range": [1, 100], "values": {"0": 0, "1": 940, "14": 674, "17": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 68008, "range": [1, 10000], "values": {"8": 0, "10": 12, "12": 15, "14": 805, "17": 104, "20": 4, "57": 2, "68": 510, "81": 151, "96": 10, "135": 1, "160": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 149901, "range": [1, 10000], "values": {"24": 0, "29": 1, "40": 5, "48": 107, "57": 764, "68": 32, "81": 15, "96": 21, "114": 601, "135": 43, "160": 8, "190": 4, "226": 4, "268": 2, "318": 4, "378": 3, "449": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8345, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8345, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 46201, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 15, "6": 19, "7": 13, "8": 33, "10": 30, "12": 20, "14": 740, "17": 63, "20": 3, "24": 2, "29": 1, "34": 19, "40": 17, "48": 635, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 1000], "values": {"0": 1587, "1": 27, "2": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 53848, "range": [1, 10000], "values": {"5": 0, "6": 6, "7": 10, "8": 522, "10": 398, "12": 2, "14": 2, "48": 2, "57": 457, "68": 204, "81": 8, "96": 2, "114": 1, "135": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12476, "range": [1, 10000], "values": {"2": 0, "3": 19, "4": 229, "5": 640, "6": 50, "7": 2, "8": 74, "10": 288, "12": 64, "14": 243, "17": 1, "20": 3, "34": 1, "40": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 67205, "range": [1, 10000], "values": {"0": 5079, "1": 24, "2": 37, "3": 48, "4": 75, "5": 104, "6": 27, "7": 19, "8": 123, "10": 328, "12": 98, "14": 1391, "17": 199, "20": 82, "24": 19, "29": 12, "34": 24, "40": 19, "48": 637, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 63827, "range": [1, 100], "values": {"0": 627, "1": 41, "7": 4, "12": 2, "56": 6, "62": 207, "67": 699, "73": 9, "78": 11, "84": 8, "89": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3984459, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "28": 1, "32": 17, "36": 59, "41": 78, "47": 76, "53": 47, "60": 57, "68": 289, "77": 397, "88": 908, "100": 588, "114": 257, "130": 216, "148": 141, "168": 149, "191": 122, "217": 48, "247": 72, "281": 208, "320": 388, "364": 553, "414": 697, "471": 1209, "536": 1115, "610": 366, "695": 434, "791": 551, "901": 303, "1026": 164, "1168": 86, "1330": 21, "1724": 1, "1963": 2, "2235": 2, "2898": 1, "3300": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 79456, "range": [1, 2000], "values": {"0": 1678, "1": 76, "2": 21, "3": 22, "4": 31, "5": 40, "6": 36, "7": 28, "8": 33, "9": 35, "10": 46, "11": 70, "13": 41, "15": 4443, "17": 43, "19": 32, "22": 8, "25": 12, "29": 6, "33": 2, "38": 6, "50": 13, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 8345, "range": [1, 200], "values": {"2": 0, "3": 50, "4": 586, "5": 212, "6": 610, "7": 139, "8": 7, "9": 3, "10": 2, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 12256314896, "range": [16, 2147483646], "values": {"0": 0, "16": 8805, "23": 115, "34": 205, "41": 3631, "50": 18, "61": 58, "74": 23, "90": 9, "109": 38, "132": 2860, "160": 22, "194": 22, "235": 3633, "284": 2193, "344": 732, "416": 3628, "503": 3666, "609": 204, "737": 2919, "892": 502, "1080": 288, "1307": 528, "1582": 703, "1915": 543, "2318": 255, "2805": 320, "3395": 129, "4109": 73, "4973": 1002, "6019": 75, "7284": 97, "8815": 30, "10668": 6, "12911": 48, "15625": 1, "18910": 6, "40569": 2, "49098": 2, "225968": 7, "3267857": 3622, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 9647260, "range": [1, 2147483646], "values": {"0": 0, "1": 8920, "3": 3932, "5": 33, "8": 2931, "12": 5365, "19": 9723, "30": 4229, "47": 859, "73": 279, "113": 318, "176": 809, "1599": 3622, "2485": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 21421336, "range": [1, 150000000], "values": {"0": 133, "1": 384, "2": 193, "3": 84, "4": 524, "5": 1729, "6": 1807, "7": 1759, "8": 3544, "10": 3847, "12": 1932, "14": 2388, "17": 1876, "20": 4886, "24": 2346, "29": 2115, "35": 2121, "42": 1663, "50": 1038, "60": 533, "72": 235, "87": 106, "105": 78, "126": 64, "151": 114, "182": 658, "219": 514, "263": 488, "316": 169, "380": 24, "457": 1, "549": 3, "660": 27, "793": 10, "953": 2, "1657": 3, "2395": 5, "2879": 28, "3461": 9, "4161": 7, "5002": 3229, "6013": 338, "7228": 3, "10445": 1, "12556": 1, "31521": 1, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 257511124, "range": [32768, 16777216], "values": {"267766": 0, "285365": 4, "304121": 8, "324110": 248, "345412": 273, "368115": 189, "392310": 1, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 383534148, "range": [32768, 16777216], "values": {"474861": 0, "506072": 723, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 183497356, "range": [32768, 16777216], "values": {"171489": 0, "182760": 3, "194772": 1, "207574": 3, "221217": 177, "235757": 109, "251252": 249, "267766": 180, "285365": 1, "304121": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 75433984, "range": [1024, 16777216], "values": {"97683": 0, "102590": 723, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 723, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 2, "1": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 39, "range": [1, 50000], "values": {"0": 6, "1": 13, "2": 3, "3": 2, "4": 2, "6": 1, "7": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 699504, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "131": 5, "144": 40, "158": 641, "174": 944, "192": 1496, "211": 434, "232": 57, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 7647, "range": [1, 2000], "values": {"0": 0, "1": 42, "2": 87, "3": 42, "4": 33, "5": 25, "6": 27, "7": 15, "8": 27, "9": 13, "10": 19, "11": 24, "13": 21, "15": 24, "17": 12, "19": 7, "22": 7, "25": 8, "29": 48, "33": 4, "38": 13, "44": 28, "50": 22, "57": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4427, "range": [1, 2000], "values": {"0": 37, "1": 163, "2": 80, "3": 62, "4": 36, "5": 35, "6": 31, "7": 13, "8": 15, "9": 10, "10": 5, "11": 13, "13": 6, "15": 8, "17": 4, "19": 2, "22": 2, "33": 7, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "225": 2, "258": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3713, "range": [1, 2000], "values": {"0": 21, "1": 159, "2": 93, "3": 68, "4": 43, "5": 31, "6": 32, "7": 17, "8": 17, "9": 10, "10": 9, "11": 14, "13": 5, "15": 6, "17": 5, "19": 3, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 39, "range": [1, 50000], "values": {"0": 6, "1": 13, "2": 3, "3": 2, "4": 2, "6": 1, "7": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 699421, "range": [1, 50000], "values": {"119": 0, "131": 5, "144": 40, "158": 641, "174": 944, "192": 1497, "211": 433, "232": 57, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1037936, "power.total_cpu_time_ms": 904548}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 904548}, "power.wakeups_per_process_type": {"extension": 1037936}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 35, "power.total_cpu_time_ms": 3}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 3}, "power.wakeups_per_process_type": {"socket": 35}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1542251, "power.total_cpu_time_ms": 86170}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 86170}, "power.wakeups_per_process_type": {"utility": 1542251}}}}, "histograms": {"CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 9952, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 2, "25": 5, "28": 4, "31": 86, "34": 68, "38": 34, "42": 61, "46": 10, "51": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7250, "range": [1, 1000], "values": {"0": 189, "1": 409, "2": 1111, "3": 381, "4": 158, "5": 98, "6": 40, "7": 51, "8": 26, "9": 18, "10": 16, "11": 11, "12": 21, "14": 5, "16": 8, "18": 5, "20": 6, "23": 4, "26": 1, "29": 3, "33": 1, "37": 1, "42": 1, "47": 2, "53": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1977, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 13, "6": 34, "7": 115, "8": 91, "9": 6, "10": 3, "12": 2, "13": 1, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12389, "range": [1, 64000], "values": {"25": 0, "28": 1, "31": 2, "34": 2, "38": 66, "42": 81, "46": 56, "51": 58, "56": 3, "62": 1, "68": 1, "75": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 271, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2140, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 21, "6": 10, "7": 129, "8": 108, "10": 8, "12": 3, "14": 1, "20": 1, "24": 1, "29": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 14, "range": [1, 100], "values": {"1": 0, "2": 7, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 9, "range": [1, 100], "values": {"0": 0, "1": 5, "2": 2, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 290, "range": [1, 10000], "values": {"29": 0, "34": 4, "40": 2, "48": 1, "57": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 990, "range": [1, 10000], "values": {"68": 0, "81": 3, "96": 1, "114": 1, "226": 2, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 51, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 51, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 255, "range": [1, 10000], "values": {"12": 0, "14": 1, "29": 1, "34": 3, "40": 1, "48": 1, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 4, "1": 1, "2": 1, "4": 1, "5": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 191, "range": [1, 10000], "values": {"17": 0, "20": 1, "24": 5, "34": 1, "40": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 97, "range": [1, 10000], "values": {"5": 0, "6": 1, "8": 2, "12": 1, "14": 1, "20": 1, "24": 1, "29": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 279, "range": [1, 10000], "values": {"0": 38, "1": 1, "3": 1, "4": 3, "8": 1, "14": 1, "29": 1, "34": 3, "40": 1, "48": 1, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 152, "range": [1, 100], "values": {"0": 0, "1": 1, "7": 1, "23": 3, "29": 1, "34": 1, "40": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 888717, "range": [1, 1000000], "values": {"5": 0, "6": 1, "13": 1, "15": 1, "17": 3, "19": 5, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 3, "47": 6, "53": 35, "60": 44, "68": 65, "77": 171, "88": 380, "100": 766, "114": 950, "130": 723, "148": 346, "168": 111, "191": 69, "217": 89, "247": 70, "281": 45, "320": 32, "364": 32, "414": 39, "471": 60, "536": 98, "610": 72, "695": 14, "791": 10, "901": 7, "1026": 6, "1168": 1, "1330": 7, "1514": 5, "1724": 3, "1963": 5, "2235": 4, "2545": 3, "2898": 10, "3300": 4, "3758": 1, "4279": 7, "4872": 2, "5548": 5, "6317": 1, "7193": 1, "8190": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 684, "range": [1, 2000], "values": {"0": 11, "1": 6, "2": 1, "4": 1, "9": 1, "11": 1, "13": 1, "15": 16, "17": 1, "22": 2, "50": 1, "99": 1, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 51, "range": [1, 200], "values": {"3": 0, "4": 3, "5": 2, "6": 1, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 60977088, "range": [16, 2147483646], "values": {"0": 0, "16": 63669, "23": 1608, "34": 26, "41": 8, "50": 8, "61": 60, "74": 102, "90": 21, "109": 41, "132": 3, "160": 4, "194": 77, "235": 1611, "284": 683, "344": 123, "416": 320, "503": 54541, "609": 3366, "737": 29, "892": 7618, "1080": 11984, "1307": 284, "1582": 39, "1915": 2, "2318": 14, "2805": 2, "3395": 4, "4109": 5, "4973": 7, "6019": 9, "7284": 22, "18910": 6, "22886": 21, "40569": 5, "71914": 9, "87033": 2, "105331": 1, "225968": 3, "273476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 1551841, "range": [1, 2147483646], "values": {"0": 0, "1": 65277, "3": 1553, "5": 257, "8": 55438, "12": 253, "19": 3519, "30": 19881, "47": 80, "73": 33, "113": 7, "176": 2, "1029": 21, "2485": 1, "6002": 11, "9328": 1, "14498": 3, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2355383, "range": [1, 150000000], "values": {"0": 217, "1": 2832, "2": 4644, "3": 6337, "4": 7814, "5": 12061, "6": 9126, "7": 5278, "8": 15528, "10": 11169, "12": 10061, "14": 17797, "17": 14047, "20": 12963, "24": 6569, "29": 2488, "35": 1841, "42": 1796, "50": 1350, "60": 748, "72": 518, "87": 342, "105": 195, "126": 111, "151": 142, "182": 126, "219": 32, "263": 28, "316": 16, "380": 12, "457": 11, "549": 24, "660": 13, "793": 11, "953": 14, "1146": 8, "1378": 12, "1657": 10, "1992": 9, "2395": 4, "2879": 12, "3461": 6, "4161": 8, "5002": 2, "6013": 4, "7228": 1, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 483490248, "range": [32768, 16777216], "values": {"506072": 0, "539334": 60, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 589957972, "range": [32768, 16777216], "values": {"741455": 0, "790188": 720, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1732126508, "range": [32768, 16777216], "values": {"1926500": 0, "2053121": 21, "2188065": 143, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 322574800, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "324110": 38, "345412": 107, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 37860352, "range": [1024, 16777216], "values": {"46831": 0, "49183": 74, "51654": 477, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 720, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 8583, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 218, "33": 37, "49": 8, "73": 2, "108": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 228, "range": [1, 200000], "values": {"6": 0, "8": 2, "10": 4, "17": 6, "22": 1, "28": 1, "36": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 644, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 4, "22": 5, "28": 2, "36": 1, "46": 1, "74": 3, "94": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 3, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 644, "range": [1, 100000], "values": {"0": 14, "1": 5, "5": 1, "16": 2, "20": 1, "25": 3, "31": 5, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 1439, "range": [1, 2], "values": {"0": 51, "1": 1439, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 1, "1": 3, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 370, "range": [1, 3], "values": {"0": 276, "2": 185, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 185, "range": [1, 2], "values": {"0": 0, "1": 185, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 905, "range": [1, 60000], "values": {"0": 184, "874": 1, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 1901, "range": [1, 16], "values": {"2": 0, "3": 19, "4": 461, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 827, "range": [1, 16], "values": {"1": 0, "2": 19, "3": 263, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 467, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 453, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 16, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 451, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 70096, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 9, "39": 10, "41": 13, "43": 20, "45": 11, "47": 21, "49": 18, "51": 8, "53": 19, "55": 17, "58": 19, "61": 9, "64": 9, "67": 11, "70": 2, "73": 11, "76": 13, "80": 8, "84": 10, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 5, "126": 5, "132": 3, "138": 2, "144": 1, "151": 6, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 11, "248": 8, "259": 8, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 2, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 68607, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 8, "39": 7, "41": 10, "43": 19, "45": 10, "47": 20, "49": 18, "51": 8, "53": 19, "55": 17, "58": 19, "61": 9, "64": 9, "67": 10, "70": 2, "73": 11, "76": 13, "80": 8, "84": 10, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 5, "126": 5, "132": 3, "138": 2, "144": 1, "151": 6, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 11, "248": 8, "259": 7, "271": 3, "283": 2, "296": 2, "310": 6, "324": 2, "339": 8, "355": 2, "371": 4, "388": 7, "406": 6, "425": 12, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1111, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 3, "37": 1, "41": 2, "45": 1, "47": 2, "53": 1, "115": 1, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 68985, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 8, "39": 10, "41": 11, "43": 20, "45": 10, "47": 19, "49": 18, "51": 8, "53": 18, "55": 17, "58": 19, "61": 9, "64": 9, "67": 11, "70": 2, "73": 11, "76": 13, "80": 8, "84": 10, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 4, "120": 5, "126": 5, "132": 3, "138": 2, "144": 1, "151": 5, "158": 3, "165": 2, "173": 4, "181": 3, "189": 3, "198": 4, "207": 2, "217": 15, "227": 27, "237": 11, "248": 8, "259": 8, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 2, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1049174, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 8, "3314": 72, "3855": 17, "4484": 2, "5216": 117, "6067": 4, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 258, "range": [1, 2], "values": {"0": 222, "1": 258, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 932, "range": [1, 16], "values": {"3": 0, "4": 211, "8": 11, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 1784, "range": [1, 16], "values": {"3": 0, "4": 70, "8": 188, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 659, "range": [1, 100000], "values": {"0": 180, "1": 109, "2": 28, "3": 6, "4": 2, "5": 3, "6": 1, "8": 19, "25": 6, "39": 1, "61": 1, "76": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 14651, "range": [1, 16], "values": {"0": 1, "1": 1, "2": 51, "8": 178, "9": 52, "10": 1260, "11": 4, "12": 1, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 1635, "range": [1, 2], "values": {"0": 5, "1": 1635, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 5292, "range": [1, 5000], "values": {"0": 191, "1": 3, "2": 2, "3": 4, "7": 4, "10": 2, "11": 3, "12": 2, "13": 1, "14": 3, "15": 6, "16": 3, "17": 5, "18": 2, "19": 5, "20": 2, "21": 4, "23": 4, "25": 3, "27": 2, "29": 3, "35": 3, "38": 3, "41": 1, "47": 3, "54": 1, "58": 1, "66": 1, "87": 2, "93": 1, "115": 1, "123": 1, "132": 2, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 7527, "range": [1, 5000], "values": {"0": 121, "1": 3, "2": 2, "3": 5, "10": 1, "11": 2, "12": 5, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 7, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "71": 1, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 1, "142": 1, "163": 3, "188": 1, "202": 2, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 14961, "range": [1, 16], "values": {"0": 0, "1": 2756, "2": 145, "6": 1791, "7": 167, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 4039, "range": [1, 60000], "values": {"0": 2, "1": 15, "2": 10, "3": 5, "4": 1, "5": 1, "6": 2, "9": 2, "11": 3, "14": 3, "17": 5, "21": 7, "26": 13, "32": 6, "40": 8, "50": 6, "62": 3, "77": 8, "95": 5, "118": 3, "146": 3, "181": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 76723, "range": [1, 60000], "values": {"0": 224, "1": 321, "2": 40, "3": 13, "4": 1, "5": 1, "6": 5, "7": 12, "9": 11, "11": 42, "14": 143, "17": 133, "21": 139, "26": 166, "32": 205, "40": 91, "50": 61, "62": 45, "77": 43, "95": 59, "118": 42, "146": 36, "181": 27, "224": 27, "278": 26, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 491, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 229, "range": [1, 60000], "values": {"0": 2359, "1": 38, "2": 14, "3": 8, "7": 1, "11": 2, "14": 1, "17": 1, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 2569, "range": [1, 60000], "values": {"17": 0, "21": 1, "26": 1, "40": 1, "77": 1, "146": 2, "181": 4, "531": 2, "658": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 556, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 8449, "range": [1, 50], "values": {"0": 39, "3": 13, "4": 266, "6": 11, "8": 910, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 279, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 6, "range": [1, 1000], "values": {"5": 0, "6": 1, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 6, "range": [1, 1000], "values": {"5": 0, "6": 1, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 10, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 3, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 14, "range": [1, 50], "values": {"1": 0, "2": 7, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 4, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 500], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 63, "range": [1, 1000], "values": {"0": 1, "1": 2, "29": 1, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 58, "range": [1, 5000], "values": {"0": 3, "55": 1, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 5, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1273, "range": [50, 1000], "values": {"69": 0, "77": 1, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 13, "range": [50, 10000], "values": {"0": 2, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 74, "range": [1, 50], "values": {"36": 0, "37": 2, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 344, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 2, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 91, "range": [1, 1000], "values": {"9": 0, "13": 1, "19": 2, "27": 1, "39": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 61, "range": [1, 1000], "values": {"0": 0, "1": 1, "6": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 305, "range": [1, 1000], "values": {"39": 0, "56": 1, "115": 2, "165": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 6, "range": [1, 100], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 100], "values": {"0": 0, "1": 1, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 4430, "range": [1, 10000], "values": {"708": 0, "759": 1, "860": 1, "1011": 1, "1718": 1, "1769": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 5074, "range": [1, 10000], "values": {"0": 264, "1": 2516, "2": 479, "3": 59, "4": 23, "5": 20, "6": 14, "7": 4, "8": 9, "10": 6, "12": 9, "14": 7, "17": 1, "20": 4, "24": 3, "40": 3, "48": 2, "96": 1, "114": 2, "135": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 2712, "range": [1, 10000], "values": {"0": 228, "1": 1186, "2": 310, "3": 39, "4": 16, "5": 13, "6": 10, "7": 2, "8": 5, "10": 4, "12": 5, "14": 4, "17": 1, "20": 2, "24": 1, "29": 1, "40": 2, "48": 1, "114": 1, "135": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 67, "range": [1, 2000], "values": {"0": 155, "1": 6, "2": 1, "3": 2, "5": 1, "7": 1, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"1": 0, "2": 45, "3": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 236, "range": [1, 10000], "values": {"0": 196, "1": 236, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 238, "range": [1, 10000], "values": {"0": 443, "1": 17, "2": 9, "3": 8, "4": 7, "5": 1, "7": 3, "8": 14, "10": 1, "12": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 8569, "range": [1, 64], "values": {"13": 0, "14": 19, "18": 456, "19": 5, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 6113, "range": [1, 36], "values": {"22": 0, "23": 1, "29": 210, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1302, "range": [1, 16], "values": {"3": 0, "4": 84, "7": 138, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 1656, "range": [1, 24], "values": {"11": 0, "12": 138, "13": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 1932, "range": [1, 36], "values": {"22": 0, "23": 84, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 7, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1132, "range": [1, 8], "values": {"0": 0, "1": 258, "2": 7, "4": 215, "5": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 238, "range": [1, 24], "values": {"0": 0, "1": 238, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 680, "range": [1, 10], "values": {"0": 0, "1": 680, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 680, "range": [1, 10], "values": {"1": 0, "2": 34, "3": 204, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 736, "range": [1, 10], "values": {"0": 0, "1": 666, "5": 14, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 238, "range": [1, 10], "values": {"0": 0, "1": 238, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 9677, "range": [1, 256], "values": {"14": 0, "15": 72, "20": 114, "89": 9, "116": 23, "119": 2, "145": 18, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 24, "range": [1, 2], "values": {"0": 0, "1": 24, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 91, "range": [1, 512], "values": {"12": 0, "13": 7, "14": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 238, "range": [1, 4], "values": {"0": 0, "1": 238, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 10, "1": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 10696, "range": [1, 30000], "values": {"12": 0, "19": 108, "29": 139, "45": 1, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 12, "range": [1, 10], "values": {"0": 0, "1": 12, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 12, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7552, "range": [1, 1000], "values": {"0": 4, "1": 226, "2": 359, "3": 353, "4": 177, "5": 205, "6": 89, "7": 53, "8": 81, "9": 52, "10": 30, "11": 26, "12": 26, "14": 17, "16": 7, "18": 7, "20": 9, "26": 1, "33": 1, "42": 2, "53": 1, "60": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 411124466, "range": [1, 5000], "values": {"13": 0, "15": 1, "18": 3, "21": 5, "25": 7, "29": 12, "34": 11, "40": 21, "47": 18, "55": 24, "64": 23, "75": 43, "88": 184, "103": 1171, "120": 104, "140": 36, "164": 27, "192": 17, "224": 3, "262": 8, "306": 4, "357": 1, "417": 2, "487": 1, "5000": 1}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 412045063, "range": [8, 792], "values": {"8": 0, "16": 1, "32": 2, "48": 5, "56": 1, "64": 3, "72": 3, "80": 1, "88": 1, "96": 14, "104": 362, "112": 798, "120": 205, "128": 55, "136": 28, "144": 18, "152": 8, "160": 7, "168": 7, "176": 1, "184": 4, "200": 2, "208": 9, "216": 8, "224": 13, "232": 8, "240": 7, "248": 4, "256": 4, "264": 3, "272": 2, "280": 2, "288": 1, "296": 2, "304": 2, "312": 1, "320": 3, "328": 4, "336": 1, "344": 1, "360": 2, "384": 1, "432": 1, "464": 1, "480": 1, "488": 2, "504": 1, "552": 1, "560": 1, "600": 1, "624": 1, "792": 3}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 619909, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 1, "47": 3, "55": 3, "64": 5, "75": 2, "88": 2, "103": 918, "120": 222, "140": 26, "164": 7, "192": 18, "224": 33, "262": 9, "306": 9, "357": 3, "417": 3, "487": 5, "569": 2, "5000": 1}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 75, "range": [1, 5000], "values": {"0": 0, "1": 5, "2": 24, "21": 1, "23": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 60, "1": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 15, "range": [1, 50], "values": {"1": 0, "2": 2, "11": 1, "12": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 7601, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 368, "9": 4, "12": 1, "14": 2, "17": 355, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 11, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1058, "range": [1, 50], "values": {"1": 0, "2": 12, "6": 164, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 3524, "range": [1, 50], "values": {"11": 0, "12": 12, "20": 169, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 3, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 714, "range": [1, 50], "values": {"0": 0, "1": 714, "2": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 3, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 74, "range": [1, 2000], "values": {"0": 87, "1": 7, "2": 3, "3": 1, "8": 5, "9": 2, "10": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"0": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 131, "1": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 1, "1": 2, "2": 3, "3": 1, "4": 1, "6": 2, "7": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 139, "range": [1, 2000], "values": {"0": 1, "1": 90, "2": 14, "3": 3, "4": 3, "5": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2049, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "41": 1, "45": 1, "47": 2, "53": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 25141, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 6, "41": 4, "43": 15, "45": 6, "47": 10, "49": 8, "51": 5, "53": 10, "55": 13, "58": 10, "61": 5, "64": 5, "67": 5, "73": 6, "76": 4, "80": 2, "84": 4, "88": 5, "92": 1, "96": 3, "100": 1, "105": 2, "110": 1, "120": 4, "126": 1, "132": 2, "138": 1, "151": 3, "158": 2, "165": 1, "173": 2, "181": 3, "189": 2, "217": 2, "227": 3, "248": 4, "259": 3, "271": 1, "310": 4, "324": 1, "339": 3, "355": 1, "371": 2, "388": 1, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 42559, "range": [1, 60000], "values": {"32": 0, "33": 1, "37": 1, "39": 4, "41": 7, "43": 6, "45": 5, "47": 9, "49": 10, "51": 3, "53": 7, "55": 6, "58": 10, "61": 4, "64": 4, "67": 6, "70": 2, "73": 5, "76": 9, "80": 6, "84": 6, "88": 11, "92": 7, "96": 4, "100": 6, "105": 6, "110": 1, "115": 4, "120": 1, "126": 5, "132": 1, "138": 1, "144": 1, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 2, "217": 13, "227": 24, "237": 11, "248": 4, "259": 5, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 1, "371": 2, "388": 6, "406": 4, "425": 10, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 549, "range": [1, 60000], "values": {"508": 0, "531": 1, "555": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 1, "range": [1, 32], "values": {"0": 295, "1": 1, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 2509, "range": [1, 50], "values": {"0": 178, "1": 39, "2": 1227, "3": 4, "4": 1, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 103, "range": [1, 50], "values": {"0": 1, "1": 1, "2": 51, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 79, "range": [1, 50], "values": {"0": 0, "1": 13, "2": 33, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 198, "range": [1, 100], "values": {"17": 0, "18": 11, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 285, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 462, "range": [1, 100], "values": {"41": 0, "42": 11, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 433, "range": [1, 2], "values": {"0": 0, "1": 433, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1057, "range": [1, 2], "values": {"0": 5, "1": 1057, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 2934, "range": [1, 50], "values": {"5": 0, "6": 489, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 11622, "range": [1, 50], "values": {"5": 0, "6": 1923, "7": 12, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 1064, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 436, "range": [1, 100000], "values": {"229": 0, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 814, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 2, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 3, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 1, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 1, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 187, "range": [1, 60000], "values": {"0": 8, "1": 1, "2": 1, "5": 3, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 10958, "range": [1, 60000], "values": {"0": 1, "1": 69, "2": 5, "5": 1, "8": 2, "21": 189, "34": 59, "137": 16, "219": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11145, "range": [1, 60000], "values": {"0": 9, "1": 70, "2": 6, "5": 4, "8": 2, "21": 189, "34": 59, "137": 17, "219": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 10662, "range": [1, 5000], "values": {"0": 67, "1": 8, "2": 1, "4": 1, "6": 1, "18": 18, "26": 218, "37": 12, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 10813, "range": [1, 5000], "values": {"0": 77, "1": 8, "2": 2, "3": 2, "4": 1, "6": 1, "18": 18, "26": 218, "37": 12, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 151, "range": [1, 5000], "values": {"0": 10, "2": 1, "3": 2, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 6, "range": [1, 5000], "values": {"0": 340, "1": 1, "4": 1, "6": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 36, "range": [1, 5000], "values": {"0": 348, "1": 6, "4": 1, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 30, "range": [1, 5000], "values": {"0": 8, "1": 5, "18": 1, "26": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 840, "range": [1, 50], "values": {"0": 100, "1": 256, "2": 292, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 2175, "range": [1, 50], "values": {"0": 172, "1": 519, "2": 828, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 46, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 18, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 14, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 138, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 16, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2169, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 17, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 15, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 27, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4310, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2985, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 216, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 149, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 152, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6908, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 73, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 283, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 366, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 12, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1775, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 726, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 89784, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 15, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 444, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 168, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 40, "range": [1, 50], "values": {"4": 0, "5": 8, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 912, "range": [1, 50], "values": {"16": 0, "17": 19, "19": 31, "20": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 345, "range": [1, 50], "values": {"16": 0, "17": 15, "18": 5, "19": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 6, "range": [1, 50], "values": {"1": 0, "2": 3, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 340, "range": [1, 50], "values": {"1": 0, "2": 170, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 74, "range": [1, 50], "values": {"1": 0, "2": 37, "3": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 158, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 9, "17": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 211, "range": [1, 50], "values": {"15": 0, "16": 12, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 16, "range": [1, 50], "values": {"1": 0, "2": 8, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 5, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3368, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 171, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1350, "range": [1, 50], "values": {"15": 0, "16": 6, "19": 66, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 46, "range": [1, 50], "values": {"1": 0, "2": 23, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1138, "range": [1, 50], "values": {"3": 0, "4": 102, "16": 18, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 83900, "subsessionLength": 43400, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}