{"type": "main", "id": "af4a6b97-019a-48c2-aee1-e1445c7a10fc", "creationDate": "2025-05-25T13:19:38.293Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 95678, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 193}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 66, "browser.engagement.tab_open_event_count": 9, "browser.engagement.max_concurrent_tab_count": 8, "urlbar.zeroprefix.exposure": 3, "dom.contentprocess.os_priority_raised": 339, "browser.engagement.unique_domains_count": 4, "dom.contentprocess.os_priority_lowered": 70, "blocklist.mlbf_source": "dump_match", "cookie.banners.service_detect_only": false, "urlbar.zeroprefix.abandonment": 2, "browser.engagement.window_open_event_count": 1, "dom.contentprocess.os_priority_change_considered": 177, "browser.engagement.active_ticks": 193, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 66, "power.total_thread_wakeups": 4243074, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 95677612, "browser.engagement.total_uri_count": 66, "browser.engagement.max_concurrent_window_count": 2, "browser.engagement.session_time_excluding_suspend": 95677612, "power.total_cpu_time_ms": 1964813, "blocklist.mlbf_softblocks_source": "dump_match"}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 5}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1416132, "parent.active.playing-audio": 6189, "parent.active.playing-video": 82123, "parent.inactive.playing-video": 332413, "parent.active": 125575, "parent.inactive.playing-audio": 2381}, "browser.ui.interaction.pageaction_urlbar": {"addon0": 1}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 3}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "cookie.banners.private_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "power.wakeups_per_process_type": {"parent.inactive": 2594010, "parent.active.playing-audio": 18891, "parent.active.playing-video": 216688, "parent.inactive.playing-video": 1025282, "parent.active": 382223, "parent.inactive.playing-audio": 5980}, "cookie.banners.normal_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "networking.data_transferred_v3_kb": {"Y1_N1": 9363, "Y0_N1Sys": 719, "Y2_N3Oth": 155003}, "telemetry.event_counts": {"pwmgr#saved_login_used#form_login": 2}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 6280, "successful": 553}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 24786, "range": [1, 10000], "values": {"0": 2, "1": 35, "2": 401, "3": 320, "4": 118, "5": 47, "6": 28, "7": 63, "8": 447, "10": 34, "12": 11, "14": 53, "17": 228, "20": 127, "24": 322, "29": 14, "34": 6, "40": 3, "48": 10, "57": 4, "68": 2, "81": 1, "96": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 1402, "range": [1, 100], "values": {"2": 0, "3": 338, "4": 50, "5": 18, "6": 5, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 980, "range": [1, 100], "values": {"0": 4, "1": 144, "2": 51, "3": 184, "4": 21, "5": 8, "6": 3, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 79594, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 2, "5": 1, "7": 1, "8": 2, "10": 3, "12": 7, "14": 4, "17": 1, "20": 2, "24": 11, "29": 13, "34": 9, "40": 16, "48": 15, "57": 16, "68": 14, "81": 2, "96": 3, "114": 6, "135": 2, "160": 29, "190": 142, "226": 40, "268": 26, "318": 26, "378": 10, "449": 5, "533": 4, "633": 1, "752": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 130882, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 3, "40": 11, "48": 5, "57": 10, "68": 20, "81": 25, "96": 20, "114": 17, "135": 8, "160": 4, "190": 1, "226": 72, "268": 100, "318": 43, "378": 14, "449": 9, "533": 12, "633": 14, "752": 12, "894": 8, "1062": 5, "1262": 2, "1500": 3, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 15, "range": [1, 2], "values": {"0": 6001, "1": 15, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 126, "range": [1, 2], "values": {"0": 5890, "1": 126, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 16888, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 3, "5": 1, "6": 1, "8": 2, "10": 5, "12": 10, "14": 14, "17": 17, "20": 14, "24": 54, "29": 32, "34": 12, "40": 22, "48": 218, "57": 3, "68": 3, "81": 1, "135": 1, "190": 1, "226": 2, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 491, "range": [1, 1000], "values": {"0": 175, "1": 167, "2": 43, "3": 10, "4": 8, "5": 4, "6": 4, "7": 1, "9": 1, "10": 1, "16": 1, "18": 1, "20": 1, "23": 2, "26": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 63837, "range": [1, 10000], "values": {"0": 6, "1": 2, "2": 1, "4": 6, "5": 4, "6": 1, "7": 1, "8": 7, "14": 1, "17": 3, "20": 20, "24": 19, "29": 6, "34": 19, "40": 15, "48": 10, "57": 7, "68": 2, "81": 1, "96": 2, "114": 1, "135": 19, "160": 152, "190": 41, "226": 31, "268": 23, "318": 9, "378": 5, "449": 1, "533": 1, "633": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 30978, "range": [1, 10000], "values": {"0": 0, "1": 3, "2": 9, "3": 3, "4": 3, "5": 8, "6": 16, "7": 14, "8": 16, "10": 7, "12": 13, "14": 6, "17": 7, "20": 9, "24": 4, "29": 7, "40": 2, "48": 1, "57": 1, "68": 77, "81": 114, "96": 25, "114": 36, "135": 21, "160": 10, "190": 4, "226": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1001, "range": [1, 10000], "values": {"1": 0, "2": 1, "5": 3, "17": 2, "24": 2, "29": 2, "48": 1, "68": 1, "190": 1, "226": 2, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 78629, "range": [1, 10000], "values": {"0": 1800, "1": 111, "2": 122, "3": 86, "4": 96, "5": 387, "6": 231, "7": 206, "8": 426, "10": 279, "12": 366, "14": 336, "17": 161, "20": 217, "24": 312, "29": 67, "34": 75, "40": 69, "48": 656, "57": 5, "68": 3, "81": 1, "135": 1, "190": 1, "226": 2, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 4370, "range": [1, 100], "values": {"0": 235, "1": 59, "7": 20, "12": 25, "18": 15, "23": 7, "29": 7, "34": 12, "40": 8, "45": 5, "51": 2, "56": 1, "62": 1, "67": 5, "73": 7, "78": 3, "84": 2, "89": 3, "95": 2, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 29573138, "range": [1, 1000000], "values": {"2": 0, "3": 3, "4": 3, "5": 6, "6": 1, "9": 1, "10": 12, "11": 38, "13": 105, "15": 238, "17": 210, "19": 350, "22": 127, "25": 16, "28": 6, "32": 2, "36": 4, "41": 14, "47": 7, "53": 25, "60": 52, "68": 31, "77": 80, "88": 39, "100": 55, "114": 83, "130": 156, "148": 283, "168": 452, "191": 1022, "217": 864, "247": 644, "281": 499, "320": 462, "364": 592, "414": 1442, "471": 2106, "536": 1819, "610": 1451, "695": 1341, "791": 2152, "901": 2777, "1026": 3953, "1168": 2884, "1330": 620, "1514": 343, "1724": 272, "1963": 178, "2235": 138, "2545": 83, "2898": 75, "3300": 51, "3758": 56, "4279": 32, "4872": 31, "5548": 20, "6317": 15, "7193": 315, "8190": 388, "9326": 48, "10619": 9, "12092": 9, "13769": 5, "15678": 7, "17852": 7, "20328": 4, "23147": 1, "26357": 1, "34174": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 49302, "range": [1, 2000], "values": {"0": 1940, "1": 427, "2": 274, "3": 306, "4": 170, "5": 121, "6": 98, "7": 69, "8": 63, "9": 45, "10": 46, "11": 74, "13": 88, "15": 1253, "17": 159, "19": 149, "22": 53, "25": 50, "29": 33, "33": 35, "38": 35, "44": 27, "50": 12, "57": 10, "65": 12, "75": 8, "86": 5, "99": 13, "113": 8, "130": 2, "149": 2, "171": 1, "196": 2, "225": 6, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 6015, "range": [1, 200], "values": {"2": 0, "3": 17, "4": 53, "5": 17, "6": 18, "7": 9, "8": 13, "9": 98, "10": 26, "11": 10, "12": 27, "13": 14, "14": 10, "15": 5, "16": 4, "17": 5, "18": 6, "19": 13, "21": 7, "23": 3, "25": 4, "27": 1, "29": 2, "31": 8, "34": 9, "37": 4, "40": 8, "43": 6, "46": 3, "50": 7, "54": 2, "58": 4, "63": 4, "68": 1, "73": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 134698960, "range": [16, 2147483646], "values": {"0": 0, "16": 76126, "23": 6082, "28": 205, "34": 70, "41": 228, "50": 78, "61": 373, "74": 110, "90": 144, "109": 282, "132": 87, "160": 679, "194": 255, "235": 410, "284": 72492, "344": 55835, "416": 67, "503": 514, "609": 72770, "737": 899, "892": 1626, "1080": 320, "1307": 400, "1582": 592, "1915": 678, "2318": 313, "2805": 424, "3395": 25, "4109": 318, "4973": 623, "6019": 18, "7284": 132, "8815": 442, "10668": 43, "12911": 15, "15625": 20, "18910": 70, "22886": 7, "27698": 67, "33521": 9, "40569": 14, "49098": 303, "71914": 9, "87033": 2, "105331": 3, "127476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 5017681, "range": [1, 2147483646], "values": {"0": 0, "1": 82585, "2": 26, "3": 1162, "5": 482, "8": 1298, "12": 145574, "19": 56080, "30": 2634, "47": 734, "73": 1224, "113": 322, "176": 1351, "274": 138, "426": 148, "662": 82, "1029": 29, "1599": 7, "2485": 299, "3862": 3, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4143240, "range": [1, 150000000], "values": {"0": 4317, "1": 16351, "2": 23552, "3": 7802, "4": 4843, "5": 4233, "6": 8023, "7": 13999, "8": 42012, "10": 43403, "12": 40188, "14": 33886, "17": 10953, "20": 7648, "24": 14807, "29": 8228, "35": 4655, "42": 1551, "50": 890, "60": 584, "72": 337, "87": 284, "105": 321, "126": 265, "151": 204, "182": 342, "219": 97, "263": 117, "316": 95, "380": 15, "457": 17, "549": 21, "660": 21, "793": 12, "953": 14, "1146": 13, "1378": 11, "1657": 12, "1992": 6, "2395": 5, "2879": 7, "3461": 8, "4161": 6, "5002": 4, "6013": 4, "7228": 13, "8689": 3, "10445": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1724736884, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 9, "103055": 25, "109828": 24, "117047": 123, "124740": 142, "132939": 10, "141677": 50, "150989": 11, "160913": 17, "171489": 150, "182760": 158, "194772": 259, "207574": 499, "221217": 131, "235757": 552, "251252": 496, "267766": 74, "285365": 4, "324110": 1, "368115": 1, "392310": 11, "418095": 8, "474861": 22, "506072": 92, "539334": 96, "574782": 105, "612560": 140, "652821": 180, "695728": 499, "741455": 280, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 2323977816, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 179, "182760": 6, "267766": 1080, "285365": 737, "304121": 9, "324110": 1, "345412": 173, "368115": 196, "392310": 4, "418095": 1, "445575": 1, "506072": 113, "539334": 253, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 249, "1019325": 915, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1276510796, "range": [32768, 16777216], "values": {"0": 60, "32768": 8, "34922": 117, "37217": 6, "39663": 5, "42270": 49, "45048": 94, "48009": 1, "51164": 5, "54527": 5, "58111": 41, "61930": 7, "66000": 1, "70338": 64, "74961": 76, "79888": 138, "85139": 13, "90735": 11, "96699": 248, "103055": 250, "109828": 247, "117047": 39, "124740": 58, "132939": 436, "141677": 478, "150989": 68, "160913": 102, "171489": 76, "182760": 28, "194772": 4, "207574": 1, "221217": 1, "285365": 1, "304121": 17, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 110, "539334": 121, "574782": 380, "612560": 456, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 805493760, "range": [1024, 16777216], "values": {"0": 0, "1024": 11, "8022": 1, "8848": 180, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 240, "33230": 216, "34899": 101, "36652": 206, "38493": 202, "40427": 203, "42458": 204, "44591": 181, "46831": 420, "49183": 55, "51654": 269, "54249": 26, "56974": 1, "59836": 3, "62842": 2, "65999": 1, "72796": 5, "76453": 3, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 890, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 229, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 4382, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 8, "range": [1, 2], "values": {"0": 0, "1": 8, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 32904, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "767": 1, "862": 2, "969": 1, "1027": 1, "1089": 2, "1223": 1, "1374": 1, "1456": 3, "1635": 1, "1733": 2, "1947": 2, "2064": 1, "2458": 1, "2761": 1, "2926": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 110, "range": [1, 30000], "values": {"0": 2, "1": 1, "5": 1, "24": 1, "29": 1, "43": 1, "52": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 70, "range": [1, 30000], "values": {"0": 2, "1": 3, "2": 1, "6": 1, "13": 1, "16": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 472, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "24": 1, "43": 1, "77": 1, "140": 2, "171": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 924, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 2, "63": 1, "94": 1, "140": 1, "209": 1, "255": 1, "311": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 360, "range": [1, 30000], "values": {"0": 7, "1": 7, "2": 3, "3": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 2, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 510, "range": [1, 50], "values": {"0": 2868, "2": 255, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 116072, "range": [1, 10000], "values": {"0": 1656, "1": 2090, "2": 6562, "3": 1297, "4": 862, "5": 741, "6": 613, "7": 572, "8": 1182, "10": 1014, "12": 835, "14": 1076, "17": 589, "20": 120, "24": 54, "29": 28, "34": 27, "40": 21, "48": 18, "57": 9, "68": 15, "81": 6, "96": 6, "114": 7, "135": 9, "190": 6, "226": 4, "268": 7, "318": 1, "533": 5, "633": 4, "752": 2, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 62069, "range": [1, 10000], "values": {"0": 1458, "1": 973, "2": 3228, "3": 626, "4": 461, "5": 389, "6": 321, "7": 296, "8": 474, "10": 479, "12": 426, "14": 438, "17": 120, "20": 70, "24": 120, "29": 61, "34": 37, "40": 57, "48": 15, "57": 13, "68": 12, "81": 5, "96": 2, "114": 2, "135": 3, "160": 1, "190": 2, "226": 2, "268": 3, "318": 1, "533": 2, "633": 4, "752": 2, "894": 0}}, "VIDEO_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1483337, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "1346410": 1, "1576847": 0}}, "VIDEO_HIDDEN_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6282, "range": [1, 7200000], "values": {"0": 2, "5974": 1, "6947": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 108, "range": [1, 50], "values": {"0": 0, "1": 56, "2": 8, "3": 8, "4": 3, "5": 0}}, "PWMGR_LOGIN_PAGE_SAFETY": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 8, "1": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 38, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5904, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "135": 1, "221": 1, "244": 1, "269": 2, "297": 2, "328": 2, "362": 2, "399": 1, "485": 2, "792": 1, "874": 0}}, "JS_PAGELOAD_DELAZIFICATION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 11, "1": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 182, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 3, "6": 1, "7": 1, "8": 3, "9": 2, "11": 1, "12": 1, "14": 1, "15": 1, "17": 2, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 4, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "VIDEO_DROPPED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_DECODED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_SINK_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_COMPOSITOR_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 79210, "range": [1, 1000], "values": {"0": 526, "1": 1552, "2": 2513, "3": 3297, "4": 3164, "5": 2143, "6": 1885, "7": 1243, "8": 973, "9": 386, "10": 159, "11": 100, "12": 93, "14": 77, "16": 47, "18": 23, "20": 33, "23": 21, "26": 14, "29": 7, "33": 3, "37": 2, "42": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10188, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 3, "909": 5, "1243": 1, "1380": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10667, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "909": 2, "1009": 4, "1380": 1, "1532": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1153, "range": [1, 5000], "values": {"0": 0, "1": 2, "2": 84, "3": 65, "4": 12, "5": 11, "6": 4, "7": 2, "8": 4, "10": 2, "11": 1, "15": 2, "17": 2, "25": 1, "27": 1, "29": 1, "31": 2, "33": 2, "35": 2, "38": 1, "47": 1, "142": 1, "152": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 141, "range": [1, 5000], "values": {"0": 52, "1": 24, "2": 4, "3": 2, "6": 1, "7": 1, "9": 3, "10": 1, "11": 2, "12": 1, "19": 1, "20": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8366, "range": [1, 50000], "values": {"4": 0, "5": 1, "13": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "412": 2, "454": 2, "500": 1, "550": 4, "605": 3, "888": 1, "977": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 16589, "range": [1, 50000], "values": {"15": 0, "17": 1, "61": 1, "666": 1, "733": 3, "807": 7, "888": 3, "977": 1, "1075": 1, "1183": 1, "1302": 1, "1433": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 26497, "range": [1, 50000], "values": {"25": 0, "28": 1, "89": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1075": 1, "1183": 1, "1302": 4, "1433": 1, "1577": 2, "1736": 3, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 27661, "range": [1, 50000], "values": {"45": 0, "50": 1, "144": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1183": 2, "1302": 2, "1433": 2, "1577": 2, "1736": 3, "2104": 2, "2549": 1, "2806": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 29732, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1302": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 2, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 29760, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1302": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 2, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 30011, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1302": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 2, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7773, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "412": 2, "454": 3, "500": 3, "550": 1, "605": 3, "888": 1, "977": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 14592, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 2, "1302": 2, "1433": 3, "1577": 1, "1911": 1, "2104": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10211, "range": [1, 50000], "values": {"500": 0, "550": 1, "605": 1, "733": 1, "807": 2, "888": 4, "1183": 1, "1433": 1, "1577": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 9790, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "888": 1, "977": 5, "1302": 1, "1433": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5519, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 3, "454": 1, "500": 2, "605": 1, "666": 1, "888": 1, "977": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6238, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 2, "888": 1, "977": 1, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4001, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 2, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7311, "range": [1, 50000], "values": {"0": 108, "1": 19, "2": 50, "3": 15, "4": 17, "5": 21, "6": 9, "7": 3, "8": 5, "9": 7, "10": 8, "11": 7, "12": 5, "13": 8, "14": 5, "15": 9, "17": 7, "19": 5, "21": 3, "23": 1, "25": 3, "31": 1, "34": 1, "37": 3, "41": 1, "50": 2, "131": 1, "174": 1, "192": 3, "211": 5, "232": 6, "255": 3, "281": 2, "309": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 27025, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "17": 1, "23": 2, "25": 1, "28": 3, "31": 3, "34": 1, "37": 2, "41": 2, "45": 2, "61": 1, "74": 1, "108": 4, "119": 1, "131": 2, "158": 2, "232": 1, "255": 2, "281": 2, "309": 1, "340": 3, "374": 1, "412": 2, "454": 1, "500": 1, "550": 1, "605": 2, "666": 1, "733": 4, "807": 6, "888": 1, "977": 3, "1075": 1, "1302": 3, "1433": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7702, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 2, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 3, "977": 1, "1075": 1, "1433": 1, "1577": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 23, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 420, "range": [1, 50], "values": {"0": 1255, "1": 53, "2": 166, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3669150, "range": [1, 2000], "values": {"1": 0, "2": 17, "3": 35, "4": 58, "5": 111, "6": 117, "7": 114, "8": 214, "9": 256, "10": 126, "11": 87, "13": 33, "15": 25, "17": 18, "19": 10, "22": 10, "25": 19, "29": 21, "33": 24, "38": 22, "44": 14, "50": 18, "57": 4, "65": 21, "75": 6, "86": 3, "113": 3, "130": 3, "149": 2, "171": 2, "196": 1, "339": 3, "1011": 255, "1159": 31, "1328": 1, "1745": 325, "2000": 1253}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1102, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "44": 1, "50": 1, "86": 3, "99": 2, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 13602, "range": [1, 2000], "values": {"1": 0, "2": 40, "3": 47, "4": 28, "5": 16, "6": 9, "7": 8, "8": 33, "9": 20, "10": 15, "11": 11, "13": 3, "15": 3, "17": 1, "19": 4, "22": 3, "25": 2, "29": 3, "33": 9, "38": 14, "44": 1, "65": 1, "75": 1, "86": 2, "99": 2, "113": 1, "149": 1, "171": 2, "196": 1, "2000": 2}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 14786, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 12, "5": 12, "6": 15, "7": 31, "8": 24, "9": 17, "10": 15, "11": 12, "13": 5, "15": 4, "17": 4, "19": 7, "22": 11, "25": 13, "29": 12, "33": 42, "38": 17, "44": 6, "50": 5, "57": 3, "65": 8, "75": 1, "86": 6, "99": 20, "113": 21, "130": 7, "149": 1, "196": 8, "770": 1, "882": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 4765, "range": [1, 2000], "values": {"0": 0, "1": 1, "2": 3, "3": 7, "4": 2, "5": 1, "6": 3, "7": 2, "8": 1, "11": 1, "22": 1, "29": 1, "50": 1, "86": 1, "113": 1, "2000": 1}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1278, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 3, "15": 3, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2102342, "range": [1, 2000], "values": {"0": 5, "1": 29, "2": 187, "3": 496, "4": 202, "5": 75, "6": 36, "7": 26, "8": 22, "9": 22, "10": 24, "11": 30, "13": 28, "15": 24, "17": 18, "19": 14, "22": 16, "25": 14, "29": 13, "33": 15, "38": 12, "44": 14, "50": 9, "57": 13, "65": 13, "75": 11, "86": 4, "99": 7, "113": 8, "130": 4, "149": 2, "171": 4, "196": 4, "225": 1, "258": 1, "296": 12, "339": 130, "389": 13, "446": 36, "511": 35, "586": 124, "672": 173, "770": 137, "882": 105, "1011": 75, "1159": 360, "1328": 274, "1522": 410, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1740, "range": [1, 2000], "values": {"2": 0, "3": 1, "5": 3, "7": 1, "17": 1, "19": 1, "113": 1, "130": 1, "171": 1, "196": 2, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 12939, "range": [1, 2000], "values": {"0": 5, "1": 226, "2": 73, "3": 9, "4": 10, "5": 6, "6": 7, "7": 3, "8": 5, "9": 3, "10": 5, "11": 5, "13": 3, "15": 5, "17": 3, "19": 3, "22": 2, "25": 13, "29": 1, "33": 2, "38": 1, "44": 2, "50": 5, "57": 3, "65": 2, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "446": 1, "511": 1, "586": 3, "672": 5, "770": 1, "882": 2, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 16195, "range": [1, 2000], "values": {"0": 1, "1": 21, "2": 95, "3": 104, "4": 18, "5": 12, "6": 6, "7": 9, "8": 3, "9": 4, "10": 1, "11": 7, "13": 4, "15": 3, "17": 7, "19": 7, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "86": 2, "99": 1, "113": 13, "130": 11, "149": 12, "171": 7, "196": 3, "225": 1, "258": 2, "389": 1, "446": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 473, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 8, "4": 3, "5": 3, "6": 2, "7": 5, "8": 2, "9": 1, "10": 1, "11": 3, "15": 3, "17": 1, "19": 2, "22": 1, "33": 1, "38": 1, "86": 1, "99": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2963, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 3, "86": 1, "99": 1, "171": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2102421, "range": [1, 2000], "values": {"0": 189, "1": 119, "2": 136, "3": 301, "4": 237, "5": 135, "6": 28, "7": 22, "8": 19, "9": 12, "10": 20, "11": 20, "13": 13, "15": 12, "17": 5, "19": 8, "22": 9, "25": 3, "29": 5, "33": 14, "38": 6, "44": 11, "50": 11, "57": 5, "65": 7, "75": 8, "86": 5, "99": 6, "113": 8, "130": 5, "149": 3, "171": 4, "196": 1, "225": 3, "258": 33, "296": 38, "339": 80, "389": 13, "446": 8, "511": 44, "586": 137, "672": 217, "770": 99, "882": 68, "1011": 110, "1159": 363, "1328": 274, "1522": 413, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 2221, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 2, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 4, "296": 2, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 12876, "range": [1, 2000], "values": {"0": 24, "1": 230, "2": 52, "3": 9, "4": 6, "5": 10, "6": 7, "7": 3, "8": 5, "9": 3, "10": 4, "11": 5, "13": 2, "15": 5, "17": 3, "19": 3, "22": 2, "25": 13, "29": 1, "33": 2, "38": 1, "44": 2, "50": 5, "57": 3, "65": 2, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "446": 1, "511": 1, "586": 3, "672": 5, "770": 1, "882": 2, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 21752, "range": [1, 2000], "values": {"0": 6, "1": 14, "2": 87, "3": 124, "4": 14, "5": 6, "6": 5, "7": 3, "8": 3, "9": 2, "10": 2, "11": 5, "13": 4, "15": 5, "17": 4, "19": 5, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 5, "99": 2, "113": 3, "130": 3, "149": 4, "171": 6, "196": 6, "225": 6, "258": 16, "296": 6, "339": 2, "389": 1, "511": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 5290, "range": [1, 2000], "values": {"0": 2, "1": 3, "3": 2, "4": 3, "6": 2, "7": 1, "11": 2, "13": 2, "15": 2, "17": 1, "22": 2, "25": 1, "50": 2, "86": 1, "149": 1, "258": 3, "296": 3, "339": 2, "389": 3, "446": 1, "511": 1, "586": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2934, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 3, "86": 3, "99": 1, "113": 1, "149": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 9790, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "888": 1, "977": 5, "1302": 1, "1433": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 2937, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "194": 1, "212": 1, "232": 1, "254": 1, "278": 1, "398": 1, "436": 1, "477": 1, "522": 0}}}, "MEDIA_PLAY_TIME_MS": {"AV": {"bucket_count": 100, "histogram_type": 0, "sum": 1483337, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "1346410": 1, "1576847": 0}}}, "AUDIBLE_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 300, "range": [1, 100], "values": {"98": 0, "100": 3}}}, "MUTED_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}}, "VIDEO_VISIBLE_PLAY_TIME_MS": {"All": {"bucket_count": 100, "histogram_type": 0, "sum": 1477054, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "1368456": 1, "1591423": 0}}, "AV,240<h<=480": {"bucket_count": 100, "histogram_type": 0, "sum": 1477054, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "1368456": 1, "1591423": 0}}}, "VIDEO_HIDDEN_PLAY_TIME_PERCENTAGE": {"All": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}, "AV,240<h<=480": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}}, "MEDIA_CODEC_USED": {"video/vp9": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}, "audio/opus": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 235, "range": [1, 50000], "values": {"3": 0, "4": 4, "5": 7, "7": 1, "8": 1, "9": 3, "10": 1, "11": 1, "12": 3, "13": 1, "15": 1, "17": 1, "19": 2, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 43, "range": [1, 50000], "values": {"0": 0, "1": 6, "2": 7, "3": 2, "4": 1, "5": 1, "8": 1, "9": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 5547, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 5, "232": 6, "255": 3, "281": 2, "309": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 154, "range": [1, 50000], "values": {"0": 23, "2": 9, "3": 3, "4": 3, "6": 2, "8": 1, "9": 1, "11": 2, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 156, "range": [1, 50000], "values": {"0": 68, "1": 1, "2": 13, "3": 6, "4": 1, "5": 3, "6": 1, "10": 1, "12": 1, "13": 2, "17": 1, "19": 1, "21": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 503, "range": [1, 50000], "values": {"0": 17, "1": 13, "2": 19, "3": 4, "4": 4, "5": 2, "6": 1, "8": 1, "9": 3, "10": 5, "11": 2, "12": 1, "13": 2, "14": 2, "15": 5, "17": 2, "19": 2, "23": 1, "25": 1, "34": 1, "37": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 4811, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "23": 1, "28": 1, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "108": 2, "131": 1, "158": 1, "232": 1, "281": 2, "309": 1, "340": 3, "374": 1, "412": 2, "454": 1, "500": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 10453, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "17": 1, "23": 1, "25": 1, "28": 2, "31": 2, "45": 1, "108": 2, "255": 2, "500": 1, "550": 1, "605": 2, "666": 1, "733": 2, "977": 1, "1075": 1, "1302": 2, "1433": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 11760, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "31": 1, "74": 1, "119": 1, "131": 1, "158": 1, "733": 2, "807": 6, "888": 1, "977": 2, "1302": 1, "1433": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 7702, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 2, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 3, "977": 1, "1075": 1, "1433": 1, "1577": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 303, "power.total_thread_wakeups": 8183675, "power.total_cpu_time_ms": 2255162}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 462877, "web.foreground": 1662744, "prealloc": 116586, "privilegedabout": 12955}, "power.wakeups_per_process_type": {"web.background": 1666427, "web.foreground": 5308757, "prealloc": 1119922, "privilegedabout": 88569}, "telemetry.event_counts": {"pictureinpicture#saw_toggle#toggle": 10}, "media.video_hardware_decoding_support": {"video/vp9": true}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 4532, "range": [1, 10000], "values": {"0": 0, "1": 222, "2": 1483, "3": 274, "4": 70, "5": 15, "6": 9, "7": 2, "8": 3, "10": 2, "12": 2, "14": 2, "17": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 30994, "range": [1, 100], "values": {"11": 0, "14": 2055, "17": 9, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 14109, "range": [1, 100], "values": {"0": 0, "1": 1192, "2": 8, "3": 6, "14": 855, "17": 3, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 89329, "range": [1, 10000], "values": {"4": 0, "5": 1, "8": 1, "10": 16, "12": 27, "14": 859, "17": 254, "20": 37, "24": 3, "29": 5, "48": 1, "57": 5, "68": 564, "81": 243, "96": 33, "114": 9, "135": 2, "160": 3, "378": 1, "449": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 192493, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 2, "40": 13, "48": 147, "57": 930, "68": 76, "81": 18, "96": 27, "114": 712, "135": 96, "160": 18, "190": 8, "226": 6, "268": 2, "318": 4, "378": 3, "449": 1, "533": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10596, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 2, "range": [1, 2], "values": {"0": 10594, "1": 2, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 59775, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 17, "6": 22, "7": 14, "8": 35, "10": 39, "12": 35, "14": 811, "17": 190, "20": 30, "24": 5, "29": 6, "34": 26, "40": 25, "48": 804, "190": 1, "226": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 308, "range": [1, 1000], "values": {"0": 1991, "1": 66, "2": 1, "3": 4, "9": 1, "216": 1, "243": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 70280, "range": [1, 10000], "values": {"0": 1, "2": 2, "6": 8, "7": 22, "8": 541, "10": 587, "12": 26, "14": 12, "17": 4, "29": 1, "34": 2, "48": 4, "57": 505, "68": 305, "81": 29, "96": 10, "114": 2, "135": 3, "160": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 16615, "range": [1, 10000], "values": {"2": 0, "3": 26, "4": 249, "5": 728, "6": 156, "7": 29, "8": 89, "10": 352, "12": 104, "14": 291, "17": 18, "20": 12, "24": 4, "29": 2, "34": 3, "40": 1, "48": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 94, "range": [1, 10000], "values": {"4": 0, "5": 1, "12": 1, "68": 1, "81": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 88300, "range": [1, 10000], "values": {"0": 6419, "1": 29, "2": 49, "3": 57, "4": 89, "5": 116, "6": 48, "7": 23, "8": 136, "10": 408, "12": 158, "14": 1548, "17": 392, "20": 148, "24": 42, "29": 39, "34": 41, "40": 32, "48": 820, "68": 1, "190": 1, "226": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 80586, "range": [1, 100], "values": {"0": 806, "1": 47, "7": 6, "12": 2, "34": 4, "40": 1, "45": 3, "51": 8, "56": 50, "62": 366, "67": 727, "73": 22, "78": 13, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 5176825, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "8": 1, "25": 1, "28": 4, "32": 23, "36": 62, "41": 101, "47": 98, "53": 70, "60": 84, "68": 412, "77": 536, "88": 1043, "100": 703, "114": 330, "130": 291, "148": 186, "168": 207, "191": 170, "217": 82, "247": 129, "281": 316, "320": 505, "364": 689, "414": 857, "471": 1501, "536": 1341, "610": 491, "695": 582, "791": 681, "901": 380, "1026": 210, "1168": 112, "1330": 37, "1514": 1, "1724": 5, "1963": 5, "2235": 3, "2545": 3, "2898": 4, "4279": 1, "4872": 1, "5548": 1, "6317": 1, "7193": 2, "8190": 2, "9326": 2, "10619": 1, "12092": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 100291, "range": [1, 2000], "values": {"0": 2134, "1": 88, "2": 29, "3": 27, "4": 40, "5": 48, "6": 47, "7": 37, "8": 44, "9": 44, "10": 52, "11": 84, "13": 50, "15": 5640, "17": 58, "19": 41, "22": 8, "25": 13, "29": 13, "33": 2, "38": 9, "50": 13, "57": 1, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 10596, "range": [1, 200], "values": {"2": 0, "3": 82, "4": 770, "5": 258, "6": 749, "7": 179, "8": 14, "9": 4, "10": 3, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 15625231520, "range": [16, 2147483646], "values": {"0": 0, "16": 19112, "23": 375, "28": 25, "34": 1314, "41": 4666, "50": 51, "61": 185, "74": 45, "90": 33, "109": 185, "132": 3636, "160": 69, "194": 44, "235": 4676, "284": 2814, "344": 960, "416": 4636, "503": 4830, "609": 1335, "737": 3870, "892": 4525, "1080": 1210, "1307": 1007, "1582": 3572, "1915": 795, "2318": 886, "2805": 2207, "3395": 1807, "4109": 657, "4973": 2421, "6019": 372, "7284": 416, "8815": 42, "10668": 11, "12911": 170, "15625": 5, "18910": 6, "22886": 2, "27698": 9, "33521": 4, "40569": 14, "49098": 9, "59421": 5, "225968": 13, "273476": 2, "400557": 1, "2231094": 1, "3267857": 4604, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 13051128, "range": [1, 2147483646], "values": {"0": 0, "1": 19532, "2": 4, "3": 6290, "5": 125, "8": 3899, "12": 8062, "19": 18371, "30": 10095, "47": 3037, "73": 854, "113": 1720, "176": 1033, "274": 1, "662": 5, "1599": 4604, "6002": 1, "22533": 1, "35021": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 27605649, "range": [1, 150000000], "values": {"0": 723, "1": 1797, "2": 1014, "3": 584, "4": 1385, "5": 4420, "6": 4480, "7": 3871, "8": 6181, "10": 5295, "12": 3065, "14": 3647, "17": 3477, "20": 8042, "24": 5646, "29": 5175, "35": 4281, "42": 3068, "50": 2006, "60": 963, "72": 439, "87": 231, "105": 173, "126": 161, "151": 220, "182": 848, "219": 721, "263": 723, "316": 250, "380": 49, "457": 9, "549": 11, "660": 33, "793": 19, "953": 3, "1146": 1, "1378": 2, "1657": 4, "1992": 4, "2395": 53, "2879": 107, "3461": 81, "4161": 57, "5002": 3792, "6013": 481, "7228": 28, "8689": 3, "10445": 4, "12556": 3, "15094": 2, "31521": 2, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 309865032, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 44, "251252": 58, "267766": 68, "285365": 21, "304121": 11, "324110": 249, "345412": 274, "368115": 189, "392310": 2, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 488037920, "range": [32768, 16777216], "values": {"474861": 0, "506072": 920, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 218719708, "range": [32768, 16777216], "values": {"132939": 0, "141677": 3, "150989": 34, "160913": 61, "171489": 10, "182760": 31, "194772": 55, "207574": 4, "221217": 181, "235757": 109, "251252": 249, "267766": 181, "285365": 1, "324110": 1, "345412": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 96003072, "range": [1024, 16777216], "values": {"97683": 0, "102590": 920, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 920, "1": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 2334, "range": [1, 10000], "values": {"0": 31, "1": 216, "2": 166, "3": 28, "4": 19, "5": 28, "6": 15, "7": 28, "8": 32, "10": 20, "12": 14, "14": 29, "24": 3, "40": 1, "48": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1218, "range": [1, 10000], "values": {"0": 19, "1": 103, "2": 83, "3": 15, "4": 9, "5": 16, "6": 7, "7": 15, "8": 16, "10": 10, "12": 7, "14": 15, "24": 2, "40": 1, "48": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3, "range": [1, 10000], "values": {"0": 3, "1": 3, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 9, "range": [1, 1000], "values": {"0": 73, "1": 5, "4": 1, "5": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 218, "range": [1, 100000], "values": {"22": 0, "24": 1, "27": 1, "75": 1, "83": 1, "92": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 418, "range": [1, 100000], "values": {"55": 0, "61": 1, "68": 1, "102": 1, "154": 1, "171": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"1": 0, "2": 7, "3": 1, "4": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 2, "8": 1, "9": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 96, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "8": 1, "10": 1, "25": 1, "45": 1, "50": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 494, "range": [1, 50000], "values": {"41": 0, "45": 2, "55": 1, "67": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 510, "range": [1, 50000], "values": {"41": 0, "45": 1, "50": 1, "55": 1, "67": 1, "74": 1, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 518, "range": [1, 50000], "values": {"45": 0, "50": 2, "55": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 518, "range": [1, 50000], "values": {"45": 0, "50": 2, "55": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 518, "range": [1, 50000], "values": {"45": 0, "50": 2, "55": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 520, "range": [1, 50000], "values": {"45": 0, "50": 2, "55": 1, "74": 2, "192": 1, "211": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 227, "range": [1, 50000], "values": {"0": 25, "1": 28, "2": 8, "3": 5, "4": 4, "5": 1, "6": 4, "7": 2, "9": 1, "12": 1, "14": 1, "15": 1, "17": 1, "19": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 884167, "range": [1, 50000], "values": {"3": 0, "4": 1, "5": 1, "10": 2, "12": 3, "13": 1, "14": 2, "15": 2, "17": 1, "19": 1, "21": 3, "28": 2, "37": 1, "45": 1, "119": 1, "131": 21, "144": 116, "158": 863, "174": 1219, "192": 1765, "211": 514, "232": 78, "255": 6, "281": 6, "309": 4, "340": 2, "374": 4, "454": 1, "500": 1, "550": 1, "605": 2, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 8, "range": [1, 50], "values": {"0": 40, "2": 4, "3": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 11639, "range": [1, 2000], "values": {"0": 0, "1": 51, "2": 111, "3": 57, "4": 49, "5": 42, "6": 39, "7": 34, "8": 41, "9": 23, "10": 27, "11": 46, "13": 29, "15": 32, "17": 25, "19": 15, "22": 10, "25": 15, "29": 70, "33": 38, "38": 15, "44": 28, "50": 26, "57": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 74, "range": [1, 2000], "values": {"0": 0, "1": 15, "2": 15, "3": 2, "4": 2, "5": 3, "6": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6091, "range": [1, 2000], "values": {"0": 54, "1": 208, "2": 116, "3": 92, "4": 67, "5": 56, "6": 55, "7": 26, "8": 25, "9": 20, "10": 9, "11": 21, "13": 13, "15": 13, "17": 9, "19": 5, "22": 3, "25": 2, "29": 2, "33": 8, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "196": 1, "225": 2, "258": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 81, "range": [1, 2000], "values": {"0": 1, "1": 31, "2": 2, "4": 1, "6": 1, "7": 2, "8": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 5217, "range": [1, 2000], "values": {"0": 28, "1": 202, "2": 129, "3": 96, "4": 71, "5": 62, "6": 52, "7": 30, "8": 31, "9": 21, "10": 20, "11": 28, "13": 11, "15": 11, "17": 8, "19": 4, "22": 4, "25": 1, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 82, "range": [1, 2000], "values": {"0": 1, "1": 31, "2": 2, "4": 1, "6": 1, "7": 2, "8": 1, "15": 1, "17": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 47, "range": [1, 50000], "values": {"5": 0, "6": 2, "15": 1, "19": 1, "21": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 77, "range": [1, 50000], "values": {"0": 1, "1": 4, "2": 1, "6": 1, "12": 1, "14": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 103, "range": [1, 50000], "values": {"0": 24, "1": 24, "2": 7, "3": 5, "4": 4, "5": 1, "6": 1, "7": 2, "9": 1, "10": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 144, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "13": 1, "14": 2, "17": 1, "19": 1, "21": 2, "23": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 173, "range": [1, 50000], "values": {"3": 0, "4": 1, "5": 1, "10": 1, "12": 1, "28": 2, "37": 1, "45": 1, "50": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1300137, "power.total_cpu_time_ms": 1115827}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 1115827}, "power.wakeups_per_process_type": {"extension": 1300137}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 273, "power.total_cpu_time_ms": 23}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 23}, "power.wakeups_per_process_type": {"socket": 273}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1913020, "power.total_cpu_time_ms": 109084}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 109084}, "power.wakeups_per_process_type": {"utility": 1913020}}}}, "histograms": {"ADDON_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 33, "histogram_type": 1, "sum": 510, "range": [1, 32], "values": {"14": 0, "15": 34, "16": 0}}, "CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85046650, "range": [1, 100000], "values": {"49": 0, "61": 1, "76": 2, "149": 1, "10589": 1, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 1305244, "range": [1, 66355200], "values": {"42226": 0, "61009": 4, "265859": 1, "554984": 1, "801854": 0}}, "CHECKERBOARD_POTENTIAL_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 10314, "range": [1, 1000000], "values": {"113": 0, "149": 6, "1774": 1, "2336": 1, "4053": 1, "5338": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5785830, "range": [1, 1073741824], "values": {"1303": 0, "1994": 3, "3052": 1, "39254": 1, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10853, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 7, "31": 86, "34": 77, "38": 38, "42": 64, "46": 12, "68": 1, "75": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 301217, "range": [1, 1000], "values": {"0": 14964, "1": 5008, "2": 19241, "3": 20695, "4": 18736, "5": 8679, "6": 2838, "7": 1242, "8": 746, "9": 512, "10": 432, "11": 333, "12": 510, "14": 322, "16": 255, "18": 194, "20": 211, "23": 128, "26": 80, "29": 51, "33": 21, "37": 19, "42": 13, "47": 10, "53": 1, "60": 3, "75": 1, "95": 1, "135": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2195, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 16, "6": 36, "7": 119, "8": 94, "9": 10, "10": 5, "11": 1, "12": 2, "13": 3, "15": 3, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 13649, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 3, "38": 68, "42": 88, "46": 60, "51": 63, "56": 5, "62": 1, "68": 1, "83": 1, "138": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 295, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3666, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 26, "6": 17, "7": 138, "8": 149, "10": 71, "12": 14, "14": 10, "17": 3, "20": 2, "24": 1, "40": 1, "48": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 74, "range": [1, 100], "values": {"1": 0, "2": 37, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 51, "range": [1, 100], "values": {"0": 0, "1": 23, "2": 14, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1876, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 4, "34": 10, "40": 9, "48": 4, "57": 1, "68": 1, "81": 3, "96": 1, "114": 2, "135": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 4298, "range": [1, 10000], "values": {"48": 0, "57": 1, "68": 8, "81": 12, "96": 6, "114": 2, "135": 1, "190": 3, "226": 4, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 230, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 4, "range": [1, 2], "values": {"0": 226, "1": 4, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 1164, "range": [1, 10000], "values": {"8": 0, "10": 1, "14": 13, "24": 1, "29": 3, "34": 6, "40": 5, "48": 8, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 58, "range": [1, 1000], "values": {"0": 26, "1": 2, "2": 1, "4": 3, "5": 1, "7": 2, "10": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1182, "range": [1, 10000], "values": {"17": 0, "20": 4, "24": 11, "29": 9, "34": 8, "40": 3, "48": 1, "57": 1, "68": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 508, "range": [1, 10000], "values": {"4": 0, "5": 1, "6": 7, "7": 3, "8": 7, "10": 3, "12": 3, "14": 5, "17": 1, "20": 2, "24": 2, "29": 1, "40": 1, "48": 1, "57": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 184, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 1, "48": 2, "57": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1833, "range": [1, 10000], "values": {"0": 129, "1": 9, "2": 2, "3": 3, "4": 5, "6": 4, "7": 4, "8": 9, "10": 6, "12": 3, "14": 25, "17": 1, "20": 1, "24": 4, "29": 4, "34": 7, "40": 5, "48": 9, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 693, "range": [1, 100], "values": {"0": 6, "1": 5, "7": 3, "12": 4, "18": 3, "23": 4, "29": 5, "34": 3, "40": 3, "45": 1, "51": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 1564746, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "9": 1, "10": 1, "11": 2, "13": 6, "15": 3, "17": 4, "19": 6, "22": 3, "25": 5, "28": 1, "32": 5, "36": 7, "41": 10, "47": 20, "53": 53, "60": 81, "68": 113, "77": 249, "88": 469, "100": 880, "114": 1087, "130": 883, "148": 466, "168": 244, "191": 244, "217": 317, "247": 193, "281": 151, "320": 106, "364": 78, "414": 75, "471": 88, "536": 121, "610": 95, "695": 28, "791": 29, "901": 22, "1026": 15, "1168": 5, "1330": 18, "1514": 12, "1724": 11, "1963": 7, "2235": 7, "2545": 11, "2898": 13, "3300": 11, "3758": 4, "4279": 10, "4872": 4, "5548": 6, "6317": 1, "7193": 3, "8190": 2, "9326": 3, "10619": 2, "12092": 1, "13769": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 2358, "range": [1, 2000], "values": {"0": 66, "1": 12, "2": 3, "4": 2, "6": 1, "9": 2, "10": 1, "11": 1, "13": 2, "15": 86, "17": 3, "19": 1, "22": 3, "38": 2, "44": 1, "50": 2, "57": 1, "75": 1, "99": 2, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 230, "range": [1, 200], "values": {"2": 0, "3": 2, "4": 12, "5": 5, "6": 8, "7": 5, "8": 1, "9": 1, "12": 1, "16": 1, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 100166864, "range": [16, 2147483646], "values": {"0": 0, "16": 94978, "23": 9069, "34": 94, "41": 46, "50": 27, "61": 519, "74": 352, "90": 146, "109": 130, "132": 79, "160": 13, "194": 667, "235": 8197, "284": 7471, "344": 955, "416": 1710, "503": 73835, "609": 6395, "737": 798, "892": 11000, "1080": 16865, "1307": 458, "1582": 78, "1915": 69, "2318": 118, "2805": 57, "3395": 27, "4109": 31, "4973": 23, "6019": 58, "7284": 160, "8815": 2, "10668": 3, "12911": 8, "15625": 1, "18910": 8, "22886": 48, "27698": 2, "33521": 5, "40569": 30, "59421": 1, "71914": 9, "87033": 17, "105331": 1, "154277": 7, "186713": 14, "225968": 3, "330972": 1, "400557": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 2816256, "range": [1, 2147483646], "values": {"0": 0, "1": 104047, "3": 7126, "5": 1286, "8": 82088, "12": 1655, "19": 8498, "30": 28896, "47": 536, "73": 149, "113": 149, "176": 37, "274": 14, "426": 8, "662": 8, "1029": 48, "1599": 1, "2485": 3, "6002": 11, "9328": 5, "14498": 20, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4339269, "range": [1, 150000000], "values": {"0": 1736, "1": 9113, "2": 12020, "3": 14316, "4": 14796, "5": 17084, "6": 13048, "7": 8363, "8": 21600, "10": 16462, "12": 15544, "14": 26632, "17": 19896, "20": 17025, "24": 8947, "29": 4192, "35": 2880, "42": 2648, "50": 2129, "60": 1301, "72": 1009, "87": 865, "105": 681, "126": 482, "151": 405, "182": 380, "219": 165, "263": 111, "316": 87, "380": 79, "457": 74, "549": 83, "660": 71, "793": 61, "953": 53, "1146": 42, "1378": 36, "1657": 31, "1992": 24, "2395": 24, "2879": 30, "3461": 19, "4161": 14, "5002": 7, "6013": 9, "7228": 9, "10445": 1, "12556": 1, "15094": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 576193348, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 15, "418095": 14, "445575": 67, "474861": 60, "506072": 35, "539334": 61, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 753831220, "range": [32768, 16777216], "values": {"741455": 0, "790188": 916, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 2121470324, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 4, "1696203": 4, "1807688": 18, "1926500": 82, "2053121": 92, "2188065": 149, "2331878": 305, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 375685116, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 12, "221217": 9, "235757": 20, "251252": 31, "267766": 48, "285365": 31, "304121": 24, "324110": 49, "345412": 108, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 47442944, "range": [1024, 16777216], "values": {"44591": 0, "46831": 155, "49183": 109, "51654": 483, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 916, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 19168, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 235, "33": 41, "49": 8, "73": 2, "771": 1, "8158": 1, "12089": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 653, "range": [1, 200000], "values": {"6": 0, "8": 5, "10": 7, "13": 2, "17": 7, "22": 2, "28": 5, "36": 1, "46": 1, "58": 1, "74": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 8562, "range": [1, 200000], "values": {"2": 0, "3": 1, "10": 7, "13": 12, "17": 23, "22": 19, "28": 11, "36": 9, "46": 4, "58": 4, "74": 9, "94": 1, "119": 1, "151": 3, "310": 2, "394": 1, "501": 1, "2680": 1, "3406": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 13, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 818, "range": [1, 100000], "values": {"0": 23, "1": 8, "5": 1, "16": 2, "20": 2, "25": 6, "31": 7, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 5456, "range": [1, 2], "values": {"0": 57, "1": 5456, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 24, "range": [1, 2], "values": {"0": 1, "1": 24, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 430, "range": [1, 3], "values": {"0": 475, "2": 215, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 215, "range": [1, 2], "values": {"0": 0, "1": 215, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 4525, "range": [1, 60000], "values": {"0": 210, "874": 5, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2862, "range": [1, 16], "values": {"2": 0, "3": 34, "4": 690, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 1434, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 34, "3": 455, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 705, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 686, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 30, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 675, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 101741, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 2, "32": 1, "33": 2, "35": 8, "37": 15, "39": 12, "41": 18, "43": 24, "45": 15, "47": 24, "49": 25, "51": 15, "53": 23, "55": 26, "58": 36, "61": 19, "64": 17, "67": 25, "70": 12, "73": 18, "76": 17, "80": 16, "84": 19, "88": 21, "92": 13, "96": 8, "100": 12, "105": 14, "110": 5, "115": 8, "120": 9, "126": 5, "132": 6, "138": 3, "144": 3, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 6, "207": 4, "217": 15, "227": 34, "237": 17, "248": 16, "259": 14, "271": 5, "283": 3, "296": 4, "310": 8, "324": 3, "339": 13, "355": 4, "371": 6, "388": 11, "406": 7, "425": 15, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 99574, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 2, "32": 1, "33": 2, "35": 8, "37": 14, "39": 9, "41": 15, "43": 23, "45": 14, "47": 23, "49": 24, "51": 15, "53": 23, "55": 26, "58": 36, "61": 18, "64": 17, "67": 23, "70": 12, "73": 18, "76": 17, "80": 16, "84": 19, "88": 21, "92": 13, "96": 8, "100": 12, "105": 14, "110": 5, "115": 8, "120": 9, "126": 5, "132": 5, "138": 3, "144": 3, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 6, "207": 4, "217": 15, "227": 34, "237": 17, "248": 16, "259": 13, "271": 5, "283": 3, "296": 3, "310": 8, "324": 3, "339": 13, "355": 3, "371": 6, "388": 11, "406": 7, "425": 14, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 2020, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 4, "37": 4, "41": 4, "45": 2, "47": 2, "49": 2, "53": 1, "92": 1, "115": 2, "151": 1, "173": 1, "181": 1, "259": 1, "271": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 99721, "range": [1, 60000], "values": {"25": 0, "26": 1, "27": 1, "33": 1, "35": 4, "37": 11, "39": 12, "41": 14, "43": 24, "45": 13, "47": 22, "49": 23, "51": 15, "53": 22, "55": 26, "58": 36, "61": 19, "64": 17, "67": 25, "70": 12, "73": 18, "76": 17, "80": 16, "84": 19, "88": 21, "92": 12, "96": 8, "100": 12, "105": 14, "110": 5, "115": 6, "120": 9, "126": 5, "132": 6, "138": 3, "144": 3, "151": 6, "158": 3, "165": 3, "173": 5, "181": 3, "189": 4, "198": 6, "207": 4, "217": 15, "227": 34, "237": 17, "248": 16, "259": 13, "271": 5, "283": 3, "296": 4, "310": 8, "324": 3, "339": 13, "355": 4, "371": 6, "388": 11, "406": 7, "425": 15, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1834430, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 14, "3314": 131, "3855": 24, "4484": 6, "5216": 182, "6067": 26, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 339, "range": [1, 2], "values": {"0": 385, "1": 339, "2": 0}}, "CERT_VALIDATION_HTTP_REQUEST_RESULT": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1676, "range": [1, 16], "values": {"3": 0, "4": 351, "8": 34, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 2312, "range": [1, 16], "values": {"3": 0, "4": 100, "8": 239, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 11699, "range": [1, 100000], "values": {"0": 246, "1": 164, "2": 40, "3": 16, "4": 5, "5": 4, "6": 3, "8": 22, "10": 4, "13": 2, "16": 1, "20": 2, "25": 7, "31": 3, "39": 1, "49": 4, "61": 1, "76": 2, "95": 1, "119": 2, "149": 2, "233": 1, "365": 1, "457": 1, "1404": 1, "5399": 1, "6758": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 65857, "range": [1, 16], "values": {"0": 3, "1": 1, "2": 57, "8": 240, "9": 1288, "10": 5214, "11": 6, "12": 2, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 7044, "range": [1, 2], "values": {"0": 9, "1": 7044, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 9342, "range": [1, 5000], "values": {"0": 1386, "1": 9, "2": 7, "3": 7, "5": 2, "7": 6, "8": 1, "10": 2, "11": 4, "12": 4, "13": 4, "14": 6, "15": 10, "16": 5, "17": 7, "18": 2, "19": 6, "20": 3, "21": 4, "23": 6, "25": 5, "27": 5, "29": 5, "31": 2, "33": 2, "35": 4, "38": 4, "41": 3, "47": 4, "50": 2, "54": 3, "58": 2, "66": 2, "71": 2, "87": 3, "93": 2, "100": 1, "107": 1, "115": 2, "123": 1, "132": 5, "163": 4, "175": 2, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 2, "1158": 1, "1242": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 9021, "range": [1, 5000], "values": {"0": 149, "1": 5, "2": 2, "3": 5, "10": 1, "11": 2, "12": 6, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 4, "38": 1, "41": 1, "44": 2, "62": 1, "66": 10, "71": 2, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 2, "142": 1, "163": 3, "188": 2, "202": 3, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 36449, "range": [1, 16], "values": {"0": 0, "1": 18070, "2": 748, "6": 2535, "7": 239, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 15488, "range": [1, 60000], "values": {"0": 5, "1": 33, "2": 26, "3": 11, "4": 7, "5": 2, "6": 3, "7": 2, "9": 9, "11": 12, "14": 58, "17": 52, "21": 80, "26": 67, "32": 44, "40": 27, "50": 17, "62": 7, "77": 11, "95": 7, "118": 4, "146": 9, "181": 1, "278": 1, "1012": 1, "1255": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 114740, "range": [1, 60000], "values": {"0": 249, "1": 523, "2": 279, "3": 48, "4": 17, "5": 14, "6": 12, "7": 19, "9": 32, "11": 78, "14": 243, "17": 257, "21": 261, "26": 280, "32": 288, "40": 139, "50": 102, "62": 63, "77": 61, "95": 76, "118": 49, "146": 49, "181": 41, "224": 35, "278": 32, "345": 8, "428": 4, "658": 1, "1012": 3, "1255": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 811, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 486, "range": [1, 60000], "values": {"0": 3888, "1": 119, "2": 52, "3": 16, "4": 8, "6": 1, "7": 1, "11": 2, "14": 1, "17": 3, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5702, "range": [1, 60000], "values": {"11": 0, "14": 1, "17": 1, "21": 1, "26": 1, "40": 1, "77": 3, "146": 2, "181": 5, "531": 2, "1255": 2, "1556": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 774, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 34989, "range": [1, 50], "values": {"0": 75, "3": 13, "4": 1391, "6": 27, "8": 3653, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 1413, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 46, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 1, "8": 1, "9": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 46, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 1, "8": 1, "9": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 48, "range": [1, 50], "values": {"0": 8, "1": 6, "2": 21, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 64, "range": [1, 50], "values": {"0": 95, "2": 32, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"0": 0, "1": 6, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 21, "1": 1, "2": 2, "3": 1, "4": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 27, "1": 1, "2": 0}}, "URLCLASSIFIER_ASYNC_CLASSIFYLOCAL_TIME": {"bucket_count": 30, "histogram_type": 0, "sum": 2, "range": [1, 60000], "values": {"0": 2, "2": 1, "3": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 282, "range": [1, 1000], "values": {"0": 7, "1": 9, "2": 2, "29": 6, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 23, "5": 1, "12": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 351, "range": [1, 5000], "values": {"0": 18, "29": 3, "55": 3, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 30, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1353, "range": [50, 1000], "values": {"69": 0, "77": 2, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 133, "range": [50, 10000], "values": {"0": 13, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 111, "range": [1, 50], "values": {"36": 0, "37": 3, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 582, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 4, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 480, "range": [1, 1000], "values": {"4": 0, "6": 1, "9": 1, "13": 8, "19": 9, "27": 1, "39": 1, "56": 1, "80": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 522, "range": [1, 1000], "values": {"0": 0, "1": 4, "2": 4, "3": 5, "6": 2, "13": 1, "39": 3, "56": 1, "237": 1, "340": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 1265, "range": [1, 1000], "values": {"27": 0, "39": 1, "56": 6, "80": 1, "115": 4, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 187, "range": [1, 1000], "values": {"115": 0, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_TRIGGER": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 30, "range": [1, 100], "values": {"3": 0, "4": 6, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 15, "range": [1, 100], "values": {"0": 0, "1": 11, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 29545, "range": [1, 10000], "values": {"355": 0, "405": 1, "759": 1, "860": 1, "1011": 2, "1314": 1, "1365": 1, "1466": 2, "1516": 1, "1567": 2, "1668": 1, "1718": 1, "1920": 1, "2021": 1, "2324": 2, "2779": 1, "2829": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 41846, "range": [1, 10000], "values": {"0": 1072, "1": 16081, "2": 7252, "3": 511, "4": 224, "5": 133, "6": 86, "7": 58, "8": 83, "10": 43, "12": 29, "14": 26, "17": 10, "20": 24, "24": 10, "29": 17, "34": 7, "40": 19, "48": 4, "57": 5, "68": 4, "81": 12, "96": 5, "114": 2, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 21878, "range": [1, 10000], "values": {"0": 743, "1": 7372, "2": 4267, "3": 300, "4": 139, "5": 80, "6": 50, "7": 35, "8": 44, "10": 27, "12": 18, "14": 14, "17": 4, "20": 12, "24": 4, "29": 9, "34": 3, "40": 10, "48": 3, "57": 3, "68": 1, "81": 1, "96": 1, "114": 1, "135": 1, "160": 2, "190": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 151, "range": [1, 2000], "values": {"0": 226, "1": 19, "2": 6, "3": 8, "5": 2, "7": 3, "15": 1, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 4025, "range": [1, 50], "values": {"1": 0, "2": 2011, "3": 1, "4": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1820, "range": [1, 10000], "values": {"0": 1536, "1": 1820, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 5389, "range": [1, 10000], "values": {"0": 766, "1": 181, "2": 94, "3": 57, "4": 36, "5": 55, "6": 15, "7": 19, "8": 31, "10": 31, "12": 38, "14": 21, "17": 9, "20": 15, "29": 1, "34": 8, "40": 2, "57": 6, "68": 4, "81": 11, "96": 3, "114": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 12905, "range": [1, 64], "values": {"13": 0, "14": 34, "18": 681, "19": 9, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 10155, "range": [1, 36], "values": {"22": 0, "23": 4, "29": 347, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 2281, "range": [1, 16], "values": {"3": 0, "4": 138, "7": 247, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 3000, "range": [1, 24], "values": {"11": 0, "12": 238, "16": 9, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 3174, "range": [1, 36], "values": {"22": 0, "23": 138, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 17, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1845, "range": [1, 8], "values": {"0": 0, "1": 339, "2": 17, "4": 368, "5": 0}}, "SSL_OCSP_STAPLING": {"bucket_count": 9, "histogram_type": 1, "sum": 3, "range": [1, 8], "values": {"0": 0, "1": 1, "2": 1, "3": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 425, "range": [1, 24], "values": {"0": 0, "1": 425, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 1211, "range": [1, 10], "values": {"0": 0, "1": 1211, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 1211, "range": [1, 10], "values": {"1": 0, "2": 64, "3": 361, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 1307, "range": [1, 10], "values": {"0": 0, "1": 1187, "5": 24, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 425, "range": [1, 10], "values": {"0": 0, "1": 425, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 17930, "range": [1, 256], "values": {"13": 0, "14": 27, "15": 122, "20": 168, "60": 2, "61": 9, "89": 20, "116": 36, "119": 8, "145": 33, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 41, "range": [1, 2], "values": {"0": 0, "1": 41, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 234, "range": [1, 512], "values": {"2": 0, "3": 6, "9": 1, "11": 1, "13": 13, "27": 1, "28": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 113, "range": [1, 512], "values": {"16": 0, "17": 3, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 425, "range": [1, 4], "values": {"0": 0, "1": 425, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 81, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 81, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 81, "1": 0}}, "STORAGE_ACCESS_REMAINING_DAYS": {"bucket_count": 61, "histogram_type": 1, "sum": 75, "range": [1, 60], "values": {"7": 0, "8": 1, "22": 2, "23": 1, "24": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 18, "1": 1, "2": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 11351, "range": [1, 30000], "values": {"12": 0, "19": 113, "29": 152, "45": 3, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 15, "range": [1, 10], "values": {"0": 0, "1": 15, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 15, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 123246, "range": [1, 1000], "values": {"0": 26, "1": 405, "2": 920, "3": 1962, "4": 1955, "5": 2429, "6": 2412, "7": 1936, "8": 1799, "9": 1294, "10": 948, "11": 826, "12": 675, "14": 199, "16": 112, "18": 97, "20": 103, "23": 57, "26": 41, "29": 37, "33": 20, "37": 10, "42": 9, "47": 1, "53": 2, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 413347165, "range": [1, 5000], "values": {"8": 0, "9": 1, "11": 1, "15": 3, "18": 6, "21": 17, "25": 29, "29": 64, "34": 177, "40": 128, "47": 122, "55": 152, "64": 227, "75": 558, "88": 3195, "103": 9924, "120": 2074, "140": 534, "164": 408, "192": 257, "224": 150, "262": 96, "306": 69, "357": 39, "417": 22, "487": 6, "569": 5, "665": 1, "777": 4, "907": 3, "1059": 3, "1237": 1, "3139": 2, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 421344670, "range": [8, 792], "values": {"8": 0, "16": 2, "24": 3, "32": 5, "40": 17, "48": 13, "56": 13, "64": 5, "72": 5, "80": 3, "88": 3, "96": 113, "104": 2559, "112": 5912, "120": 3726, "128": 1861, "136": 634, "144": 235, "152": 103, "160": 62, "168": 41, "176": 22, "184": 16, "192": 13, "200": 24, "208": 167, "216": 215, "224": 167, "232": 130, "240": 95, "248": 76, "256": 49, "264": 36, "272": 25, "280": 28, "288": 23, "296": 17, "304": 25, "312": 32, "320": 24, "328": 29, "336": 27, "344": 19, "352": 15, "360": 19, "368": 11, "376": 7, "384": 3, "392": 5, "400": 5, "408": 11, "416": 10, "424": 11, "432": 18, "440": 12, "448": 6, "456": 7, "464": 8, "472": 6, "480": 3, "488": 5, "496": 1, "504": 3, "512": 1, "520": 3, "528": 4, "536": 6, "544": 3, "552": 4, "560": 8, "568": 5, "576": 3, "584": 3, "592": 1, "600": 3, "608": 4, "616": 1, "624": 2, "632": 2, "640": 3, "648": 3, "656": 3, "672": 1, "680": 2, "688": 1, "704": 2, "728": 3, "736": 1, "752": 1, "768": 2, "776": 1, "784": 1, "792": 36}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 9590294, "range": [1, 5000], "values": {"21": 0, "25": 1, "29": 2, "34": 4, "40": 16, "47": 10, "55": 15, "64": 7, "75": 5, "88": 44, "103": 8266, "120": 5935, "140": 566, "164": 95, "192": 412, "224": 501, "262": 137, "306": 154, "357": 69, "417": 76, "487": 39, "569": 30, "665": 12, "777": 9, "907": 5, "1237": 1, "1445": 2, "1688": 2, "2688": 1, "3139": 2, "3666": 1, "5000": 9}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 668, "range": [1, 100000], "values": {"113": 0, "125": 1, "486": 1, "540": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1161, "range": [1, 5000], "values": {"0": 2, "1": 69, "2": 145, "3": 15, "4": 6, "6": 1, "9": 1, "12": 1, "15": 2, "21": 3, "23": 3, "27": 2, "29": 4, "31": 2, "33": 1, "38": 1, "41": 1, "44": 3, "54": 1, "58": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"0": 135, "8": 1, "9": 1, "10": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 297, "range": [1, 50000], "values": {"34": 0, "37": 1, "41": 1, "74": 1, "131": 1, "144": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 28, "range": [1, 50], "values": {"1": 0, "2": 7, "3": 1, "11": 1, "12": 0}}, "COOKIE_PURGING_ORIGINS_PURGED": {"bucket_count": 30, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 0, "1": 1, "2": 0}}, "COOKIE_PURGING_TRACKERS_WITH_USER_INTERACTION": {"bucket_count": 30, "histogram_type": 0, "sum": 0, "range": [1, 500], "values": {"0": 1, "1": 0}}, "COOKIE_PURGING_DURATION_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 102, "range": [1, 600000], "values": {"53": 0, "85": 1, "136": 0}}, "COOKIE_PURGING_INTERVAL_HOURS": {"bucket_count": 56, "histogram_type": 1, "sum": 24, "range": [1, 168], "values": {"20": 0, "23": 1, "26": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 34482, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 1579, "8": 13, "9": 10, "12": 1, "14": 2, "17": 1642, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 48, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 2034, "range": [1, 50], "values": {"1": 0, "2": 13, "5": 130, "6": 218, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 7210, "range": [1, 50], "values": {"5": 0, "6": 1, "12": 12, "20": 353, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 13, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 3210, "range": [1, 50], "values": {"0": 6, "1": 3210, "2": 0}}, "vscode": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 20, "1": 9, "2": 5, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 160, "range": [1, 2000], "values": {"0": 127, "1": 13, "2": 8, "3": 6, "4": 2, "5": 3, "7": 1, "8": 5, "9": 2, "10": 1, "15": 1, "17": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 7, "range": [1, 2000], "values": {"0": 6, "2": 2, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 133, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 57, "range": [1, 2000], "values": {"0": 2, "1": 3, "2": 6, "3": 2, "4": 1, "5": 2, "6": 2, "10": 1, "11": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 10, "1": 2, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 181, "range": [1, 2000], "values": {"0": 2, "1": 98, "2": 23, "3": 4, "4": 4, "9": 1, "10": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 43, "range": [1, 2000], "values": {"0": 20, "1": 4, "2": 1, "3": 1, "33": 1, "38": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2257, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "37": 2, "41": 2, "45": 1, "47": 2, "53": 1, "92": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 36413, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 18, "45": 6, "47": 11, "49": 11, "51": 5, "53": 11, "55": 13, "58": 16, "61": 8, "64": 11, "67": 9, "70": 4, "73": 8, "76": 5, "80": 6, "84": 7, "88": 6, "92": 1, "96": 4, "100": 2, "105": 2, "110": 2, "115": 2, "120": 5, "126": 1, "132": 4, "138": 1, "151": 4, "158": 2, "165": 1, "173": 3, "181": 3, "189": 3, "198": 2, "217": 2, "227": 4, "237": 1, "248": 5, "259": 3, "271": 2, "283": 1, "296": 1, "310": 6, "324": 2, "339": 7, "355": 1, "371": 2, "388": 3, "406": 2, "425": 3, "445": 1, "531": 2, "555": 2, "608": 1, "696": 1, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 62207, "range": [1, 60000], "values": {"25": 0, "26": 1, "27": 1, "33": 2, "37": 4, "39": 5, "41": 10, "43": 7, "45": 8, "47": 11, "49": 13, "51": 10, "53": 13, "55": 14, "58": 22, "61": 13, "64": 7, "67": 16, "70": 9, "73": 10, "76": 12, "80": 10, "84": 11, "88": 15, "92": 11, "96": 4, "100": 10, "105": 12, "110": 3, "115": 4, "120": 4, "126": 6, "132": 2, "138": 2, "144": 3, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 4, "217": 13, "227": 30, "237": 16, "248": 11, "259": 10, "271": 3, "283": 2, "296": 4, "310": 2, "324": 1, "339": 6, "355": 3, "371": 4, "388": 9, "406": 5, "425": 12, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 1362, "range": [1, 60000], "values": {"80": 0, "84": 1, "165": 1, "531": 1, "555": 1, "581": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 3, "range": [1, 32], "values": {"0": 415, "1": 3, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 7700, "range": [1, 50], "values": {"0": 239, "1": 1206, "2": 3234, "3": 6, "4": 2, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 115, "range": [1, 50], "values": {"0": 3, "1": 1, "2": 57, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 4042, "range": [1, 50], "values": {"0": 1, "1": 82, "2": 1980, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 306, "range": [1, 100], "values": {"17": 0, "18": 17, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 401, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 714, "range": [1, 100], "values": {"41": 0, "42": 17, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1744, "range": [1, 2], "values": {"0": 0, "1": 1744, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 3769, "range": [1, 2], "values": {"0": 9, "1": 3769, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 4842, "range": [1, 50], "values": {"5": 0, "6": 807, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 19711, "range": [1, 50], "values": {"5": 0, "6": 3263, "7": 19, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 6122, "range": [20, 120000], "values": {"440": 0, "601": 2, "821": 2, "1121": 2, "1531": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 6, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 6, "range": [1, 16], "values": {"0": 0, "1": 6, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 1152, "range": [1, 100000], "values": {"30": 0, "45": 1, "68": 1, "102": 2, "229": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 6, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 6, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 904, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 3, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 13, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 11, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 11, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-other_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 13524, "range": [1, 60000], "values": {"0": 204, "1": 901, "2": 114, "3": 43, "5": 34, "8": 16, "13": 4, "21": 204, "34": 63, "54": 2, "86": 1, "137": 17, "219": 0}}, "subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 392, "range": [1, 60000], "values": {"0": 12, "1": 8, "2": 9, "3": 11, "5": 16, "8": 1, "13": 2, "21": 1, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 13132, "range": [1, 60000], "values": {"0": 192, "1": 893, "2": 105, "3": 32, "5": 18, "8": 15, "13": 2, "21": 203, "34": 63, "54": 2, "86": 1, "137": 16, "219": 0}}, "subresource-image_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}, "subresource_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 11619, "range": [1, 5000], "values": {"0": 1080, "1": 128, "2": 26, "3": 7, "4": 8, "6": 8, "9": 2, "18": 20, "26": 232, "37": 14, "53": 2, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11836, "range": [1, 5000], "values": {"0": 1107, "1": 143, "2": 36, "3": 12, "4": 11, "6": 8, "9": 3, "18": 20, "26": 232, "37": 14, "53": 2, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 217, "range": [1, 5000], "values": {"0": 27, "1": 15, "2": 10, "3": 5, "4": 3, "9": 1, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_FINISH_SYNTHESIZED_RESPONSE_MS_2": {"subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 229, "range": [1, 5000], "values": {"0": 1479, "1": 33, "2": 10, "3": 6, "4": 9, "6": 3, "13": 1, "75": 1, "106": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 367, "range": [1, 5000], "values": {"0": 1500, "1": 51, "2": 17, "3": 14, "4": 13, "6": 3, "13": 2, "18": 2, "75": 1, "106": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 138, "range": [1, 5000], "values": {"0": 21, "1": 18, "2": 7, "3": 8, "4": 4, "13": 1, "18": 2, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 297, "range": [1, 50000], "values": {"34": 0, "37": 1, "41": 1, "74": 1, "131": 1, "144": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 1213, "range": [1, 50], "values": {"0": 157, "1": 357, "2": 428, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 9163, "range": [1, 50], "values": {"0": 265, "1": 1541, "2": 3811, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 434, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 59, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 24, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 44, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 180, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 16, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 52, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2758, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 46, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 44, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 52, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4794, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 88, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3830, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 166, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 300, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 332, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 27, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 578, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 9166, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 30, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 861, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 32, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3956, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 17, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 930, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 474, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 59, "1": 0}}, "indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6960, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 114130, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3016, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 39, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1220, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 261, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"4": 0, "5": 12, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1535, "range": [1, 50], "values": {"16": 0, "17": 21, "19": 62, "20": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 254, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 15, "17": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 14, "range": [1, 50], "values": {"13": 0, "14": 1, "15": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 8, "range": [1, 50], "values": {"1": 0, "2": 4, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 474, "range": [1, 50], "values": {"1": 0, "2": 237, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 102, "range": [1, 50], "values": {"1": 0, "2": 51, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 128, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 7, "19": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 6, "range": [1, 50], "values": {"1": 0, "2": 3, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 307, "range": [1, 50], "values": {"15": 0, "16": 18, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 82, "range": [1, 50], "values": {"1": 0, "2": 41, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 40, "range": [1, 50], "values": {"1": 0, "2": 18, "4": 1, "5": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 778, "range": [1, 50], "values": {"16": 0, "17": 32, "18": 13, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 4584, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 235, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1802, "range": [1, 50], "values": {"3": 0, "4": 6, "16": 9, "19": 86, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 70, "range": [1, 50], "values": {"1": 0, "2": 33, "4": 1, "5": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 42, "range": [1, 50], "values": {"1": 0, "2": 3, "18": 2, "19": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1594, "range": [1, 50], "values": {"3": 0, "4": 180, "16": 27, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 95677, "subsessionLength": 55178, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}