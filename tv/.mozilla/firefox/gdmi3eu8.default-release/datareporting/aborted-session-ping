{"type": "main", "id": "dc87039a-ab09-4421-ad40-02a539569dac", "creationDate": "2025-05-25T10:28:23.295Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 85403, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 54}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 29, "browser.engagement.tab_open_event_count": 4, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 2, "dom.contentprocess.os_priority_raised": 291, "browser.engagement.unique_domains_count": 3, "dom.contentprocess.os_priority_lowered": 34, "urlbar.zeroprefix.abandonment": 1, "dom.contentprocess.os_priority_change_considered": 75, "browser.engagement.active_ticks": 54, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 29, "power.total_thread_wakeups": 2055552, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 85402613, "browser.engagement.total_uri_count": 29, "browser.engagement.max_concurrent_window_count": 1, "browser.engagement.session_time_excluding_suspend": 85402613, "power.total_cpu_time_ms": 1195645}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 5}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1145983, "parent.active": 49662}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 2}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "power.wakeups_per_process_type": {"parent.inactive": 1927682, "parent.active": 127870}, "networking.data_transferred_v3_kb": {"Y1_N1": 6060, "Y0_N1Sys": 444, "Y2_N3Oth": 8872}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 1735, "successful": 396}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 16372, "range": [1, 10000], "values": {"0": 0, "1": 23, "2": 339, "3": 254, "4": 17, "5": 7, "6": 4, "7": 5, "8": 368, "10": 9, "12": 3, "14": 20, "17": 158, "20": 35, "24": 284, "29": 7, "34": 1, "48": 3, "57": 2, "68": 1, "81": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 782, "range": [1, 100], "values": {"2": 0, "3": 199, "4": 19, "5": 7, "6": 1, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 566, "range": [1, 100], "values": {"0": 3, "1": 74, "2": 25, "3": 116, "4": 7, "5": 4, "6": 1, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 40050, "range": [1, 10000], "values": {"1": 0, "2": 2, "4": 2, "7": 1, "8": 2, "12": 2, "14": 2, "17": 1, "20": 1, "24": 5, "29": 8, "34": 2, "40": 11, "48": 3, "57": 8, "68": 7, "81": 1, "96": 1, "114": 4, "135": 2, "160": 28, "190": 131, "226": 3, "268": 2, "318": 1, "449": 1, "533": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 58675, "range": [1, 10000], "values": {"29": 0, "34": 3, "40": 5, "48": 2, "57": 2, "68": 14, "81": 13, "96": 6, "114": 8, "135": 5, "190": 1, "226": 70, "268": 84, "318": 10, "378": 4, "533": 2, "633": 1, "752": 1, "894": 1, "1262": 1, "1500": 1, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 2166, "1": 1, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 21, "range": [1, 2], "values": {"0": 2146, "1": 21, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 10693, "range": [1, 10000], "values": {"1": 0, "2": 2, "4": 3, "8": 2, "12": 3, "14": 6, "17": 4, "20": 1, "24": 8, "29": 8, "34": 2, "40": 13, "48": 176, "57": 3, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 203, "range": [1, 1000], "values": {"0": 95, "1": 114, "2": 15, "3": 3, "4": 4, "6": 1, "7": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 31893, "range": [1, 10000], "values": {"0": 4, "1": 2, "4": 1, "5": 4, "8": 3, "14": 1, "17": 2, "20": 8, "24": 11, "29": 2, "34": 10, "40": 7, "48": 4, "57": 1, "68": 2, "81": 1, "96": 2, "135": 19, "160": 141, "190": 3, "226": 1, "268": 2, "378": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 15956, "range": [1, 10000], "values": {"0": 0, "1": 1, "2": 6, "3": 3, "4": 2, "5": 2, "6": 4, "7": 7, "8": 8, "10": 5, "12": 5, "14": 2, "17": 6, "20": 5, "24": 2, "29": 2, "40": 1, "68": 72, "81": 85, "96": 8, "114": 4, "135": 1, "190": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 330, "range": [1, 10000], "values": {"1": 0, "2": 1, "17": 2, "24": 1, "226": 1, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 39763, "range": [1, 10000], "values": {"0": 963, "1": 17, "2": 38, "3": 18, "4": 15, "5": 22, "6": 16, "7": 14, "8": 30, "10": 16, "12": 24, "14": 96, "17": 55, "20": 97, "24": 65, "29": 32, "34": 56, "40": 47, "48": 538, "57": 5, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 1880, "range": [1, 100], "values": {"0": 183, "1": 4, "7": 8, "12": 6, "18": 3, "23": 1, "29": 3, "34": 6, "40": 6, "45": 1, "51": 1, "56": 1, "67": 3, "73": 1, "78": 2, "84": 1, "89": 3, "95": 1, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 16748515, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 2, "5": 3, "6": 1, "9": 1, "10": 7, "11": 30, "13": 97, "15": 221, "17": 201, "19": 340, "22": 127, "25": 16, "28": 5, "32": 1, "36": 2, "41": 6, "47": 6, "53": 16, "60": 26, "68": 14, "77": 53, "88": 18, "100": 22, "114": 52, "130": 87, "148": 179, "168": 301, "191": 791, "217": 642, "247": 477, "281": 309, "320": 235, "364": 367, "414": 643, "471": 733, "536": 973, "610": 1121, "695": 1127, "791": 1950, "901": 2562, "1026": 3678, "1168": 2531, "1330": 344, "1514": 93, "1724": 54, "1963": 22, "2235": 13, "2545": 9, "2898": 15, "3300": 6, "3758": 18, "4279": 8, "4872": 12, "5548": 8, "6317": 5, "7193": 14, "8190": 7, "9326": 3, "10619": 3, "12092": 3, "13769": 3, "17852": 3, "20328": 3, "23147": 1, "26357": 1, "34174": 1, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 18183, "range": [1, 2000], "values": {"0": 858, "1": 35, "2": 23, "3": 32, "4": 13, "5": 14, "6": 23, "7": 15, "8": 9, "9": 4, "10": 3, "11": 19, "13": 25, "15": 794, "17": 14, "19": 5, "22": 4, "25": 3, "29": 3, "33": 6, "38": 5, "44": 5, "50": 1, "57": 2, "65": 1, "75": 1, "99": 7, "113": 2, "196": 2, "225": 4, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 2166, "range": [1, 200], "values": {"2": 0, "3": 6, "4": 27, "5": 10, "6": 8, "7": 2, "8": 10, "9": 90, "10": 18, "11": 6, "12": 25, "13": 11, "14": 9, "15": 4, "16": 1, "18": 1, "19": 1, "21": 1, "25": 1, "29": 1, "31": 1, "34": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 107077264, "range": [16, 2147483646], "values": {"0": 0, "16": 58368, "23": 5236, "28": 126, "34": 36, "41": 139, "50": 36, "61": 77, "74": 50, "90": 79, "109": 168, "132": 56, "160": 365, "194": 105, "235": 64, "284": 56410, "344": 45207, "416": 46, "503": 318, "609": 56681, "737": 595, "892": 1211, "1080": 296, "1307": 290, "1582": 566, "1915": 571, "2318": 296, "2805": 385, "3395": 11, "4109": 299, "4973": 587, "6019": 10, "7284": 105, "8815": 395, "10668": 38, "12911": 15, "15625": 2, "18910": 12, "27698": 17, "33521": 7, "40569": 2, "49098": 288, "71914": 6, "87033": 1, "105331": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 4068769, "range": [1, 2147483646], "values": {"0": 0, "1": 63846, "2": 16, "3": 290, "5": 286, "8": 502, "12": 113189, "19": 45430, "30": 2137, "47": 664, "73": 1154, "113": 299, "176": 1279, "274": 58, "426": 111, "662": 12, "1029": 9, "1599": 5, "2485": 284, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2998936, "range": [1, 150000000], "values": {"0": 3278, "1": 13690, "2": 19236, "3": 6003, "4": 3467, "5": 2498, "6": 5211, "7": 10488, "8": 35849, "10": 35098, "12": 32624, "14": 25275, "17": 7656, "20": 5360, "24": 10913, "29": 5820, "35": 3495, "42": 1014, "50": 642, "60": 399, "72": 230, "87": 209, "105": 253, "126": 204, "151": 129, "182": 273, "219": 58, "263": 68, "316": 61, "380": 5, "457": 6, "549": 8, "660": 10, "793": 2, "953": 1, "1146": 8, "1378": 5, "1657": 3, "1992": 2, "2395": 1, "2879": 3, "3461": 3, "4161": 3, "6013": 3, "7228": 8, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1375032024, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 3, "103055": 10, "109828": 5, "117047": 2, "124740": 4, "132939": 10, "141677": 18, "150989": 11, "160913": 8, "171489": 5, "182760": 7, "194772": 245, "207574": 472, "221217": 18, "235757": 422, "251252": 315, "267766": 2, "285365": 2, "368115": 1, "392310": 11, "418095": 7, "474861": 22, "506072": 92, "539334": 96, "574782": 103, "612560": 123, "652821": 134, "695728": 394, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1682111536, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 24, "267766": 747, "285365": 737, "324110": 1, "345412": 5, "368115": 22, "418095": 1, "445575": 1, "506072": 113, "539334": 96, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 78, "1019325": 744, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1051232196, "range": [32768, 16777216], "values": {"0": 23, "34922": 2, "37217": 5, "39663": 5, "42270": 3, "45048": 2, "48009": 1, "51164": 5, "54527": 5, "58111": 10, "61930": 6, "66000": 1, "70338": 9, "74961": 1, "85139": 7, "90735": 3, "96699": 241, "103055": 240, "109828": 232, "117047": 2, "124740": 26, "132939": 383, "141677": 345, "160913": 1, "171489": 1, "194772": 1, "207574": 1, "285365": 1, "304121": 16, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 97, "539334": 113, "574782": 299, "612560": 387, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 617865216, "range": [1024, 16777216], "values": {"0": 0, "1024": 5, "8848": 26, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 79, "33230": 207, "34899": 101, "36652": 205, "38493": 202, "40427": 203, "42458": 204, "44591": 181, "46831": 90, "49183": 12, "54249": 4, "59836": 3, "62842": 1, "76453": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 719, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 58, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 3034, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 6, "range": [1, 2], "values": {"0": 0, "1": 6, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 17176, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "767": 1, "862": 2, "969": 1, "1223": 1, "1456": 3, "1733": 2, "2064": 1, "2188": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 32, "range": [1, 30000], "values": {"0": 2, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 30000], "values": {"0": 2, "1": 2, "13": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 176, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "140": 1, "171": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 446, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 1, "140": 1, "209": 1, "255": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 289, "range": [1, 30000], "values": {"0": 4, "1": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 1, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 294, "range": [1, 50], "values": {"0": 2685, "2": 147, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 29776, "range": [1, 10000], "values": {"0": 458, "1": 550, "2": 1173, "3": 286, "4": 213, "5": 162, "6": 167, "7": 149, "8": 313, "10": 246, "12": 227, "14": 286, "17": 151, "20": 13, "24": 18, "29": 11, "34": 4, "40": 5, "48": 7, "57": 4, "68": 4, "81": 4, "96": 2, "114": 1, "135": 1, "190": 1, "226": 2, "268": 1, "318": 1, "533": 2, "752": 2, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 16401, "range": [1, 10000], "values": {"0": 391, "1": 257, "2": 586, "3": 145, "4": 114, "5": 80, "6": 89, "7": 80, "8": 129, "10": 119, "12": 120, "14": 122, "17": 33, "20": 12, "24": 38, "29": 16, "34": 4, "40": 10, "48": 5, "57": 4, "68": 3, "81": 4, "96": 1, "114": 1, "135": 1, "318": 1, "533": 1, "752": 2, "894": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"0": 0, "1": 52, "2": 6, "3": 6, "4": 2, "5": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 15, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2115, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "135": 1, "221": 1, "244": 1, "297": 1, "328": 1, "485": 1, "535": 0}}, "JS_PAGELOAD_DELAZIFICATION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 83, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 3, "6": 1, "7": 1, "14": 1, "15": 1, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 3, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7441, "range": [1, 1000], "values": {"0": 365, "1": 708, "2": 722, "3": 557, "4": 285, "5": 120, "6": 97, "7": 54, "8": 41, "9": 9, "10": 14, "11": 9, "12": 7, "14": 6, "16": 1, "20": 1, "26": 1, "33": 1, "37": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5030, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "909": 2, "1009": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5282, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "1009": 2, "1120": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 179, "range": [1, 5000], "values": {"0": 0, "1": 1, "2": 14, "3": 11, "4": 2, "5": 1, "10": 1, "17": 2, "29": 1, "31": 1, "33": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 38, "range": [1, 5000], "values": {"0": 25, "1": 10, "2": 2, "6": 1, "9": 2, "10": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3027, "range": [1, 50000], "values": {"4": 0, "5": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "412": 1, "454": 1, "500": 1, "605": 1, "666": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7358, "range": [1, 50000], "values": {"15": 0, "17": 1, "666": 1, "733": 3, "807": 4, "977": 1, "1075": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 11563, "range": [1, 50000], "values": {"25": 0, "28": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 2, "1577": 1, "1736": 1, "2104": 1, "2316": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 11727, "range": [1, 50000], "values": {"45": 0, "50": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 2, "1577": 1, "1736": 1, "2104": 1, "2316": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12783, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 2, "1433": 2, "1736": 2, "2316": 1, "2549": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12788, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 2, "1433": 2, "1736": 2, "2316": 1, "2549": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12877, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 2, "1433": 2, "1736": 2, "2316": 1, "2549": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2627, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "412": 1, "454": 1, "500": 1, "605": 1, "666": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6737, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 2, "1433": 2, "1577": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5082, "range": [1, 50000], "values": {"500": 0, "550": 1, "605": 1, "733": 1, "888": 2, "1183": 1, "1302": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4405, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 2, "1075": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2858, "range": [1, 50000], "values": {"374": 0, "412": 1, "500": 2, "605": 1, "666": 1, "733": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4344, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 2, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3193, "range": [1, 50000], "values": {"374": 0, "412": 1, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4333, "range": [1, 50000], "values": {"0": 60, "1": 11, "2": 29, "3": 9, "4": 9, "5": 14, "6": 4, "7": 2, "8": 2, "9": 1, "10": 5, "11": 3, "12": 5, "13": 5, "14": 4, "15": 7, "17": 5, "19": 2, "21": 2, "25": 2, "31": 1, "37": 2, "41": 1, "131": 1, "174": 1, "192": 3, "211": 1, "232": 4, "255": 2, "281": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10154, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "23": 1, "25": 1, "28": 2, "31": 2, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "74": 1, "108": 3, "119": 1, "131": 1, "158": 2, "232": 1, "255": 2, "281": 1, "374": 1, "412": 1, "605": 1, "733": 2, "807": 4, "1075": 1, "1183": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 718, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "119": 1, "158": 1, "174": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 14, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 274, "range": [1, 50], "values": {"0": 445, "1": 53, "2": 93, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4087, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 13, "4": 35, "5": 42, "6": 16, "7": 12, "8": 7, "9": 2, "10": 4, "11": 6, "13": 4, "15": 4, "17": 3, "19": 4, "22": 2, "29": 1, "33": 13, "38": 9, "44": 2, "50": 4, "57": 1, "65": 7, "75": 4, "113": 1, "130": 1, "149": 1, "171": 2, "196": 1, "225": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1102, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "44": 1, "50": 1, "86": 3, "99": 2, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 2480, "range": [1, 2000], "values": {"1": 0, "2": 13, "3": 14, "4": 8, "5": 4, "6": 5, "7": 1, "8": 16, "9": 10, "10": 12, "11": 6, "13": 1, "15": 1, "22": 1, "25": 1, "29": 2, "33": 9, "38": 11, "44": 1, "75": 1, "86": 2, "99": 2, "149": 1, "171": 2, "196": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 12622, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 6, "5": 3, "6": 6, "7": 17, "8": 9, "9": 7, "10": 8, "11": 7, "13": 4, "15": 4, "17": 3, "19": 6, "22": 10, "25": 10, "29": 11, "33": 39, "38": 14, "44": 6, "50": 5, "57": 3, "65": 8, "75": 1, "86": 6, "99": 20, "113": 21, "130": 7, "196": 7, "225": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 100, "range": [1, 2000], "values": {"2": 0, "3": 4, "5": 1, "6": 2, "8": 1, "11": 1, "22": 1, "29": 1, "33": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1251, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 2, "15": 2, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6109, "range": [1, 2000], "values": {"0": 4, "1": 12, "2": 7, "3": 43, "4": 69, "5": 17, "6": 3, "8": 2, "9": 1, "10": 1, "11": 1, "13": 2, "15": 5, "19": 2, "22": 3, "25": 2, "29": 1, "33": 8, "38": 4, "44": 8, "50": 3, "57": 5, "65": 4, "75": 5, "86": 1, "99": 3, "149": 1, "171": 1, "196": 3, "258": 1, "770": 2, "882": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1740, "range": [1, 2000], "values": {"2": 0, "3": 1, "5": 3, "7": 1, "17": 1, "19": 1, "113": 1, "130": 1, "171": 1, "196": 2, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 5269, "range": [1, 2000], "values": {"0": 4, "1": 145, "2": 51, "4": 5, "5": 2, "6": 1, "8": 3, "9": 1, "10": 2, "13": 1, "15": 2, "17": 1, "19": 2, "22": 1, "29": 1, "33": 1, "38": 1, "44": 1, "50": 4, "57": 2, "99": 1, "130": 2, "339": 1, "672": 4, "770": 1, "882": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 12739, "range": [1, 2000], "values": {"0": 1, "1": 16, "2": 60, "3": 84, "4": 10, "5": 9, "6": 4, "7": 7, "8": 2, "9": 2, "11": 6, "13": 4, "15": 2, "17": 6, "19": 6, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "86": 1, "99": 1, "113": 12, "130": 10, "149": 10, "171": 7, "196": 3, "225": 1, "258": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 154, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 4, "4": 1, "5": 1, "7": 2, "8": 1, "10": 1, "11": 2, "15": 1, "22": 1, "38": 1, "44": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1976, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 1, "86": 1, "99": 1, "171": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 8742, "range": [1, 2000], "values": {"0": 18, "1": 13, "2": 8, "3": 28, "4": 31, "5": 56, "6": 1, "7": 1, "8": 1, "10": 1, "11": 2, "15": 4, "22": 4, "25": 1, "33": 7, "38": 2, "44": 7, "50": 6, "57": 4, "65": 2, "75": 3, "86": 5, "99": 4, "113": 1, "171": 2, "225": 1, "258": 2, "296": 4, "339": 2, "389": 1, "770": 1, "882": 1, "1011": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 2221, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 2, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 4, "296": 2, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 5205, "range": [1, 2000], "values": {"0": 20, "1": 152, "2": 31, "4": 3, "5": 3, "6": 1, "8": 3, "9": 1, "10": 1, "15": 2, "17": 1, "19": 2, "22": 1, "29": 1, "33": 1, "38": 1, "44": 1, "50": 4, "57": 2, "99": 1, "130": 2, "339": 1, "672": 4, "770": 1, "882": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 18114, "range": [1, 2000], "values": {"0": 6, "1": 11, "2": 54, "3": 99, "4": 7, "5": 4, "6": 2, "7": 1, "8": 2, "10": 1, "11": 4, "13": 4, "15": 4, "17": 3, "19": 4, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 4, "99": 2, "113": 2, "130": 2, "149": 2, "171": 6, "196": 6, "225": 6, "258": 16, "296": 6, "339": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 2108, "range": [1, 2000], "values": {"0": 1, "1": 1, "3": 1, "4": 1, "7": 1, "13": 2, "25": 1, "50": 2, "149": 1, "258": 1, "296": 1, "339": 2, "446": 1, "511": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1920, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 2, "86": 3, "113": 1, "149": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 4405, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 2, "1075": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 1351, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "194": 1, "278": 1, "477": 1, "522": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 152, "range": [1, 50000], "values": {"3": 0, "4": 2, "5": 5, "7": 1, "11": 1, "12": 3, "13": 1, "15": 1, "17": 1, "19": 1, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 13, "range": [1, 50000], "values": {"0": 0, "1": 6, "2": 2, "3": 1, "4": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 3319, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 1, "232": 4, "255": 2, "281": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 107, "range": [1, 50000], "values": {"0": 14, "2": 6, "3": 2, "4": 1, "6": 2, "9": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 87, "range": [1, 50000], "values": {"0": 37, "2": 8, "3": 3, "4": 1, "5": 1, "10": 1, "12": 1, "13": 1, "17": 1, "19": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 299, "range": [1, 50000], "values": {"0": 9, "1": 5, "2": 13, "3": 2, "4": 3, "6": 1, "8": 1, "10": 4, "12": 1, "13": 1, "14": 2, "15": 4, "17": 2, "19": 1, "25": 1, "28": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 2049, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "61": 1, "108": 2, "158": 1, "232": 1, "281": 1, "374": 1, "412": 1, "454": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 4127, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "108": 1, "255": 2, "605": 1, "733": 2, "1075": 1, "1183": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 3977, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "74": 1, "119": 1, "131": 1, "158": 1, "807": 4, "888": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 718, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "119": 1, "158": 1, "174": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 291, "power.total_thread_wakeups": 3133191, "power.total_cpu_time_ms": 1192764}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 337238, "web.foreground": 753878, "prealloc": 98577, "privilegedabout": 3071}, "power.wakeups_per_process_type": {"web.background": 1256525, "web.foreground": 1008614, "prealloc": 854232, "privilegedabout": 13820}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3204, "range": [1, 10000], "values": {"0": 0, "1": 216, "2": 1416, "3": 44, "4": 3, "12": 1, "14": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 25054, "range": [1, 100], "values": {"11": 0, "14": 1668, "17": 2, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 11431, "range": [1, 100], "values": {"0": 0, "1": 972, "2": 1, "14": 696, "17": 1, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 70504, "range": [1, 10000], "values": {"4": 0, "5": 1, "10": 14, "12": 17, "14": 820, "17": 115, "20": 6, "57": 3, "68": 514, "81": 165, "96": 13, "114": 1, "135": 1, "160": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 155213, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 1, "40": 7, "48": 111, "57": 783, "68": 37, "81": 17, "96": 23, "114": 613, "135": 48, "160": 10, "190": 6, "226": 4, "268": 2, "318": 4, "378": 3, "449": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8631, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8631, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 47811, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 16, "6": 19, "7": 13, "8": 33, "10": 33, "12": 21, "14": 758, "17": 71, "20": 5, "24": 2, "29": 1, "34": 21, "40": 18, "48": 655, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 1000], "values": {"0": 1641, "1": 29, "2": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 55805, "range": [1, 10000], "values": {"0": 1, "6": 7, "7": 12, "8": 528, "10": 418, "12": 4, "14": 3, "48": 3, "57": 466, "68": 213, "81": 11, "96": 3, "114": 1, "135": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12965, "range": [1, 10000], "values": {"2": 0, "3": 23, "4": 232, "5": 656, "6": 54, "7": 6, "8": 76, "10": 295, "12": 66, "14": 253, "17": 4, "20": 4, "34": 1, "40": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 69670, "range": [1, 10000], "values": {"0": 5249, "1": 26, "2": 40, "3": 49, "4": 75, "5": 105, "6": 31, "7": 22, "8": 124, "10": 338, "12": 102, "14": 1430, "17": 214, "20": 87, "24": 20, "29": 14, "34": 27, "40": 20, "48": 658, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 66041, "range": [1, 100], "values": {"0": 648, "1": 42, "7": 5, "12": 2, "51": 2, "56": 10, "62": 220, "67": 708, "73": 13, "78": 11, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4139978, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "25": 1, "28": 1, "32": 18, "36": 60, "41": 82, "47": 79, "53": 48, "60": 64, "68": 310, "77": 412, "88": 923, "100": 604, "114": 263, "130": 223, "148": 148, "168": 155, "191": 126, "217": 53, "247": 82, "281": 224, "320": 403, "364": 570, "414": 720, "471": 1240, "536": 1157, "610": 380, "695": 450, "791": 566, "901": 314, "1026": 172, "1168": 89, "1330": 21, "1724": 1, "1963": 2, "2235": 2, "2898": 1, "8190": 1, "9326": 1, "10619": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 82209, "range": [1, 2000], "values": {"0": 1735, "1": 78, "2": 24, "3": 22, "4": 34, "5": 40, "6": 37, "7": 28, "8": 34, "9": 36, "10": 46, "11": 73, "13": 42, "15": 4590, "17": 46, "19": 35, "22": 8, "25": 13, "29": 8, "33": 2, "38": 7, "50": 13, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 8631, "range": [1, 200], "values": {"2": 0, "3": 56, "4": 606, "5": 219, "6": 621, "7": 150, "8": 7, "9": 3, "10": 3, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 12689791432, "range": [16, 2147483646], "values": {"0": 0, "16": 10598, "23": 176, "28": 1, "34": 530, "41": 3773, "50": 29, "61": 95, "74": 29, "90": 16, "109": 81, "132": 2959, "160": 32, "194": 28, "235": 3771, "284": 2279, "344": 765, "416": 3757, "503": 3836, "609": 536, "737": 3074, "892": 896, "1080": 472, "1307": 674, "1582": 1275, "1915": 671, "2318": 310, "2805": 394, "3395": 297, "4109": 212, "4973": 1047, "6019": 174, "7284": 179, "8815": 34, "10668": 8, "12911": 58, "15625": 2, "18910": 6, "27698": 1, "33521": 4, "40569": 2, "49098": 2, "225968": 13, "3267857": 3748, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 10090010, "range": [1, 2147483646], "values": {"0": 0, "1": 10782, "3": 4452, "5": 54, "8": 3082, "12": 5931, "19": 10986, "30": 4925, "47": 1193, "73": 424, "113": 427, "176": 838, "662": 2, "1599": 3748, "2485": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 22177648, "range": [1, 150000000], "values": {"0": 305, "1": 818, "2": 448, "3": 236, "4": 709, "5": 2048, "6": 2193, "7": 1998, "8": 3909, "10": 4060, "12": 2134, "14": 2661, "17": 2242, "20": 5434, "24": 2843, "29": 2450, "35": 2374, "42": 1803, "50": 1137, "60": 592, "72": 263, "87": 120, "105": 105, "126": 84, "151": 130, "182": 692, "219": 530, "263": 504, "316": 180, "380": 32, "457": 2, "549": 6, "660": 31, "793": 14, "953": 3, "1146": 1, "1657": 3, "1992": 1, "2395": 15, "2879": 43, "3461": 25, "4161": 13, "5002": 3295, "6013": 347, "7228": 7, "8689": 1, "10445": 1, "12556": 1, "31521": 1, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 264366884, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 5, "267766": 7, "285365": 10, "304121": 10, "324110": 249, "345412": 273, "368115": 189, "392310": 1, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 396796048, "range": [32768, 16777216], "values": {"474861": 0, "506072": 748, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 188131920, "range": [32768, 16777216], "values": {"132939": 0, "141677": 2, "150989": 3, "160913": 4, "171489": 2, "182760": 5, "194772": 10, "207574": 3, "221217": 180, "235757": 109, "251252": 249, "267766": 180, "285365": 1, "304121": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 78043136, "range": [1024, 16777216], "values": {"97683": 0, "102590": 748, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 748, "1": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1, "range": [1, 10000], "values": {"0": 0, "1": 1, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 5, "1": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10, "range": [1, 50000], "values": {"9": 0, "10": 1, "11": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 71, "range": [1, 50000], "values": {"61": 0, "67": 1, "74": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 73, "range": [1, 50000], "values": {"61": 0, "67": 1, "74": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 113, "range": [1, 50000], "values": {"0": 11, "1": 21, "2": 5, "3": 4, "4": 3, "6": 2, "7": 1, "17": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 723335, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "17": 1, "19": 1, "21": 3, "131": 6, "144": 54, "158": 666, "174": 970, "192": 1535, "211": 450, "232": 60, "255": 1, "309": 1, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 7647, "range": [1, 2000], "values": {"0": 0, "1": 42, "2": 87, "3": 42, "4": 33, "5": 25, "6": 27, "7": 15, "8": 27, "9": 13, "10": 19, "11": 24, "13": 21, "15": 24, "17": 12, "19": 7, "22": 7, "25": 8, "29": 48, "33": 4, "38": 13, "44": 28, "50": 22, "57": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4438, "range": [1, 2000], "values": {"0": 37, "1": 163, "2": 80, "3": 62, "4": 36, "5": 35, "6": 31, "7": 13, "8": 15, "9": 10, "10": 5, "11": 14, "13": 6, "15": 8, "17": 4, "19": 2, "22": 2, "33": 7, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "225": 2, "258": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3726, "range": [1, 2000], "values": {"0": 21, "1": 159, "2": 93, "3": 68, "4": 43, "5": 31, "6": 32, "7": 17, "8": 17, "9": 10, "10": 9, "11": 14, "13": 6, "15": 6, "17": 5, "19": 3, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 49, "range": [1, 50000], "values": {"0": 1, "1": 2, "2": 1, "6": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 64, "range": [1, 50000], "values": {"0": 10, "1": 19, "2": 4, "3": 4, "4": 3, "6": 1, "7": 1, "8": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 81, "range": [1, 50000], "values": {"15": 0, "17": 1, "19": 1, "21": 2, "23": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1070708, "power.total_cpu_time_ms": 931114}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 931114}, "power.wakeups_per_process_type": {"extension": 1070708}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 107, "power.total_cpu_time_ms": 9}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 9}, "power.wakeups_per_process_type": {"socket": 107}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1542382, "power.total_cpu_time_ms": 86182}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 86182}, "power.wakeups_per_process_type": {"utility": 1542382}}}}, "histograms": {"CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85035070, "range": [1, 100000], "values": {"79889": 0, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 644112, "range": [1, 66355200], "values": {"384119": 0, "554984": 1, "801854": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5729659, "range": [1, 1073741824], "values": {"2771032": 0, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10332, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 5, "31": 86, "34": 73, "38": 36, "42": 61, "46": 11, "51": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 13116, "range": [1, 1000], "values": {"0": 466, "1": 827, "2": 1804, "3": 716, "4": 315, "5": 166, "6": 86, "7": 79, "8": 52, "9": 37, "10": 29, "11": 24, "12": 36, "14": 15, "16": 14, "18": 6, "20": 10, "23": 7, "26": 2, "29": 5, "33": 2, "37": 2, "42": 1, "47": 3, "60": 1, "75": 1, "84": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2096, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 13, "6": 35, "7": 117, "8": 92, "9": 7, "10": 4, "12": 2, "13": 3, "15": 3, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12924, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 67, "42": 85, "46": 57, "51": 61, "56": 3, "62": 1, "68": 1, "83": 1, "92": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 282, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2426, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 21, "6": 13, "7": 133, "8": 118, "10": 11, "12": 7, "14": 2, "17": 1, "20": 1, "24": 1, "40": 1, "48": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 28, "range": [1, 100], "values": {"1": 0, "2": 14, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 19, "range": [1, 100], "values": {"0": 0, "1": 9, "2": 5, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 669, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 2, "34": 4, "40": 3, "48": 2, "81": 1, "96": 1, "114": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1830, "range": [1, 10000], "values": {"57": 0, "68": 4, "81": 4, "96": 1, "114": 1, "190": 1, "226": 3, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 82, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 81, "1": 1, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 535, "range": [1, 10000], "values": {"12": 0, "14": 1, "24": 1, "29": 3, "34": 3, "40": 2, "48": 4, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 1000], "values": {"0": 9, "1": 1, "2": 1, "4": 1, "7": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 423, "range": [1, 10000], "values": {"17": 0, "20": 2, "24": 7, "34": 4, "57": 1, "68": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 214, "range": [1, 10000], "values": {"4": 0, "5": 1, "6": 3, "8": 2, "12": 3, "14": 1, "20": 2, "24": 1, "48": 1, "57": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 26, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 655, "range": [1, 10000], "values": {"0": 58, "1": 1, "3": 2, "4": 3, "8": 1, "14": 1, "17": 1, "24": 2, "29": 3, "34": 3, "40": 2, "48": 5, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 291, "range": [1, 100], "values": {"0": 2, "1": 2, "7": 2, "23": 3, "29": 1, "34": 1, "40": 2, "45": 1, "51": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 978955, "range": [1, 1000000], "values": {"3": 0, "4": 1, "6": 1, "13": 1, "15": 1, "17": 4, "19": 6, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 4, "47": 8, "53": 39, "60": 45, "68": 72, "77": 180, "88": 389, "100": 772, "114": 964, "130": 741, "148": 361, "168": 128, "191": 85, "217": 109, "247": 76, "281": 58, "320": 42, "364": 40, "414": 47, "471": 66, "536": 101, "610": 76, "695": 16, "791": 15, "901": 13, "1026": 10, "1168": 2, "1330": 9, "1514": 5, "1724": 5, "1963": 5, "2235": 4, "2545": 4, "2898": 10, "3300": 6, "3758": 1, "4279": 7, "4872": 2, "5548": 5, "6317": 1, "7193": 1, "12092": 1, "13769": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1136, "range": [1, 2000], "values": {"0": 15, "1": 7, "2": 1, "4": 1, "9": 1, "11": 1, "13": 1, "15": 33, "17": 1, "22": 2, "50": 1, "75": 1, "99": 2, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 82, "range": [1, 200], "values": {"2": 0, "3": 1, "4": 7, "5": 2, "6": 3, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 65551616, "range": [16, 2147483646], "values": {"0": 0, "16": 68204, "23": 2278, "34": 47, "41": 13, "50": 19, "61": 122, "74": 163, "90": 32, "109": 56, "132": 25, "160": 7, "194": 187, "235": 2486, "284": 2271, "344": 271, "416": 629, "503": 56649, "609": 3447, "737": 37, "892": 7655, "1080": 12025, "1307": 305, "1582": 41, "1915": 30, "2318": 42, "2805": 13, "3395": 4, "4109": 5, "4973": 7, "6019": 23, "7284": 63, "18910": 6, "22886": 26, "33521": 4, "40569": 5, "71914": 9, "87033": 7, "105331": 1, "154277": 5, "225968": 3, "273476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 1702987, "range": [1, 2147483646], "values": {"0": 0, "1": 70482, "3": 2229, "5": 442, "8": 59440, "12": 502, "19": 3869, "30": 19977, "47": 145, "73": 57, "113": 30, "176": 2, "1029": 26, "2485": 1, "6002": 11, "9328": 5, "14498": 4, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2615006, "range": [1, 150000000], "values": {"0": 424, "1": 3885, "2": 5580, "3": 7482, "4": 8595, "5": 12477, "6": 9564, "7": 5702, "8": 16335, "10": 11780, "12": 10670, "14": 18763, "17": 14717, "20": 13488, "24": 6862, "29": 2684, "35": 1967, "42": 1891, "50": 1428, "60": 823, "72": 609, "87": 411, "105": 279, "126": 154, "151": 167, "182": 153, "219": 50, "263": 39, "316": 22, "380": 21, "457": 17, "549": 31, "660": 18, "793": 18, "953": 17, "1146": 11, "1378": 13, "1657": 16, "1992": 12, "2395": 4, "2879": 14, "3461": 8, "4161": 9, "5002": 2, "6013": 4, "7228": 6, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 494084744, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 9, "418095": 4, "445575": 8, "539334": 60, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 610860172, "range": [32768, 16777216], "values": {"741455": 0, "790188": 745, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1774583368, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 3, "1696203": 4, "1807688": 3, "1926500": 6, "2053121": 21, "2188065": 143, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 327873804, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 5, "221217": 2, "235757": 9, "324110": 38, "345412": 107, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 39118848, "range": [1024, 16777216], "values": {"46831": 0, "49183": 99, "51654": 477, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 745, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 9953, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 228, "33": 38, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 346, "range": [1, 200000], "values": {"6": 0, "8": 2, "10": 4, "17": 6, "22": 1, "28": 3, "46": 1, "58": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 1940, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 5, "22": 6, "28": 4, "36": 1, "46": 2, "58": 4, "74": 4, "151": 1, "501": 1, "637": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 645, "range": [1, 100000], "values": {"0": 14, "1": 6, "5": 1, "16": 2, "20": 1, "25": 3, "31": 5, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 1666, "range": [1, 2], "values": {"0": 52, "1": 1666, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 11, "range": [1, 2], "values": {"0": 1, "1": 11, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 386, "range": [1, 3], "values": {"0": 307, "2": 193, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 193, "range": [1, 2], "values": {"0": 0, "1": 193, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 1810, "range": [1, 60000], "values": {"0": 191, "874": 2, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2057, "range": [1, 16], "values": {"2": 0, "3": 19, "4": 500, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 918, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 19, "3": 293, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 506, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 491, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 16, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 490, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 74739, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 9, "39": 11, "41": 13, "43": 23, "45": 11, "47": 23, "49": 18, "51": 9, "53": 19, "55": 18, "58": 25, "61": 11, "64": 10, "67": 12, "70": 4, "73": 12, "76": 13, "80": 11, "84": 12, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 3, "173": 5, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 28, "237": 13, "248": 9, "259": 9, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 72882, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 8, "39": 8, "41": 10, "43": 22, "45": 10, "47": 22, "49": 18, "51": 9, "53": 19, "55": 18, "58": 25, "61": 11, "64": 10, "67": 11, "70": 4, "73": 12, "76": 13, "80": 11, "84": 12, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 3, "173": 5, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 28, "237": 13, "248": 9, "259": 8, "271": 3, "283": 2, "296": 2, "310": 6, "324": 2, "339": 8, "355": 3, "371": 4, "388": 7, "406": 6, "425": 12, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1111, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 3, "37": 1, "41": 2, "45": 1, "47": 2, "53": 1, "115": 1, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 73628, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 8, "39": 11, "41": 11, "43": 23, "45": 10, "47": 21, "49": 18, "51": 9, "53": 18, "55": 18, "58": 25, "61": 11, "64": 10, "67": 12, "70": 4, "73": 12, "76": 13, "80": 11, "84": 12, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 4, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 6, "158": 3, "165": 3, "173": 4, "181": 3, "189": 4, "198": 5, "207": 3, "217": 15, "227": 28, "237": 13, "248": 9, "259": 9, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1127409, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 10, "3314": 84, "3855": 17, "4484": 3, "5216": 119, "6067": 6, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 278, "range": [1, 2], "values": {"0": 241, "1": 278, "2": 0}}, "CERT_VALIDATION_HTTP_REQUEST_RESULT": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1012, "range": [1, 16], "values": {"3": 0, "4": 229, "8": 12, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 1932, "range": [1, 16], "values": {"3": 0, "4": 73, "8": 205, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 2907, "range": [1, 100000], "values": {"0": 188, "1": 112, "2": 31, "3": 8, "4": 4, "5": 3, "6": 2, "8": 20, "25": 6, "31": 1, "39": 1, "61": 1, "149": 2, "233": 1, "1404": 1, "1757": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 20007, "range": [1, 16], "values": {"0": 1, "1": 1, "2": 52, "8": 185, "9": 396, "10": 1479, "11": 4, "12": 2, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 2261, "range": [1, 2], "values": {"0": 5, "1": 2261, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 5418, "range": [1, 5000], "values": {"0": 191, "1": 6, "2": 4, "3": 4, "7": 4, "10": 2, "11": 3, "12": 2, "13": 1, "14": 3, "15": 7, "16": 3, "17": 5, "18": 2, "19": 5, "20": 2, "21": 4, "23": 5, "25": 4, "27": 2, "29": 3, "35": 3, "38": 3, "41": 1, "47": 3, "54": 2, "58": 1, "66": 1, "87": 2, "93": 1, "115": 1, "123": 1, "132": 2, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 7555, "range": [1, 5000], "values": {"0": 122, "1": 3, "2": 2, "3": 5, "10": 1, "11": 2, "12": 5, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "71": 1, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 1, "142": 1, "163": 3, "188": 1, "202": 2, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 17039, "range": [1, 16], "values": {"0": 0, "1": 4028, "2": 164, "6": 1912, "7": 173, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5303, "range": [1, 60000], "values": {"0": 3, "1": 21, "2": 14, "3": 6, "4": 3, "5": 2, "6": 2, "9": 3, "11": 6, "14": 5, "17": 7, "21": 12, "26": 19, "32": 15, "40": 11, "50": 7, "62": 4, "77": 10, "95": 6, "118": 3, "146": 3, "181": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 80541, "range": [1, 60000], "values": {"0": 230, "1": 336, "2": 46, "3": 17, "4": 5, "5": 5, "6": 7, "7": 12, "9": 12, "11": 48, "14": 148, "17": 143, "21": 152, "26": 181, "32": 227, "40": 100, "50": 69, "62": 47, "77": 48, "95": 60, "118": 43, "146": 36, "181": 27, "224": 28, "278": 26, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 538, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 278, "range": [1, 60000], "values": {"0": 2521, "1": 56, "2": 19, "3": 9, "7": 1, "11": 2, "14": 1, "17": 2, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 2569, "range": [1, 60000], "values": {"17": 0, "21": 1, "26": 1, "40": 1, "77": 1, "146": 2, "181": 4, "531": 2, "658": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 577, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 10171, "range": [1, 50], "values": {"0": 42, "3": 13, "4": 267, "6": 16, "8": 1121, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 280, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 14, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 1, "6": 1, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 14, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 1, "6": 1, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 26, "range": [1, 50], "values": {"0": 1, "1": 4, "2": 11, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 26, "range": [1, 50], "values": {"0": 21, "2": 13, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 4, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 6, "range": [1, 5000], "values": {"0": 9, "1": 1, "2": 1, "3": 1, "4": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 11, "1": 1, "2": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 102, "range": [1, 1000], "values": {"0": 2, "1": 4, "29": 2, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 8, "1": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 129, "range": [1, 5000], "values": {"0": 6, "55": 2, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1273, "range": [50, 1000], "values": {"69": 0, "77": 1, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 42, "range": [50, 10000], "values": {"0": 5, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 74, "range": [1, 50], "values": {"36": 0, "37": 2, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 582, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 4, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 205, "range": [1, 1000], "values": {"4": 0, "6": 1, "13": 3, "19": 3, "27": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 129, "range": [1, 1000], "values": {"0": 0, "1": 2, "2": 1, "3": 1, "6": 1, "13": 1, "39": 2, "56": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 446, "range": [1, 1000], "values": {"39": 0, "56": 3, "115": 2, "165": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 100], "values": {"3": 0, "4": 2, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 10, "range": [1, 100], "values": {"0": 0, "1": 6, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 12937, "range": [1, 10000], "values": {"355": 0, "405": 1, "759": 1, "860": 1, "1011": 2, "1466": 2, "1668": 1, "1718": 1, "2324": 1, "2375": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 10479, "range": [1, 10000], "values": {"0": 444, "1": 5067, "2": 1185, "3": 108, "4": 42, "5": 29, "6": 28, "7": 17, "8": 26, "10": 13, "12": 13, "14": 9, "17": 1, "20": 11, "24": 4, "29": 3, "40": 8, "48": 2, "96": 3, "114": 2, "135": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 5571, "range": [1, 10000], "values": {"0": 353, "1": 2409, "2": 719, "3": 69, "4": 27, "5": 18, "6": 18, "7": 9, "8": 14, "10": 8, "12": 9, "14": 6, "17": 1, "20": 5, "24": 1, "29": 3, "40": 4, "48": 2, "96": 1, "114": 1, "135": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 95, "range": [1, 2000], "values": {"0": 177, "1": 10, "2": 2, "3": 4, "5": 1, "7": 3, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 130, "range": [1, 50], "values": {"1": 0, "2": 65, "3": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 344, "range": [1, 10000], "values": {"0": 270, "1": 344, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 555, "range": [1, 10000], "values": {"0": 485, "1": 36, "2": 25, "3": 15, "4": 13, "5": 5, "7": 9, "8": 19, "10": 11, "12": 1, "14": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 9273, "range": [1, 64], "values": {"13": 0, "14": 19, "18": 493, "19": 7, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 6623, "range": [1, 36], "values": {"22": 0, "23": 3, "29": 226, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1399, "range": [1, 16], "values": {"3": 0, "4": 96, "7": 145, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 1748, "range": [1, 24], "values": {"11": 0, "12": 143, "16": 2, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 2208, "range": [1, 36], "values": {"22": 0, "23": 96, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 7, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1228, "range": [1, 8], "values": {"0": 0, "1": 278, "2": 7, "4": 234, "5": 0}}, "SSL_OCSP_STAPLING": {"bucket_count": 9, "histogram_type": 1, "sum": 3, "range": [1, 8], "values": {"0": 0, "1": 1, "2": 1, "3": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 262, "range": [1, 24], "values": {"0": 0, "1": 262, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 750, "range": [1, 10], "values": {"0": 0, "1": 750, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 750, "range": [1, 10], "values": {"1": 0, "2": 36, "3": 226, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 806, "range": [1, 10], "values": {"0": 0, "1": 736, "5": 14, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 262, "range": [1, 10], "values": {"0": 0, "1": 262, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 10922, "range": [1, 256], "values": {"13": 0, "14": 3, "15": 84, "20": 114, "60": 2, "89": 11, "116": 23, "119": 2, "145": 23, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 24, "range": [1, 2], "values": {"0": 0, "1": 24, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 91, "range": [1, 512], "values": {"12": 0, "13": 7, "14": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 62, "range": [1, 512], "values": {"30": 0, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 262, "range": [1, 4], "values": {"0": 0, "1": 262, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 34, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 34, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 34, "1": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 8, "1": 1, "2": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 10989, "range": [1, 30000], "values": {"12": 0, "19": 111, "29": 144, "45": 2, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 12, "range": [1, 10], "values": {"0": 0, "1": 12, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 12, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 13372, "range": [1, 1000], "values": {"0": 5, "1": 267, "2": 488, "3": 656, "4": 463, "5": 407, "6": 195, "7": 122, "8": 111, "9": 60, "10": 38, "11": 31, "12": 42, "14": 26, "16": 8, "18": 9, "20": 11, "23": 2, "26": 3, "29": 2, "33": 1, "42": 2, "53": 1, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 411573127, "range": [1, 5000], "values": {"13": 0, "15": 1, "18": 3, "21": 5, "25": 10, "29": 16, "34": 29, "40": 47, "47": 32, "55": 45, "64": 48, "75": 90, "88": 328, "103": 1925, "120": 189, "140": 68, "164": 43, "192": 31, "224": 10, "262": 14, "306": 8, "357": 3, "417": 2, "487": 2, "569": 2, "777": 1, "1059": 1, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 416921178, "range": [8, 792], "values": {"8": 0, "16": 1, "32": 3, "40": 1, "48": 6, "56": 1, "64": 4, "72": 4, "80": 1, "88": 2, "96": 22, "104": 632, "112": 1291, "120": 342, "128": 95, "136": 38, "144": 29, "152": 14, "160": 9, "168": 11, "176": 4, "184": 5, "200": 6, "208": 14, "216": 20, "224": 15, "232": 13, "240": 10, "248": 7, "256": 10, "264": 7, "272": 4, "280": 3, "288": 1, "296": 5, "304": 2, "312": 4, "320": 4, "328": 5, "336": 3, "344": 4, "352": 1, "360": 3, "384": 1, "424": 2, "432": 4, "456": 2, "464": 4, "472": 2, "480": 1, "488": 2, "504": 1, "512": 1, "528": 1, "536": 2, "544": 1, "552": 3, "560": 3, "584": 2, "600": 1, "608": 1, "624": 1, "632": 1, "648": 1, "792": 7}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 5182837, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 2, "40": 1, "47": 4, "55": 3, "64": 6, "75": 3, "88": 10, "103": 1682, "120": 406, "140": 46, "164": 16, "192": 38, "224": 51, "262": 19, "306": 18, "357": 5, "417": 14, "487": 14, "569": 7, "777": 1, "5000": 3}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 130, "range": [1, 100000], "values": {"113": 0, "125": 1, "139": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 203, "range": [1, 5000], "values": {"0": 0, "1": 13, "2": 35, "3": 3, "9": 1, "21": 2, "27": 1, "29": 1, "31": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"0": 69, "8": 1, "9": 1, "10": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 60000], "values": {"92": 0, "101": 1, "111": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 30023, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "30144": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 21, "range": [1, 50], "values": {"1": 0, "2": 5, "11": 1, "12": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 7869, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 380, "8": 4, "9": 6, "12": 1, "14": 2, "17": 365, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 22, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1207, "range": [1, 50], "values": {"1": 0, "2": 12, "5": 25, "6": 168, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 4104, "range": [1, 50], "values": {"11": 0, "12": 12, "20": 198, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 8, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 734, "range": [1, 50], "values": {"0": 2, "1": 734, "2": 0}}, "vscode": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 3, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 74, "range": [1, 2000], "values": {"0": 90, "1": 7, "2": 3, "3": 1, "8": 5, "9": 2, "10": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"0": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 131, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 1, "1": 2, "2": 3, "3": 1, "4": 1, "6": 2, "7": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 3, "1": 1, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 147, "range": [1, 2000], "values": {"0": 1, "1": 91, "2": 14, "3": 4, "4": 4, "5": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 4, "range": [1, 2000], "values": {"0": 5, "1": 2, "2": 1, "3": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2049, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "41": 1, "45": 1, "47": 2, "53": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 26688, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 17, "45": 6, "47": 11, "49": 8, "51": 5, "53": 10, "55": 13, "58": 13, "61": 6, "64": 6, "67": 5, "70": 2, "73": 6, "76": 4, "80": 4, "84": 5, "88": 5, "92": 1, "96": 3, "100": 1, "105": 2, "110": 1, "120": 5, "126": 1, "132": 2, "138": 1, "151": 4, "158": 2, "165": 1, "173": 2, "181": 3, "189": 3, "198": 1, "217": 2, "227": 3, "248": 4, "259": 3, "271": 1, "310": 4, "324": 1, "339": 3, "355": 1, "371": 2, "388": 1, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 45402, "range": [1, 60000], "values": {"32": 0, "33": 1, "37": 1, "39": 4, "41": 7, "43": 7, "45": 5, "47": 10, "49": 10, "51": 4, "53": 7, "55": 7, "58": 13, "61": 5, "64": 4, "67": 7, "70": 2, "73": 6, "76": 9, "80": 7, "84": 6, "88": 11, "92": 7, "96": 4, "100": 6, "105": 6, "110": 1, "115": 4, "120": 1, "126": 5, "132": 1, "138": 1, "144": 1, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 3, "217": 13, "227": 25, "237": 13, "248": 5, "259": 6, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 3, "371": 2, "388": 6, "406": 4, "425": 10, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 803, "range": [1, 60000], "values": {"80": 0, "84": 1, "165": 1, "531": 1, "555": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 1, "range": [1, 32], "values": {"0": 314, "1": 1, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 3243, "range": [1, 50], "values": {"0": 185, "1": 361, "2": 1431, "3": 4, "4": 2, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 105, "range": [1, 50], "values": {"0": 1, "1": 1, "2": 52, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 131, "range": [1, 50], "values": {"0": 0, "1": 35, "2": 48, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 198, "range": [1, 100], "values": {"17": 0, "18": 11, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 304, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 462, "range": [1, 100], "values": {"41": 0, "42": 11, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 447, "range": [1, 2], "values": {"0": 0, "1": 447, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1271, "range": [1, 2], "values": {"0": 5, "1": 1271, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 3216, "range": [1, 50], "values": {"5": 0, "6": 536, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 12462, "range": [1, 50], "values": {"5": 0, "6": 2063, "7": 12, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 2280, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 1, "1531": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 2, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 2, "range": [1, 16], "values": {"0": 0, "1": 2, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 500, "range": [1, 100000], "values": {"30": 0, "45": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 2, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 2, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 814, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 2, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 8, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 6, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 6, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 187, "range": [1, 60000], "values": {"0": 8, "1": 1, "2": 1, "5": 3, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11269, "range": [1, 60000], "values": {"0": 1, "1": 69, "2": 6, "5": 1, "8": 2, "21": 195, "34": 61, "54": 1, "137": 16, "219": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11456, "range": [1, 60000], "values": {"0": 9, "1": 70, "2": 7, "5": 4, "8": 2, "21": 195, "34": 61, "54": 1, "137": 17, "219": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 10963, "range": [1, 5000], "values": {"0": 67, "1": 9, "2": 1, "4": 1, "6": 1, "18": 19, "26": 224, "37": 13, "53": 1, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11114, "range": [1, 5000], "values": {"0": 77, "1": 9, "2": 2, "3": 2, "4": 1, "6": 1, "18": 19, "26": 224, "37": 13, "53": 1, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 151, "range": [1, 5000], "values": {"0": 10, "2": 1, "3": 2, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 7, "range": [1, 5000], "values": {"0": 349, "1": 2, "4": 1, "6": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 37, "range": [1, 5000], "values": {"0": 357, "1": 7, "4": 1, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 30, "range": [1, 5000], "values": {"0": 8, "1": 5, "18": 1, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 60000], "values": {"92": 0, "101": 1, "111": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 30023, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "30144": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 883, "range": [1, 50], "values": {"0": 104, "1": 281, "2": 301, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 2555, "range": [1, 50], "values": {"0": 176, "1": 635, "2": 960, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 48, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 18, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 24, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 143, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 25, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2244, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 24, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 31, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4454, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3115, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 216, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 149, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 302, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7158, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 9, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 135, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 436, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 499, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 30, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1868, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1341, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 92898, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 495, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 264, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 40, "range": [1, 50], "values": {"4": 0, "5": 8, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 988, "range": [1, 50], "values": {"16": 0, "17": 19, "19": 35, "20": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 469, "range": [1, 50], "values": {"16": 0, "17": 17, "18": 10, "19": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 16, "range": [1, 50], "values": {"1": 0, "2": 8, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 364, "range": [1, 50], "values": {"1": 0, "2": 182, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 78, "range": [1, 50], "values": {"1": 0, "2": 39, "3": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 174, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 10, "17": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 211, "range": [1, 50], "values": {"15": 0, "16": 12, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 16, "range": [1, 50], "values": {"1": 0, "2": 8, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 5, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3539, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 180, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1430, "range": [1, 50], "values": {"3": 0, "4": 1, "16": 6, "19": 70, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 46, "range": [1, 50], "values": {"1": 0, "2": 23, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1182, "range": [1, 50], "values": {"3": 0, "4": 113, "16": 18, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 85402, "subsessionLength": 44903, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}