{"type": "main", "id": "3675e516-02ca-4111-bd4f-48c80bb70347", "creationDate": "2025-05-25T10:57:04.906Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 87124, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 126}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 52, "browser.engagement.tab_open_event_count": 7, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 3, "dom.contentprocess.os_priority_raised": 326, "browser.engagement.unique_domains_count": 4, "dom.contentprocess.os_priority_lowered": 58, "blocklist.mlbf_source": "dump_match", "urlbar.zeroprefix.abandonment": 2, "browser.engagement.window_open_event_count": 1, "dom.contentprocess.os_priority_change_considered": 147, "browser.engagement.active_ticks": 126, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 52, "power.total_thread_wakeups": 2652948, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 87124226, "browser.engagement.total_uri_count": 52, "browser.engagement.max_concurrent_window_count": 2, "browser.engagement.session_time_excluding_suspend": 87124226, "power.total_cpu_time_ms": 1403186, "blocklist.mlbf_softblocks_source": "dump_match"}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 5}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1177491, "parent.active.playing-audio": 1127, "parent.active.playing-video": 63288, "parent.inactive.playing-video": 69155, "parent.active": 92123, "parent.inactive.playing-audio": 2}, "browser.ui.interaction.pageaction_urlbar": {"addon0": 1}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 3}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "power.wakeups_per_process_type": {"parent.inactive": 2002478, "parent.active.playing-audio": 3210, "parent.active.playing-video": 162606, "parent.inactive.playing-video": 214785, "parent.active": 269858, "parent.inactive.playing-audio": 11}, "networking.data_transferred_v3_kb": {"Y1_N1": 7908, "Y0_N1Sys": 638, "Y2_N3Oth": 70647}, "telemetry.event_counts": {"pwmgr#saved_login_used#form_login": 1}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 4894, "successful": 478}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 18827, "range": [1, 10000], "values": {"0": 2, "1": 25, "2": 354, "3": 274, "4": 39, "5": 19, "6": 9, "7": 19, "8": 378, "10": 18, "12": 7, "14": 29, "17": 174, "20": 58, "24": 302, "29": 10, "34": 2, "40": 1, "48": 6, "57": 3, "68": 1, "81": 1, "96": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 995, "range": [1, 100], "values": {"2": 0, "3": 243, "4": 33, "5": 12, "6": 1, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 726, "range": [1, 100], "values": {"0": 4, "1": 85, "2": 40, "3": 144, "4": 12, "5": 7, "6": 1, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 50838, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 2, "7": 1, "8": 2, "10": 1, "12": 5, "14": 4, "17": 1, "20": 2, "24": 10, "29": 11, "34": 6, "40": 13, "48": 8, "57": 11, "68": 10, "81": 1, "96": 2, "114": 6, "135": 2, "160": 28, "190": 136, "226": 6, "268": 5, "318": 9, "378": 5, "449": 3, "533": 1, "633": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 79786, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 3, "40": 7, "48": 5, "57": 9, "68": 18, "81": 18, "96": 14, "114": 10, "135": 6, "160": 3, "190": 1, "226": 70, "268": 91, "318": 11, "378": 8, "449": 2, "533": 5, "633": 2, "752": 3, "894": 4, "1062": 1, "1262": 2, "1500": 3, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 15, "range": [1, 2], "values": {"0": 3161, "1": 15, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 47, "range": [1, 2], "values": {"0": 3129, "1": 47, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 12771, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 3, "8": 2, "10": 2, "12": 9, "14": 7, "17": 5, "20": 5, "24": 25, "29": 12, "34": 7, "40": 16, "48": 193, "57": 3, "68": 3, "135": 1, "226": 1, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 262, "range": [1, 1000], "values": {"0": 129, "1": 130, "2": 19, "3": 7, "4": 7, "5": 1, "6": 2, "7": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 40502, "range": [1, 10000], "values": {"0": 6, "1": 2, "4": 3, "5": 4, "7": 1, "8": 7, "14": 1, "17": 3, "20": 16, "24": 16, "29": 5, "34": 15, "40": 9, "48": 5, "57": 3, "68": 2, "81": 1, "96": 2, "135": 19, "160": 146, "190": 6, "226": 7, "268": 7, "318": 5, "378": 3, "533": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 20087, "range": [1, 10000], "values": {"0": 0, "1": 3, "2": 8, "3": 3, "4": 3, "5": 5, "6": 7, "7": 10, "8": 13, "10": 6, "12": 10, "14": 5, "17": 6, "20": 7, "24": 3, "29": 5, "40": 2, "68": 75, "81": 87, "96": 9, "114": 14, "135": 7, "160": 5, "190": 2, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 483, "range": [1, 10000], "values": {"1": 0, "2": 1, "5": 1, "17": 2, "24": 2, "48": 1, "68": 1, "226": 1, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 50353, "range": [1, 10000], "values": {"0": 1213, "1": 42, "2": 64, "3": 38, "4": 40, "5": 98, "6": 59, "7": 62, "8": 130, "10": 82, "12": 120, "14": 151, "17": 80, "20": 130, "24": 107, "29": 41, "34": 64, "40": 55, "48": 590, "57": 5, "68": 3, "135": 1, "226": 1, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 3087, "range": [1, 100], "values": {"0": 202, "1": 19, "7": 10, "12": 10, "18": 6, "23": 4, "29": 5, "34": 8, "40": 7, "45": 5, "51": 2, "56": 1, "62": 1, "67": 5, "73": 3, "78": 3, "84": 1, "89": 3, "95": 2, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 18708539, "range": [1, 1000000], "values": {"2": 0, "3": 3, "4": 3, "5": 6, "6": 1, "9": 1, "10": 10, "11": 34, "13": 100, "15": 229, "17": 204, "19": 345, "22": 127, "25": 16, "28": 6, "32": 2, "36": 3, "41": 10, "47": 6, "53": 19, "60": 29, "68": 16, "77": 56, "88": 26, "100": 32, "114": 65, "130": 110, "148": 216, "168": 337, "191": 830, "217": 682, "247": 518, "281": 369, "320": 304, "364": 423, "414": 719, "471": 807, "536": 1047, "610": 1180, "695": 1170, "791": 1997, "901": 2621, "1026": 3735, "1168": 2599, "1330": 387, "1514": 113, "1724": 80, "1963": 43, "2235": 27, "2545": 28, "2898": 30, "3300": 22, "3758": 25, "4279": 21, "4872": 26, "5548": 17, "6317": 11, "7193": 50, "8190": 39, "9326": 8, "10619": 4, "12092": 5, "13769": 4, "15678": 4, "17852": 5, "20328": 3, "23147": 1, "26357": 1, "34174": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 28096, "range": [1, 2000], "values": {"0": 1135, "1": 117, "2": 66, "3": 77, "4": 54, "5": 45, "6": 43, "7": 29, "8": 21, "9": 14, "10": 16, "11": 39, "13": 38, "15": 969, "17": 51, "19": 38, "22": 16, "25": 17, "29": 9, "33": 11, "38": 15, "44": 13, "50": 3, "57": 4, "65": 4, "75": 4, "86": 4, "99": 11, "113": 6, "130": 2, "196": 2, "225": 5, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 3175, "range": [1, 200], "values": {"2": 0, "3": 14, "4": 42, "5": 14, "6": 12, "7": 5, "8": 11, "9": 94, "10": 21, "11": 7, "12": 26, "13": 12, "14": 10, "15": 4, "16": 1, "17": 1, "18": 1, "19": 1, "21": 1, "25": 2, "29": 1, "31": 3, "34": 3, "37": 1, "40": 1, "43": 1, "50": 3, "54": 1, "58": 1, "63": 1, "68": 1, "73": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 114376056, "range": [16, 2147483646], "values": {"0": 0, "16": 61844, "23": 5715, "28": 166, "34": 57, "41": 208, "50": 63, "61": 247, "74": 85, "90": 117, "109": 238, "132": 73, "160": 513, "194": 217, "235": 392, "284": 58963, "344": 47131, "416": 58, "503": 415, "609": 59245, "737": 744, "892": 1466, "1080": 312, "1307": 348, "1582": 588, "1915": 632, "2318": 311, "2805": 400, "3395": 20, "4109": 316, "4973": 617, "6019": 13, "7284": 112, "8815": 416, "10668": 43, "12911": 15, "15625": 2, "18910": 16, "22886": 1, "27698": 35, "33521": 9, "40569": 10, "49098": 301, "71914": 9, "87033": 2, "105331": 3, "127476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 4316190, "range": [1, 2147483646], "values": {"0": 0, "1": 67870, "2": 21, "3": 974, "5": 405, "8": 917, "12": 118411, "19": 47327, "30": 2449, "47": 702, "73": 1207, "113": 318, "176": 1338, "274": 86, "426": 119, "662": 16, "1029": 20, "1599": 7, "2485": 297, "3862": 3, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3319183, "range": [1, 150000000], "values": {"0": 3753, "1": 14641, "2": 19912, "3": 6332, "4": 3840, "5": 2963, "6": 5841, "7": 11175, "8": 37497, "10": 36683, "12": 33831, "14": 26368, "17": 8216, "20": 5878, "24": 11527, "29": 6207, "35": 3729, "42": 1130, "50": 709, "60": 459, "72": 257, "87": 223, "105": 268, "126": 229, "151": 148, "182": 296, "219": 70, "263": 89, "316": 74, "380": 10, "457": 14, "549": 13, "660": 15, "793": 9, "953": 9, "1146": 11, "1378": 9, "1657": 7, "1992": 3, "2395": 5, "2879": 6, "3461": 8, "4161": 6, "5002": 4, "6013": 4, "7228": 10, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1433491888, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 7, "103055": 25, "109828": 16, "117047": 2, "124740": 4, "132939": 10, "141677": 46, "150989": 11, "160913": 8, "171489": 5, "182760": 7, "194772": 245, "207574": 472, "221217": 105, "235757": 453, "251252": 331, "267766": 3, "285365": 2, "368115": 1, "392310": 11, "418095": 8, "474861": 22, "506072": 92, "539334": 96, "574782": 105, "612560": 140, "652821": 139, "695728": 399, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1785625520, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 50, "182760": 4, "267766": 805, "285365": 737, "304121": 8, "324110": 1, "345412": 31, "368115": 51, "418095": 1, "445575": 1, "506072": 113, "539334": 110, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 107, "1019325": 773, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1089307700, "range": [32768, 16777216], "values": {"0": 52, "32768": 1, "34922": 2, "37217": 5, "39663": 5, "42270": 3, "45048": 2, "48009": 1, "51164": 5, "54527": 5, "58111": 37, "61930": 7, "66000": 1, "70338": 9, "74961": 1, "85139": 7, "90735": 3, "96699": 241, "103055": 240, "109828": 232, "117047": 26, "124740": 44, "132939": 424, "141677": 355, "150989": 25, "160913": 17, "171489": 2, "194772": 1, "207574": 1, "285365": 1, "304121": 17, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 110, "539334": 120, "574782": 305, "612560": 390, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 649009152, "range": [1024, 16777216], "values": {"0": 0, "1024": 9, "8022": 1, "8848": 51, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 107, "33230": 216, "34899": 101, "36652": 206, "38493": 202, "40427": 203, "42458": 204, "44591": 181, "46831": 136, "49183": 41, "51654": 4, "54249": 20, "56974": 1, "59836": 3, "62842": 1, "65999": 1, "76453": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 748, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 87, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 3257, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 7, "range": [1, 2], "values": {"0": 0, "1": 7, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 26223, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "767": 1, "862": 2, "969": 1, "1089": 1, "1223": 1, "1374": 1, "1456": 3, "1635": 1, "1733": 2, "1947": 1, "2064": 1, "2761": 1, "2926": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 30000], "values": {"0": 2, "1": 1, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 63, "range": [1, 30000], "values": {"0": 2, "1": 2, "2": 1, "13": 1, "16": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 250, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "24": 1, "43": 1, "140": 1, "171": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 565, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 2, "63": 1, "140": 1, "209": 1, "255": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 354, "range": [1, 30000], "values": {"0": 7, "1": 7, "3": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 2, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 486, "range": [1, 50], "values": {"0": 2850, "2": 243, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 75775, "range": [1, 10000], "values": {"0": 1324, "1": 1416, "2": 2597, "3": 698, "4": 516, "5": 441, "6": 392, "7": 397, "8": 756, "10": 643, "12": 518, "14": 709, "17": 411, "20": 89, "24": 45, "29": 24, "34": 14, "40": 18, "48": 15, "57": 7, "68": 12, "81": 6, "96": 6, "114": 5, "135": 9, "190": 6, "226": 4, "268": 5, "318": 1, "533": 5, "633": 2, "752": 2, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 40729, "range": [1, 10000], "values": {"0": 1167, "1": 660, "2": 1293, "3": 322, "4": 278, "5": 228, "6": 208, "7": 206, "8": 302, "10": 305, "12": 266, "14": 291, "17": 94, "20": 52, "24": 86, "29": 43, "34": 23, "40": 36, "48": 11, "57": 10, "68": 10, "81": 5, "96": 2, "114": 2, "135": 3, "160": 1, "190": 2, "226": 2, "268": 2, "318": 1, "533": 2, "633": 2, "752": 2, "894": 0}}, "VIDEO_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "11771": 0}}, "VIDEO_HIDDEN_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 7200000], "values": {"0": 2, "1": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 108, "range": [1, 50], "values": {"0": 0, "1": 56, "2": 8, "3": 8, "4": 3, "5": 0}}, "PWMGR_LOGIN_PAGE_SAFETY": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 4, "1": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 29, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4179, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "135": 1, "221": 1, "244": 1, "297": 1, "328": 2, "362": 2, "399": 1, "485": 2, "535": 0}}, "JS_PAGELOAD_DELAZIFICATION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 7, "1": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 139, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 3, "6": 1, "7": 1, "8": 2, "11": 1, "12": 1, "14": 1, "15": 1, "17": 1, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 4, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "VIDEO_DROPPED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_DECODED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_SINK_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_COMPOSITOR_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 32295, "range": [1, 1000], "values": {"0": 466, "1": 1191, "2": 1868, "3": 1407, "4": 961, "5": 641, "6": 700, "7": 490, "8": 329, "9": 152, "10": 79, "11": 68, "12": 56, "14": 46, "16": 22, "18": 11, "20": 15, "23": 11, "26": 8, "29": 5, "33": 2, "37": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8360, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "909": 4, "1243": 1, "1380": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8760, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "1009": 4, "1380": 1, "1532": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 626, "range": [1, 5000], "values": {"0": 0, "1": 2, "2": 40, "3": 26, "4": 6, "5": 5, "6": 2, "7": 1, "8": 2, "10": 1, "11": 1, "15": 2, "17": 2, "29": 1, "31": 1, "38": 1, "47": 1, "142": 1, "152": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 105, "range": [1, 5000], "values": {"0": 38, "1": 16, "2": 4, "3": 2, "6": 1, "9": 3, "11": 1, "12": 1, "19": 1, "20": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5855, "range": [1, 50000], "values": {"4": 0, "5": 1, "13": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "412": 2, "454": 1, "500": 1, "550": 2, "605": 3, "666": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12376, "range": [1, 50000], "values": {"15": 0, "17": 1, "61": 1, "666": 1, "733": 3, "807": 5, "888": 3, "977": 1, "1183": 1, "1302": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 20367, "range": [1, 50000], "values": {"25": 0, "28": 1, "89": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 4, "1433": 1, "1577": 2, "1736": 1, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 21018, "range": [1, 50000], "values": {"45": 0, "50": 1, "144": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 2, "1433": 2, "1577": 2, "1736": 2, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22593, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22613, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22800, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5316, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "412": 2, "454": 2, "500": 1, "550": 1, "605": 3, "666": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 11844, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 1, "1911": 1, "2104": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8476, "range": [1, 50000], "values": {"500": 0, "550": 1, "605": 1, "733": 1, "888": 4, "1183": 1, "1433": 1, "1577": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4624, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 2, "500": 2, "605": 1, "666": 1, "888": 1, "977": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5341, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 2, "977": 1, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3570, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 1, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5986, "range": [1, 50000], "values": {"0": 89, "1": 14, "2": 38, "3": 13, "4": 16, "5": 18, "6": 5, "7": 3, "8": 4, "9": 2, "10": 6, "11": 5, "12": 5, "13": 7, "14": 5, "15": 9, "17": 6, "19": 5, "21": 2, "25": 2, "31": 1, "34": 1, "37": 2, "41": 1, "50": 2, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 18506, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "17": 1, "23": 1, "25": 1, "28": 2, "31": 3, "34": 1, "37": 2, "41": 2, "45": 2, "61": 1, "74": 1, "108": 4, "119": 1, "131": 2, "158": 2, "232": 1, "255": 2, "281": 1, "340": 1, "374": 1, "412": 2, "454": 1, "605": 2, "733": 3, "807": 5, "977": 3, "1075": 1, "1302": 1, "1433": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3257, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1183": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 19, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 400, "range": [1, 50], "values": {"0": 903, "1": 53, "2": 156, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3660234, "range": [1, 2000], "values": {"1": 0, "2": 13, "3": 20, "4": 43, "5": 62, "6": 35, "7": 48, "8": 53, "9": 35, "10": 25, "11": 26, "13": 12, "15": 16, "17": 12, "19": 6, "22": 4, "25": 13, "29": 13, "33": 22, "38": 14, "44": 9, "50": 16, "57": 3, "65": 18, "75": 6, "86": 2, "113": 1, "130": 3, "149": 1, "171": 2, "196": 1, "339": 3, "1011": 255, "1159": 31, "1328": 1, "1745": 325, "2000": 1253}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1102, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "44": 1, "50": 1, "86": 3, "99": 2, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 13003, "range": [1, 2000], "values": {"1": 0, "2": 37, "3": 39, "4": 24, "5": 13, "6": 6, "7": 4, "8": 21, "9": 14, "10": 14, "11": 8, "13": 3, "15": 3, "19": 2, "22": 1, "25": 1, "29": 3, "33": 9, "38": 13, "44": 1, "65": 1, "75": 1, "86": 2, "99": 2, "149": 1, "171": 2, "196": 1, "2000": 2}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 14375, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 6, "5": 7, "6": 8, "7": 20, "8": 12, "9": 9, "10": 11, "11": 9, "13": 5, "15": 4, "17": 4, "19": 7, "22": 11, "25": 13, "29": 12, "33": 42, "38": 17, "44": 6, "50": 5, "57": 3, "65": 8, "75": 1, "86": 6, "99": 20, "113": 21, "130": 7, "149": 1, "196": 8, "770": 1, "882": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 4752, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 7, "4": 1, "5": 1, "6": 2, "7": 2, "8": 1, "11": 1, "22": 1, "29": 1, "50": 1, "86": 1, "113": 1, "2000": 1}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1278, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 3, "15": 3, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2095493, "range": [1, 2000], "values": {"0": 4, "1": 16, "2": 56, "3": 105, "4": 101, "5": 31, "6": 17, "7": 8, "8": 13, "9": 14, "10": 10, "11": 11, "13": 14, "15": 14, "17": 8, "19": 9, "22": 8, "25": 7, "29": 3, "33": 10, "38": 6, "44": 12, "50": 8, "57": 9, "65": 8, "75": 8, "86": 4, "99": 6, "113": 5, "130": 4, "149": 2, "171": 3, "196": 4, "225": 1, "258": 1, "296": 12, "339": 128, "389": 13, "446": 36, "511": 35, "586": 124, "672": 173, "770": 137, "882": 105, "1011": 75, "1159": 360, "1328": 274, "1522": 410, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1740, "range": [1, 2000], "values": {"2": 0, "3": 1, "5": 3, "7": 1, "17": 1, "19": 1, "113": 1, "130": 1, "171": 1, "196": 2, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 9953, "range": [1, 2000], "values": {"0": 5, "1": 180, "2": 66, "3": 8, "4": 10, "5": 3, "6": 6, "7": 2, "8": 5, "9": 2, "10": 5, "11": 5, "13": 3, "15": 4, "17": 2, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 1, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 1, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 16034, "range": [1, 2000], "values": {"0": 1, "1": 16, "2": 65, "3": 93, "4": 13, "5": 12, "6": 6, "7": 7, "8": 3, "9": 2, "10": 1, "11": 6, "13": 4, "15": 3, "17": 7, "19": 7, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "86": 2, "99": 1, "113": 13, "130": 11, "149": 12, "171": 7, "196": 3, "225": 1, "258": 2, "389": 1, "446": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 400, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 8, "4": 2, "5": 3, "6": 1, "7": 4, "8": 2, "9": 1, "10": 1, "11": 3, "15": 2, "19": 1, "22": 1, "33": 1, "38": 1, "86": 1, "99": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2963, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 3, "86": 1, "99": 1, "171": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2096951, "range": [1, 2000], "values": {"0": 67, "1": 47, "2": 45, "3": 76, "4": 56, "5": 71, "6": 9, "7": 10, "8": 5, "9": 7, "10": 10, "11": 9, "13": 5, "15": 7, "17": 4, "19": 5, "22": 7, "25": 3, "29": 3, "33": 10, "38": 5, "44": 11, "50": 10, "57": 5, "65": 5, "75": 4, "86": 5, "99": 6, "113": 7, "130": 4, "149": 3, "171": 3, "196": 1, "225": 3, "258": 33, "296": 37, "339": 80, "389": 13, "446": 8, "511": 42, "586": 137, "672": 217, "770": 99, "882": 68, "1011": 110, "1159": 363, "1328": 274, "1522": 413, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 2221, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 2, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 4, "296": 2, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 9889, "range": [1, 2000], "values": {"0": 21, "1": 187, "2": 46, "3": 9, "4": 6, "5": 5, "6": 6, "7": 2, "8": 5, "9": 2, "10": 4, "11": 5, "13": 2, "15": 4, "17": 2, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 1, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 1, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 21586, "range": [1, 2000], "values": {"0": 6, "1": 11, "2": 59, "3": 108, "4": 10, "5": 6, "6": 5, "7": 1, "8": 3, "10": 2, "11": 4, "13": 4, "15": 5, "17": 4, "19": 5, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 5, "99": 2, "113": 3, "130": 3, "149": 4, "171": 6, "196": 6, "225": 6, "258": 16, "296": 6, "339": 2, "389": 1, "511": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 3642, "range": [1, 2000], "values": {"0": 2, "1": 2, "3": 1, "4": 3, "6": 2, "7": 1, "11": 2, "13": 2, "15": 1, "17": 1, "22": 2, "25": 1, "50": 2, "86": 1, "149": 1, "258": 2, "296": 3, "339": 2, "389": 1, "446": 1, "511": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2934, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 3, "86": 3, "99": 1, "113": 1, "149": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 2455, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "194": 1, "232": 1, "278": 1, "398": 1, "436": 1, "477": 1, "522": 0}}}, "MEDIA_PLAY_TIME_MS": {"AV": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "11771": 0}}}, "AUDIBLE_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 200, "range": [1, 100], "values": {"98": 0, "100": 2}}}, "MUTED_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}}, "VIDEO_VISIBLE_PLAY_TIME_MS": {"All": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "10926": 0}}, "AV,240<h<=480": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "10926": 0}}}, "VIDEO_HIDDEN_PLAY_TIME_PERCENTAGE": {"All": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}, "AV,240<h<=480": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}}, "MEDIA_CODEC_USED": {"video/vp9": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}, "audio/opus": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 195, "range": [1, 50000], "values": {"3": 0, "4": 4, "5": 6, "7": 1, "10": 1, "11": 1, "12": 3, "13": 1, "15": 1, "17": 1, "19": 2, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 34, "range": [1, 50000], "values": {"0": 0, "1": 6, "2": 4, "3": 1, "4": 1, "5": 1, "8": 1, "9": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 4535, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 135, "range": [1, 50000], "values": {"0": 19, "2": 7, "3": 3, "4": 2, "6": 2, "8": 1, "9": 1, "11": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 135, "range": [1, 50000], "values": {"0": 56, "2": 10, "3": 5, "4": 1, "5": 2, "10": 1, "12": 1, "13": 2, "17": 1, "19": 1, "21": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 414, "range": [1, 50000], "values": {"0": 14, "1": 8, "2": 16, "3": 4, "4": 4, "5": 1, "6": 1, "8": 1, "9": 1, "10": 4, "12": 1, "13": 2, "14": 2, "15": 5, "17": 2, "19": 2, "25": 1, "34": 1, "37": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 3478, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "108": 2, "131": 1, "158": 1, "232": 1, "281": 1, "340": 1, "374": 1, "412": 2, "454": 1, "500": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 7294, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "17": 1, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "108": 2, "255": 2, "605": 2, "733": 2, "977": 1, "1075": 1, "1302": 1, "1433": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 7733, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "31": 1, "74": 1, "119": 1, "131": 1, "158": 1, "733": 1, "807": 5, "977": 2, "1075": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 3257, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1183": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 301, "power.total_thread_wakeups": 4097049, "power.total_cpu_time_ms": 1415456}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 372875, "web.foreground": 935259, "prealloc": 102234, "privilegedabout": 5088}, "power.wakeups_per_process_type": {"web.background": 1338546, "web.foreground": 1838657, "prealloc": 892986, "privilegedabout": 26860}, "telemetry.event_counts": {"pictureinpicture#saw_toggle#toggle": 5}, "media.video_hardware_decoding_support": {"video/vp9": true}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3482, "range": [1, 10000], "values": {"0": 0, "1": 218, "2": 1431, "3": 74, "4": 17, "5": 6, "6": 3, "7": 1, "8": 1, "10": 1, "12": 2, "14": 1, "17": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 26125, "range": [1, 100], "values": {"11": 0, "14": 1734, "17": 6, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 11884, "range": [1, 100], "values": {"0": 0, "1": 1008, "2": 6, "3": 3, "14": 721, "17": 2, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 74286, "range": [1, 10000], "values": {"4": 0, "5": 1, "8": 1, "10": 14, "12": 20, "14": 829, "17": 130, "20": 16, "29": 3, "48": 1, "57": 4, "68": 519, "81": 175, "96": 19, "114": 4, "135": 1, "160": 2, "378": 1, "449": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 162191, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 1, "40": 9, "48": 116, "57": 807, "68": 48, "81": 17, "96": 24, "114": 624, "135": 58, "160": 13, "190": 7, "226": 5, "268": 2, "318": 4, "378": 3, "449": 1, "533": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8974, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 2, "range": [1, 2], "values": {"0": 8972, "1": 2, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 50082, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 17, "6": 19, "7": 13, "8": 34, "10": 34, "12": 23, "14": 768, "17": 85, "20": 15, "24": 2, "29": 4, "34": 22, "40": 19, "48": 680, "190": 1, "226": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 266, "range": [1, 1000], "values": {"0": 1697, "1": 39, "2": 1, "3": 2, "216": 1, "243": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 58454, "range": [1, 10000], "values": {"0": 1, "2": 1, "6": 7, "7": 16, "8": 532, "10": 438, "12": 12, "14": 5, "17": 2, "29": 1, "34": 2, "48": 3, "57": 472, "68": 223, "81": 16, "96": 6, "114": 1, "135": 2, "160": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 13709, "range": [1, 10000], "values": {"2": 0, "3": 24, "4": 239, "5": 667, "6": 63, "7": 14, "8": 81, "10": 300, "12": 68, "14": 261, "17": 10, "20": 7, "24": 3, "29": 1, "34": 2, "40": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 82, "range": [1, 10000], "values": {"4": 0, "5": 1, "68": 1, "81": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 73413, "range": [1, 10000], "values": {"0": 5449, "1": 28, "2": 42, "3": 50, "4": 80, "5": 107, "6": 31, "7": 22, "8": 128, "10": 345, "12": 106, "14": 1455, "17": 238, "20": 101, "24": 25, "29": 23, "34": 30, "40": 23, "48": 689, "68": 1, "190": 1, "226": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 68588, "range": [1, 100], "values": {"0": 676, "1": 43, "7": 5, "12": 2, "34": 2, "40": 1, "51": 2, "56": 25, "62": 233, "67": 715, "73": 15, "78": 12, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4370521, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "25": 1, "28": 3, "32": 18, "36": 61, "41": 84, "47": 83, "53": 56, "60": 70, "68": 325, "77": 431, "88": 941, "100": 615, "114": 273, "130": 234, "148": 155, "168": 161, "191": 138, "217": 63, "247": 91, "281": 238, "320": 423, "364": 595, "414": 747, "471": 1264, "536": 1181, "610": 405, "695": 470, "791": 582, "901": 323, "1026": 182, "1168": 96, "1330": 30, "1514": 1, "1724": 4, "1963": 3, "2235": 3, "2545": 1, "2898": 4, "4279": 1, "5548": 1, "6317": 1, "7193": 2, "8190": 1, "9326": 1, "10619": 1, "12092": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 85335, "range": [1, 2000], "values": {"0": 1804, "1": 81, "2": 26, "3": 22, "4": 34, "5": 44, "6": 41, "7": 29, "8": 35, "9": 38, "10": 47, "11": 76, "13": 45, "15": 4763, "17": 50, "19": 36, "22": 8, "25": 13, "29": 8, "33": 2, "38": 8, "50": 13, "57": 1, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 8974, "range": [1, 200], "values": {"2": 0, "3": 60, "4": 637, "5": 231, "6": 638, "7": 154, "8": 8, "9": 4, "10": 3, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 13193436736, "range": [16, 2147483646], "values": {"0": 0, "16": 13685, "23": 283, "28": 15, "34": 984, "41": 3934, "50": 41, "61": 140, "74": 37, "90": 25, "109": 141, "132": 3072, "160": 51, "194": 40, "235": 3953, "284": 2378, "344": 807, "416": 3913, "503": 4056, "609": 993, "737": 3252, "892": 3337, "1080": 905, "1307": 827, "1582": 2120, "1915": 706, "2318": 385, "2805": 900, "3395": 891, "4109": 457, "4973": 1475, "6019": 286, "7284": 331, "8815": 39, "10668": 11, "12911": 134, "15625": 5, "18910": 6, "22886": 2, "27698": 9, "33521": 4, "40569": 10, "49098": 9, "59421": 1, "225968": 13, "273476": 2, "400557": 1, "2231094": 1, "3267857": 3891, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 10813460, "range": [1, 2147483646], "values": {"0": 0, "1": 13999, "2": 2, "3": 5163, "5": 97, "8": 3271, "12": 6668, "19": 13284, "30": 8113, "47": 1746, "73": 650, "113": 792, "176": 874, "274": 1, "662": 5, "1599": 3891, "6002": 1, "22533": 1, "35021": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 23260291, "range": [1, 150000000], "values": {"0": 546, "1": 1386, "2": 750, "3": 421, "4": 1122, "5": 3579, "6": 3414, "7": 2601, "8": 4539, "10": 4449, "12": 2415, "14": 3040, "17": 2743, "20": 6268, "24": 3831, "29": 3282, "35": 2982, "42": 2152, "50": 1378, "60": 713, "72": 331, "87": 159, "105": 131, "126": 108, "151": 168, "182": 728, "219": 560, "263": 539, "316": 202, "380": 43, "457": 5, "549": 9, "660": 33, "793": 18, "953": 3, "1146": 1, "1378": 2, "1657": 4, "1992": 4, "2395": 21, "2879": 68, "3461": 44, "4161": 25, "5002": 3339, "6013": 375, "7228": 16, "8689": 1, "10445": 4, "12556": 3, "15094": 1, "31521": 2, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 272275164, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 13, "251252": 7, "267766": 16, "285365": 12, "304121": 11, "324110": 249, "345412": 274, "368115": 189, "392310": 2, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 412179852, "range": [32768, 16777216], "values": {"474861": 0, "506072": 777, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 193538300, "range": [32768, 16777216], "values": {"132939": 0, "141677": 3, "150989": 4, "160913": 16, "171489": 3, "182760": 11, "194772": 14, "207574": 4, "221217": 181, "235757": 109, "251252": 249, "267766": 181, "285365": 1, "324110": 1, "345412": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 81069056, "range": [1024, 16777216], "values": {"97683": 0, "102590": 777, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 777, "1": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1368, "range": [1, 10000], "values": {"0": 16, "1": 116, "2": 115, "3": 15, "4": 13, "5": 14, "6": 8, "7": 14, "8": 20, "10": 13, "12": 6, "14": 14, "24": 3, "40": 1, "48": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 723, "range": [1, 10000], "values": {"0": 9, "1": 56, "2": 58, "3": 8, "4": 5, "5": 8, "6": 4, "7": 7, "8": 10, "10": 7, "12": 3, "14": 7, "24": 2, "40": 1, "48": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 45, "1": 3, "4": 1, "5": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 164, "range": [1, 100000], "values": {"68": 0, "75": 1, "83": 1, "92": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 282, "range": [1, 100000], "values": {"92": 0, "102": 1, "154": 1, "171": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 9, "range": [1, 5000], "values": {"1": 0, "2": 3, "3": 1, "4": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 2, "8": 1, "9": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 85, "range": [1, 50000], "values": {"1": 0, "2": 1, "10": 1, "25": 1, "45": 1, "50": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 389, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 401, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 1, "74": 1, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 409, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 207, "range": [1, 50000], "values": {"0": 18, "1": 24, "2": 8, "3": 5, "4": 3, "5": 1, "6": 2, "7": 2, "9": 1, "12": 1, "14": 1, "15": 1, "17": 1, "19": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 750735, "range": [1, 50000], "values": {"9": 0, "10": 2, "12": 3, "13": 1, "14": 2, "15": 2, "17": 1, "19": 1, "21": 3, "37": 1, "45": 1, "131": 10, "144": 72, "158": 697, "174": 1021, "192": 1553, "211": 456, "232": 64, "255": 4, "281": 3, "309": 3, "340": 1, "374": 2, "454": 1, "500": 1, "550": 1, "605": 2, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 8, "range": [1, 50], "values": {"0": 24, "2": 4, "3": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 11639, "range": [1, 2000], "values": {"0": 0, "1": 51, "2": 111, "3": 57, "4": 49, "5": 42, "6": 39, "7": 34, "8": 41, "9": 23, "10": 27, "11": 46, "13": 29, "15": 32, "17": 25, "19": 15, "22": 10, "25": 15, "29": 70, "33": 38, "38": 15, "44": 28, "50": 26, "57": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 39, "range": [1, 2000], "values": {"0": 0, "1": 8, "2": 6, "4": 1, "5": 3, "6": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6091, "range": [1, 2000], "values": {"0": 54, "1": 208, "2": 116, "3": 92, "4": 67, "5": 56, "6": 55, "7": 26, "8": 25, "9": 20, "10": 9, "11": 21, "13": 13, "15": 13, "17": 9, "19": 5, "22": 3, "25": 2, "29": 2, "33": 8, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "196": 1, "225": 2, "258": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 48, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 5217, "range": [1, 2000], "values": {"0": 28, "1": 202, "2": 129, "3": 96, "4": 71, "5": 62, "6": 52, "7": 30, "8": 31, "9": 21, "10": 20, "11": 28, "13": 11, "15": 11, "17": 8, "19": 4, "22": 4, "25": 1, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 49, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "15": 1, "17": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 35, "range": [1, 50000], "values": {"14": 0, "15": 1, "19": 1, "21": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 77, "range": [1, 50000], "values": {"0": 1, "1": 4, "2": 1, "6": 1, "12": 1, "14": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 95, "range": [1, 50000], "values": {"0": 17, "1": 20, "2": 7, "3": 5, "4": 3, "5": 1, "6": 1, "7": 2, "9": 1, "10": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 144, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "13": 1, "14": 2, "17": 1, "19": 1, "21": 2, "23": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "37": 1, "45": 1, "50": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1116013, "power.total_cpu_time_ms": 962858}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 962858}, "power.wakeups_per_process_type": {"extension": 1116013}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 168, "power.total_cpu_time_ms": 14}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 14}, "power.wakeups_per_process_type": {"socket": 168}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1648630, "power.total_cpu_time_ms": 92910}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 92910}, "power.wakeups_per_process_type": {"utility": 1648630}}}}, "histograms": {"ADDON_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 33, "histogram_type": 1, "sum": 510, "range": [1, 32], "values": {"14": 0, "15": 34, "16": 0}}, "CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85046497, "range": [1, 100000], "values": {"61": 0, "76": 1, "149": 1, "10589": 1, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 1130594, "range": [1, 66355200], "values": {"42226": 0, "61009": 2, "265859": 1, "554984": 1, "801854": 0}}, "CHECKERBOARD_POTENTIAL_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 2402, "range": [1, 1000000], "values": {"113": 0, "149": 2, "1774": 1, "2336": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5781696, "range": [1, 1073741824], "values": {"1303": 0, "1994": 1, "3052": 1, "39254": 1, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10781, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 6, "31": 86, "34": 77, "38": 38, "42": 63, "46": 12, "68": 1, "75": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 108576, "range": [1, 1000], "values": {"0": 5120, "1": 2097, "2": 4584, "3": 5596, "4": 6371, "5": 3196, "6": 1217, "7": 619, "8": 411, "9": 266, "10": 248, "11": 186, "12": 287, "14": 151, "16": 136, "18": 83, "20": 105, "23": 72, "26": 45, "29": 30, "33": 9, "37": 12, "42": 8, "47": 5, "60": 1, "75": 1, "135": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2181, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 15, "6": 36, "7": 119, "8": 94, "9": 9, "10": 5, "11": 1, "12": 2, "13": 3, "15": 3, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 13557, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 68, "42": 88, "46": 60, "51": 63, "56": 4, "62": 1, "68": 1, "83": 1, "138": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 293, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2829, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 23, "6": 16, "7": 136, "8": 127, "10": 26, "12": 9, "14": 6, "17": 2, "20": 2, "24": 1, "40": 1, "48": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 56, "range": [1, 100], "values": {"1": 0, "2": 28, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 39, "range": [1, 100], "values": {"0": 0, "1": 17, "2": 11, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1337, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 4, "34": 7, "40": 6, "48": 4, "57": 1, "81": 3, "96": 1, "114": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 3272, "range": [1, 10000], "values": {"48": 0, "57": 1, "68": 6, "81": 9, "96": 5, "114": 1, "135": 1, "190": 1, "226": 4, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 181, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 180, "1": 1, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 831, "range": [1, 10000], "values": {"8": 0, "10": 1, "14": 11, "24": 1, "29": 3, "34": 4, "40": 2, "48": 6, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 1000], "values": {"0": 20, "1": 2, "2": 1, "4": 2, "7": 1, "10": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 891, "range": [1, 10000], "values": {"17": 0, "20": 4, "24": 10, "29": 3, "34": 6, "40": 3, "48": 1, "57": 1, "68": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 393, "range": [1, 10000], "values": {"4": 0, "5": 1, "6": 5, "7": 2, "8": 5, "10": 1, "12": 3, "14": 5, "17": 1, "20": 2, "24": 1, "40": 1, "48": 1, "57": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 56, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 1, "34": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1305, "range": [1, 10000], "values": {"0": 104, "1": 8, "2": 1, "3": 3, "4": 3, "6": 3, "7": 4, "8": 7, "10": 5, "12": 3, "14": 20, "17": 1, "24": 2, "29": 4, "34": 4, "40": 2, "48": 7, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 561, "range": [1, 100], "values": {"0": 4, "1": 4, "7": 3, "12": 2, "18": 1, "23": 4, "29": 3, "34": 3, "40": 3, "45": 1, "51": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 1237542, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "9": 1, "10": 1, "11": 1, "13": 2, "15": 1, "17": 4, "19": 6, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 7, "47": 12, "53": 41, "60": 51, "68": 76, "77": 193, "88": 394, "100": 784, "114": 975, "130": 759, "148": 385, "168": 152, "191": 110, "217": 136, "247": 100, "281": 78, "320": 61, "364": 51, "414": 55, "471": 77, "536": 109, "610": 84, "695": 22, "791": 22, "901": 17, "1026": 14, "1168": 4, "1330": 15, "1514": 10, "1724": 8, "1963": 7, "2235": 5, "2545": 10, "2898": 13, "3300": 11, "3758": 4, "4279": 10, "4872": 3, "5548": 5, "6317": 1, "7193": 3, "8190": 2, "9326": 1, "10619": 2, "12092": 1, "13769": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1886, "range": [1, 2000], "values": {"0": 51, "1": 12, "2": 3, "4": 2, "6": 1, "9": 2, "10": 1, "11": 1, "13": 2, "15": 64, "17": 3, "22": 3, "38": 1, "44": 1, "50": 2, "75": 1, "99": 2, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 181, "range": [1, 200], "values": {"2": 0, "3": 1, "4": 9, "5": 4, "6": 7, "7": 3, "8": 1, "12": 1, "16": 1, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 82012664, "range": [16, 2147483646], "values": {"0": 0, "16": 75084, "23": 5829, "34": 75, "41": 31, "50": 25, "61": 349, "74": 257, "90": 109, "109": 108, "132": 57, "160": 9, "194": 323, "235": 4492, "284": 4524, "344": 719, "416": 1387, "503": 60155, "609": 5817, "737": 768, "892": 9847, "1080": 14990, "1307": 405, "1582": 65, "1915": 66, "2318": 114, "2805": 46, "3395": 27, "4109": 31, "4973": 20, "6019": 45, "7284": 124, "8815": 2, "10668": 3, "12911": 8, "15625": 1, "18910": 8, "22886": 36, "27698": 2, "33521": 5, "40569": 22, "59421": 1, "71914": 9, "87033": 13, "105331": 1, "154277": 7, "186713": 3, "225968": 3, "330972": 1, "400557": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 2254151, "range": [1, 2147483646], "values": {"0": 0, "1": 80913, "3": 3873, "5": 939, "8": 64828, "12": 1159, "19": 7633, "30": 25833, "47": 442, "73": 129, "113": 144, "176": 35, "274": 14, "426": 8, "662": 8, "1029": 36, "1599": 1, "2485": 3, "6002": 11, "9328": 5, "14498": 9, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3266960, "range": [1, 150000000], "values": {"0": 1508, "1": 7086, "2": 8388, "3": 10991, "4": 12064, "5": 14216, "6": 10920, "7": 6766, "8": 18031, "10": 13093, "12": 11721, "14": 20251, "17": 15856, "20": 14400, "24": 7458, "29": 3111, "35": 2240, "42": 2088, "50": 1603, "60": 961, "72": 761, "87": 638, "105": 457, "126": 306, "151": 267, "182": 208, "219": 86, "263": 75, "316": 49, "380": 43, "457": 45, "549": 57, "660": 36, "793": 35, "953": 32, "1146": 28, "1378": 24, "1657": 23, "1992": 20, "2395": 14, "2879": 21, "3461": 14, "4161": 12, "5002": 3, "6013": 7, "7228": 9, "10445": 1, "12556": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 507360392, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 15, "418095": 14, "445575": 13, "474861": 2, "506072": 5, "539334": 61, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 635106724, "range": [32768, 16777216], "values": {"741455": 0, "790188": 774, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1833248784, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 4, "1696203": 4, "1807688": 8, "1926500": 15, "2053121": 33, "2188065": 145, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 335364832, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 12, "221217": 9, "235757": 11, "251252": 2, "267766": 5, "304121": 2, "324110": 41, "345412": 108, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 40583168, "range": [1024, 16777216], "values": {"44591": 0, "46831": 13, "49183": 109, "51654": 483, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 774, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 10205, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 234, "33": 40, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 515, "range": [1, 200000], "values": {"6": 0, "8": 3, "10": 5, "13": 1, "17": 7, "22": 1, "28": 3, "36": 1, "46": 1, "58": 1, "74": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 4474, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 10, "22": 12, "28": 7, "36": 6, "46": 3, "58": 4, "74": 8, "119": 1, "151": 3, "310": 2, "394": 1, "501": 1, "637": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 11, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 818, "range": [1, 100000], "values": {"0": 23, "1": 8, "5": 1, "16": 2, "20": 2, "25": 6, "31": 7, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 4382, "range": [1, 2], "values": {"0": 57, "1": 4382, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 18, "range": [1, 2], "values": {"0": 1, "1": 18, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 398, "range": [1, 3], "values": {"0": 414, "2": 199, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 199, "range": [1, 2], "values": {"0": 0, "1": 199, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 2715, "range": [1, 60000], "values": {"0": 196, "874": 3, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2545, "range": [1, 16], "values": {"2": 0, "3": 31, "4": 613, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 1245, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 31, "3": 394, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 625, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 609, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 27, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 598, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 90746, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 12, "39": 11, "41": 15, "43": 24, "45": 14, "47": 23, "49": 23, "51": 13, "53": 21, "55": 23, "58": 33, "61": 16, "64": 14, "67": 21, "70": 11, "73": 15, "76": 16, "80": 14, "84": 16, "88": 19, "92": 13, "96": 8, "100": 10, "105": 14, "110": 3, "115": 8, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 29, "237": 13, "248": 11, "259": 11, "271": 4, "283": 2, "296": 3, "310": 7, "324": 3, "339": 12, "355": 4, "371": 5, "388": 10, "406": 7, "425": 15, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 88822, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 11, "39": 8, "41": 12, "43": 23, "45": 13, "47": 22, "49": 23, "51": 13, "53": 21, "55": 23, "58": 33, "61": 16, "64": 14, "67": 19, "70": 11, "73": 15, "76": 16, "80": 14, "84": 16, "88": 19, "92": 13, "96": 8, "100": 10, "105": 14, "110": 3, "115": 8, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 29, "237": 13, "248": 11, "259": 10, "271": 4, "283": 2, "296": 2, "310": 7, "324": 3, "339": 12, "355": 3, "371": 5, "388": 10, "406": 7, "425": 14, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1672, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 4, "37": 4, "41": 3, "45": 1, "47": 2, "49": 2, "53": 1, "92": 1, "115": 2, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 89074, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 8, "39": 11, "41": 12, "43": 24, "45": 13, "47": 21, "49": 21, "51": 13, "53": 20, "55": 23, "58": 33, "61": 16, "64": 14, "67": 21, "70": 11, "73": 15, "76": 16, "80": 14, "84": 16, "88": 19, "92": 12, "96": 8, "100": 10, "105": 14, "110": 3, "115": 6, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 6, "158": 3, "165": 3, "173": 5, "181": 3, "189": 4, "198": 5, "207": 3, "217": 15, "227": 29, "237": 13, "248": 11, "259": 11, "271": 4, "283": 2, "296": 3, "310": 7, "324": 3, "339": 12, "355": 4, "371": 5, "388": 10, "406": 7, "425": 15, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1630193, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 11, "3314": 102, "3855": 23, "4484": 3, "5216": 178, "6067": 18, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 307, "range": [1, 2], "values": {"0": 337, "1": 307, "2": 0}}, "CERT_VALIDATION_HTTP_REQUEST_RESULT": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1444, "range": [1, 16], "values": {"3": 0, "4": 313, "8": 24, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 2112, "range": [1, 16], "values": {"3": 0, "4": 86, "8": 221, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 11228, "range": [1, 100000], "values": {"0": 209, "1": 157, "2": 35, "3": 11, "4": 4, "5": 4, "6": 2, "8": 22, "13": 2, "16": 1, "20": 1, "25": 7, "31": 3, "39": 1, "49": 1, "61": 1, "76": 1, "95": 1, "119": 1, "149": 2, "233": 1, "365": 1, "457": 1, "1404": 1, "5399": 1, "6758": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 51737, "range": [1, 16], "values": {"0": 3, "1": 1, "2": 57, "8": 202, "9": 904, "10": 4178, "11": 6, "12": 2, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 5562, "range": [1, 2], "values": {"0": 9, "1": 5562, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 6405, "range": [1, 5000], "values": {"0": 498, "1": 9, "2": 7, "3": 7, "5": 2, "7": 5, "8": 1, "10": 2, "11": 3, "12": 3, "13": 3, "14": 3, "15": 10, "16": 5, "17": 5, "18": 2, "19": 6, "20": 2, "21": 4, "23": 5, "25": 4, "27": 2, "29": 5, "31": 1, "35": 4, "38": 3, "41": 2, "47": 3, "50": 2, "54": 3, "58": 1, "66": 1, "71": 1, "87": 2, "93": 1, "115": 1, "123": 1, "132": 5, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 8657, "range": [1, 5000], "values": {"0": 132, "1": 4, "2": 2, "3": 5, "10": 1, "11": 2, "12": 6, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "62": 1, "66": 9, "71": 2, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 2, "142": 1, "163": 3, "188": 2, "202": 2, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 28893, "range": [1, 16], "values": {"0": 0, "1": 13114, "2": 253, "6": 2297, "7": 213, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 13447, "range": [1, 60000], "values": {"0": 4, "1": 29, "2": 24, "3": 9, "4": 6, "5": 2, "6": 2, "7": 2, "9": 9, "11": 11, "14": 53, "17": 48, "21": 72, "26": 61, "32": 39, "40": 26, "50": 17, "62": 7, "77": 10, "95": 6, "118": 4, "146": 9, "181": 1, "278": 1, "345": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 92643, "range": [1, 60000], "values": {"0": 241, "1": 373, "2": 74, "3": 36, "4": 11, "5": 9, "6": 8, "7": 15, "9": 17, "11": 56, "14": 207, "17": 210, "21": 218, "26": 240, "32": 260, "40": 117, "50": 88, "62": 53, "77": 51, "95": 60, "118": 45, "146": 46, "181": 30, "224": 28, "278": 27, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 718, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 441, "range": [1, 60000], "values": {"0": 3095, "1": 97, "2": 42, "3": 15, "4": 8, "6": 1, "7": 1, "11": 2, "14": 1, "17": 3, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5702, "range": [1, 60000], "values": {"11": 0, "14": 1, "17": 1, "21": 1, "26": 1, "40": 1, "77": 3, "146": 2, "181": 5, "531": 2, "1255": 2, "1556": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 753, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 30321, "range": [1, 50], "values": {"0": 64, "3": 13, "4": 523, "6": 25, "8": 3505, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 540, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 31, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 31, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 46, "range": [1, 50], "values": {"0": 7, "1": 6, "2": 20, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 60, "range": [1, 50], "values": {"0": 95, "2": 30, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"0": 0, "1": 6, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 15, "1": 1, "2": 2, "3": 1, "4": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 18, "1": 1, "2": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 166, "range": [1, 1000], "values": {"0": 2, "1": 5, "2": 2, "29": 3, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 11, "5": 1, "12": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 213, "range": [1, 5000], "values": {"0": 9, "55": 3, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 15, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1353, "range": [50, 1000], "values": {"69": 0, "77": 2, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 104, "range": [50, 10000], "values": {"0": 10, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 111, "range": [1, 50], "values": {"36": 0, "37": 3, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 582, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 4, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 372, "range": [1, 1000], "values": {"4": 0, "6": 1, "13": 6, "19": 6, "27": 1, "39": 1, "56": 1, "80": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 439, "range": [1, 1000], "values": {"0": 0, "1": 3, "2": 2, "3": 3, "6": 2, "13": 1, "39": 3, "237": 1, "340": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 880, "range": [1, 1000], "values": {"27": 0, "39": 1, "56": 4, "115": 3, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 187, "range": [1, 1000], "values": {"115": 0, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_TRIGGER": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 22, "range": [1, 100], "values": {"3": 0, "4": 4, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 13, "range": [1, 100], "values": {"0": 0, "1": 9, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 22497, "range": [1, 10000], "values": {"355": 0, "405": 1, "759": 1, "860": 1, "1011": 2, "1466": 2, "1516": 1, "1567": 2, "1668": 1, "1718": 1, "2021": 1, "2324": 1, "2779": 1, "2829": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 26783, "range": [1, 10000], "values": {"0": 859, "1": 10278, "2": 3613, "3": 404, "4": 160, "5": 114, "6": 70, "7": 46, "8": 79, "10": 36, "12": 21, "14": 20, "17": 6, "20": 20, "24": 6, "29": 8, "34": 6, "40": 16, "48": 4, "57": 3, "68": 4, "81": 12, "96": 5, "114": 2, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 13938, "range": [1, 10000], "values": {"0": 602, "1": 4775, "2": 2169, "3": 239, "4": 103, "5": 67, "6": 41, "7": 27, "8": 42, "10": 24, "12": 14, "14": 12, "17": 3, "20": 10, "24": 1, "29": 6, "34": 1, "40": 8, "48": 3, "57": 1, "68": 1, "81": 1, "96": 1, "114": 1, "135": 1, "160": 2, "190": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 137, "range": [1, 2000], "values": {"0": 193, "1": 15, "2": 5, "3": 7, "5": 1, "7": 3, "15": 1, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 3999, "range": [1, 50], "values": {"1": 0, "2": 1998, "3": 1, "4": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1530, "range": [1, 10000], "values": {"0": 1247, "1": 1530, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 4145, "range": [1, 10000], "values": {"0": 658, "1": 155, "2": 85, "3": 43, "4": 29, "5": 44, "6": 13, "7": 17, "8": 30, "10": 23, "12": 20, "14": 18, "17": 2, "34": 2, "40": 2, "57": 6, "68": 4, "81": 11, "96": 3, "114": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 11477, "range": [1, 64], "values": {"13": 0, "14": 31, "18": 604, "19": 9, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 9053, "range": [1, 36], "values": {"22": 0, "23": 4, "29": 309, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 2038, "range": [1, 16], "values": {"3": 0, "4": 107, "7": 230, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 2784, "range": [1, 24], "values": {"11": 0, "12": 224, "16": 6, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 2461, "range": [1, 36], "values": {"22": 0, "23": 107, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 15, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1625, "range": [1, 8], "values": {"0": 0, "1": 307, "2": 15, "4": 322, "5": 0}}, "SSL_OCSP_STAPLING": {"bucket_count": 9, "histogram_type": 1, "sum": 3, "range": [1, 8], "values": {"0": 0, "1": 1, "2": 1, "3": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 365, "range": [1, 24], "values": {"0": 0, "1": 365, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 1053, "range": [1, 10], "values": {"0": 0, "1": 1053, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 1053, "range": [1, 10], "values": {"1": 0, "2": 42, "3": 323, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 1141, "range": [1, 10], "values": {"0": 0, "1": 1031, "5": 22, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 365, "range": [1, 10], "values": {"0": 0, "1": 365, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 14768, "range": [1, 256], "values": {"13": 0, "14": 19, "15": 95, "20": 168, "60": 2, "61": 7, "89": 13, "116": 26, "119": 3, "145": 32, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 28, "range": [1, 2], "values": {"0": 0, "1": 28, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 135, "range": [1, 512], "values": {"2": 0, "3": 6, "13": 9, "14": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 79, "range": [1, 512], "values": {"16": 0, "17": 1, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 365, "range": [1, 4], "values": {"0": 0, "1": 365, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 63, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 63, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 63, "1": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 14, "1": 1, "2": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 11288, "range": [1, 30000], "values": {"12": 0, "19": 112, "29": 151, "45": 3, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 13, "range": [1, 10], "values": {"0": 0, "1": 13, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 13, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 52835, "range": [1, 1000], "values": {"0": 16, "1": 348, "2": 767, "3": 1441, "4": 1180, "5": 930, "6": 734, "7": 611, "8": 648, "9": 509, "10": 360, "11": 240, "12": 276, "14": 146, "16": 72, "18": 59, "20": 58, "23": 35, "26": 25, "29": 20, "33": 13, "37": 8, "42": 6, "47": 1, "53": 1, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 412267055, "range": [1, 5000], "values": {"8": 0, "9": 1, "15": 3, "18": 6, "21": 11, "25": 18, "29": 42, "34": 68, "40": 76, "47": 67, "55": 87, "64": 128, "75": 247, "88": 1032, "103": 4591, "120": 1065, "140": 349, "164": 284, "192": 172, "224": 87, "262": 61, "306": 52, "357": 23, "417": 15, "487": 6, "569": 3, "777": 4, "907": 3, "1059": 3, "1237": 1, "3139": 2, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 420180946, "range": [8, 792], "values": {"8": 0, "16": 2, "32": 3, "40": 7, "48": 11, "56": 7, "64": 4, "72": 5, "80": 2, "88": 3, "96": 59, "104": 1340, "112": 2627, "120": 1315, "128": 794, "136": 350, "144": 162, "152": 72, "160": 52, "168": 34, "176": 16, "184": 11, "192": 7, "200": 15, "208": 65, "216": 116, "224": 108, "232": 88, "240": 72, "248": 53, "256": 36, "264": 26, "272": 16, "280": 13, "288": 14, "296": 13, "304": 14, "312": 7, "320": 17, "328": 18, "336": 21, "344": 13, "352": 11, "360": 16, "368": 9, "376": 5, "384": 3, "392": 5, "400": 3, "408": 6, "416": 8, "424": 10, "432": 15, "440": 6, "448": 3, "456": 5, "464": 7, "472": 3, "480": 2, "488": 5, "496": 1, "504": 3, "512": 1, "528": 3, "536": 6, "544": 2, "552": 3, "560": 8, "568": 4, "576": 2, "584": 2, "592": 1, "600": 3, "608": 2, "616": 1, "624": 2, "632": 2, "640": 2, "648": 1, "656": 2, "672": 1, "680": 2, "704": 2, "728": 3, "752": 1, "768": 2, "776": 1, "784": 1, "792": 33}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 8431404, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 2, "40": 6, "47": 9, "55": 10, "64": 6, "75": 4, "88": 21, "103": 3741, "120": 2272, "140": 371, "164": 72, "192": 200, "224": 343, "262": 88, "306": 88, "357": 52, "417": 57, "487": 32, "569": 21, "665": 10, "777": 7, "907": 4, "1237": 1, "1445": 2, "1688": 2, "2688": 1, "3139": 2, "3666": 1, "5000": 9}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 668, "range": [1, 100000], "values": {"113": 0, "125": 1, "486": 1, "540": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 712, "range": [1, 5000], "values": {"0": 2, "1": 41, "2": 69, "3": 9, "4": 1, "6": 1, "9": 1, "15": 2, "21": 3, "27": 1, "29": 2, "33": 1, "38": 1, "41": 1, "44": 3, "54": 1, "58": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"0": 106, "8": 1, "9": 1, "10": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 26, "range": [1, 50], "values": {"1": 0, "2": 6, "3": 1, "11": 1, "12": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 16684, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 559, "8": 9, "9": 8, "12": 1, "14": 2, "17": 838, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 38, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1728, "range": [1, 50], "values": {"1": 0, "2": 13, "5": 112, "6": 182, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 6130, "range": [1, 50], "values": {"5": 0, "6": 1, "12": 12, "20": 299, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 11, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 1386, "range": [1, 50], "values": {"0": 4, "1": 1386, "2": 0}}, "vscode": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 20, "1": 9, "2": 5, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 159, "range": [1, 2000], "values": {"0": 120, "1": 12, "2": 8, "3": 6, "4": 2, "5": 3, "7": 1, "8": 5, "9": 2, "10": 1, "15": 1, "17": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"0": 4, "2": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 133, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 57, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 6, "3": 2, "4": 1, "5": 2, "6": 2, "10": 1, "11": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 7, "1": 2, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 164, "range": [1, 2000], "values": {"0": 1, "1": 95, "2": 16, "3": 4, "4": 4, "9": 1, "10": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 8, "range": [1, 2000], "values": {"0": 12, "1": 3, "2": 1, "3": 1, "4": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2215, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "37": 2, "41": 1, "45": 1, "47": 2, "53": 1, "92": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 32207, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 18, "45": 6, "47": 11, "49": 10, "51": 5, "53": 10, "55": 13, "58": 16, "61": 7, "64": 8, "67": 6, "70": 4, "73": 6, "76": 4, "80": 4, "84": 5, "88": 5, "92": 1, "96": 4, "100": 1, "105": 2, "110": 1, "115": 2, "120": 5, "126": 1, "132": 4, "138": 1, "151": 4, "158": 2, "165": 1, "173": 3, "181": 3, "189": 3, "198": 1, "217": 2, "227": 3, "248": 4, "259": 3, "271": 2, "310": 5, "324": 2, "339": 7, "355": 1, "371": 2, "388": 2, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "608": 1, "696": 1, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 55305, "range": [1, 60000], "values": {"32": 0, "33": 1, "37": 1, "39": 4, "41": 8, "43": 7, "45": 8, "47": 10, "49": 12, "51": 8, "53": 12, "55": 11, "58": 19, "61": 10, "64": 7, "67": 15, "70": 8, "73": 9, "76": 12, "80": 10, "84": 10, "88": 14, "92": 11, "96": 4, "100": 9, "105": 12, "110": 2, "115": 4, "120": 3, "126": 6, "132": 1, "138": 2, "144": 2, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 3, "217": 13, "227": 26, "237": 13, "248": 7, "259": 8, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 3, "371": 3, "388": 8, "406": 5, "425": 12, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 1362, "range": [1, 60000], "values": {"80": 0, "84": 1, "165": 1, "531": 1, "555": 1, "581": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 3, "range": [1, 32], "values": {"0": 357, "1": 3, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 5281, "range": [1, 50], "values": {"0": 201, "1": 837, "2": 2209, "3": 6, "4": 2, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 115, "range": [1, 50], "values": {"0": 3, "1": 1, "2": 57, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 4005, "range": [1, 50], "values": {"0": 1, "1": 67, "2": 1969, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 252, "range": [1, 100], "values": {"17": 0, "18": 14, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 346, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 588, "range": [1, 100], "values": {"41": 0, "42": 14, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 803, "range": [1, 2], "values": {"0": 0, "1": 803, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 3636, "range": [1, 2], "values": {"0": 9, "1": 3636, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 4284, "range": [1, 50], "values": {"5": 0, "6": 714, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 15313, "range": [1, 50], "values": {"5": 0, "6": 2530, "7": 19, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 3629, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 2, "1531": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 3, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 3, "range": [1, 16], "values": {"0": 0, "1": 3, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 832, "range": [1, 100000], "values": {"30": 0, "45": 1, "229": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 3, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 3, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 904, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 3, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 11, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-other_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 12479, "range": [1, 60000], "values": {"0": 69, "1": 224, "2": 48, "3": 24, "5": 30, "8": 7, "13": 4, "21": 203, "34": 62, "54": 2, "86": 1, "137": 17, "219": 0}}, "subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 387, "range": [1, 60000], "values": {"0": 11, "1": 7, "2": 9, "3": 10, "5": 16, "8": 1, "13": 2, "21": 1, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 12092, "range": [1, 60000], "values": {"0": 58, "1": 217, "2": 39, "3": 14, "5": 14, "8": 6, "13": 2, "21": 202, "34": 62, "54": 2, "86": 1, "137": 16, "219": 0}}, "subresource-image_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}, "subresource_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 11400, "range": [1, 5000], "values": {"0": 272, "1": 54, "2": 12, "3": 3, "4": 5, "6": 4, "9": 2, "18": 19, "26": 232, "37": 13, "53": 2, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11616, "range": [1, 5000], "values": {"0": 297, "1": 68, "2": 22, "3": 8, "4": 8, "6": 4, "9": 3, "18": 19, "26": 232, "37": 13, "53": 2, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 216, "range": [1, 5000], "values": {"0": 25, "1": 14, "2": 10, "3": 5, "4": 3, "9": 1, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_FINISH_SYNTHESIZED_RESPONSE_MS_2": {"subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 175, "range": [1, 5000], "values": {"0": 597, "1": 16, "2": 6, "3": 6, "4": 5, "6": 1, "13": 1, "75": 1, "106": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 312, "range": [1, 5000], "values": {"0": 616, "1": 33, "2": 13, "3": 14, "4": 9, "6": 1, "13": 2, "18": 2, "75": 1, "106": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 137, "range": [1, 5000], "values": {"0": 19, "1": 17, "2": 7, "3": 8, "4": 4, "13": 1, "18": 2, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 1093, "range": [1, 50], "values": {"0": 133, "1": 307, "2": 393, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 7700, "range": [1, 50], "values": {"0": 230, "1": 910, "2": 3395, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 25, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 49, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 36, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 151, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 12, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 39, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2330, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 34, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 39, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4728, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3180, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 149, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 239, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 160, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 17, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 494, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7620, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 30, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 606, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 32, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2088, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 691, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 369, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 46, "1": 0}}, "indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 412, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 96448, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2186, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 25, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 919, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 205, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"4": 0, "5": 12, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1309, "range": [1, 50], "values": {"16": 0, "17": 20, "19": 51, "20": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 254, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 15, "17": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 14, "range": [1, 50], "values": {"13": 0, "14": 1, "15": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 8, "range": [1, 50], "values": {"1": 0, "2": 4, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 20, "range": [1, 50], "values": {"1": 0, "2": 10, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 406, "range": [1, 50], "values": {"1": 0, "2": 203, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 46, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 110, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 6, "19": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 6, "range": [1, 50], "values": {"1": 0, "2": 3, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 307, "range": [1, 50], "values": {"15": 0, "16": 18, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 22, "range": [1, 50], "values": {"1": 0, "2": 11, "3": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 778, "range": [1, 50], "values": {"16": 0, "17": 32, "18": 13, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3900, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 199, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1524, "range": [1, 50], "values": {"3": 0, "4": 3, "16": 9, "19": 72, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"1": 0, "2": 30, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 20, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 1, "19": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1534, "range": [1, 50], "values": {"3": 0, "4": 165, "16": 27, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 87124, "subsessionLength": 46624, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}