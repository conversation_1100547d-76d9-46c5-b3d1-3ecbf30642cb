{"type": "main", "id": "944affc3-2ad5-48c1-b848-732d8f9fea18", "creationDate": "2025-05-25T11:07:05.031Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 87724, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 131}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 52, "browser.engagement.tab_open_event_count": 7, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 3, "dom.contentprocess.os_priority_raised": 326, "browser.engagement.unique_domains_count": 4, "dom.contentprocess.os_priority_lowered": 58, "blocklist.mlbf_source": "dump_match", "cookie.banners.service_detect_only": false, "urlbar.zeroprefix.abandonment": 2, "browser.engagement.window_open_event_count": 1, "dom.contentprocess.os_priority_change_considered": 147, "browser.engagement.active_ticks": 131, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 52, "power.total_thread_wakeups": 3085326, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 87724353, "browser.engagement.total_uri_count": 52, "browser.engagement.max_concurrent_window_count": 2, "browser.engagement.session_time_excluding_suspend": 87724353, "power.total_cpu_time_ms": 1544867, "blocklist.mlbf_softblocks_source": "dump_match"}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 5}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1177491, "parent.active.playing-audio": 1127, "parent.active.playing-video": 76783, "parent.inactive.playing-video": 197341, "parent.active": 92123, "parent.inactive.playing-audio": 2}, "browser.ui.interaction.pageaction_urlbar": {"addon0": 1}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 3}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "cookie.banners.private_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "power.wakeups_per_process_type": {"parent.inactive": 2002478, "parent.active.playing-audio": 3210, "parent.active.playing-video": 200844, "parent.inactive.playing-video": 608925, "parent.active": 269858, "parent.inactive.playing-audio": 11}, "cookie.banners.normal_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "networking.data_transferred_v3_kb": {"Y1_N1": 7980, "Y0_N1Sys": 638, "Y2_N3Oth": 121667}, "telemetry.event_counts": {"pwmgr#saved_login_used#form_login": 1}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 5185, "successful": 484}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 19832, "range": [1, 10000], "values": {"0": 2, "1": 27, "2": 358, "3": 282, "4": 42, "5": 25, "6": 10, "7": 19, "8": 380, "10": 18, "12": 9, "14": 34, "17": 181, "20": 74, "24": 306, "29": 12, "34": 4, "40": 2, "48": 7, "57": 3, "68": 1, "81": 1, "96": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 1052, "range": [1, 100], "values": {"2": 0, "3": 262, "4": 33, "5": 12, "6": 1, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 764, "range": [1, 100], "values": {"0": 4, "1": 93, "2": 43, "3": 152, "4": 12, "5": 7, "6": 1, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 56713, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 2, "5": 1, "7": 1, "8": 2, "10": 1, "12": 5, "14": 4, "17": 1, "20": 2, "24": 11, "29": 12, "34": 6, "40": 13, "48": 8, "57": 11, "68": 11, "81": 1, "96": 2, "114": 6, "135": 2, "160": 28, "190": 136, "226": 6, "268": 9, "318": 13, "378": 9, "449": 5, "533": 2, "633": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 91728, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 3, "40": 8, "48": 5, "57": 9, "68": 19, "81": 19, "96": 14, "114": 10, "135": 6, "160": 3, "190": 1, "226": 71, "268": 91, "318": 11, "378": 9, "449": 4, "533": 6, "633": 4, "752": 8, "894": 7, "1062": 2, "1262": 2, "1500": 3, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 15, "range": [1, 2], "values": {"0": 3897, "1": 15, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 64, "range": [1, 2], "values": {"0": 3848, "1": 64, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 13458, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 3, "5": 1, "8": 2, "10": 2, "12": 9, "14": 8, "17": 9, "20": 7, "24": 31, "29": 13, "34": 8, "40": 17, "48": 194, "57": 3, "68": 3, "135": 1, "226": 2, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 307, "range": [1, 1000], "values": {"0": 136, "1": 132, "2": 25, "3": 8, "4": 8, "5": 2, "6": 2, "7": 1, "18": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 45174, "range": [1, 10000], "values": {"0": 6, "1": 2, "2": 1, "4": 3, "5": 4, "7": 1, "8": 7, "14": 1, "17": 3, "20": 17, "24": 17, "29": 5, "34": 15, "40": 9, "48": 5, "57": 4, "68": 2, "81": 1, "96": 2, "135": 19, "160": 146, "190": 6, "226": 11, "268": 13, "318": 8, "378": 5, "533": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 22322, "range": [1, 10000], "values": {"0": 0, "1": 3, "2": 9, "3": 3, "4": 3, "5": 6, "6": 8, "7": 10, "8": 13, "10": 6, "12": 11, "14": 5, "17": 6, "20": 7, "24": 3, "29": 5, "40": 2, "68": 75, "81": 87, "96": 9, "114": 20, "135": 12, "160": 8, "190": 3, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 728, "range": [1, 10000], "values": {"1": 0, "2": 1, "5": 2, "17": 2, "24": 2, "48": 1, "68": 1, "226": 2, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 56066, "range": [1, 10000], "values": {"0": 1330, "1": 69, "2": 88, "3": 62, "4": 67, "5": 204, "6": 120, "7": 112, "8": 212, "10": 143, "12": 177, "14": 193, "17": 95, "20": 148, "24": 127, "29": 42, "34": 65, "40": 56, "48": 591, "57": 5, "68": 3, "135": 1, "226": 2, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 3393, "range": [1, 100], "values": {"0": 205, "1": 24, "7": 11, "12": 13, "18": 10, "23": 4, "29": 5, "34": 9, "40": 8, "45": 5, "51": 2, "56": 1, "62": 1, "67": 5, "73": 3, "78": 3, "84": 2, "89": 3, "95": 2, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 19682371, "range": [1, 1000000], "values": {"2": 0, "3": 3, "4": 3, "5": 6, "6": 1, "9": 1, "10": 10, "11": 35, "13": 101, "15": 229, "17": 205, "19": 345, "22": 127, "25": 16, "28": 6, "32": 2, "36": 4, "41": 11, "47": 6, "53": 20, "60": 29, "68": 17, "77": 58, "88": 26, "100": 34, "114": 66, "130": 116, "148": 222, "168": 343, "191": 838, "217": 686, "247": 527, "281": 388, "320": 345, "364": 456, "414": 757, "471": 859, "536": 1087, "610": 1202, "695": 1185, "791": 2008, "901": 2629, "1026": 3753, "1168": 2619, "1330": 397, "1514": 128, "1724": 99, "1963": 59, "2235": 42, "2545": 36, "2898": 36, "3300": 24, "3758": 33, "4279": 24, "4872": 26, "5548": 19, "6317": 11, "7193": 79, "8190": 64, "9326": 15, "10619": 5, "12092": 6, "13769": 4, "15678": 4, "17852": 5, "20328": 3, "23147": 1, "26357": 1, "34174": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 33860, "range": [1, 2000], "values": {"0": 1355, "1": 177, "2": 114, "3": 151, "4": 90, "5": 71, "6": 60, "7": 38, "8": 30, "9": 23, "10": 29, "11": 49, "13": 50, "15": 1024, "17": 82, "19": 67, "22": 30, "25": 26, "29": 14, "33": 15, "38": 23, "44": 18, "50": 9, "57": 6, "65": 7, "75": 5, "86": 4, "99": 12, "113": 6, "130": 2, "149": 1, "196": 2, "225": 5, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 3911, "range": [1, 200], "values": {"2": 0, "3": 14, "4": 45, "5": 14, "6": 12, "7": 5, "8": 11, "9": 94, "10": 21, "11": 7, "12": 26, "13": 13, "14": 10, "15": 4, "16": 1, "17": 1, "18": 1, "19": 1, "21": 1, "25": 2, "29": 2, "31": 6, "34": 3, "37": 1, "40": 2, "43": 3, "50": 6, "54": 2, "58": 3, "63": 3, "68": 1, "73": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 115920648, "range": [16, 2147483646], "values": {"0": 0, "16": 62799, "23": 5715, "28": 166, "34": 59, "41": 208, "50": 63, "61": 275, "74": 85, "90": 117, "109": 238, "132": 73, "160": 513, "194": 219, "235": 392, "284": 59865, "344": 47759, "416": 58, "503": 415, "609": 60145, "737": 744, "892": 1494, "1080": 312, "1307": 348, "1582": 588, "1915": 632, "2318": 311, "2805": 402, "3395": 20, "4109": 316, "4973": 617, "6019": 13, "7284": 114, "8815": 420, "10668": 43, "12911": 15, "15625": 2, "18910": 19, "22886": 1, "27698": 42, "33521": 9, "40569": 12, "49098": 301, "71914": 9, "87033": 2, "105331": 3, "127476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 4365865, "range": [1, 2147483646], "values": {"0": 0, "1": 68825, "2": 21, "3": 1004, "5": 405, "8": 947, "12": 120213, "19": 47927, "30": 2479, "47": 704, "73": 1207, "113": 318, "176": 1338, "274": 94, "426": 121, "662": 19, "1029": 23, "1599": 7, "2485": 297, "3862": 3, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3374286, "range": [1, 150000000], "values": {"0": 3793, "1": 14899, "2": 20059, "3": 6396, "4": 3922, "5": 3098, "6": 6047, "7": 11459, "8": 38038, "10": 37143, "12": 34132, "14": 26601, "17": 8391, "20": 6053, "24": 11678, "29": 6298, "35": 3773, "42": 1150, "50": 716, "60": 465, "72": 263, "87": 227, "105": 269, "126": 231, "151": 155, "182": 301, "219": 75, "263": 89, "316": 79, "380": 11, "457": 15, "549": 16, "660": 15, "793": 11, "953": 9, "1146": 11, "1378": 9, "1657": 9, "1992": 4, "2395": 5, "2879": 6, "3461": 8, "4161": 6, "5002": 4, "6013": 4, "7228": 10, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1453020924, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 8, "103055": 25, "109828": 24, "117047": 2, "124740": 10, "132939": 10, "141677": 50, "150989": 11, "160913": 15, "171489": 13, "182760": 14, "194772": 245, "207574": 479, "221217": 115, "235757": 464, "251252": 331, "267766": 3, "285365": 2, "368115": 1, "392310": 11, "418095": 8, "474861": 22, "506072": 92, "539334": 96, "574782": 105, "612560": 140, "652821": 147, "695728": 401, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1823436560, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 58, "182760": 5, "267766": 825, "285365": 737, "304121": 8, "324110": 1, "345412": 41, "368115": 61, "418095": 1, "445575": 1, "506072": 113, "539334": 120, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 117, "1019325": 783, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1101560072, "range": [32768, 16777216], "values": {"0": 59, "32768": 3, "34922": 2, "37217": 5, "39663": 5, "42270": 9, "45048": 2, "48009": 1, "51164": 5, "54527": 5, "58111": 41, "61930": 7, "66000": 1, "70338": 16, "74961": 1, "79888": 6, "85139": 13, "90735": 4, "96699": 241, "103055": 242, "109828": 238, "117047": 30, "124740": 48, "132939": 427, "141677": 365, "150989": 25, "160913": 18, "171489": 2, "194772": 1, "207574": 1, "285365": 1, "304121": 17, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 110, "539334": 121, "574782": 312, "612560": 392, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 659970048, "range": [1024, 16777216], "values": {"0": 0, "1024": 10, "8022": 1, "8848": 59, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 117, "33230": 216, "34899": 101, "36652": 206, "38493": 202, "40427": 203, "42458": 204, "44591": 181, "46831": 156, "49183": 51, "51654": 14, "54249": 20, "56974": 1, "59836": 3, "62842": 1, "65999": 1, "76453": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 758, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 97, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 3336, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 7, "range": [1, 2], "values": {"0": 0, "1": 7, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 26223, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "767": 1, "862": 2, "969": 1, "1089": 1, "1223": 1, "1374": 1, "1456": 3, "1635": 1, "1733": 2, "1947": 1, "2064": 1, "2761": 1, "2926": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 30000], "values": {"0": 2, "1": 1, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 63, "range": [1, 30000], "values": {"0": 2, "1": 2, "2": 1, "13": 1, "16": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 250, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "24": 1, "43": 1, "140": 1, "171": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 565, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 2, "63": 1, "140": 1, "209": 1, "255": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 354, "range": [1, 30000], "values": {"0": 7, "1": 7, "3": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 2, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 486, "range": [1, 50], "values": {"0": 2850, "2": 243, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 79364, "range": [1, 10000], "values": {"0": 1369, "1": 1434, "2": 2623, "3": 744, "4": 558, "5": 469, "6": 413, "7": 413, "8": 778, "10": 666, "12": 541, "14": 738, "17": 435, "20": 101, "24": 49, "29": 24, "34": 17, "40": 19, "48": 16, "57": 9, "68": 12, "81": 6, "96": 6, "114": 5, "135": 9, "190": 6, "226": 4, "268": 7, "318": 1, "533": 5, "633": 2, "752": 2, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 42688, "range": [1, 10000], "values": {"0": 1201, "1": 673, "2": 1302, "3": 332, "4": 306, "5": 247, "6": 218, "7": 212, "8": 316, "10": 312, "12": 277, "14": 305, "17": 97, "20": 60, "24": 89, "29": 45, "34": 26, "40": 40, "48": 12, "57": 11, "68": 10, "81": 5, "96": 2, "114": 2, "135": 3, "160": 1, "190": 2, "226": 2, "268": 3, "318": 1, "533": 2, "633": 2, "752": 2, "894": 0}}, "VIDEO_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "11771": 0}}, "VIDEO_HIDDEN_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 7200000], "values": {"0": 2, "1": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 108, "range": [1, 50], "values": {"0": 0, "1": 56, "2": 8, "3": 8, "4": 3, "5": 0}}, "PWMGR_LOGIN_PAGE_SAFETY": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 4, "1": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 29, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4179, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "135": 1, "221": 1, "244": 1, "297": 1, "328": 2, "362": 2, "399": 1, "485": 2, "535": 0}}, "JS_PAGELOAD_DELAZIFICATION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 7, "1": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 139, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 3, "6": 1, "7": 1, "8": 2, "11": 1, "12": 1, "14": 1, "15": 1, "17": 1, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 4, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "VIDEO_DROPPED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_DECODED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_SINK_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "VIDEO_DROPPED_COMPOSITOR_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 2, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 43634, "range": [1, 1000], "values": {"0": 468, "1": 1194, "2": 1946, "3": 1852, "4": 1606, "5": 1048, "6": 1071, "7": 652, "8": 385, "9": 170, "10": 88, "11": 75, "12": 74, "14": 54, "16": 33, "18": 17, "20": 23, "23": 17, "26": 10, "29": 6, "33": 2, "37": 2, "42": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8360, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "909": 4, "1243": 1, "1380": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8760, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "1009": 4, "1380": 1, "1532": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 640, "range": [1, 5000], "values": {"0": 0, "1": 2, "2": 40, "3": 26, "4": 6, "5": 5, "6": 3, "7": 1, "8": 3, "10": 1, "11": 1, "15": 2, "17": 2, "29": 1, "31": 1, "38": 1, "47": 1, "142": 1, "152": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 124, "range": [1, 5000], "values": {"0": 40, "1": 18, "2": 4, "3": 2, "6": 1, "7": 1, "9": 3, "10": 1, "11": 1, "12": 1, "19": 1, "20": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5855, "range": [1, 50000], "values": {"4": 0, "5": 1, "13": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "412": 2, "454": 1, "500": 1, "550": 2, "605": 3, "666": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12376, "range": [1, 50000], "values": {"15": 0, "17": 1, "61": 1, "666": 1, "733": 3, "807": 5, "888": 3, "977": 1, "1183": 1, "1302": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 20367, "range": [1, 50000], "values": {"25": 0, "28": 1, "89": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 4, "1433": 1, "1577": 2, "1736": 1, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 21018, "range": [1, 50000], "values": {"45": 0, "50": 1, "144": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 2, "1433": 2, "1577": 2, "1736": 2, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22593, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22613, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22800, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 1, "2806": 1, "3089": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5316, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "412": 2, "454": 2, "500": 1, "550": 1, "605": 3, "666": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 11844, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 1, "1911": 1, "2104": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8476, "range": [1, 50000], "values": {"500": 0, "550": 1, "605": 1, "733": 1, "888": 4, "1183": 1, "1433": 1, "1577": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4624, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 2, "500": 2, "605": 1, "666": 1, "888": 1, "977": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5341, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 2, "977": 1, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3570, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 1, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5986, "range": [1, 50000], "values": {"0": 91, "1": 14, "2": 38, "3": 13, "4": 16, "5": 18, "6": 5, "7": 3, "8": 4, "9": 2, "10": 6, "11": 5, "12": 5, "13": 7, "14": 5, "15": 9, "17": 6, "19": 5, "21": 2, "25": 2, "31": 1, "34": 1, "37": 2, "41": 1, "50": 2, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 18506, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "17": 1, "23": 1, "25": 1, "28": 2, "31": 3, "34": 1, "37": 2, "41": 2, "45": 2, "61": 1, "74": 1, "108": 4, "119": 1, "131": 2, "158": 2, "232": 1, "255": 2, "281": 1, "340": 1, "374": 1, "412": 2, "454": 1, "605": 2, "733": 3, "807": 5, "977": 3, "1075": 1, "1302": 1, "1433": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3257, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1183": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 19, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 402, "range": [1, 50], "values": {"0": 903, "1": 53, "2": 157, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3662858, "range": [1, 2000], "values": {"1": 0, "2": 13, "3": 20, "4": 49, "5": 95, "6": 85, "7": 76, "8": 95, "9": 75, "10": 42, "11": 47, "13": 21, "15": 21, "17": 15, "19": 8, "22": 5, "25": 15, "29": 19, "33": 22, "38": 19, "44": 11, "50": 16, "57": 3, "65": 18, "75": 6, "86": 2, "113": 1, "130": 3, "149": 1, "171": 2, "196": 1, "339": 3, "1011": 255, "1159": 31, "1328": 1, "1745": 325, "2000": 1253}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1102, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "44": 1, "50": 1, "86": 3, "99": 2, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 13009, "range": [1, 2000], "values": {"1": 0, "2": 37, "3": 39, "4": 24, "5": 13, "6": 7, "7": 4, "8": 21, "9": 14, "10": 14, "11": 8, "13": 3, "15": 3, "19": 2, "22": 1, "25": 1, "29": 3, "33": 9, "38": 13, "44": 1, "65": 1, "75": 1, "86": 2, "99": 2, "149": 1, "171": 2, "196": 1, "2000": 2}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 14503, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 7, "5": 10, "6": 10, "7": 24, "8": 12, "9": 14, "10": 11, "11": 11, "13": 5, "15": 4, "17": 4, "19": 7, "22": 11, "25": 13, "29": 12, "33": 42, "38": 17, "44": 6, "50": 5, "57": 3, "65": 8, "75": 1, "86": 6, "99": 20, "113": 21, "130": 7, "149": 1, "196": 8, "770": 1, "882": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 4752, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 7, "4": 1, "5": 1, "6": 2, "7": 2, "8": 1, "11": 1, "22": 1, "29": 1, "50": 1, "86": 1, "113": 1, "2000": 1}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1278, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 3, "15": 3, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2097715, "range": [1, 2000], "values": {"0": 4, "1": 26, "2": 123, "3": 168, "4": 132, "5": 50, "6": 24, "7": 13, "8": 18, "9": 18, "10": 14, "11": 21, "13": 21, "15": 19, "17": 14, "19": 11, "22": 14, "25": 11, "29": 9, "33": 13, "38": 7, "44": 13, "50": 8, "57": 11, "65": 11, "75": 8, "86": 4, "99": 7, "113": 5, "130": 4, "149": 2, "171": 3, "196": 4, "225": 1, "258": 1, "296": 12, "339": 128, "389": 13, "446": 36, "511": 35, "586": 124, "672": 173, "770": 137, "882": 105, "1011": 75, "1159": 360, "1328": 274, "1522": 410, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1740, "range": [1, 2000], "values": {"2": 0, "3": 1, "5": 3, "7": 1, "17": 1, "19": 1, "113": 1, "130": 1, "171": 1, "196": 2, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 9955, "range": [1, 2000], "values": {"0": 5, "1": 182, "2": 66, "3": 8, "4": 10, "5": 3, "6": 6, "7": 2, "8": 5, "9": 2, "10": 5, "11": 5, "13": 3, "15": 4, "17": 2, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 1, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 1, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 16088, "range": [1, 2000], "values": {"0": 1, "1": 18, "2": 75, "3": 94, "4": 14, "5": 12, "6": 6, "7": 8, "8": 3, "9": 4, "10": 1, "11": 6, "13": 4, "15": 3, "17": 7, "19": 7, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "86": 2, "99": 1, "113": 13, "130": 11, "149": 12, "171": 7, "196": 3, "225": 1, "258": 2, "389": 1, "446": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 400, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 8, "4": 2, "5": 3, "6": 1, "7": 4, "8": 2, "9": 1, "10": 1, "11": 3, "15": 2, "19": 1, "22": 1, "33": 1, "38": 1, "86": 1, "99": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2963, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 3, "86": 1, "99": 1, "171": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2097793, "range": [1, 2000], "values": {"0": 141, "1": 86, "2": 96, "3": 110, "4": 81, "5": 88, "6": 15, "7": 12, "8": 9, "9": 9, "10": 12, "11": 14, "13": 7, "15": 11, "17": 4, "19": 6, "22": 8, "25": 3, "29": 3, "33": 13, "38": 5, "44": 11, "50": 10, "57": 5, "65": 5, "75": 4, "86": 5, "99": 6, "113": 7, "130": 4, "149": 3, "171": 3, "196": 1, "225": 3, "258": 33, "296": 37, "339": 80, "389": 13, "446": 8, "511": 42, "586": 137, "672": 217, "770": 99, "882": 68, "1011": 110, "1159": 363, "1328": 274, "1522": 413, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 2221, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 2, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 4, "296": 2, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 9891, "range": [1, 2000], "values": {"0": 21, "1": 189, "2": 46, "3": 9, "4": 6, "5": 5, "6": 6, "7": 2, "8": 5, "9": 2, "10": 4, "11": 5, "13": 2, "15": 4, "17": 2, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 1, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 1, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 21642, "range": [1, 2000], "values": {"0": 6, "1": 12, "2": 69, "3": 110, "4": 11, "5": 6, "6": 5, "7": 2, "8": 3, "9": 2, "10": 2, "11": 4, "13": 4, "15": 5, "17": 4, "19": 5, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 5, "99": 2, "113": 3, "130": 3, "149": 4, "171": 6, "196": 6, "225": 6, "258": 16, "296": 6, "339": 2, "389": 1, "511": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 3642, "range": [1, 2000], "values": {"0": 2, "1": 2, "3": 1, "4": 3, "6": 2, "7": 1, "11": 2, "13": 2, "15": 1, "17": 1, "22": 2, "25": 1, "50": 2, "86": 1, "149": 1, "258": 2, "296": 3, "339": 2, "389": 1, "446": 1, "511": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2934, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 3, "86": 3, "99": 1, "113": 1, "149": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 2455, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "194": 1, "232": 1, "278": 1, "398": 1, "436": 1, "477": 1, "522": 0}}}, "MEDIA_PLAY_TIME_MS": {"AV": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "11771": 0}}}, "AUDIBLE_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 200, "range": [1, 100], "values": {"98": 0, "100": 2}}}, "MUTED_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}}, "VIDEO_VISIBLE_PLAY_TIME_MS": {"All": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "10926": 0}}, "AV,240<h<=480": {"bucket_count": 100, "histogram_type": 0, "sum": 15894, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "10926": 0}}}, "VIDEO_HIDDEN_PLAY_TIME_PERCENTAGE": {"All": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}, "AV,240<h<=480": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}}, "MEDIA_CODEC_USED": {"video/vp9": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}, "audio/opus": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 195, "range": [1, 50000], "values": {"3": 0, "4": 4, "5": 6, "7": 1, "10": 1, "11": 1, "12": 3, "13": 1, "15": 1, "17": 1, "19": 2, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 34, "range": [1, 50000], "values": {"0": 0, "1": 6, "2": 4, "3": 1, "4": 1, "5": 1, "8": 1, "9": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 4535, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 135, "range": [1, 50000], "values": {"0": 19, "2": 7, "3": 3, "4": 2, "6": 2, "8": 1, "9": 1, "11": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 135, "range": [1, 50000], "values": {"0": 58, "2": 10, "3": 5, "4": 1, "5": 2, "10": 1, "12": 1, "13": 2, "17": 1, "19": 1, "21": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 414, "range": [1, 50000], "values": {"0": 14, "1": 8, "2": 16, "3": 4, "4": 4, "5": 1, "6": 1, "8": 1, "9": 1, "10": 4, "12": 1, "13": 2, "14": 2, "15": 5, "17": 2, "19": 2, "25": 1, "34": 1, "37": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 3478, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "108": 2, "131": 1, "158": 1, "232": 1, "281": 1, "340": 1, "374": 1, "412": 2, "454": 1, "500": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 7294, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "17": 1, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "108": 2, "255": 2, "605": 2, "733": 2, "977": 1, "1075": 1, "1302": 1, "1433": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 7733, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "31": 1, "74": 1, "119": 1, "131": 1, "158": 1, "733": 1, "807": 5, "977": 2, "1075": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 3257, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 1, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1183": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 301, "power.total_thread_wakeups": 5023454, "power.total_cpu_time_ms": 1540095}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 376175, "web.foreground": 1055529, "prealloc": 102871, "privilegedabout": 5520}, "power.wakeups_per_process_type": {"web.background": 1354766, "web.foreground": 2732135, "prealloc": 906404, "privilegedabout": 30149}, "telemetry.event_counts": {"pictureinpicture#saw_toggle#toggle": 8}, "media.video_hardware_decoding_support": {"video/vp9": true}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3554, "range": [1, 10000], "values": {"0": 0, "1": 218, "2": 1436, "3": 81, "4": 22, "5": 9, "6": 4, "7": 1, "8": 1, "10": 1, "12": 2, "14": 1, "17": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 26440, "range": [1, 100], "values": {"11": 0, "14": 1755, "17": 6, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 12045, "range": [1, 100], "values": {"0": 0, "1": 1019, "2": 6, "3": 3, "14": 731, "17": 2, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 75583, "range": [1, 10000], "values": {"4": 0, "5": 1, "8": 1, "10": 15, "12": 22, "14": 833, "17": 133, "20": 17, "29": 3, "48": 1, "57": 4, "68": 520, "81": 178, "96": 22, "114": 5, "135": 2, "160": 3, "378": 1, "449": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 164491, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 1, "40": 10, "48": 117, "57": 814, "68": 49, "81": 18, "96": 24, "114": 626, "135": 63, "160": 14, "190": 8, "226": 6, "268": 2, "318": 4, "378": 3, "449": 1, "533": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 9084, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 2, "range": [1, 2], "values": {"0": 9082, "1": 2, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 50732, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 17, "6": 19, "7": 13, "8": 34, "10": 35, "12": 25, "14": 772, "17": 88, "20": 16, "24": 2, "29": 4, "34": 23, "40": 19, "48": 689, "190": 1, "226": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 270, "range": [1, 1000], "values": {"0": 1716, "1": 40, "2": 1, "3": 3, "216": 1, "243": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 59484, "range": [1, 10000], "values": {"0": 1, "2": 1, "6": 7, "7": 20, "8": 533, "10": 443, "12": 12, "14": 6, "17": 2, "29": 1, "34": 2, "48": 3, "57": 473, "68": 226, "81": 19, "96": 7, "114": 2, "135": 3, "160": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 13977, "range": [1, 10000], "values": {"2": 0, "3": 24, "4": 243, "5": 669, "6": 67, "7": 15, "8": 81, "10": 300, "12": 69, "14": 263, "17": 13, "20": 8, "24": 4, "29": 2, "34": 3, "40": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 82, "range": [1, 10000], "values": {"4": 0, "5": 1, "68": 1, "81": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 74698, "range": [1, 10000], "values": {"0": 5509, "1": 28, "2": 43, "3": 53, "4": 81, "5": 107, "6": 32, "7": 22, "8": 128, "10": 348, "12": 109, "14": 1463, "17": 244, "20": 104, "24": 27, "29": 25, "34": 33, "40": 24, "48": 702, "68": 1, "190": 1, "226": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 69330, "range": [1, 100], "values": {"0": 686, "1": 43, "7": 5, "12": 2, "34": 2, "40": 1, "51": 2, "56": 27, "62": 236, "67": 718, "73": 18, "78": 12, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4422660, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "25": 1, "28": 3, "32": 18, "36": 61, "41": 85, "47": 84, "53": 57, "60": 71, "68": 328, "77": 434, "88": 950, "100": 620, "114": 281, "130": 243, "148": 156, "168": 164, "191": 141, "217": 65, "247": 97, "281": 245, "320": 435, "364": 607, "414": 756, "471": 1274, "536": 1186, "610": 415, "695": 475, "791": 587, "901": 324, "1026": 183, "1168": 97, "1330": 31, "1514": 1, "1724": 4, "1963": 3, "2235": 3, "2545": 2, "2898": 4, "4279": 1, "5548": 1, "6317": 1, "7193": 2, "8190": 1, "9326": 1, "10619": 1, "12092": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 86321, "range": [1, 2000], "values": {"0": 1831, "1": 82, "2": 26, "3": 22, "4": 34, "5": 44, "6": 41, "7": 29, "8": 35, "9": 38, "10": 48, "11": 76, "13": 45, "15": 4822, "17": 50, "19": 36, "22": 8, "25": 13, "29": 9, "33": 2, "38": 8, "50": 13, "57": 1, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 9084, "range": [1, 200], "values": {"2": 0, "3": 62, "4": 645, "5": 232, "6": 643, "7": 157, "8": 10, "9": 4, "10": 3, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 13369326296, "range": [16, 2147483646], "values": {"0": 0, "16": 14672, "23": 283, "28": 15, "34": 991, "41": 3990, "50": 41, "61": 142, "74": 37, "90": 25, "109": 141, "132": 3112, "160": 51, "194": 40, "235": 4003, "284": 2408, "344": 817, "416": 3963, "503": 4108, "609": 1004, "737": 3292, "892": 3405, "1080": 908, "1307": 837, "1582": 2191, "1915": 712, "2318": 447, "2805": 1440, "3395": 1384, "4109": 492, "4973": 1945, "6019": 286, "7284": 335, "8815": 39, "10668": 11, "12911": 136, "15625": 5, "18910": 6, "22886": 2, "27698": 9, "33521": 4, "40569": 12, "49098": 9, "59421": 3, "225968": 13, "273476": 2, "400557": 1, "2231094": 1, "3267857": 3941, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 11034317, "range": [1, 2147483646], "values": {"0": 0, "1": 14986, "2": 2, "3": 5228, "5": 99, "8": 3311, "12": 6740, "19": 14279, "30": 8613, "47": 1881, "73": 658, "113": 1080, "176": 885, "274": 1, "662": 5, "1599": 3941, "6002": 1, "22533": 1, "35021": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 23582670, "range": [1, 150000000], "values": {"0": 550, "1": 1386, "2": 760, "3": 424, "4": 1137, "5": 3796, "6": 3706, "7": 2810, "8": 4732, "10": 4539, "12": 2456, "14": 3108, "17": 2841, "20": 6614, "24": 4240, "29": 3751, "35": 3258, "42": 2306, "50": 1470, "60": 745, "72": 348, "87": 173, "105": 134, "126": 119, "151": 181, "182": 739, "219": 567, "263": 545, "316": 204, "380": 43, "457": 5, "549": 9, "660": 33, "793": 19, "953": 3, "1146": 1, "1378": 2, "1657": 4, "1992": 4, "2395": 25, "2879": 78, "3461": 54, "4161": 34, "5002": 3346, "6013": 382, "7228": 17, "8689": 2, "10445": 4, "12556": 3, "15094": 2, "31521": 2, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 274974300, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 15, "251252": 8, "267766": 22, "285365": 13, "304121": 11, "324110": 249, "345412": 274, "368115": 189, "392310": 2, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 417484612, "range": [32768, 16777216], "values": {"474861": 0, "506072": 787, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 195371032, "range": [32768, 16777216], "values": {"132939": 0, "141677": 3, "150989": 6, "160913": 17, "171489": 3, "182760": 14, "194772": 18, "207574": 4, "221217": 181, "235757": 109, "251252": 249, "267766": 181, "285365": 1, "324110": 1, "345412": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 82113536, "range": [1024, 16777216], "values": {"97683": 0, "102590": 787, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 787, "1": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1368, "range": [1, 10000], "values": {"0": 16, "1": 116, "2": 115, "3": 15, "4": 13, "5": 14, "6": 8, "7": 14, "8": 20, "10": 13, "12": 6, "14": 14, "24": 3, "40": 1, "48": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 723, "range": [1, 10000], "values": {"0": 9, "1": 56, "2": 58, "3": 8, "4": 5, "5": 8, "6": 4, "7": 7, "8": 10, "10": 7, "12": 3, "14": 7, "24": 2, "40": 1, "48": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 45, "1": 3, "4": 1, "5": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 164, "range": [1, 100000], "values": {"68": 0, "75": 1, "83": 1, "92": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 282, "range": [1, 100000], "values": {"92": 0, "102": 1, "154": 1, "171": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 9, "range": [1, 5000], "values": {"1": 0, "2": 3, "3": 1, "4": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 2, "8": 1, "9": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 85, "range": [1, 50000], "values": {"1": 0, "2": 1, "10": 1, "25": 1, "45": 1, "50": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 389, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 401, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 1, "74": 1, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 409, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 207, "range": [1, 50000], "values": {"0": 18, "1": 24, "2": 8, "3": 5, "4": 3, "5": 1, "6": 2, "7": 2, "9": 1, "12": 1, "14": 1, "15": 1, "17": 1, "19": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 760206, "range": [1, 50000], "values": {"9": 0, "10": 2, "12": 3, "13": 1, "14": 2, "15": 2, "17": 1, "19": 1, "21": 3, "37": 1, "45": 1, "131": 11, "144": 81, "158": 713, "174": 1033, "192": 1559, "211": 456, "232": 66, "255": 4, "281": 3, "309": 4, "340": 2, "374": 4, "454": 1, "500": 1, "550": 1, "605": 2, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 8, "range": [1, 50], "values": {"0": 24, "2": 4, "3": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 11639, "range": [1, 2000], "values": {"0": 0, "1": 51, "2": 111, "3": 57, "4": 49, "5": 42, "6": 39, "7": 34, "8": 41, "9": 23, "10": 27, "11": 46, "13": 29, "15": 32, "17": 25, "19": 15, "22": 10, "25": 15, "29": 70, "33": 38, "38": 15, "44": 28, "50": 26, "57": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 39, "range": [1, 2000], "values": {"0": 0, "1": 8, "2": 6, "4": 1, "5": 3, "6": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6091, "range": [1, 2000], "values": {"0": 54, "1": 208, "2": 116, "3": 92, "4": 67, "5": 56, "6": 55, "7": 26, "8": 25, "9": 20, "10": 9, "11": 21, "13": 13, "15": 13, "17": 9, "19": 5, "22": 3, "25": 2, "29": 2, "33": 8, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "196": 1, "225": 2, "258": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 48, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 5217, "range": [1, 2000], "values": {"0": 28, "1": 202, "2": 129, "3": 96, "4": 71, "5": 62, "6": 52, "7": 30, "8": 31, "9": 21, "10": 20, "11": 28, "13": 11, "15": 11, "17": 8, "19": 4, "22": 4, "25": 1, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 49, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "15": 1, "17": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 35, "range": [1, 50000], "values": {"14": 0, "15": 1, "19": 1, "21": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 77, "range": [1, 50000], "values": {"0": 1, "1": 4, "2": 1, "6": 1, "12": 1, "14": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 95, "range": [1, 50000], "values": {"0": 17, "1": 20, "2": 7, "3": 5, "4": 3, "5": 1, "6": 1, "7": 2, "9": 1, "10": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 144, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "13": 1, "14": 2, "17": 1, "19": 1, "21": 2, "23": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "37": 1, "45": 1, "50": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1128376, "power.total_cpu_time_ms": 972072}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 972072}, "power.wakeups_per_process_type": {"extension": 1128376}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 181, "power.total_cpu_time_ms": 15}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 15}, "power.wakeups_per_process_type": {"socket": 181}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1775555, "power.total_cpu_time_ms": 100487}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 100487}, "power.wakeups_per_process_type": {"utility": 1775555}}}}, "histograms": {"ADDON_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 33, "histogram_type": 1, "sum": 510, "range": [1, 32], "values": {"14": 0, "15": 34, "16": 0}}, "CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85046497, "range": [1, 100000], "values": {"61": 0, "76": 1, "149": 1, "10589": 1, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 1130594, "range": [1, 66355200], "values": {"42226": 0, "61009": 2, "265859": 1, "554984": 1, "801854": 0}}, "CHECKERBOARD_POTENTIAL_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 2734, "range": [1, 1000000], "values": {"113": 0, "149": 4, "1774": 1, "2336": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5781696, "range": [1, 1073741824], "values": {"1303": 0, "1994": 1, "3052": 1, "39254": 1, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10781, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 6, "31": 86, "34": 77, "38": 38, "42": 63, "46": 12, "68": 1, "75": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 199027, "range": [1, 1000], "values": {"0": 10782, "1": 2340, "2": 6511, "3": 12236, "4": 13436, "5": 6337, "6": 2179, "7": 995, "8": 617, "9": 420, "10": 355, "11": 268, "12": 425, "14": 250, "16": 211, "18": 149, "20": 161, "23": 101, "26": 64, "29": 42, "33": 15, "37": 15, "42": 10, "47": 9, "53": 1, "60": 2, "75": 1, "135": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2181, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 15, "6": 36, "7": 119, "8": 94, "9": 9, "10": 5, "11": 1, "12": 2, "13": 3, "15": 3, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 13557, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 68, "42": 88, "46": 60, "51": 63, "56": 4, "62": 1, "68": 1, "83": 1, "138": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 293, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2867, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 24, "6": 16, "7": 138, "8": 128, "10": 27, "12": 9, "14": 6, "17": 2, "20": 2, "24": 1, "40": 1, "48": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 56, "range": [1, 100], "values": {"1": 0, "2": 28, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 39, "range": [1, 100], "values": {"0": 0, "1": 17, "2": 11, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1337, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 4, "34": 7, "40": 6, "48": 4, "57": 1, "81": 3, "96": 1, "114": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 3272, "range": [1, 10000], "values": {"48": 0, "57": 1, "68": 6, "81": 9, "96": 5, "114": 1, "135": 1, "190": 1, "226": 4, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 181, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 180, "1": 1, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 831, "range": [1, 10000], "values": {"8": 0, "10": 1, "14": 11, "24": 1, "29": 3, "34": 4, "40": 2, "48": 6, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 1000], "values": {"0": 20, "1": 2, "2": 1, "4": 2, "7": 1, "10": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 891, "range": [1, 10000], "values": {"17": 0, "20": 4, "24": 10, "29": 3, "34": 6, "40": 3, "48": 1, "57": 1, "68": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 393, "range": [1, 10000], "values": {"4": 0, "5": 1, "6": 5, "7": 2, "8": 5, "10": 1, "12": 3, "14": 5, "17": 1, "20": 2, "24": 1, "40": 1, "48": 1, "57": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 56, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 1, "34": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1305, "range": [1, 10000], "values": {"0": 104, "1": 8, "2": 1, "3": 3, "4": 3, "6": 3, "7": 4, "8": 7, "10": 5, "12": 3, "14": 20, "17": 1, "24": 2, "29": 4, "34": 4, "40": 2, "48": 7, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 561, "range": [1, 100], "values": {"0": 4, "1": 4, "7": 3, "12": 2, "18": 1, "23": 4, "29": 3, "34": 3, "40": 3, "45": 1, "51": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 1274501, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "9": 1, "10": 1, "11": 1, "13": 2, "15": 1, "17": 4, "19": 6, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 7, "47": 12, "53": 41, "60": 51, "68": 76, "77": 193, "88": 394, "100": 784, "114": 975, "130": 760, "148": 391, "168": 163, "191": 130, "217": 154, "247": 114, "281": 99, "320": 72, "364": 62, "414": 58, "471": 80, "536": 110, "610": 84, "695": 22, "791": 23, "901": 17, "1026": 14, "1168": 4, "1330": 16, "1514": 10, "1724": 9, "1963": 7, "2235": 5, "2545": 10, "2898": 13, "3300": 11, "3758": 4, "4279": 10, "4872": 3, "5548": 5, "6317": 1, "7193": 3, "8190": 2, "9326": 1, "10619": 2, "12092": 1, "13769": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1886, "range": [1, 2000], "values": {"0": 51, "1": 12, "2": 3, "4": 2, "6": 1, "9": 2, "10": 1, "11": 1, "13": 2, "15": 64, "17": 3, "22": 3, "38": 1, "44": 1, "50": 2, "75": 1, "99": 2, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 181, "range": [1, 200], "values": {"2": 0, "3": 1, "4": 9, "5": 4, "6": 7, "7": 3, "8": 1, "12": 1, "16": 1, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 83647568, "range": [16, 2147483646], "values": {"0": 0, "16": 77006, "23": 6704, "34": 77, "41": 31, "50": 25, "61": 381, "74": 263, "90": 109, "109": 108, "132": 57, "160": 9, "194": 391, "235": 5444, "284": 4974, "344": 749, "416": 1393, "503": 61059, "609": 5817, "737": 768, "892": 9847, "1080": 14990, "1307": 407, "1582": 67, "1915": 66, "2318": 114, "2805": 46, "3395": 27, "4109": 31, "4973": 20, "6019": 45, "7284": 124, "8815": 2, "10668": 3, "12911": 8, "15625": 1, "18910": 8, "22886": 38, "27698": 2, "33521": 5, "40569": 26, "59421": 1, "71914": 9, "87033": 13, "105331": 1, "154277": 7, "186713": 5, "225968": 3, "330972": 1, "400557": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 2314864, "range": [1, 2147483646], "values": {"0": 0, "1": 83710, "3": 4749, "5": 977, "8": 66290, "12": 1229, "19": 7639, "30": 25835, "47": 444, "73": 131, "113": 144, "176": 35, "274": 14, "426": 8, "662": 8, "1029": 38, "1599": 1, "2485": 3, "6002": 11, "9328": 5, "14498": 11, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3443546, "range": [1, 150000000], "values": {"0": 1513, "1": 7482, "2": 8944, "3": 11346, "4": 12225, "5": 14440, "6": 11167, "7": 7041, "8": 18613, "10": 13479, "12": 12040, "14": 20672, "17": 16108, "20": 14625, "24": 7641, "29": 3235, "35": 2331, "42": 2162, "50": 1649, "60": 990, "72": 802, "87": 673, "105": 497, "126": 359, "151": 300, "182": 232, "219": 97, "263": 86, "316": 56, "380": 48, "457": 49, "549": 60, "660": 44, "793": 40, "953": 39, "1146": 33, "1378": 27, "1657": 26, "1992": 21, "2395": 16, "2879": 24, "3461": 15, "4161": 13, "5002": 4, "6013": 7, "7228": 9, "10445": 1, "12556": 1, "15094": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 512237924, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 15, "418095": 14, "445575": 13, "474861": 12, "506072": 5, "539334": 61, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 643467604, "range": [32768, 16777216], "values": {"741455": 0, "790188": 784, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1852867856, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 4, "1696203": 4, "1807688": 14, "1926500": 17, "2053121": 35, "2188065": 145, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 338198688, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 12, "221217": 9, "235757": 11, "251252": 2, "267766": 10, "285365": 5, "304121": 2, "324110": 41, "345412": 108, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 41074688, "range": [1024, 16777216], "values": {"44591": 0, "46831": 23, "49183": 109, "51654": 483, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 784, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 10205, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 234, "33": 40, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 566, "range": [1, 200000], "values": {"6": 0, "8": 3, "10": 5, "13": 1, "17": 7, "22": 2, "28": 4, "36": 1, "46": 1, "58": 1, "74": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 4589, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 10, "22": 12, "28": 7, "36": 6, "46": 3, "58": 4, "74": 8, "94": 1, "119": 1, "151": 3, "310": 2, "394": 1, "501": 1, "637": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 11, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 818, "range": [1, 100000], "values": {"0": 23, "1": 8, "5": 1, "16": 2, "20": 2, "25": 6, "31": 7, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 4676, "range": [1, 2], "values": {"0": 57, "1": 4676, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 18, "range": [1, 2], "values": {"0": 1, "1": 18, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 398, "range": [1, 3], "values": {"0": 421, "2": 199, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 199, "range": [1, 2], "values": {"0": 0, "1": 199, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 2715, "range": [1, 60000], "values": {"0": 196, "874": 3, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2573, "range": [1, 16], "values": {"2": 0, "3": 31, "4": 620, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 1266, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 31, "3": 401, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 632, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 615, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 27, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 605, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 91692, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 12, "39": 11, "41": 15, "43": 24, "45": 14, "47": 23, "49": 24, "51": 13, "53": 21, "55": 23, "58": 33, "61": 16, "64": 14, "67": 21, "70": 11, "73": 15, "76": 16, "80": 14, "84": 17, "88": 21, "92": 13, "96": 8, "100": 10, "105": 14, "110": 3, "115": 8, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 30, "237": 13, "248": 11, "259": 11, "271": 4, "283": 2, "296": 3, "310": 7, "324": 3, "339": 12, "355": 4, "371": 6, "388": 10, "406": 7, "425": 15, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 89719, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 11, "39": 8, "41": 12, "43": 23, "45": 13, "47": 22, "49": 23, "51": 13, "53": 21, "55": 23, "58": 33, "61": 16, "64": 14, "67": 19, "70": 11, "73": 15, "76": 16, "80": 14, "84": 17, "88": 21, "92": 13, "96": 8, "100": 10, "105": 14, "110": 3, "115": 8, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 5, "207": 3, "217": 15, "227": 30, "237": 13, "248": 11, "259": 10, "271": 4, "283": 2, "296": 2, "310": 7, "324": 3, "339": 12, "355": 3, "371": 6, "388": 10, "406": 7, "425": 14, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1672, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 4, "37": 4, "41": 3, "45": 1, "47": 2, "49": 2, "53": 1, "92": 1, "115": 2, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 90020, "range": [1, 60000], "values": {"25": 0, "26": 1, "35": 4, "37": 8, "39": 11, "41": 12, "43": 24, "45": 13, "47": 21, "49": 22, "51": 13, "53": 20, "55": 23, "58": 33, "61": 16, "64": 14, "67": 21, "70": 11, "73": 15, "76": 16, "80": 14, "84": 17, "88": 21, "92": 12, "96": 8, "100": 10, "105": 14, "110": 3, "115": 6, "120": 8, "126": 5, "132": 5, "138": 3, "144": 2, "151": 6, "158": 3, "165": 3, "173": 5, "181": 3, "189": 4, "198": 5, "207": 3, "217": 15, "227": 30, "237": 13, "248": 11, "259": 11, "271": 4, "283": 2, "296": 3, "310": 7, "324": 3, "339": 12, "355": 4, "371": 6, "388": 10, "406": 7, "425": 15, "445": 1, "531": 2, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1654371, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 11, "3314": 106, "3855": 23, "4484": 4, "5216": 179, "6067": 18, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 308, "range": [1, 2], "values": {"0": 343, "1": 308, "2": 0}}, "CERT_VALIDATION_HTTP_REQUEST_RESULT": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1472, "range": [1, 16], "values": {"3": 0, "4": 318, "8": 25, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 2116, "range": [1, 16], "values": {"3": 0, "4": 87, "8": 221, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 11241, "range": [1, 100000], "values": {"0": 211, "1": 157, "2": 35, "3": 12, "4": 4, "5": 4, "6": 2, "8": 22, "10": 1, "13": 2, "16": 1, "20": 1, "25": 7, "31": 3, "39": 1, "49": 1, "61": 1, "76": 1, "95": 1, "119": 1, "149": 2, "233": 1, "365": 1, "457": 1, "1404": 1, "5399": 1, "6758": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 54700, "range": [1, 16], "values": {"0": 3, "1": 1, "2": 57, "8": 204, "9": 907, "10": 4470, "11": 6, "12": 2, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 5860, "range": [1, 2], "values": {"0": 9, "1": 5860, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 6519, "range": [1, 5000], "values": {"0": 784, "1": 9, "2": 7, "3": 7, "5": 2, "7": 5, "8": 1, "10": 2, "11": 3, "12": 3, "13": 4, "14": 3, "15": 10, "16": 5, "17": 5, "18": 2, "19": 6, "20": 2, "21": 4, "23": 5, "25": 5, "27": 2, "29": 5, "31": 1, "35": 4, "38": 3, "41": 2, "47": 3, "50": 2, "54": 3, "58": 1, "66": 1, "71": 2, "87": 2, "93": 1, "115": 1, "123": 1, "132": 5, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 8873, "range": [1, 5000], "values": {"0": 132, "1": 4, "2": 2, "3": 5, "10": 1, "11": 2, "12": 6, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "62": 1, "66": 9, "71": 2, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 2, "142": 1, "163": 3, "188": 2, "202": 3, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 30507, "range": [1, 16], "values": {"0": 0, "1": 14497, "2": 317, "6": 2313, "7": 214, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 13615, "range": [1, 60000], "values": {"0": 4, "1": 29, "2": 24, "3": 9, "4": 6, "5": 2, "6": 2, "7": 2, "9": 9, "11": 11, "14": 54, "17": 48, "21": 72, "26": 63, "32": 39, "40": 26, "50": 17, "62": 7, "77": 10, "95": 7, "118": 4, "146": 9, "181": 1, "278": 1, "345": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 94090, "range": [1, 60000], "values": {"0": 244, "1": 401, "2": 92, "3": 38, "4": 12, "5": 9, "6": 8, "7": 15, "9": 19, "11": 58, "14": 211, "17": 213, "21": 221, "26": 245, "32": 262, "40": 117, "50": 89, "62": 54, "77": 52, "95": 61, "118": 45, "146": 46, "181": 33, "224": 28, "278": 27, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 724, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 443, "range": [1, 60000], "values": {"0": 3179, "1": 99, "2": 42, "3": 15, "4": 8, "6": 1, "7": 1, "11": 2, "14": 1, "17": 3, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5702, "range": [1, 60000], "values": {"11": 0, "14": 1, "17": 1, "21": 1, "26": 1, "40": 1, "77": 3, "146": 2, "181": 5, "531": 2, "1255": 2, "1556": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 754, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 31501, "range": [1, 50], "values": {"0": 67, "3": 13, "4": 806, "6": 25, "8": 3511, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 826, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 31, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 31, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 48, "range": [1, 50], "values": {"0": 7, "1": 6, "2": 21, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 62, "range": [1, 50], "values": {"0": 95, "2": 31, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"0": 0, "1": 6, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 15, "1": 1, "2": 2, "3": 1, "4": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 21, "1": 1, "2": 0}}, "URLCLASSIFIER_ASYNC_CLASSIFYLOCAL_TIME": {"bucket_count": 30, "histogram_type": 0, "sum": 2, "range": [1, 60000], "values": {"0": 2, "2": 1, "3": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 166, "range": [1, 1000], "values": {"0": 2, "1": 5, "2": 2, "29": 3, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 11, "5": 1, "12": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 213, "range": [1, 5000], "values": {"0": 9, "55": 3, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 15, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1353, "range": [50, 1000], "values": {"69": 0, "77": 2, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 104, "range": [50, 10000], "values": {"0": 10, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 111, "range": [1, 50], "values": {"36": 0, "37": 3, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 582, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 4, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 372, "range": [1, 1000], "values": {"4": 0, "6": 1, "13": 6, "19": 6, "27": 1, "39": 1, "56": 1, "80": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 439, "range": [1, 1000], "values": {"0": 0, "1": 3, "2": 2, "3": 3, "6": 2, "13": 1, "39": 3, "237": 1, "340": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 880, "range": [1, 1000], "values": {"27": 0, "39": 1, "56": 4, "115": 3, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 187, "range": [1, 1000], "values": {"115": 0, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_TRIGGER": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 22, "range": [1, 100], "values": {"3": 0, "4": 4, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 13, "range": [1, 100], "values": {"0": 0, "1": 9, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 22497, "range": [1, 10000], "values": {"355": 0, "405": 1, "759": 1, "860": 1, "1011": 2, "1466": 2, "1516": 1, "1567": 2, "1668": 1, "1718": 1, "2021": 1, "2324": 1, "2779": 1, "2829": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 27570, "range": [1, 10000], "values": {"0": 876, "1": 10486, "2": 3812, "3": 417, "4": 166, "5": 115, "6": 70, "7": 46, "8": 81, "10": 36, "12": 22, "14": 20, "17": 6, "20": 20, "24": 6, "29": 8, "34": 6, "40": 18, "48": 4, "57": 3, "68": 4, "81": 12, "96": 5, "114": 2, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 14374, "range": [1, 10000], "values": {"0": 615, "1": 4873, "2": 2275, "3": 247, "4": 106, "5": 68, "6": 41, "7": 27, "8": 43, "10": 24, "12": 15, "14": 12, "17": 3, "20": 11, "24": 1, "29": 6, "34": 1, "40": 9, "48": 3, "57": 1, "68": 1, "81": 1, "96": 1, "114": 1, "135": 1, "160": 2, "190": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 137, "range": [1, 2000], "values": {"0": 195, "1": 15, "2": 5, "3": 7, "5": 1, "7": 3, "15": 1, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 4003, "range": [1, 50], "values": {"1": 0, "2": 2000, "3": 1, "4": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1563, "range": [1, 10000], "values": {"0": 1284, "1": 1563, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 4152, "range": [1, 10000], "values": {"0": 660, "1": 155, "2": 85, "3": 43, "4": 29, "5": 44, "6": 13, "7": 18, "8": 30, "10": 23, "12": 20, "14": 18, "17": 2, "34": 2, "40": 2, "57": 6, "68": 4, "81": 11, "96": 3, "114": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 11603, "range": [1, 64], "values": {"13": 0, "14": 31, "18": 611, "19": 9, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 9198, "range": [1, 36], "values": {"22": 0, "23": 4, "29": 314, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 2068, "range": [1, 16], "values": {"3": 0, "4": 111, "7": 232, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 2808, "range": [1, 24], "values": {"11": 0, "12": 226, "16": 6, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 2553, "range": [1, 36], "values": {"22": 0, "23": 111, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 15, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1650, "range": [1, 8], "values": {"0": 0, "1": 308, "2": 15, "4": 328, "5": 0}}, "SSL_OCSP_STAPLING": {"bucket_count": 9, "histogram_type": 1, "sum": 3, "range": [1, 8], "values": {"0": 0, "1": 1, "2": 1, "3": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 374, "range": [1, 24], "values": {"0": 0, "1": 374, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 1075, "range": [1, 10], "values": {"0": 0, "1": 1075, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 1075, "range": [1, 10], "values": {"1": 0, "2": 47, "3": 327, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 1163, "range": [1, 10], "values": {"0": 0, "1": 1053, "5": 22, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 374, "range": [1, 10], "values": {"0": 0, "1": 374, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 15417, "range": [1, 256], "values": {"13": 0, "14": 19, "15": 99, "20": 168, "60": 2, "61": 7, "89": 13, "116": 28, "119": 6, "145": 32, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 33, "range": [1, 2], "values": {"0": 0, "1": 33, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 135, "range": [1, 512], "values": {"2": 0, "3": 6, "13": 9, "14": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 79, "range": [1, 512], "values": {"16": 0, "17": 1, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 374, "range": [1, 4], "values": {"0": 0, "1": 374, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 63, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 63, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 63, "1": 0}}, "STORAGE_ACCESS_REMAINING_DAYS": {"bucket_count": 61, "histogram_type": 1, "sum": 75, "range": [1, 60], "values": {"7": 0, "8": 1, "22": 2, "23": 1, "24": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 14, "1": 1, "2": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 11288, "range": [1, 30000], "values": {"12": 0, "19": 112, "29": 151, "45": 3, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 13, "range": [1, 10], "values": {"0": 0, "1": 13, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 13, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 69443, "range": [1, 1000], "values": {"0": 16, "1": 348, "2": 767, "3": 1475, "4": 1330, "5": 1380, "6": 1234, "7": 975, "8": 946, "9": 693, "10": 456, "11": 286, "12": 322, "14": 164, "16": 91, "18": 72, "20": 72, "23": 43, "26": 33, "29": 28, "33": 16, "37": 9, "42": 8, "47": 1, "53": 2, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 412541326, "range": [1, 5000], "values": {"8": 0, "9": 1, "15": 3, "18": 6, "21": 11, "25": 19, "29": 42, "34": 68, "40": 76, "47": 69, "55": 94, "64": 141, "75": 275, "88": 1232, "103": 5900, "120": 1568, "140": 413, "164": 332, "192": 198, "224": 112, "262": 78, "306": 62, "357": 29, "417": 18, "487": 6, "569": 4, "777": 4, "907": 3, "1059": 3, "1237": 1, "3139": 2, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 420484661, "range": [8, 792], "values": {"8": 0, "16": 2, "32": 3, "40": 7, "48": 11, "56": 7, "64": 4, "72": 5, "80": 2, "88": 3, "96": 60, "104": 1406, "112": 3011, "120": 2098, "128": 1331, "136": 526, "144": 196, "152": 91, "160": 57, "168": 36, "176": 17, "184": 13, "192": 10, "200": 16, "208": 72, "216": 142, "224": 140, "232": 111, "240": 79, "248": 64, "256": 45, "264": 29, "272": 22, "280": 20, "288": 16, "296": 14, "304": 15, "312": 10, "320": 21, "328": 24, "336": 23, "344": 16, "352": 13, "360": 18, "368": 9, "376": 6, "384": 3, "392": 5, "400": 4, "408": 8, "416": 8, "424": 10, "432": 16, "440": 7, "448": 3, "456": 6, "464": 7, "472": 5, "480": 3, "488": 5, "496": 1, "504": 3, "512": 1, "520": 1, "528": 3, "536": 6, "544": 3, "552": 4, "560": 8, "568": 4, "576": 2, "584": 2, "592": 1, "600": 3, "608": 3, "616": 1, "624": 2, "632": 2, "640": 3, "648": 2, "656": 3, "672": 1, "680": 2, "704": 2, "728": 3, "736": 1, "752": 1, "768": 2, "776": 1, "784": 1, "792": 34}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 8735119, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 2, "40": 6, "47": 9, "55": 10, "64": 6, "75": 4, "88": 21, "103": 4192, "120": 3711, "140": 485, "164": 78, "192": 237, "224": 423, "262": 109, "306": 109, "357": 58, "417": 62, "487": 36, "569": 25, "665": 11, "777": 8, "907": 4, "1237": 1, "1445": 2, "1688": 2, "2688": 1, "3139": 2, "3666": 1, "5000": 9}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 668, "range": [1, 100000], "values": {"113": 0, "125": 1, "486": 1, "540": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 717, "range": [1, 5000], "values": {"0": 2, "1": 42, "2": 69, "3": 9, "4": 2, "6": 1, "9": 1, "15": 2, "21": 3, "27": 1, "29": 2, "33": 1, "38": 1, "41": 1, "44": 3, "54": 1, "58": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"0": 116, "8": 1, "9": 1, "10": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 26, "range": [1, 50], "values": {"1": 0, "2": 6, "3": 1, "11": 1, "12": 0}}, "COOKIE_PURGING_ORIGINS_PURGED": {"bucket_count": 30, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 0, "1": 1, "2": 0}}, "COOKIE_PURGING_TRACKERS_WITH_USER_INTERACTION": {"bucket_count": 30, "histogram_type": 0, "sum": 0, "range": [1, 500], "values": {"0": 1, "1": 0}}, "COOKIE_PURGING_DURATION_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 102, "range": [1, 600000], "values": {"53": 0, "85": 1, "136": 0}}, "COOKIE_PURGING_INTERVAL_HOURS": {"bucket_count": 56, "histogram_type": 1, "sum": 24, "range": [1, 168], "values": {"20": 0, "23": 1, "26": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 24802, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 693, "8": 9, "9": 8, "12": 1, "14": 2, "17": 1284, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 38, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1740, "range": [1, 50], "values": {"1": 0, "2": 13, "5": 112, "6": 184, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 6170, "range": [1, 50], "values": {"5": 0, "6": 1, "12": 12, "20": 301, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 11, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 1966, "range": [1, 50], "values": {"0": 4, "1": 1966, "2": 0}}, "vscode": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 20, "1": 9, "2": 5, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 159, "range": [1, 2000], "values": {"0": 121, "1": 12, "2": 8, "3": 6, "4": 2, "5": 3, "7": 1, "8": 5, "9": 2, "10": 1, "15": 1, "17": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"0": 4, "2": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 133, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 57, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 6, "3": 2, "4": 1, "5": 2, "6": 2, "10": 1, "11": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 7, "1": 2, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 165, "range": [1, 2000], "values": {"0": 2, "1": 96, "2": 16, "3": 4, "4": 4, "9": 1, "10": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 8, "range": [1, 2000], "values": {"0": 12, "1": 3, "2": 1, "3": 1, "4": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2215, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "37": 2, "41": 1, "45": 1, "47": 2, "53": 1, "92": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 32298, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 18, "45": 6, "47": 11, "49": 10, "51": 5, "53": 10, "55": 13, "58": 16, "61": 7, "64": 8, "67": 6, "70": 4, "73": 6, "76": 4, "80": 4, "84": 5, "88": 6, "92": 1, "96": 4, "100": 1, "105": 2, "110": 1, "115": 2, "120": 5, "126": 1, "132": 4, "138": 1, "151": 4, "158": 2, "165": 1, "173": 3, "181": 3, "189": 3, "198": 1, "217": 2, "227": 3, "248": 4, "259": 3, "271": 2, "310": 5, "324": 2, "339": 7, "355": 1, "371": 2, "388": 2, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "608": 1, "696": 1, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 56160, "range": [1, 60000], "values": {"25": 0, "26": 1, "33": 1, "37": 1, "39": 4, "41": 8, "43": 7, "45": 8, "47": 10, "49": 13, "51": 8, "53": 12, "55": 11, "58": 19, "61": 10, "64": 7, "67": 15, "70": 8, "73": 9, "76": 12, "80": 10, "84": 11, "88": 15, "92": 11, "96": 4, "100": 9, "105": 12, "110": 2, "115": 4, "120": 3, "126": 6, "132": 1, "138": 2, "144": 2, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 3, "217": 13, "227": 27, "237": 13, "248": 7, "259": 8, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 3, "371": 4, "388": 8, "406": 5, "425": 12, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 1362, "range": [1, 60000], "values": {"80": 0, "84": 1, "165": 1, "531": 1, "555": 1, "581": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 3, "range": [1, 32], "values": {"0": 362, "1": 3, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 5865, "range": [1, 50], "values": {"0": 203, "1": 839, "2": 2500, "3": 6, "4": 2, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 115, "range": [1, 50], "values": {"0": 3, "1": 1, "2": 57, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 4008, "range": [1, 50], "values": {"0": 1, "1": 68, "2": 1970, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 270, "range": [1, 100], "values": {"17": 0, "18": 15, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 350, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 630, "range": [1, 100], "values": {"41": 0, "42": 15, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1092, "range": [1, 2], "values": {"0": 0, "1": 1092, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 3641, "range": [1, 2], "values": {"0": 9, "1": 3641, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 4320, "range": [1, 50], "values": {"5": 0, "6": 720, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 15793, "range": [1, 50], "values": {"5": 0, "6": 2610, "7": 19, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 3629, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 2, "1531": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 3, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 3, "range": [1, 16], "values": {"0": 0, "1": 3, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 832, "range": [1, 100000], "values": {"30": 0, "45": 1, "229": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 3, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 3, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 904, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 3, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 11, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-other_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 12769, "range": [1, 60000], "values": {"0": 152, "1": 387, "2": 81, "3": 30, "5": 31, "8": 11, "13": 4, "21": 203, "34": 62, "54": 2, "86": 1, "137": 17, "219": 0}}, "subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 392, "range": [1, 60000], "values": {"0": 11, "1": 8, "2": 9, "3": 11, "5": 16, "8": 1, "13": 2, "21": 1, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 12377, "range": [1, 60000], "values": {"0": 141, "1": 379, "2": 72, "3": 19, "5": 15, "8": 10, "13": 2, "21": 202, "34": 62, "54": 2, "86": 1, "137": 16, "219": 0}}, "subresource-image_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}, "subresource_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 11474, "range": [1, 5000], "values": {"0": 506, "1": 100, "2": 16, "3": 4, "4": 7, "6": 5, "9": 2, "18": 19, "26": 232, "37": 13, "53": 2, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11691, "range": [1, 5000], "values": {"0": 532, "1": 115, "2": 26, "3": 9, "4": 10, "6": 5, "9": 3, "18": 19, "26": 232, "37": 13, "53": 2, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 217, "range": [1, 5000], "values": {"0": 26, "1": 15, "2": 10, "3": 5, "4": 3, "9": 1, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_FINISH_SYNTHESIZED_RESPONSE_MS_2": {"subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 196, "range": [1, 5000], "values": {"0": 875, "1": 21, "2": 9, "3": 6, "4": 6, "6": 2, "13": 1, "75": 1, "106": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 334, "range": [1, 5000], "values": {"0": 895, "1": 39, "2": 16, "3": 14, "4": 10, "6": 2, "13": 2, "18": 2, "75": 1, "106": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 138, "range": [1, 5000], "values": {"0": 20, "1": 18, "2": 7, "3": 8, "4": 4, "13": 1, "18": 2, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 1104, "range": [1, 50], "values": {"0": 133, "1": 312, "2": 396, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 8218, "range": [1, 50], "values": {"0": 230, "1": 982, "2": 3618, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 55, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 50, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 21, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 36, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 153, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 12, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 39, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2360, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 35, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 34, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 42, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4728, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 88, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3245, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 166, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 264, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 160, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 494, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7728, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 30, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 711, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 32, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2211, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 17, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 691, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 369, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 46, "1": 0}}, "indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 892, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 97688, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2343, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 961, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 205, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"4": 0, "5": 12, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1309, "range": [1, 50], "values": {"16": 0, "17": 20, "19": 51, "20": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 254, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 15, "17": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 14, "range": [1, 50], "values": {"13": 0, "14": 1, "15": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 8, "range": [1, 50], "values": {"1": 0, "2": 4, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 410, "range": [1, 50], "values": {"1": 0, "2": 205, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 46, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 110, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 6, "19": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 6, "range": [1, 50], "values": {"1": 0, "2": 3, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 307, "range": [1, 50], "values": {"15": 0, "16": 18, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 22, "range": [1, 50], "values": {"1": 0, "2": 11, "3": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 778, "range": [1, 50], "values": {"16": 0, "17": 32, "18": 13, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3938, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 201, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1566, "range": [1, 50], "values": {"3": 0, "4": 4, "16": 9, "19": 74, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"1": 0, "2": 30, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 22, "range": [1, 50], "values": {"1": 0, "2": 2, "18": 1, "19": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1538, "range": [1, 50], "values": {"3": 0, "4": 166, "16": 27, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 87724, "subsessionLength": 47224, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}