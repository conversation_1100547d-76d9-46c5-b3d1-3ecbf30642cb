{"type": "main", "id": "51f5a973-3b5d-419e-9330-92cca8b3460e", "creationDate": "2025-05-25T10:23:22.323Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 85102, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 44}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 17, "browser.engagement.tab_open_event_count": 2, "browser.engagement.max_concurrent_tab_count": 6, "urlbar.zeroprefix.exposure": 2, "dom.contentprocess.os_priority_raised": 281, "browser.engagement.unique_domains_count": 3, "dom.contentprocess.os_priority_lowered": 25, "urlbar.zeroprefix.abandonment": 1, "dom.contentprocess.os_priority_change_considered": 56, "browser.engagement.active_ticks": 44, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 17, "power.total_thread_wakeups": 2016102, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 85101637, "browser.engagement.total_uri_count": 17, "browser.engagement.max_concurrent_window_count": 1, "browser.engagement.session_time_excluding_suspend": 85101637, "power.total_cpu_time_ms": 1179363}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 4}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1136086, "parent.active": 43277}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 2}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "power.wakeups_per_process_type": {"parent.inactive": 1907717, "parent.active": 108385}, "networking.data_transferred_v3_kb": {"Y1_N1": 5776, "Y0_N1Sys": 419, "Y2_N3Oth": 8683}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 1479, "successful": 385}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 16082, "range": [1, 10000], "values": {"0": 0, "1": 21, "2": 334, "3": 251, "4": 14, "5": 6, "6": 2, "7": 5, "8": 365, "10": 8, "12": 2, "14": 18, "17": 156, "20": 35, "24": 283, "29": 7, "34": 1, "48": 1, "57": 2, "68": 1, "81": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 729, "range": [1, 100], "values": {"2": 0, "3": 189, "4": 16, "5": 6, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 526, "range": [1, 100], "values": {"0": 2, "1": 71, "2": 23, "3": 110, "4": 6, "5": 3, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 39108, "range": [1, 10000], "values": {"1": 0, "2": 1, "4": 2, "7": 1, "8": 2, "12": 2, "14": 2, "17": 1, "20": 1, "24": 2, "29": 6, "34": 2, "40": 8, "48": 2, "57": 7, "68": 5, "81": 1, "96": 1, "114": 4, "135": 2, "160": 28, "190": 129, "226": 3, "268": 2, "318": 1, "449": 1, "533": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 56944, "range": [1, 10000], "values": {"29": 0, "34": 2, "40": 5, "48": 2, "57": 2, "68": 10, "81": 8, "96": 5, "114": 7, "135": 4, "190": 1, "226": 70, "268": 82, "318": 10, "378": 4, "533": 2, "633": 1, "752": 1, "894": 1, "1262": 1, "1500": 1, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 2081, "1": 1, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 20, "range": [1, 2], "values": {"0": 2062, "1": 20, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 10164, "range": [1, 10000], "values": {"1": 0, "2": 1, "4": 3, "8": 2, "12": 3, "14": 6, "17": 4, "20": 1, "24": 4, "29": 5, "34": 2, "40": 10, "48": 172, "57": 3, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 192, "range": [1, 1000], "values": {"0": 85, "1": 113, "2": 12, "3": 3, "4": 3, "6": 1, "7": 1, "20": 1, "23": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 31207, "range": [1, 10000], "values": {"0": 3, "1": 2, "5": 4, "8": 3, "14": 1, "17": 2, "20": 6, "24": 6, "29": 1, "34": 9, "40": 5, "48": 4, "57": 1, "68": 2, "81": 1, "96": 2, "135": 19, "160": 139, "190": 3, "226": 1, "268": 2, "378": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 15634, "range": [1, 10000], "values": {"0": 0, "1": 1, "2": 5, "3": 3, "4": 1, "5": 2, "6": 4, "7": 4, "8": 7, "10": 4, "12": 4, "14": 1, "17": 5, "20": 3, "24": 2, "29": 1, "40": 1, "68": 71, "81": 84, "96": 8, "114": 4, "135": 1, "190": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 306, "range": [1, 10000], "values": {"1": 0, "2": 1, "17": 2, "226": 1, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 38831, "range": [1, 10000], "values": {"0": 913, "1": 17, "2": 35, "3": 17, "4": 14, "5": 20, "6": 16, "7": 13, "8": 30, "10": 15, "12": 23, "14": 94, "17": 54, "20": 96, "24": 60, "29": 27, "34": 56, "40": 44, "48": 530, "57": 5, "68": 1, "135": 1, "226": 1, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 1518, "range": [1, 100], "values": {"0": 180, "1": 3, "7": 6, "12": 3, "18": 3, "23": 1, "29": 2, "34": 5, "40": 4, "51": 1, "56": 1, "67": 3, "73": 1, "78": 2, "84": 1, "89": 2, "95": 1, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 16471848, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 2, "5": 3, "6": 1, "9": 1, "10": 7, "11": 30, "13": 96, "15": 220, "17": 200, "19": 338, "22": 127, "25": 15, "28": 5, "32": 1, "36": 2, "41": 6, "47": 6, "53": 16, "60": 24, "68": 14, "77": 52, "88": 18, "100": 21, "114": 46, "130": 81, "148": 174, "168": 294, "191": 779, "217": 635, "247": 464, "281": 299, "320": 226, "364": 357, "414": 631, "471": 728, "536": 967, "610": 1114, "695": 1120, "791": 1936, "901": 2545, "1026": 3672, "1168": 2524, "1330": 341, "1514": 89, "1724": 54, "1963": 22, "2235": 7, "2545": 8, "2898": 15, "3300": 4, "3758": 15, "4279": 8, "4872": 12, "5548": 6, "6317": 5, "7193": 12, "8190": 5, "9326": 3, "10619": 2, "12092": 3, "13769": 3, "17852": 3, "20328": 2, "23147": 1, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 17415, "range": [1, 2000], "values": {"0": 843, "1": 31, "2": 22, "3": 32, "4": 13, "5": 14, "6": 23, "7": 13, "8": 7, "9": 4, "10": 3, "11": 19, "13": 23, "15": 753, "17": 11, "19": 5, "22": 4, "25": 3, "29": 3, "33": 6, "38": 5, "44": 5, "50": 1, "57": 2, "65": 1, "75": 1, "99": 7, "113": 2, "196": 2, "225": 4, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 2081, "range": [1, 200], "values": {"2": 0, "3": 5, "4": 20, "5": 6, "6": 8, "7": 2, "8": 10, "9": 89, "10": 17, "11": 6, "12": 25, "13": 11, "14": 9, "15": 3, "16": 1, "18": 1, "19": 1, "21": 1, "25": 1, "29": 1, "31": 1, "34": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 105361640, "range": [16, 2147483646], "values": {"0": 0, "16": 57688, "23": 5058, "28": 102, "34": 32, "41": 103, "50": 24, "61": 63, "74": 39, "90": 65, "109": 139, "132": 46, "160": 297, "194": 80, "235": 51, "284": 55999, "344": 44884, "416": 40, "503": 309, "609": 56268, "737": 534, "892": 1180, "1080": 290, "1307": 283, "1582": 560, "1915": 563, "2318": 289, "2805": 378, "3395": 8, "4109": 292, "4973": 573, "6019": 8, "7284": 101, "8815": 388, "10668": 36, "12911": 15, "15625": 2, "18910": 8, "27698": 13, "33521": 5, "40569": 2, "49098": 283, "71914": 4, "87033": 1, "105331": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 4010046, "range": [1, 2147483646], "values": {"0": 0, "1": 62949, "2": 13, "3": 219, "5": 237, "8": 397, "12": 112347, "19": 45071, "30": 2084, "47": 654, "73": 1136, "113": 290, "176": 1255, "274": 50, "426": 103, "662": 8, "1029": 5, "1599": 3, "2485": 281, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2953944, "range": [1, 150000000], "values": {"0": 3126, "1": 13469, "2": 19093, "3": 5929, "4": 3381, "5": 2391, "6": 5073, "7": 10364, "8": 35558, "10": 34862, "12": 32406, "14": 25105, "17": 7570, "20": 5282, "24": 10829, "29": 5726, "35": 3444, "42": 999, "50": 615, "60": 384, "72": 221, "87": 204, "105": 249, "126": 199, "151": 122, "182": 267, "219": 53, "263": 60, "316": 56, "380": 4, "457": 6, "549": 7, "660": 9, "793": 2, "953": 1, "1146": 8, "1378": 5, "1657": 2, "1992": 2, "2395": 1, "2879": 3, "3461": 3, "4161": 2, "6013": 3, "7228": 8, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1367131952, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 3, "103055": 10, "117047": 2, "124740": 4, "132939": 10, "141677": 13, "150989": 11, "160913": 8, "171489": 5, "182760": 7, "194772": 245, "207574": 472, "221217": 12, "235757": 415, "251252": 315, "267766": 1, "285365": 2, "368115": 1, "392310": 11, "418095": 7, "474861": 22, "506072": 92, "539334": 96, "574782": 103, "612560": 122, "652821": 130, "695728": 394, "741455": 279, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 1667893816, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 19, "267766": 738, "285365": 737, "324110": 1, "345412": 5, "368115": 17, "418095": 1, "445575": 1, "506072": 113, "539334": 96, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 73, "1019325": 739, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1045908300, "range": [32768, 16777216], "values": {"0": 18, "34922": 2, "37217": 5, "39663": 5, "42270": 3, "45048": 2, "48009": 1, "51164": 5, "54527": 5, "58111": 5, "61930": 6, "66000": 1, "70338": 9, "74961": 1, "85139": 7, "90735": 3, "96699": 241, "103055": 240, "109828": 232, "117047": 2, "124740": 26, "132939": 381, "141677": 334, "160913": 1, "194772": 1, "207574": 1, "285365": 1, "304121": 16, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 97, "539334": 110, "574782": 297, "612560": 387, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 612908032, "range": [1024, 16777216], "values": {"0": 0, "1024": 5, "8848": 21, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 79, "33230": 204, "34899": 101, "36652": 205, "38493": 202, "40427": 202, "42458": 204, "44591": 181, "46831": 85, "49183": 11, "59836": 3, "62842": 1, "76453": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 714, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 53, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 3005, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 6, "range": [1, 2], "values": {"0": 0, "1": 6, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 12468, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "862": 2, "969": 1, "1223": 1, "1456": 3, "1733": 1, "1837": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 32, "range": [1, 30000], "values": {"0": 1, "5": 1, "24": 1, "29": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 30000], "values": {"0": 1, "1": 2, "13": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 233, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 1, "140": 1, "171": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 289, "range": [1, 30000], "values": {"0": 4, "1": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 1, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 166, "range": [1, 50], "values": {"0": 2604, "2": 83, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 23332, "range": [1, 10000], "values": {"0": 386, "1": 335, "2": 885, "3": 237, "4": 179, "5": 143, "6": 136, "7": 130, "8": 259, "10": 195, "12": 194, "14": 232, "17": 129, "20": 11, "24": 15, "29": 6, "34": 4, "40": 4, "48": 7, "57": 3, "68": 4, "81": 4, "96": 1, "114": 1, "135": 1, "226": 1, "533": 2, "752": 1, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12795, "range": [1, 10000], "values": {"0": 328, "1": 155, "2": 438, "3": 118, "4": 97, "5": 70, "6": 72, "7": 71, "8": 110, "10": 93, "12": 102, "14": 103, "17": 32, "20": 10, "24": 30, "29": 11, "34": 4, "40": 9, "48": 4, "57": 2, "68": 3, "81": 4, "114": 1, "135": 1, "533": 1, "752": 1, "894": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 90, "range": [1, 50], "values": {"0": 0, "1": 52, "2": 6, "3": 6, "4": 2, "5": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1404, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "244": 1, "297": 1, "485": 1, "535": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 58, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 2, "7": 1, "14": 1, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 3, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 6706, "range": [1, 1000], "values": {"0": 306, "1": 561, "2": 554, "3": 508, "4": 278, "5": 117, "6": 92, "7": 54, "8": 40, "9": 9, "10": 14, "11": 9, "12": 5, "14": 6, "16": 1, "20": 1, "26": 1, "33": 1, "37": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4170, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "909": 2, "1009": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4405, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 1, "1009": 2, "1120": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 111, "range": [1, 5000], "values": {"1": 0, "2": 12, "3": 10, "4": 2, "5": 1, "10": 1, "17": 2, "18": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 15, "range": [1, 5000], "values": {"0": 21, "1": 5, "2": 2, "6": 1, "7": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1461, "range": [1, 50000], "values": {"4": 0, "5": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "500": 1, "550": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4726, "range": [1, 50000], "values": {"15": 0, "17": 1, "666": 1, "733": 3, "807": 2, "888": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6757, "range": [1, 50000], "values": {"25": 0, "28": 1, "666": 1, "733": 1, "807": 1, "1302": 2, "1736": 1, "1911": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6830, "range": [1, 50000], "values": {"45": 0, "50": 1, "666": 1, "733": 1, "807": 1, "1302": 2, "1736": 1, "1911": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7584, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1433": 2, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7587, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1433": 2, "1736": 1, "1911": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7642, "range": [1, 50000], "values": {"98": 0, "108": 1, "733": 1, "807": 1, "977": 1, "1433": 2, "1736": 1, "1911": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1084, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "500": 1, "550": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5694, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1433": 2, "1577": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4481, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "888": 2, "1183": 1, "1302": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3528, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "977": 2, "1075": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2424, "range": [1, 50000], "values": {"454": 0, "500": 2, "605": 1, "666": 1, "733": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3484, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2776, "range": [1, 50000], "values": {"412": 0, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3346, "range": [1, 50000], "values": {"0": 50, "1": 8, "2": 24, "3": 6, "4": 6, "5": 12, "6": 2, "7": 1, "8": 2, "9": 1, "10": 5, "11": 3, "12": 4, "13": 5, "14": 3, "15": 6, "17": 3, "19": 1, "21": 1, "25": 2, "37": 2, "41": 1, "131": 1, "174": 1, "192": 3, "211": 1, "232": 2, "255": 1, "281": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5692, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "23": 1, "25": 1, "28": 2, "31": 2, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "74": 1, "108": 1, "119": 1, "158": 1, "232": 1, "255": 2, "281": 1, "374": 1, "605": 1, "733": 1, "807": 2, "888": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 638, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "55": 1, "61": 2, "67": 1, "119": 1, "158": 1, "174": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 172, "range": [1, 50], "values": {"0": 262, "1": 53, "2": 42, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3780, "range": [1, 2000], "values": {"1": 0, "2": 6, "3": 7, "4": 34, "5": 40, "6": 14, "7": 11, "8": 6, "9": 2, "10": 4, "11": 6, "13": 4, "15": 4, "17": 3, "19": 4, "22": 2, "29": 1, "33": 12, "38": 9, "44": 2, "50": 4, "57": 1, "65": 7, "75": 4, "113": 1, "130": 1, "149": 1, "171": 2, "196": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 865, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "50": 1, "86": 2, "99": 1, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 2200, "range": [1, 2000], "values": {"1": 0, "2": 8, "3": 10, "4": 6, "5": 3, "6": 5, "7": 1, "8": 15, "9": 10, "10": 11, "11": 5, "13": 1, "29": 2, "33": 9, "38": 11, "75": 1, "86": 2, "99": 1, "149": 1, "171": 2, "196": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 11725, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 6, "5": 3, "6": 6, "7": 17, "8": 9, "9": 7, "10": 8, "11": 7, "13": 4, "15": 4, "17": 3, "19": 6, "22": 10, "25": 10, "29": 11, "33": 39, "38": 14, "44": 5, "50": 3, "57": 2, "65": 8, "75": 1, "86": 2, "99": 17, "113": 21, "130": 7, "196": 7, "225": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 78, "range": [1, 2000], "values": {"2": 0, "3": 3, "5": 1, "6": 2, "22": 1, "29": 1, "33": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1251, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 2, "15": 2, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4339, "range": [1, 2000], "values": {"0": 4, "1": 11, "2": 7, "3": 39, "4": 67, "5": 15, "6": 3, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1, "15": 5, "19": 2, "22": 2, "25": 2, "29": 1, "33": 7, "38": 4, "44": 7, "50": 3, "57": 5, "65": 4, "75": 5, "86": 1, "99": 3, "149": 1, "171": 1, "196": 3, "258": 1, "296": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1403, "range": [1, 2000], "values": {"4": 0, "5": 3, "7": 1, "17": 1, "19": 1, "130": 1, "171": 1, "196": 1, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 4240, "range": [1, 2000], "values": {"0": 2, "1": 139, "2": 47, "4": 4, "5": 1, "6": 1, "8": 3, "9": 1, "10": 2, "13": 1, "15": 1, "17": 1, "19": 1, "22": 1, "29": 1, "38": 1, "44": 1, "50": 2, "57": 2, "99": 1, "130": 1, "339": 1, "672": 3, "770": 1, "882": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 9418, "range": [1, 2000], "values": {"0": 1, "1": 15, "2": 60, "3": 84, "4": 10, "5": 9, "6": 4, "7": 7, "8": 2, "9": 2, "11": 6, "13": 4, "15": 2, "17": 6, "19": 6, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "99": 1, "113": 10, "130": 8, "149": 10, "171": 7, "196": 1, "258": 2, "296": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 130, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 3, "5": 1, "7": 1, "8": 1, "11": 2, "15": 1, "22": 1, "38": 1, "44": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1976, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 1, "86": 1, "99": 1, "171": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6746, "range": [1, 2000], "values": {"0": 17, "1": 12, "2": 8, "3": 27, "4": 28, "5": 53, "6": 1, "7": 1, "10": 1, "11": 2, "15": 4, "22": 4, "25": 1, "33": 5, "38": 2, "44": 6, "50": 6, "57": 3, "65": 2, "75": 3, "86": 5, "99": 4, "113": 1, "171": 2, "225": 1, "258": 2, "296": 4, "339": 2, "389": 1, "446": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1620, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 3, "296": 1, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 4179, "range": [1, 2000], "values": {"0": 17, "1": 146, "2": 28, "4": 3, "5": 1, "6": 1, "8": 3, "9": 1, "10": 1, "15": 1, "17": 1, "19": 1, "22": 1, "29": 1, "38": 1, "44": 1, "50": 2, "57": 2, "99": 1, "130": 1, "339": 1, "672": 3, "770": 1, "882": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 13846, "range": [1, 2000], "values": {"0": 6, "1": 11, "2": 53, "3": 99, "4": 7, "5": 4, "6": 2, "7": 1, "8": 2, "10": 1, "11": 4, "13": 4, "15": 4, "17": 3, "19": 4, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 4, "99": 2, "113": 2, "130": 1, "149": 2, "171": 6, "196": 6, "225": 6, "258": 10, "296": 5, "339": 2, "389": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 1091, "range": [1, 2000], "values": {"0": 1, "1": 1, "4": 1, "7": 1, "13": 2, "25": 1, "50": 2, "149": 1, "296": 1, "446": 1, "511": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1920, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 2, "86": 3, "113": 1, "149": 1, "225": 1, "258": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 3528, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "977": 2, "1075": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 1143, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "278": 1, "477": 1, "522": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 128, "range": [1, 50000], "values": {"3": 0, "4": 2, "5": 4, "11": 1, "12": 2, "13": 1, "15": 1, "17": 1, "19": 1, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 8, "range": [1, 50000], "values": {"0": 0, "1": 4, "2": 2, "3": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 2544, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 1, "232": 2, "255": 1, "281": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 95, "range": [1, 50000], "values": {"0": 11, "2": 5, "3": 2, "6": 1, "9": 1, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 79, "range": [1, 50000], "values": {"0": 33, "2": 7, "3": 1, "4": 1, "5": 1, "10": 1, "12": 1, "13": 1, "17": 1, "19": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 222, "range": [1, 50000], "values": {"0": 6, "1": 4, "2": 10, "3": 2, "4": 2, "6": 1, "8": 1, "10": 4, "12": 1, "13": 1, "14": 1, "15": 3, "17": 1, "25": 1, "28": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 1367, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "28": 1, "34": 1, "37": 2, "41": 2, "61": 1, "108": 1, "232": 1, "281": 1, "374": 1, "412": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 2173, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "23": 1, "25": 1, "28": 1, "31": 2, "45": 1, "255": 2, "605": 1, "733": 1, "807": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 2152, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "74": 1, "119": 1, "158": 1, "807": 2, "888": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 638, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 1, "11": 1, "12": 1, "13": 1, "21": 1, "31": 1, "55": 1, "61": 2, "67": 1, "119": 1, "158": 1, "174": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 287, "power.total_thread_wakeups": 3093609, "power.total_cpu_time_ms": 1171054}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 330354, "web.foreground": 740366, "prealloc": 97615, "privilegedabout": 2719}, "power.wakeups_per_process_type": {"web.background": 1243011, "web.foreground": 993579, "prealloc": 845839, "privilegedabout": 11180}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3179, "range": [1, 10000], "values": {"0": 0, "1": 215, "2": 1410, "3": 40, "4": 3, "12": 1, "14": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 24889, "range": [1, 100], "values": {"11": 0, "14": 1657, "17": 2, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 11350, "range": [1, 100], "values": {"0": 0, "1": 966, "2": 1, "14": 691, "17": 1, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 69945, "range": [1, 10000], "values": {"4": 0, "5": 1, "10": 13, "12": 15, "14": 817, "17": 115, "20": 6, "57": 3, "68": 513, "81": 163, "96": 12, "135": 1, "160": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 154067, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 1, "40": 7, "48": 110, "57": 778, "68": 37, "81": 17, "96": 23, "114": 611, "135": 47, "160": 9, "190": 5, "226": 4, "268": 2, "318": 4, "378": 3, "449": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8571, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 8571, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 47485, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 16, "6": 19, "7": 13, "8": 33, "10": 31, "12": 20, "14": 755, "17": 71, "20": 5, "24": 2, "29": 1, "34": 21, "40": 18, "48": 650, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 28, "range": [1, 1000], "values": {"0": 1631, "1": 28, "2": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 55359, "range": [1, 10000], "values": {"0": 1, "6": 6, "7": 11, "8": 527, "10": 415, "12": 4, "14": 3, "48": 3, "57": 464, "68": 212, "81": 10, "96": 2, "114": 1, "135": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 12861, "range": [1, 10000], "values": {"2": 0, "3": 21, "4": 231, "5": 653, "6": 54, "7": 6, "8": 76, "10": 295, "12": 66, "14": 249, "17": 4, "20": 3, "34": 1, "40": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 69120, "range": [1, 10000], "values": {"0": 5215, "1": 25, "2": 38, "3": 49, "4": 75, "5": 105, "6": 31, "7": 21, "8": 124, "10": 336, "12": 101, "14": 1419, "17": 214, "20": 86, "24": 19, "29": 14, "34": 27, "40": 20, "48": 652, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 65616, "range": [1, 100], "values": {"0": 643, "1": 42, "7": 5, "12": 2, "51": 2, "56": 10, "62": 218, "67": 707, "73": 10, "78": 11, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4102647, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "28": 1, "32": 18, "36": 60, "41": 80, "47": 78, "53": 47, "60": 63, "68": 307, "77": 412, "88": 921, "100": 599, "114": 261, "130": 221, "148": 147, "168": 154, "191": 123, "217": 52, "247": 80, "281": 220, "320": 400, "364": 564, "414": 714, "471": 1234, "536": 1145, "610": 376, "695": 449, "791": 565, "901": 313, "1026": 170, "1168": 89, "1330": 21, "1724": 1, "1963": 2, "2235": 2, "2898": 1, "8190": 1, "9326": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 81632, "range": [1, 2000], "values": {"0": 1720, "1": 78, "2": 24, "3": 22, "4": 34, "5": 40, "6": 37, "7": 28, "8": 34, "9": 36, "10": 46, "11": 73, "13": 42, "15": 4560, "17": 45, "19": 34, "22": 8, "25": 12, "29": 7, "33": 2, "38": 7, "50": 13, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 8571, "range": [1, 200], "values": {"2": 0, "3": 55, "4": 601, "5": 219, "6": 620, "7": 147, "8": 7, "9": 3, "10": 2, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 12602568856, "range": [16, 2147483646], "values": {"0": 0, "16": 9756, "23": 145, "28": 1, "34": 344, "41": 3743, "50": 23, "61": 75, "74": 26, "90": 13, "109": 54, "132": 2939, "160": 26, "194": 26, "235": 3740, "284": 2258, "344": 752, "416": 3732, "503": 3788, "609": 344, "737": 3019, "892": 747, "1080": 416, "1307": 564, "1582": 1014, "1915": 556, "2318": 260, "2805": 366, "3395": 235, "4109": 155, "4973": 1026, "6019": 131, "7284": 140, "8815": 30, "10668": 8, "12911": 58, "15625": 2, "18910": 6, "27698": 1, "33521": 4, "40569": 2, "49098": 2, "225968": 13, "3267857": 3723, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 9971501, "range": [1, 2147483646], "values": {"0": 0, "1": 9909, "3": 4202, "5": 47, "8": 3029, "12": 5671, "19": 10451, "30": 4637, "47": 1021, "73": 359, "113": 380, "176": 832, "662": 2, "1599": 3723, "2485": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 22016049, "range": [1, 150000000], "values": {"0": 212, "1": 553, "2": 310, "3": 165, "4": 628, "5": 1917, "6": 2013, "7": 1898, "8": 3785, "10": 3955, "12": 2053, "14": 2542, "17": 2065, "20": 5230, "24": 2614, "29": 2296, "35": 2257, "42": 1749, "50": 1099, "60": 561, "72": 250, "87": 116, "105": 97, "126": 78, "151": 127, "182": 680, "219": 526, "263": 498, "316": 177, "380": 30, "457": 2, "549": 6, "660": 30, "793": 14, "953": 2, "1146": 1, "1657": 3, "1992": 1, "2395": 9, "2879": 39, "3461": 22, "4161": 11, "5002": 3286, "6013": 345, "7228": 7, "8689": 1, "10445": 1, "12556": 1, "31521": 1, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 262955256, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 5, "267766": 3, "285365": 9, "304121": 10, "324110": 249, "345412": 273, "368115": 189, "392310": 1, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 394143668, "range": [32768, 16777216], "values": {"474861": 0, "506072": 743, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 187133404, "range": [32768, 16777216], "values": {"132939": 0, "141677": 2, "150989": 3, "160913": 4, "171489": 2, "182760": 5, "194772": 5, "207574": 3, "221217": 180, "235757": 109, "251252": 249, "267766": 180, "285365": 1, "304121": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 77521920, "range": [1024, 16777216], "values": {"97683": 0, "102590": 743, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 743, "1": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1, "range": [1, 10000], "values": {"0": 0, "1": 1, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 5, "1": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10, "range": [1, 50000], "values": {"9": 0, "10": 1, "11": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 71, "range": [1, 50000], "values": {"61": 0, "67": 1, "74": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 73, "range": [1, 50000], "values": {"61": 0, "67": 1, "74": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 76, "range": [1, 50000], "values": {"67": 0, "74": 1, "81": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 94, "range": [1, 50000], "values": {"0": 10, "1": 16, "2": 5, "3": 3, "4": 2, "6": 2, "17": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 718867, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "17": 1, "19": 1, "21": 3, "131": 6, "144": 50, "158": 658, "174": 964, "192": 1532, "211": 446, "232": 60, "255": 1, "309": 1, "374": 1, "454": 1, "500": 1, "550": 1, "605": 1, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 7647, "range": [1, 2000], "values": {"0": 0, "1": 42, "2": 87, "3": 42, "4": 33, "5": 25, "6": 27, "7": 15, "8": 27, "9": 13, "10": 19, "11": 24, "13": 21, "15": 24, "17": 12, "19": 7, "22": 7, "25": 8, "29": 48, "33": 4, "38": 13, "44": 28, "50": 22, "57": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 4438, "range": [1, 2000], "values": {"0": 37, "1": 163, "2": 80, "3": 62, "4": 36, "5": 35, "6": 31, "7": 13, "8": 15, "9": 10, "10": 5, "11": 14, "13": 6, "15": 8, "17": 4, "19": 2, "22": 2, "33": 7, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "225": 2, "258": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3726, "range": [1, 2000], "values": {"0": 21, "1": 159, "2": 93, "3": 68, "4": 43, "5": 31, "6": 32, "7": 17, "8": 17, "9": 10, "10": 9, "11": 14, "13": 6, "15": 6, "17": 5, "19": 3, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 49, "range": [1, 50000], "values": {"0": 1, "1": 2, "2": 1, "6": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 45, "range": [1, 50000], "values": {"0": 9, "1": 14, "2": 4, "3": 3, "4": 2, "6": 1, "7": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 81, "range": [1, 50000], "values": {"15": 0, "17": 1, "19": 1, "21": 2, "23": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1062995, "power.total_cpu_time_ms": 925169}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 925169}, "power.wakeups_per_process_type": {"extension": 1062995}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 94, "power.total_cpu_time_ms": 8}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 8}, "power.wakeups_per_process_type": {"socket": 94}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1542339, "power.total_cpu_time_ms": 86178}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 86178}, "power.wakeups_per_process_type": {"utility": 1542339}}}}, "histograms": {"CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85035070, "range": [1, 100000], "values": {"79889": 0, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 644112, "range": [1, 66355200], "values": {"384119": 0, "554984": 1, "801854": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5729659, "range": [1, 1073741824], "values": {"2771032": 0, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10208, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 5, "31": 86, "34": 72, "38": 35, "42": 61, "46": 10, "51": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 10625, "range": [1, 1000], "values": {"0": 282, "1": 674, "2": 1522, "3": 585, "4": 232, "5": 133, "6": 67, "7": 62, "8": 43, "9": 29, "10": 23, "11": 17, "12": 29, "14": 8, "16": 12, "18": 6, "20": 9, "23": 6, "26": 2, "29": 5, "33": 2, "37": 1, "42": 1, "47": 3, "75": 1, "84": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2065, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 13, "6": 34, "7": 117, "8": 92, "9": 7, "10": 3, "12": 2, "13": 3, "15": 2, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 12744, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 67, "42": 84, "46": 57, "51": 60, "56": 3, "62": 1, "68": 1, "75": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 279, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 2298, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 21, "6": 11, "7": 130, "8": 116, "10": 9, "12": 7, "14": 1, "17": 1, "20": 1, "24": 1, "29": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 20, "range": [1, 100], "values": {"1": 0, "2": 10, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 13, "range": [1, 100], "values": {"0": 0, "1": 7, "2": 3, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 457, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 4, "40": 3, "48": 1, "81": 1, "96": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1364, "range": [1, 10000], "values": {"57": 0, "68": 1, "81": 4, "96": 1, "114": 1, "190": 1, "226": 2, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 65, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 65, "1": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 378, "range": [1, 10000], "values": {"12": 0, "14": 1, "29": 2, "34": 3, "40": 2, "48": 2, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 1000], "values": {"0": 6, "1": 1, "2": 1, "4": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 284, "range": [1, 10000], "values": {"17": 0, "20": 1, "24": 6, "34": 3, "40": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 136, "range": [1, 10000], "values": {"5": 0, "6": 2, "8": 2, "12": 2, "14": 1, "20": 2, "24": 1, "29": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 26, "range": [1, 10000], "values": {"20": 0, "24": 1, "29": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 445, "range": [1, 10000], "values": {"0": 47, "1": 1, "3": 1, "4": 3, "8": 1, "14": 1, "17": 1, "24": 1, "29": 2, "34": 3, "40": 2, "48": 2, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 201, "range": [1, 100], "values": {"0": 1, "1": 1, "7": 2, "23": 3, "29": 1, "34": 1, "40": 1, "45": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 936791, "range": [1, 1000000], "values": {"3": 0, "4": 1, "6": 1, "13": 1, "15": 1, "17": 3, "19": 6, "22": 1, "25": 4, "28": 1, "32": 5, "36": 3, "41": 4, "47": 8, "53": 38, "60": 45, "68": 71, "77": 177, "88": 388, "100": 772, "114": 960, "130": 736, "148": 359, "168": 121, "191": 84, "217": 108, "247": 74, "281": 54, "320": 38, "364": 36, "414": 47, "471": 65, "536": 100, "610": 75, "695": 16, "791": 13, "901": 10, "1026": 7, "1168": 1, "1330": 9, "1514": 5, "1724": 5, "1963": 5, "2235": 4, "2545": 3, "2898": 10, "3300": 4, "3758": 1, "4279": 7, "4872": 2, "5548": 5, "6317": 1, "7193": 1, "8190": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 887, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 1, "4": 1, "9": 1, "11": 1, "13": 1, "15": 24, "17": 1, "22": 2, "50": 1, "75": 1, "99": 1, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 65, "range": [1, 200], "values": {"3": 0, "4": 5, "5": 2, "6": 2, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 64053904, "range": [16, 2147483646], "values": {"0": 0, "16": 66528, "23": 1990, "34": 37, "41": 11, "50": 17, "61": 93, "74": 136, "90": 26, "109": 50, "132": 14, "160": 7, "194": 160, "235": 2139, "284": 1813, "344": 218, "416": 454, "503": 56164, "609": 3406, "737": 36, "892": 7634, "1080": 11996, "1307": 294, "1582": 41, "1915": 12, "2318": 24, "2805": 8, "3395": 4, "4109": 5, "4973": 7, "6019": 13, "7284": 39, "18910": 6, "22886": 25, "33521": 4, "40569": 5, "71914": 9, "87033": 5, "105331": 1, "154277": 4, "225968": 3, "273476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 1659315, "range": [1, 2147483646], "values": {"0": 0, "1": 68518, "3": 1920, "5": 357, "8": 58438, "12": 399, "19": 3669, "30": 19926, "47": 106, "73": 45, "113": 13, "176": 2, "1029": 25, "2485": 1, "6002": 11, "9328": 5, "14498": 3, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 2514333, "range": [1, 150000000], "values": {"0": 304, "1": 3471, "2": 5184, "3": 7139, "4": 8317, "5": 12329, "6": 9426, "7": 5552, "8": 16094, "10": 11600, "12": 10456, "14": 18472, "17": 14512, "20": 13305, "24": 6755, "29": 2616, "35": 1920, "42": 1853, "50": 1403, "60": 803, "72": 574, "87": 383, "105": 237, "126": 131, "151": 159, "182": 148, "219": 45, "263": 35, "316": 18, "380": 17, "457": 15, "549": 28, "660": 17, "793": 16, "953": 15, "1146": 11, "1378": 12, "1657": 13, "1992": 11, "2395": 4, "2879": 13, "3461": 7, "4161": 8, "5002": 2, "6013": 4, "7228": 4, "8689": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 491915268, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 8, "418095": 2, "445575": 6, "539334": 60, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 606679732, "range": [32768, 16777216], "values": {"741455": 0, "790188": 740, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 1765649948, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 2, "1696203": 2, "1807688": 1, "1926500": 6, "2053121": 21, "2188065": 143, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 326724084, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 3, "221217": 2, "235757": 6, "324110": 38, "345412": 107, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 38865920, "range": [1024, 16777216], "values": {"46831": 0, "49183": 94, "51654": 477, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 740, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 9875, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 227, "33": 37, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 228, "range": [1, 200000], "values": {"6": 0, "8": 2, "10": 4, "17": 6, "22": 1, "28": 1, "36": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 1776, "range": [1, 200000], "values": {"10": 0, "13": 4, "17": 4, "22": 5, "28": 2, "36": 1, "46": 2, "58": 3, "74": 4, "151": 1, "501": 1, "637": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 6, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 644, "range": [1, 100000], "values": {"0": 14, "1": 5, "5": 1, "16": 2, "20": 1, "25": 3, "31": 5, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 1606, "range": [1, 2], "values": {"0": 51, "1": 1606, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 1, "1": 5, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 384, "range": [1, 3], "values": {"0": 298, "2": 192, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 192, "range": [1, 2], "values": {"0": 0, "1": 192, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 1810, "range": [1, 60000], "values": {"0": 190, "874": 2, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2017, "range": [1, 16], "values": {"2": 0, "3": 19, "4": 490, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 893, "range": [1, 16], "values": {"1": 0, "2": 19, "3": 285, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 496, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 481, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 16, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 480, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 73587, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 9, "39": 11, "41": 13, "43": 22, "45": 11, "47": 23, "49": 18, "51": 9, "53": 19, "55": 17, "58": 24, "61": 10, "64": 10, "67": 11, "70": 4, "73": 12, "76": 13, "80": 11, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 28, "237": 13, "248": 9, "259": 9, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 71730, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 7, "37": 8, "39": 8, "41": 10, "43": 21, "45": 10, "47": 22, "49": 18, "51": 9, "53": 19, "55": 17, "58": 24, "61": 10, "64": 10, "67": 10, "70": 4, "73": 12, "76": 13, "80": 11, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 5, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 7, "158": 3, "165": 2, "173": 5, "181": 4, "189": 3, "198": 4, "207": 2, "217": 15, "227": 28, "237": 13, "248": 9, "259": 8, "271": 3, "283": 2, "296": 2, "310": 6, "324": 2, "339": 8, "355": 3, "371": 4, "388": 7, "406": 6, "425": 12, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 1111, "range": [1, 60000], "values": {"23": 0, "24": 1, "32": 1, "35": 3, "37": 1, "41": 2, "45": 1, "47": 2, "53": 1, "115": 1, "151": 1, "173": 1, "181": 1, "189": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 72476, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 8, "39": 11, "41": 11, "43": 22, "45": 10, "47": 21, "49": 18, "51": 9, "53": 18, "55": 17, "58": 24, "61": 10, "64": 10, "67": 11, "70": 4, "73": 12, "76": 13, "80": 11, "84": 11, "88": 16, "92": 8, "96": 7, "100": 7, "105": 8, "110": 2, "115": 4, "120": 6, "126": 5, "132": 3, "138": 2, "144": 1, "151": 6, "158": 3, "165": 2, "173": 4, "181": 3, "189": 3, "198": 4, "207": 2, "217": 15, "227": 28, "237": 13, "248": 9, "259": 9, "271": 3, "283": 2, "296": 3, "310": 6, "324": 2, "339": 8, "355": 4, "371": 4, "388": 7, "406": 6, "425": 13, "445": 1, "531": 2, "555": 3, "608": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1097854, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 10, "3314": 79, "3855": 17, "4484": 2, "5216": 119, "6067": 5, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 275, "range": [1, 2], "values": {"0": 234, "1": 275, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 984, "range": [1, 16], "values": {"3": 0, "4": 222, "8": 12, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 1908, "range": [1, 16], "values": {"3": 0, "4": 73, "8": 202, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 2707, "range": [1, 100000], "values": {"0": 185, "1": 111, "2": 30, "3": 8, "4": 3, "5": 3, "6": 2, "8": 19, "25": 6, "39": 1, "61": 1, "149": 1, "233": 1, "1404": 1, "1757": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 17591, "range": [1, 16], "values": {"0": 1, "1": 1, "2": 51, "8": 182, "9": 194, "10": 1423, "11": 4, "12": 1, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 1983, "range": [1, 2], "values": {"0": 5, "1": 1983, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 5401, "range": [1, 5000], "values": {"0": 191, "1": 6, "2": 3, "3": 4, "7": 4, "10": 2, "11": 3, "12": 2, "13": 1, "14": 3, "15": 6, "16": 3, "17": 5, "18": 2, "19": 5, "20": 2, "21": 4, "23": 5, "25": 4, "27": 2, "29": 3, "35": 3, "38": 3, "41": 1, "47": 3, "54": 2, "58": 1, "66": 1, "87": 2, "93": 1, "115": 1, "123": 1, "132": 2, "163": 4, "175": 1, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 1, "330": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 7555, "range": [1, 5000], "values": {"0": 122, "1": 3, "2": 2, "3": 5, "10": 1, "11": 2, "12": 5, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "44": 2, "71": 1, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 1, "142": 1, "163": 3, "188": 1, "202": 2, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 16200, "range": [1, 16], "values": {"0": 0, "1": 3465, "2": 150, "6": 1873, "7": 171, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 4781, "range": [1, 60000], "values": {"0": 3, "1": 19, "2": 13, "3": 6, "4": 1, "5": 1, "6": 2, "9": 3, "11": 6, "14": 5, "17": 7, "21": 11, "26": 17, "32": 12, "40": 11, "50": 6, "62": 4, "77": 8, "95": 5, "118": 3, "146": 3, "181": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 79109, "range": [1, 60000], "values": {"0": 228, "1": 330, "2": 45, "3": 16, "4": 3, "5": 2, "6": 5, "7": 12, "9": 12, "11": 46, "14": 146, "17": 138, "21": 149, "26": 175, "32": 219, "40": 100, "50": 63, "62": 47, "77": 46, "95": 59, "118": 42, "146": 36, "181": 27, "224": 28, "278": 26, "345": 8, "428": 1, "658": 1, "816": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 518, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 268, "range": [1, 60000], "values": {"0": 2457, "1": 48, "2": 18, "3": 9, "7": 1, "11": 2, "14": 1, "17": 2, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 2569, "range": [1, 60000], "values": {"17": 0, "21": 1, "26": 1, "40": 1, "77": 1, "146": 2, "181": 4, "531": 2, "658": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 565, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 9709, "range": [1, 50], "values": {"0": 41, "3": 13, "4": 267, "6": 15, "8": 1064, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 280, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 9, "range": [1, 1000], "values": {"2": 0, "3": 1, "6": 1, "7": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 9, "range": [1, 1000], "values": {"2": 0, "3": 1, "6": 1, "7": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 10, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 3, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 14, "range": [1, 50], "values": {"1": 0, "2": 7, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 4, "range": [1, 50], "values": {"0": 0, "1": 4, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 1, "range": [1, 5000], "values": {"0": 5, "1": 1, "2": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 5, "1": 1, "2": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 100, "range": [1, 1000], "values": {"0": 1, "1": 2, "29": 2, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 4, "1": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 58, "range": [1, 5000], "values": {"0": 3, "55": 1, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1273, "range": [50, 1000], "values": {"69": 0, "77": 1, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 26, "range": [50, 10000], "values": {"0": 3, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 74, "range": [1, 50], "values": {"36": 0, "37": 2, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 462, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 3, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 169, "range": [1, 1000], "values": {"9": 0, "13": 1, "19": 3, "27": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 80, "range": [1, 1000], "values": {"0": 0, "1": 1, "2": 1, "6": 1, "13": 1, "39": 1, "56": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 379, "range": [1, 1000], "values": {"39": 0, "56": 2, "115": 2, "165": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 10, "range": [1, 100], "values": {"3": 0, "4": 1, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 8, "range": [1, 100], "values": {"0": 0, "1": 4, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 7425, "range": [1, 10000], "values": {"708": 0, "759": 1, "860": 1, "1011": 1, "1466": 2, "1718": 1, "1769": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 8454, "range": [1, 10000], "values": {"0": 389, "1": 3845, "2": 1053, "3": 88, "4": 32, "5": 23, "6": 22, "7": 11, "8": 22, "10": 12, "12": 13, "14": 9, "17": 1, "20": 7, "24": 3, "40": 6, "48": 2, "96": 3, "114": 2, "135": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 4507, "range": [1, 10000], "values": {"0": 305, "1": 1813, "2": 639, "3": 57, "4": 22, "5": 15, "6": 14, "7": 6, "8": 10, "10": 8, "12": 8, "14": 6, "17": 1, "20": 3, "24": 1, "29": 1, "40": 3, "48": 2, "96": 1, "114": 1, "135": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 82, "range": [1, 2000], "values": {"0": 172, "1": 8, "2": 2, "3": 3, "5": 1, "7": 2, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 122, "range": [1, 50], "values": {"1": 0, "2": 61, "3": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 311, "range": [1, 10000], "values": {"0": 242, "1": 311, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 496, "range": [1, 10000], "values": {"0": 464, "1": 28, "2": 23, "3": 13, "4": 12, "5": 1, "7": 8, "8": 19, "10": 10, "12": 1, "14": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 9091, "range": [1, 64], "values": {"13": 0, "14": 19, "18": 485, "19": 5, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 6432, "range": [1, 36], "values": {"22": 0, "23": 1, "29": 221, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1365, "range": [1, 16], "values": {"3": 0, "4": 91, "7": 143, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 1724, "range": [1, 24], "values": {"11": 0, "12": 141, "16": 2, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 2093, "range": [1, 36], "values": {"22": 0, "23": 91, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 7, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1197, "range": [1, 8], "values": {"0": 0, "1": 275, "2": 7, "4": 227, "5": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 254, "range": [1, 24], "values": {"0": 0, "1": 254, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 726, "range": [1, 10], "values": {"0": 0, "1": 726, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 726, "range": [1, 10], "values": {"1": 0, "2": 36, "3": 218, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 782, "range": [1, 10], "values": {"0": 0, "1": 712, "5": 14, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 254, "range": [1, 10], "values": {"0": 0, "1": 254, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 10582, "range": [1, 256], "values": {"13": 0, "14": 3, "15": 79, "20": 114, "89": 11, "116": 23, "119": 2, "145": 22, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 24, "range": [1, 2], "values": {"0": 0, "1": 24, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 91, "range": [1, 512], "values": {"12": 0, "13": 7, "14": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 62, "range": [1, 512], "values": {"30": 0, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 254, "range": [1, 4], "values": {"0": 0, "1": 254, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 21, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 21, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 21, "1": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 10954, "range": [1, 30000], "values": {"12": 0, "19": 111, "29": 143, "45": 2, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 12, "range": [1, 10], "values": {"0": 0, "1": 12, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 12, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 11884, "range": [1, 1000], "values": {"0": 4, "1": 227, "2": 375, "3": 503, "4": 382, "5": 397, "6": 184, "7": 113, "8": 104, "9": 59, "10": 32, "11": 29, "12": 37, "14": 24, "16": 8, "18": 9, "20": 11, "23": 1, "26": 3, "29": 2, "33": 1, "42": 2, "53": 1, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 411524321, "range": [1, 5000], "values": {"13": 0, "15": 1, "18": 3, "21": 5, "25": 7, "29": 14, "34": 20, "40": 36, "47": 26, "55": 39, "64": 38, "75": 74, "88": 287, "103": 1653, "120": 154, "140": 55, "164": 39, "192": 27, "224": 8, "262": 11, "306": 6, "357": 1, "417": 2, "487": 2, "569": 1, "777": 1, "1059": 1, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 412453253, "range": [8, 792], "values": {"8": 0, "16": 1, "32": 2, "40": 1, "48": 6, "56": 1, "64": 4, "72": 4, "80": 1, "88": 2, "96": 20, "104": 538, "112": 1122, "120": 291, "128": 79, "136": 35, "144": 22, "152": 14, "160": 8, "168": 9, "176": 3, "184": 5, "200": 4, "208": 13, "216": 16, "224": 15, "232": 11, "240": 9, "248": 6, "256": 8, "264": 4, "272": 3, "280": 3, "288": 1, "296": 4, "304": 2, "312": 2, "320": 3, "328": 5, "336": 2, "344": 3, "352": 1, "360": 3, "384": 1, "424": 2, "432": 3, "456": 2, "464": 2, "472": 1, "480": 1, "488": 2, "504": 1, "512": 1, "528": 1, "536": 2, "544": 1, "552": 3, "560": 3, "584": 2, "600": 1, "624": 1, "632": 1, "648": 1, "792": 5}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 716349, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 1, "40": 1, "47": 4, "55": 3, "64": 6, "75": 3, "88": 8, "103": 1419, "120": 337, "140": 38, "164": 13, "192": 32, "224": 45, "262": 15, "306": 14, "357": 5, "417": 11, "487": 14, "569": 6, "777": 1, "5000": 1}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 111, "range": [1, 5000], "values": {"0": 0, "1": 10, "2": 32, "3": 2, "9": 1, "21": 1, "23": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 60, "1": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 60000], "values": {"92": 0, "101": 1, "111": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 30023, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "30144": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 19, "range": [1, 50], "values": {"1": 0, "2": 4, "11": 1, "12": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 7793, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 378, "8": 2, "9": 4, "12": 1, "14": 2, "17": 363, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 16, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1142, "range": [1, 50], "values": {"1": 0, "2": 12, "5": 12, "6": 168, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 3844, "range": [1, 50], "values": {"11": 0, "12": 12, "20": 185, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 6, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 732, "range": [1, 50], "values": {"0": 0, "1": 732, "2": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 20, "range": [1, 2000], "values": {"0": 13, "1": 6, "2": 3, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 74, "range": [1, 2000], "values": {"0": 90, "1": 7, "2": 3, "3": 1, "8": 5, "9": 2, "10": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"0": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 131, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 1, "1": 2, "2": 3, "3": 1, "4": 1, "6": 2, "7": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 2, "1": 1, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 147, "range": [1, 2000], "values": {"0": 1, "1": 91, "2": 14, "3": 4, "4": 4, "5": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 4, "range": [1, 2000], "values": {"0": 2, "1": 2, "2": 1, "3": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2049, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "41": 1, "45": 1, "47": 2, "53": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 26246, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 16, "45": 6, "47": 11, "49": 8, "51": 5, "53": 10, "55": 13, "58": 13, "61": 6, "64": 6, "67": 5, "70": 2, "73": 6, "76": 4, "80": 4, "84": 5, "88": 5, "92": 1, "96": 3, "100": 1, "105": 2, "110": 1, "120": 5, "126": 1, "132": 2, "138": 1, "151": 4, "158": 2, "165": 1, "173": 2, "181": 3, "189": 2, "217": 2, "227": 3, "248": 4, "259": 3, "271": 1, "310": 4, "324": 1, "339": 3, "355": 1, "371": 2, "388": 1, "406": 2, "425": 3, "445": 1, "531": 1, "555": 2, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 44945, "range": [1, 60000], "values": {"32": 0, "33": 1, "37": 1, "39": 4, "41": 7, "43": 7, "45": 5, "47": 10, "49": 10, "51": 4, "53": 7, "55": 6, "58": 12, "61": 4, "64": 4, "67": 6, "70": 2, "73": 6, "76": 9, "80": 7, "84": 6, "88": 11, "92": 7, "96": 4, "100": 6, "105": 6, "110": 1, "115": 4, "120": 1, "126": 5, "132": 1, "138": 1, "144": 1, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 2, "217": 13, "227": 25, "237": 13, "248": 5, "259": 6, "271": 2, "283": 2, "296": 4, "310": 2, "324": 1, "339": 5, "355": 3, "371": 2, "388": 6, "406": 4, "425": 10, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 549, "range": [1, 60000], "values": {"508": 0, "531": 1, "555": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 1, "range": [1, 32], "values": {"0": 311, "1": 1, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 2940, "range": [1, 50], "values": {"0": 182, "1": 168, "2": 1378, "3": 4, "4": 1, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 103, "range": [1, 50], "values": {"0": 1, "1": 1, "2": 51, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 116, "range": [1, 50], "values": {"0": 0, "1": 26, "2": 45, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 198, "range": [1, 100], "values": {"17": 0, "18": 11, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 301, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 462, "range": [1, 100], "values": {"41": 0, "42": 11, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 444, "range": [1, 2], "values": {"0": 0, "1": 444, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1213, "range": [1, 2], "values": {"0": 5, "1": 1213, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 3096, "range": [1, 50], "values": {"5": 0, "6": 516, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 12144, "range": [1, 50], "values": {"5": 0, "6": 2010, "7": 12, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 1064, "range": [20, 120000], "values": {"601": 0, "821": 1, "1121": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 2, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 2, "range": [1, 16], "values": {"0": 0, "1": 2, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 500, "range": [1, 100000], "values": {"30": 0, "45": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 2, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 814, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 2, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 6, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 4, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 4, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 187, "range": [1, 60000], "values": {"0": 8, "1": 1, "2": 1, "5": 3, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11231, "range": [1, 60000], "values": {"0": 1, "1": 69, "2": 6, "5": 1, "8": 2, "21": 195, "34": 60, "54": 1, "137": 16, "219": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 11418, "range": [1, 60000], "values": {"0": 9, "1": 70, "2": 7, "5": 4, "8": 2, "21": 195, "34": 60, "54": 1, "137": 17, "219": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 10927, "range": [1, 5000], "values": {"0": 67, "1": 9, "2": 1, "4": 1, "6": 1, "18": 19, "26": 223, "37": 13, "53": 1, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11078, "range": [1, 5000], "values": {"0": 77, "1": 9, "2": 2, "3": 2, "4": 1, "6": 1, "18": 19, "26": 223, "37": 13, "53": 1, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 151, "range": [1, 5000], "values": {"0": 10, "2": 1, "3": 2, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 6, "range": [1, 5000], "values": {"0": 349, "1": 1, "4": 1, "6": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 36, "range": [1, 5000], "values": {"0": 357, "1": 6, "4": 1, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 30, "range": [1, 5000], "values": {"0": 8, "1": 5, "18": 1, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 60000], "values": {"92": 0, "101": 1, "111": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 30023, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "30144": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 875, "range": [1, 50], "values": {"0": 102, "1": 273, "2": 301, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 2461, "range": [1, 50], "values": {"0": 174, "1": 591, "2": 935, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 47, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 18, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 142, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2229, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 21, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 29, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4438, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3050, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 216, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 149, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 248, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7108, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 20, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 7, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 101, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 349, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 464, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 18, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1846, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1104, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 92276, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 15, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 470, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 210, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 22, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 40, "range": [1, 50], "values": {"4": 0, "5": 8, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 988, "range": [1, 50], "values": {"16": 0, "17": 19, "19": 35, "20": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 434, "range": [1, 50], "values": {"16": 0, "17": 16, "18": 9, "19": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 14, "range": [1, 50], "values": {"1": 0, "2": 7, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 362, "range": [1, 50], "values": {"1": 0, "2": 181, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 76, "range": [1, 50], "values": {"1": 0, "2": 38, "3": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 158, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 9, "17": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 4, "range": [1, 50], "values": {"1": 0, "2": 2, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 211, "range": [1, 50], "values": {"15": 0, "16": 12, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 16, "range": [1, 50], "values": {"1": 0, "2": 8, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 92, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 5, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 3501, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 178, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1426, "range": [1, 50], "values": {"15": 0, "16": 6, "19": 70, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 46, "range": [1, 50], "values": {"1": 0, "2": 23, "3": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1158, "range": [1, 50], "values": {"3": 0, "4": 107, "16": 18, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 85101, "subsessionLength": 44602, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}