{"type": "main", "id": "888c913c-07c2-495c-acca-cfa7e28cb9e6", "creationDate": "2025-05-25T12:54:35.784Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250421163656", "name": "Firefox", "version": "138.0", "displayVersion": "138.0", "vendor": "Mozilla", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 94175, "start": 20, "main": 48, "selectProfile": 254, "afterProfileLocked": 260, "startupCrashDetectionBegin": 593, "startupCrashDetectionEnd": 32789, "firstPaint": 1781, "firstPaint2": 1776, "sessionRestoreInit": 869, "sessionRestored": 1853, "createTopLevelWindow": 886, "AMI_startup_begin": 604, "XPI_startup_begin": 610, "XPI_bootstrap_addons_begin": 616, "XPI_bootstrap_addons_end": 638, "XPI_startup_end": 638, "AMI_startup_end": 639, "XPI_finalUIStartup": 868, "sessionRestoreInitialized": 879, "delayedStartupStarted": 1787, "delayedStartupFinished": 1809, "sessionRestoreRestoring": 1810, "debuggerAttached": 0, "activeTicks": 140}, "processes": {"parent": {"scalars": {"browser.engagement.unfiltered_uri_count": 55, "browser.engagement.tab_open_event_count": 8, "browser.engagement.max_concurrent_tab_count": 7, "urlbar.zeroprefix.exposure": 3, "dom.contentprocess.os_priority_raised": 332, "browser.engagement.unique_domains_count": 4, "dom.contentprocess.os_priority_lowered": 64, "blocklist.mlbf_source": "dump_match", "cookie.banners.service_detect_only": false, "urlbar.zeroprefix.abandonment": 2, "browser.engagement.window_open_event_count": 1, "dom.contentprocess.os_priority_change_considered": 160, "browser.engagement.active_ticks": 140, "urlbar.persistedsearchterms.view_count": 1, "browser.engagement.total_uri_count_normal_and_private_mode": 55, "power.total_thread_wakeups": 4062588, "media.element_in_page_count": 2, "browser.engagement.session_time_including_suspend": 94175102, "browser.engagement.total_uri_count": 55, "browser.engagement.max_concurrent_window_count": 2, "browser.engagement.session_time_excluding_suspend": 94175102, "power.total_cpu_time_ms": 1903840, "blocklist.mlbf_softblocks_source": "dump_match"}, "keyedScalars": {"browser.ui.interaction.tabs_bar": {"tabs-newtab-button": 1, "tab-close-button": 5}, "power.cpu_time_per_process_type_ms": {"parent.inactive": 1387644, "parent.active.playing-audio": 6189, "parent.active.playing-video": 82123, "parent.inactive.playing-video": 332413, "parent.active": 93090, "parent.inactive.playing-audio": 2381}, "browser.ui.interaction.pageaction_urlbar": {"addon0": 1}, "browser.search.content.unknown": {"google:tagged-follow-on:firefox-b-lm": 1}, "browser.ui.interaction.nav_bar": {"urlbar-input": 3}, "browser.engagement.navigation.urlbar": {"search_enter": 1}, "cookie.banners.private_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "power.wakeups_per_process_type": {"parent.inactive": 2524770, "parent.active.playing-audio": 18891, "parent.active.playing-video": 216688, "parent.inactive.playing-video": 1025282, "parent.active": 270977, "parent.inactive.playing-audio": 5980}, "cookie.banners.normal_window_service_mode": {"disabled": true, "invalid": false, "reject_or_accept": false, "reject": false}, "networking.data_transferred_v3_kb": {"Y1_N1": 8865, "Y0_N1Sys": 670, "Y2_N3Oth": 154976}, "telemetry.event_counts": {"pwmgr#saved_login_used#form_login": 1}, "contextual.services.topsites.impression": {"newtab_1": 1, "newtab_2": 1, "newtab_3": 1}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 5852, "successful": 523}, "browser.search.content.urlbar": {"google:tagged:firefox-b-lm": 1}}}, "content": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 23490, "range": [1, 10000], "values": {"0": 2, "1": 29, "2": 392, "3": 311, "4": 104, "5": 34, "6": 16, "7": 57, "8": 431, "10": 29, "12": 11, "14": 49, "17": 226, "20": 120, "24": 313, "29": 12, "34": 5, "40": 2, "48": 7, "57": 3, "68": 2, "81": 1, "96": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 1272, "range": [1, 100], "values": {"2": 0, "3": 326, "4": 37, "5": 12, "6": 3, "7": 6, "11": 2, "14": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 898, "range": [1, 100], "values": {"0": 4, "1": 133, "2": 45, "3": 176, "4": 15, "5": 7, "6": 2, "7": 2, "11": 2, "14": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 75173, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 2, "5": 1, "7": 1, "8": 2, "10": 1, "12": 5, "14": 4, "17": 1, "20": 2, "24": 11, "29": 13, "34": 7, "40": 13, "48": 11, "57": 11, "68": 11, "81": 2, "96": 3, "114": 6, "135": 2, "160": 29, "190": 140, "226": 34, "268": 25, "318": 24, "378": 10, "449": 5, "533": 3, "633": 1, "752": 1, "894": 1, "1062": 1, "1262": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 124401, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 3, "40": 8, "48": 5, "57": 10, "68": 19, "81": 20, "96": 17, "114": 10, "135": 7, "160": 4, "190": 1, "226": 72, "268": 93, "318": 40, "378": 12, "449": 9, "533": 12, "633": 13, "752": 12, "894": 8, "1062": 5, "1262": 2, "1500": 3, "1782": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 15, "range": [1, 2], "values": {"0": 5749, "1": 15, "2": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 124, "range": [1, 2], "values": {"0": 5640, "1": 124, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 15461, "range": [1, 10000], "values": {"1": 0, "2": 3, "4": 3, "5": 1, "8": 2, "10": 2, "12": 9, "14": 14, "17": 15, "20": 12, "24": 54, "29": 32, "34": 11, "40": 18, "48": 200, "57": 3, "68": 3, "81": 1, "135": 1, "226": 2, "268": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 430, "range": [1, 1000], "values": {"0": 157, "1": 161, "2": 38, "3": 10, "4": 8, "5": 3, "6": 3, "7": 1, "10": 1, "16": 1, "18": 1, "20": 1, "23": 1, "26": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 60473, "range": [1, 10000], "values": {"0": 6, "1": 2, "2": 1, "4": 3, "5": 4, "7": 1, "8": 7, "14": 1, "17": 3, "20": 18, "24": 18, "29": 5, "34": 16, "40": 11, "48": 6, "57": 5, "68": 2, "81": 1, "96": 2, "114": 1, "135": 19, "160": 149, "190": 36, "226": 30, "268": 20, "318": 9, "378": 5, "449": 1, "533": 1, "633": 1, "752": 2, "894": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 29428, "range": [1, 10000], "values": {"0": 0, "1": 3, "2": 9, "3": 3, "4": 3, "5": 7, "6": 9, "7": 11, "8": 15, "10": 7, "12": 11, "14": 5, "17": 6, "20": 7, "24": 3, "29": 5, "40": 2, "48": 1, "57": 1, "68": 77, "81": 107, "96": 24, "114": 35, "135": 19, "160": 9, "190": 4, "226": 1, "378": 1, "533": 1, "633": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 790, "range": [1, 10000], "values": {"1": 0, "2": 1, "5": 2, "17": 2, "24": 2, "29": 2, "48": 1, "68": 1, "226": 2, "268": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 74245, "range": [1, 10000], "values": {"0": 1685, "1": 108, "2": 118, "3": 83, "4": 93, "5": 381, "6": 227, "7": 200, "8": 420, "10": 273, "12": 362, "14": 333, "17": 156, "20": 214, "24": 311, "29": 63, "34": 68, "40": 58, "48": 599, "57": 5, "68": 3, "81": 1, "135": 1, "226": 2, "268": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 3896, "range": [1, 100], "values": {"0": 216, "1": 58, "7": 16, "12": 25, "18": 13, "23": 6, "29": 6, "34": 11, "40": 8, "45": 5, "51": 2, "56": 1, "62": 1, "67": 5, "73": 3, "78": 3, "84": 2, "89": 3, "95": 2, "100": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 28099619, "range": [1, 1000000], "values": {"2": 0, "3": 3, "4": 3, "5": 6, "6": 1, "9": 1, "10": 12, "11": 36, "13": 102, "15": 232, "17": 207, "19": 347, "22": 127, "25": 16, "28": 6, "32": 2, "36": 4, "41": 12, "47": 6, "53": 24, "60": 52, "68": 28, "77": 76, "88": 37, "100": 49, "114": 80, "130": 144, "148": 268, "168": 429, "191": 991, "217": 845, "247": 621, "281": 466, "320": 425, "364": 545, "414": 1299, "471": 1878, "536": 1677, "610": 1382, "695": 1278, "791": 2099, "901": 2716, "1026": 3870, "1168": 2806, "1330": 580, "1514": 320, "1724": 255, "1963": 172, "2235": 131, "2545": 77, "2898": 74, "3300": 46, "3758": 53, "4279": 30, "4872": 30, "5548": 20, "6317": 15, "7193": 306, "8190": 349, "9326": 44, "10619": 8, "12092": 7, "13769": 5, "15678": 6, "17852": 6, "20328": 3, "23147": 1, "26357": 1, "34174": 2, "44309": 2, "50453": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 47312, "range": [1, 2000], "values": {"0": 1847, "1": 426, "2": 269, "3": 303, "4": 169, "5": 118, "6": 98, "7": 66, "8": 63, "9": 44, "10": 46, "11": 74, "13": 86, "15": 1158, "17": 154, "19": 146, "22": 53, "25": 50, "29": 32, "33": 33, "38": 35, "44": 27, "50": 12, "57": 10, "65": 12, "75": 8, "86": 5, "99": 13, "113": 7, "130": 2, "149": 2, "171": 1, "196": 2, "225": 6, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 5763, "range": [1, 200], "values": {"2": 0, "3": 14, "4": 49, "5": 14, "6": 13, "7": 7, "8": 12, "9": 94, "10": 21, "11": 7, "12": 26, "13": 13, "14": 10, "15": 5, "16": 3, "17": 5, "18": 6, "19": 13, "21": 7, "23": 3, "25": 4, "27": 1, "29": 2, "31": 8, "34": 9, "37": 4, "40": 8, "43": 6, "46": 3, "50": 7, "54": 2, "58": 4, "63": 4, "68": 1, "73": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 130192856, "range": [16, 2147483646], "values": {"0": 0, "16": 73193, "23": 5815, "28": 181, "34": 63, "41": 210, "50": 67, "61": 323, "74": 94, "90": 123, "109": 252, "132": 76, "160": 558, "194": 228, "235": 396, "284": 70048, "344": 54259, "416": 61, "503": 420, "609": 70328, "737": 787, "892": 1550, "1080": 313, "1307": 350, "1582": 588, "1915": 632, "2318": 311, "2805": 416, "3395": 22, "4109": 316, "4973": 617, "6019": 16, "7284": 128, "8815": 428, "10668": 43, "12911": 15, "15625": 11, "18910": 67, "22886": 7, "27698": 50, "33521": 9, "40569": 14, "49098": 301, "71914": 9, "87033": 2, "105331": 3, "127476": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 4869488, "range": [1, 2147483646], "values": {"0": 0, "1": 79342, "2": 23, "3": 1068, "5": 427, "8": 1050, "12": 140586, "19": 54416, "30": 2542, "47": 718, "73": 1211, "113": 318, "176": 1341, "274": 110, "426": 138, "662": 76, "1029": 26, "1599": 7, "2485": 297, "3862": 3, "6002": 1, "9328": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 3957885, "range": [1, 150000000], "values": {"0": 4001, "1": 15802, "2": 22910, "3": 7531, "4": 4570, "5": 3807, "6": 7476, "7": 13469, "8": 40925, "10": 42113, "12": 39195, "14": 32682, "17": 10419, "20": 7273, "24": 14301, "29": 7776, "35": 4471, "42": 1448, "50": 848, "60": 558, "72": 312, "87": 270, "105": 306, "126": 253, "151": 188, "182": 332, "219": 93, "263": 105, "316": 90, "380": 14, "457": 17, "549": 19, "660": 19, "793": 11, "953": 12, "1146": 12, "1378": 10, "1657": 10, "1992": 5, "2395": 5, "2879": 7, "3461": 8, "4161": 6, "5002": 4, "6013": 4, "7228": 12, "8689": 1, "10445": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 1671757568, "range": [32768, 16777216], "values": {"66000": 0, "70338": 2, "74961": 9, "103055": 25, "109828": 24, "117047": 102, "124740": 117, "132939": 10, "141677": 50, "150989": 11, "160913": 17, "171489": 133, "182760": 133, "194772": 259, "207574": 499, "221217": 131, "235757": 552, "251252": 443, "267766": 45, "285365": 4, "368115": 1, "392310": 11, "418095": 8, "474861": 22, "506072": 92, "539334": 96, "574782": 105, "612560": 140, "652821": 166, "695728": 488, "741455": 280, "790188": 145, "842124": 66, "897474": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 2228988888, "range": [32768, 16777216], "values": {"132939": 0, "141677": 5, "171489": 158, "182760": 6, "267766": 1038, "285365": 737, "304121": 8, "324110": 1, "345412": 148, "368115": 168, "418095": 1, "445575": 1, "506072": 113, "539334": 228, "574782": 104, "612560": 113, "652821": 120, "695728": 123, "741455": 224, "1019325": 890, "1086321": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 1241686260, "range": [32768, 16777216], "values": {"0": 60, "32768": 8, "34922": 97, "37217": 5, "39663": 5, "42270": 49, "45048": 69, "48009": 1, "51164": 5, "54527": 5, "58111": 41, "61930": 7, "66000": 1, "70338": 64, "74961": 59, "79888": 113, "85139": 13, "90735": 11, "96699": 248, "103055": 250, "109828": 247, "117047": 39, "124740": 58, "132939": 436, "141677": 478, "150989": 43, "160913": 84, "171489": 62, "182760": 5, "194772": 2, "207574": 1, "285365": 1, "304121": 17, "324110": 2, "392310": 42, "418095": 75, "445575": 81, "474861": 85, "506072": 110, "539334": 121, "574782": 363, "612560": 448, "652821": 142, "695728": 119, "741455": 14, "790188": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 777747456, "range": [1024, 16777216], "values": {"0": 0, "1024": 11, "8022": 1, "8848": 159, "9759": 3, "19382": 11, "20356": 4, "22453": 1, "31641": 223, "33230": 216, "34899": 101, "36652": 206, "38493": 202, "40427": 203, "42458": 204, "44591": 181, "46831": 370, "49183": 55, "51654": 219, "54249": 26, "56974": 1, "59836": 3, "62842": 1, "65999": 1, "76453": 1, "260322": 32, "273398": 43, "287131": 43, "301554": 47, "316701": 46, "332609": 52, "349316": 865, "366862": 132, "385290": 59, "404644": 61, "424970": 61, "446317": 68, "468736": 70, "492281": 204, "517009": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 4186, "1": 0}}, "CANVAS_WEBGL_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 8, "range": [1, 2], "values": {"0": 0, "1": 8, "2": 0}}, "TOTAL_CONTENT_PAGE_LOAD_TIME": {"bucket_count": 100, "histogram_type": 0, "sum": 28691, "range": [100, 30000], "values": {"644": 0, "683": 1, "724": 2, "767": 1, "862": 2, "969": 1, "1089": 1, "1223": 1, "1374": 1, "1456": 3, "1635": 1, "1733": 2, "1947": 1, "2064": 1, "2458": 1, "2761": 1, "2926": 0}}, "HTTP_PAGE_DNS_ISSUE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 30000], "values": {"0": 2, "1": 1, "5": 1, "24": 1, "29": 1, "35": 0}}, "HTTP_PAGE_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 69, "range": [1, 30000], "values": {"0": 2, "1": 2, "2": 1, "6": 1, "13": 1, "16": 1, "24": 1, "29": 0}}, "HTTP_PAGE_TLS_HANDSHAKE": {"bucket_count": 50, "histogram_type": 0, "sum": 328, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "24": 1, "43": 1, "77": 1, "140": 1, "171": 0}}, "HTTP_PAGE_TCP_CONNECTION_2": {"bucket_count": 50, "histogram_type": 0, "sum": 667, "range": [1, 30000], "values": {"0": 1, "35": 1, "43": 2, "63": 1, "94": 1, "140": 1, "209": 1, "255": 0}}, "HTTP_SUB_DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 358, "range": [1, 30000], "values": {"0": 7, "1": 7, "2": 2, "3": 1, "9": 1, "13": 1, "16": 2, "20": 1, "43": 1, "52": 2, "94": 1, "115": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 486, "range": [1, 50], "values": {"0": 2850, "2": 243, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 81869, "range": [1, 10000], "values": {"0": 1413, "1": 1440, "2": 2633, "3": 750, "4": 580, "5": 485, "6": 430, "7": 421, "8": 811, "10": 702, "12": 572, "14": 760, "17": 456, "20": 110, "24": 52, "29": 24, "34": 17, "40": 21, "48": 17, "57": 9, "68": 12, "81": 6, "96": 6, "114": 5, "135": 9, "190": 6, "226": 4, "268": 7, "318": 1, "533": 5, "633": 2, "752": 2, "894": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 44137, "range": [1, 10000], "values": {"0": 1237, "1": 679, "2": 1307, "3": 333, "4": 315, "5": 257, "6": 225, "7": 218, "8": 323, "10": 328, "12": 294, "14": 313, "17": 102, "20": 62, "24": 96, "29": 46, "34": 27, "40": 45, "48": 14, "57": 11, "68": 10, "81": 5, "96": 2, "114": 2, "135": 3, "160": 1, "190": 2, "226": 2, "268": 3, "318": 1, "533": 2, "633": 2, "752": 2, "894": 0}}, "VIDEO_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 1483337, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "1346410": 1, "1576847": 0}}, "VIDEO_HIDDEN_PLAY_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6282, "range": [1, 7200000], "values": {"0": 2, "5974": 1, "6947": 0}}, "MSE_SOURCE_BUFFER_TYPE": {"bucket_count": 51, "histogram_type": 5, "sum": 108, "range": [1, 50], "values": {"0": 0, "1": 56, "2": 8, "3": 8, "4": 3, "5": 0}}, "PWMGR_LOGIN_PAGE_SAFETY": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 4, "1": 0}}, "PWMGR_IS_USERNAME_ONLY_FORM": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 31, "1": 0}}, "WEBFONT_DOWNLOAD_TIME_AFTER_START": {"bucket_count": 50, "histogram_type": 0, "sum": 27339, "range": [1, 60000], "values": {"345": 0, "428": 5, "658": 4, "816": 2, "1012": 2, "1556": 1, "4555": 3, "5647": 0}}, "JS_PAGELOAD_EXECUTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5013, "range": [1, 60000], "values": {"62": 0, "68": 1, "111": 1, "122": 1, "135": 1, "221": 1, "244": 1, "297": 1, "328": 2, "362": 2, "399": 1, "485": 2, "792": 1, "874": 0}}, "JS_PAGELOAD_DELAZIFICATION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 8, "1": 0}}, "JS_PAGELOAD_BASELINE_COMPILE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 156, "range": [1, 10000], "values": {"2": 0, "3": 1, "4": 3, "6": 1, "7": 1, "8": 2, "11": 1, "12": 1, "14": 1, "15": 1, "17": 2, "25": 1, "27": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 293, "range": [1, 10000], "values": {"0": 4, "1": 1, "21": 1, "34": 1, "40": 2, "43": 1, "54": 2, "58": 0}}, "VIDEO_DROPPED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_DECODED_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_SINK_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "VIDEO_DROPPED_COMPOSITOR_FRAMES_PROPORTION_EXPONENTIAL": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 3, "1": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 74080, "range": [1, 1000], "values": {"0": 473, "1": 1207, "2": 2069, "3": 2843, "4": 2992, "5": 2092, "6": 1788, "7": 1193, "8": 934, "9": 374, "10": 146, "11": 97, "12": 92, "14": 77, "16": 46, "18": 23, "20": 32, "23": 21, "26": 14, "29": 6, "33": 3, "37": 2, "42": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8360, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "909": 4, "1243": 1, "1380": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8760, "range": [1, 100000], "values": {"540": 0, "599": 1, "738": 1, "819": 2, "1009": 4, "1380": 1, "1532": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 653, "range": [1, 5000], "values": {"0": 0, "1": 2, "2": 40, "3": 26, "4": 6, "5": 6, "6": 3, "7": 1, "8": 4, "10": 1, "11": 1, "15": 2, "17": 2, "29": 1, "31": 1, "38": 1, "47": 1, "142": 1, "152": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 124, "range": [1, 5000], "values": {"0": 40, "1": 18, "2": 4, "3": 2, "6": 1, "7": 1, "9": 3, "10": 1, "11": 1, "12": 1, "19": 1, "20": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6417, "range": [1, 50000], "values": {"4": 0, "5": 1, "13": 1, "131": 1, "144": 1, "158": 1, "174": 1, "309": 1, "412": 2, "454": 1, "500": 1, "550": 3, "605": 3, "666": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 13477, "range": [1, 50000], "values": {"15": 0, "17": 1, "61": 1, "666": 1, "733": 3, "807": 5, "888": 3, "977": 1, "1075": 1, "1183": 1, "1302": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 22277, "range": [1, 50000], "values": {"25": 0, "28": 1, "89": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 4, "1433": 1, "1577": 2, "1736": 2, "2104": 1, "2549": 1, "2806": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 23220, "range": [1, 50000], "values": {"45": 0, "50": 1, "144": 1, "666": 1, "733": 1, "807": 1, "977": 1, "1302": 2, "1433": 2, "1577": 2, "1736": 2, "2104": 2, "2549": 1, "2806": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 25042, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 25065, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 25280, "range": [1, 50000], "values": {"98": 0, "108": 1, "158": 1, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 2, "1736": 2, "1911": 1, "2316": 2, "2806": 1, "3089": 0}}, "TIME_TO_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5856, "range": [1, 50000], "values": {"98": 0, "108": 1, "144": 1, "309": 1, "412": 2, "454": 2, "500": 2, "550": 1, "605": 3, "666": 0}}, "PERF_PAGE_LOAD_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 11844, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 2, "1433": 3, "1577": 1, "1911": 1, "2104": 0}}, "PERF_PAGE_LOAD_TIME_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8476, "range": [1, 50000], "values": {"500": 0, "550": 1, "605": 1, "733": 1, "888": 4, "1183": 1, "1433": 1, "1577": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}, "PERF_FIRST_CONTENTFUL_PAINT_FROM_RESPONSESTART_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4624, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 2, "500": 2, "605": 1, "666": 1, "888": 1, "977": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 5341, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 2, "977": 1, "1183": 1, "1302": 0}}, "PERF_LARGEST_CONTENTFUL_PAINT_FROM_RESPONSE_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3570, "range": [1, 50000], "values": {"340": 0, "374": 1, "412": 1, "454": 1, "605": 1, "666": 1, "888": 1, "977": 0}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 6450, "range": [1, 50000], "values": {"0": 96, "1": 15, "2": 40, "3": 14, "4": 16, "5": 20, "6": 6, "7": 3, "8": 4, "9": 5, "10": 7, "11": 6, "12": 5, "13": 8, "14": 5, "15": 9, "17": 6, "19": 5, "21": 2, "23": 1, "25": 2, "31": 1, "34": 1, "37": 3, "41": 1, "50": 2, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "309": 1, "340": 1, "374": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 21635, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "6": 1, "7": 1, "9": 1, "12": 1, "14": 1, "15": 3, "17": 1, "23": 2, "25": 1, "28": 3, "31": 3, "34": 1, "37": 2, "41": 2, "45": 2, "61": 1, "74": 1, "108": 4, "119": 1, "131": 2, "158": 2, "232": 1, "255": 2, "281": 2, "340": 1, "374": 1, "412": 2, "454": 1, "605": 2, "733": 3, "807": 5, "977": 3, "1075": 1, "1302": 3, "1433": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 4821, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 2, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1433": 1, "1577": 0}}, "APZ_ZOOM_ACTIVITY": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 20, "1": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 414, "range": [1, 50], "values": {"0": 986, "1": 53, "2": 163, "7": 5, "8": 0}}}, "keyedHistograms": {"HTTP3_TLS_HANDSHAKE": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 30000], "values": {"2": 0, "3": 1, "4": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 14, "range": [1, 30000], "values": {"5": 0, "6": 1, "7": 1, "9": 0}}}, "HTTP3_OPEN_TO_FIRST_SENT": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 2382, "range": [1, 30000], "values": {"1": 0, "2": 4, "3": 12, "4": 7, "5": 2, "6": 1, "7": 19, "9": 6, "11": 3, "13": 2, "16": 3, "20": 1, "24": 3, "29": 3, "43": 2, "52": 2, "77": 1, "94": 5, "115": 2, "140": 1, "209": 1, "255": 1, "311": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 414, "range": [1, 30000], "values": {"5": 0, "6": 1, "11": 1, "16": 1, "20": 2, "35": 1, "77": 1, "171": 1, "209": 0}}}, "HTTP3_FIRST_SENT_TO_LAST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9765, "range": [1, 30000], "values": {"9": 0, "11": 1, "16": 2, "20": 1, "24": 1, "29": 4, "35": 7, "43": 3, "52": 2, "63": 10, "77": 10, "94": 9, "115": 7, "140": 8, "171": 1, "209": 9, "255": 4, "311": 1, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1745, "range": [1, 30000], "values": {"63": 0, "77": 1, "94": 2, "115": 1, "140": 1, "171": 1, "311": 1, "564": 1, "688": 0}}}, "HTTP3_OPEN_TO_FIRST_RECEIVED": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 9026, "range": [1, 30000], "values": {"13": 0, "16": 2, "20": 3, "24": 3, "29": 1, "35": 7, "43": 2, "52": 6, "63": 3, "77": 14, "94": 9, "115": 11, "140": 9, "171": 4, "209": 1, "311": 5, "379": 1, "462": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 1300, "range": [1, 30000], "values": {"77": 0, "94": 2, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "311": 0}}}, "HTTP3_COMPLETE_LOAD": {"uses_http3_sub": {"bucket_count": 50, "histogram_type": 0, "sum": 12176, "range": [1, 30000], "values": {"13": 0, "16": 1, "20": 1, "24": 3, "29": 1, "35": 3, "43": 2, "52": 4, "63": 3, "77": 13, "94": 9, "115": 10, "140": 10, "171": 2, "209": 6, "255": 4, "311": 5, "379": 3, "688": 1, "839": 0}}, "uses_http3_page": {"bucket_count": 50, "histogram_type": 0, "sum": 2164, "range": [1, 30000], "values": {"94": 0, "115": 2, "140": 1, "171": 1, "209": 1, "255": 1, "379": 1, "564": 1, "688": 0}}}, "HTTP3_PERF_PAGE_LOAD_TIME_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 2712, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "977": 1, "1075": 0}}}, "HTTP3_PERF_FIRST_CONTENTFUL_PAINT_MS": {"http3": {"bucket_count": 100, "histogram_type": 0, "sum": 1458, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 0}}}, "H3P_PERF_PAGE_LOAD_TIME_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1042, "range": [1, 50000], "values": {"888": 0, "977": 1, "1075": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 1670, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 1, "888": 0}}}, "H3P_PERF_FIRST_CONTENTFUL_PAINT_MS": {"with_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 794, "range": [1, 50000], "values": {"666": 0, "733": 1, "807": 0}}, "without_priority": {"bucket_count": 100, "histogram_type": 0, "sum": 664, "range": [1, 50000], "values": {"550": 0, "605": 1, "666": 0}}}, "NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 3668198, "range": [1, 2000], "values": {"1": 0, "2": 13, "3": 27, "4": 53, "5": 109, "6": 115, "7": 110, "8": 205, "9": 231, "10": 115, "11": 80, "13": 32, "15": 25, "17": 17, "19": 10, "22": 9, "25": 19, "29": 21, "33": 23, "38": 22, "44": 14, "50": 17, "57": 4, "65": 21, "75": 6, "86": 2, "113": 2, "130": 3, "149": 2, "171": 2, "196": 1, "339": 3, "1011": 255, "1159": 31, "1328": 1, "1745": 325, "2000": 1253}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1102, "range": [1, 2000], "values": {"3": 0, "4": 2, "15": 1, "44": 1, "50": 1, "86": 3, "99": 2, "113": 3, "130": 1, "149": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 13262, "range": [1, 2000], "values": {"1": 0, "2": 38, "3": 40, "4": 24, "5": 14, "6": 7, "7": 7, "8": 29, "9": 18, "10": 15, "11": 10, "13": 3, "15": 3, "19": 3, "22": 3, "25": 2, "29": 3, "33": 9, "38": 13, "44": 1, "65": 1, "75": 1, "86": 2, "99": 2, "149": 1, "171": 2, "196": 1, "2000": 2}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 14757, "range": [1, 2000], "values": {"1": 0, "2": 7, "3": 17, "4": 11, "5": 11, "6": 13, "7": 31, "8": 23, "9": 17, "10": 15, "11": 12, "13": 5, "15": 4, "17": 4, "19": 7, "22": 11, "25": 13, "29": 12, "33": 42, "38": 17, "44": 6, "50": 5, "57": 3, "65": 8, "75": 1, "86": 6, "99": 20, "113": 21, "130": 7, "149": 1, "196": 8, "770": 1, "882": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 4752, "range": [1, 2000], "values": {"1": 0, "2": 2, "3": 7, "4": 1, "5": 1, "6": 2, "7": 2, "8": 1, "11": 1, "22": 1, "29": 1, "50": 1, "86": 1, "113": 1, "2000": 1}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 24, "range": [1, 2000], "values": {"2": 0, "3": 5, "4": 1, "5": 1, "6": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 149, "range": [1, 2000], "values": {"130": 0, "149": 1, "171": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 1278, "range": [1, 2000], "values": {"1": 0, "2": 3, "3": 2, "4": 3, "5": 1, "7": 1, "11": 3, "15": 3, "17": 1, "19": 1, "29": 1, "33": 8, "38": 11, "44": 7, "50": 1, "57": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 36, "range": [1, 2000], "values": {"8": 0, "9": 1, "25": 1, "29": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 647, "range": [1, 2000], "values": {"86": 0, "99": 2, "130": 3, "149": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2101856, "range": [1, 2000], "values": {"0": 5, "1": 29, "2": 180, "3": 450, "4": 186, "5": 71, "6": 33, "7": 22, "8": 22, "9": 21, "10": 23, "11": 28, "13": 28, "15": 24, "17": 18, "19": 14, "22": 16, "25": 14, "29": 12, "33": 15, "38": 12, "44": 13, "50": 9, "57": 13, "65": 13, "75": 10, "86": 4, "99": 7, "113": 8, "130": 4, "149": 2, "171": 4, "196": 4, "225": 1, "258": 1, "296": 12, "339": 130, "389": 13, "446": 36, "511": 35, "586": 124, "672": 173, "770": 137, "882": 105, "1011": 75, "1159": 360, "1328": 274, "1522": 410, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 1740, "range": [1, 2000], "values": {"2": 0, "3": 1, "5": 3, "7": 1, "17": 1, "19": 1, "113": 1, "130": 1, "171": 1, "196": 2, "258": 3, "296": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 10989, "range": [1, 2000], "values": {"0": 5, "1": 206, "2": 69, "3": 9, "4": 10, "5": 4, "6": 6, "7": 2, "8": 5, "9": 3, "10": 5, "11": 5, "13": 3, "15": 5, "17": 3, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 2, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 2, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 16184, "range": [1, 2000], "values": {"0": 1, "1": 20, "2": 93, "3": 102, "4": 18, "5": 12, "6": 6, "7": 9, "8": 3, "9": 4, "10": 1, "11": 7, "13": 4, "15": 3, "17": 7, "19": 7, "22": 9, "25": 11, "29": 7, "33": 10, "38": 7, "44": 7, "50": 1, "57": 11, "65": 1, "86": 2, "99": 1, "113": 13, "130": 11, "149": 12, "171": 7, "196": 3, "225": 1, "258": 2, "389": 1, "446": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 421, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 8, "4": 2, "5": 3, "6": 1, "7": 4, "8": 2, "9": 1, "10": 1, "11": 3, "15": 2, "19": 2, "22": 1, "33": 1, "38": 1, "86": 1, "99": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 21, "range": [1, 2000], "values": {"2": 0, "3": 7, "4": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 64, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2963, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 2, "4": 3, "5": 1, "7": 1, "9": 1, "10": 1, "11": 1, "17": 1, "19": 2, "22": 2, "25": 2, "33": 4, "38": 3, "44": 4, "65": 9, "75": 3, "86": 1, "99": 1, "171": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"3": 0, "4": 1, "7": 1, "8": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 33, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 2101637, "range": [1, 2000], "values": {"0": 188, "1": 118, "2": 134, "3": 278, "4": 206, "5": 123, "6": 24, "7": 19, "8": 16, "9": 12, "10": 18, "11": 20, "13": 11, "15": 12, "17": 5, "19": 8, "22": 9, "25": 3, "29": 4, "33": 14, "38": 6, "44": 11, "50": 11, "57": 5, "65": 7, "75": 7, "86": 5, "99": 6, "113": 8, "130": 5, "149": 3, "171": 4, "196": 1, "225": 3, "258": 33, "296": 37, "339": 80, "389": 13, "446": 8, "511": 44, "586": 137, "672": 217, "770": 99, "882": 68, "1011": 110, "1159": 363, "1328": 274, "1522": 413, "1745": 0}}, "1": {"bucket_count": 50, "histogram_type": 0, "sum": 2221, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 2, "5": 1, "6": 1, "19": 1, "25": 1, "171": 1, "225": 1, "258": 4, "296": 2, "339": 0}}, "8": {"bucket_count": 50, "histogram_type": 0, "sum": 10928, "range": [1, 2000], "values": {"0": 23, "1": 212, "2": 47, "3": 9, "4": 6, "5": 7, "6": 7, "7": 2, "8": 5, "9": 3, "10": 4, "11": 5, "13": 2, "15": 5, "17": 3, "19": 3, "22": 1, "25": 13, "29": 1, "33": 2, "38": 1, "44": 1, "50": 5, "57": 2, "65": 2, "75": 1, "99": 1, "113": 1, "130": 2, "149": 2, "171": 3, "339": 1, "511": 1, "586": 2, "672": 4, "770": 1, "882": 2, "1011": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 21740, "range": [1, 2000], "values": {"0": 6, "1": 13, "2": 86, "3": 121, "4": 14, "5": 6, "6": 5, "7": 3, "8": 3, "9": 2, "10": 2, "11": 5, "13": 4, "15": 5, "17": 4, "19": 5, "22": 7, "25": 7, "29": 9, "33": 8, "38": 11, "44": 5, "50": 2, "57": 14, "65": 7, "86": 5, "99": 2, "113": 3, "130": 3, "149": 4, "171": 6, "196": 6, "225": 6, "258": 16, "296": 6, "339": 2, "389": 1, "511": 1, "770": 2, "1011": 2, "1159": 0}}, "40": {"bucket_count": 50, "histogram_type": 0, "sum": 4157, "range": [1, 2000], "values": {"0": 2, "1": 2, "3": 1, "4": 3, "6": 2, "7": 1, "11": 2, "13": 2, "15": 1, "17": 1, "22": 2, "25": 1, "50": 2, "86": 1, "149": 1, "258": 2, "296": 3, "339": 2, "389": 1, "446": 1, "511": 1, "586": 0}}, "50": {"bucket_count": 50, "histogram_type": 0, "sum": 134, "range": [1, 2000], "values": {"9": 0, "10": 1, "17": 1, "19": 4, "29": 1, "33": 0}}, "120": {"bucket_count": 50, "histogram_type": 0, "sum": 62, "range": [1, 2000], "values": {"50": 0, "57": 1, "65": 0}}, "210": {"bucket_count": 50, "histogram_type": 0, "sum": 2934, "range": [1, 2000], "values": {"0": 1, "1": 3, "2": 4, "3": 1, "4": 2, "6": 1, "8": 1, "9": 1, "11": 2, "15": 1, "17": 1, "19": 1, "22": 5, "29": 1, "44": 1, "50": 9, "57": 3, "75": 3, "86": 3, "99": 1, "113": 1, "149": 1, "225": 1, "770": 1, "882": 0}}, "400": {"bucket_count": 50, "histogram_type": 0, "sum": 108, "range": [1, 2000], "values": {"3": 0, "4": 1, "99": 1, "113": 0}}, "410": {"bucket_count": 50, "histogram_type": 0, "sum": 80, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "7": 1, "57": 1, "65": 0}}}, "DNS_PERF_FIRST_CONTENTFUL_PAINT_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 7883, "range": [1, 50000], "values": {"550": 0, "605": 1, "733": 1, "807": 1, "977": 4, "1302": 1, "1433": 0}}}, "DNS_PERF_FIRST_BYTE_MS": {"Native": {"bucket_count": 100, "histogram_type": 0, "sum": 2455, "range": [1, 30000], "values": {"93": 0, "102": 1, "112": 1, "135": 1, "194": 1, "232": 1, "278": 1, "398": 1, "436": 1, "477": 1, "522": 0}}}, "MEDIA_PLAY_TIME_MS": {"AV": {"bucket_count": 100, "histogram_type": 0, "sum": 1483337, "range": [1, 14400000], "values": {"3895": 0, "4562": 1, "10051": 1, "1346410": 1, "1576847": 0}}}, "AUDIBLE_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 300, "range": [1, 100], "values": {"98": 0, "100": 3}}}, "MUTED_PLAY_TIME_PERCENT": {"AV": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}}, "VIDEO_VISIBLE_PLAY_TIME_MS": {"All": {"bucket_count": 100, "histogram_type": 0, "sum": 1477054, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "1368456": 1, "1591423": 0}}, "AV,240<h<=480": {"bucket_count": 100, "histogram_type": 0, "sum": 1477054, "range": [1, 7200000], "values": {"4417": 0, "5137": 1, "9395": 1, "1368456": 1, "1591423": 0}}}, "VIDEO_HIDDEN_PLAY_TIME_PERCENTAGE": {"All": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}, "AV,240<h<=480": {"bucket_count": 50, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 3, "1": 0}}}, "MEDIA_CODEC_USED": {"video/vp9": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}, "audio/opus": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "WEBEXT_CONTENT_SCRIPT_INJECTION_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 213, "range": [1, 50000], "values": {"3": 0, "4": 4, "5": 6, "7": 1, "9": 2, "10": 1, "11": 1, "12": 3, "13": 1, "15": 1, "17": 1, "19": 2, "21": 0}}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"bucket_count": 100, "histogram_type": 0, "sum": 37, "range": [1, 50000], "values": {"0": 0, "1": 6, "2": 4, "3": 2, "4": 1, "5": 1, "8": 1, "9": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 4852, "range": [1, 50000], "values": {"119": 0, "131": 1, "174": 1, "192": 3, "211": 4, "232": 4, "255": 3, "281": 2, "309": 1, "340": 1, "374": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 146, "range": [1, 50000], "values": {"0": 20, "2": 7, "3": 3, "4": 2, "6": 2, "8": 1, "9": 1, "11": 2, "21": 1, "41": 1, "45": 0}}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"bucket_count": 100, "histogram_type": 0, "sum": 147, "range": [1, 50000], "values": {"0": 62, "1": 1, "2": 10, "3": 5, "4": 1, "5": 3, "6": 1, "10": 1, "12": 1, "13": 2, "17": 1, "19": 1, "21": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 465, "range": [1, 50000], "values": {"0": 14, "1": 9, "2": 17, "3": 4, "4": 4, "5": 2, "6": 1, "8": 1, "9": 2, "10": 4, "11": 1, "12": 1, "13": 2, "14": 2, "15": 5, "17": 2, "19": 2, "23": 1, "25": 1, "34": 1, "37": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"bucket_count": 100, "histogram_type": 0, "sum": 3793, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 1, "23": 1, "28": 1, "34": 1, "37": 2, "41": 2, "45": 1, "61": 1, "108": 2, "131": 1, "158": 1, "232": 1, "281": 2, "340": 1, "374": 1, "412": 2, "454": 1, "500": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 8680, "range": [1, 50000], "values": {"5": 0, "6": 1, "15": 2, "17": 1, "23": 1, "25": 1, "28": 2, "31": 2, "45": 1, "108": 2, "255": 2, "605": 2, "733": 2, "977": 1, "1075": 1, "1302": 2, "1433": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 9161, "range": [1, 50000], "values": {"1": 0, "2": 1, "3": 1, "4": 1, "7": 1, "9": 1, "14": 1, "31": 1, "74": 1, "119": 1, "131": 1, "158": 1, "733": 1, "807": 5, "977": 2, "1302": 1, "1433": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 4821, "range": [1, 50000], "values": {"2": 0, "3": 1, "5": 2, "11": 1, "12": 1, "13": 1, "15": 1, "21": 2, "31": 1, "55": 2, "61": 2, "67": 1, "74": 1, "119": 1, "158": 2, "211": 1, "888": 1, "1075": 1, "1433": 1, "1577": 0}}}}, "scalars": {"script.preloader.mainthread_recompile": 301, "power.total_thread_wakeups": 6728497, "power.total_cpu_time_ms": 1872307}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"web.background": 388903, "web.foreground": 1374021, "prealloc": 103572, "privilegedabout": 5811}, "power.wakeups_per_process_type": {"web.background": 1412542, "web.foreground": 4363286, "prealloc": 921370, "privilegedabout": 31299}, "telemetry.event_counts": {"pictureinpicture#saw_toggle#toggle": 10}, "media.video_hardware_decoding_support": {"video/vp9": true}}}, "extension": {"histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 4303, "range": [1, 10000], "values": {"0": 0, "1": 221, "2": 1473, "3": 245, "4": 54, "5": 14, "6": 6, "7": 1, "8": 1, "10": 1, "12": 2, "14": 2, "17": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 30070, "range": [1, 100], "values": {"11": 0, "14": 1997, "17": 6, "21": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 13687, "range": [1, 100], "values": {"0": 0, "1": 1161, "2": 6, "3": 3, "14": 831, "17": 2, "21": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 86385, "range": [1, 10000], "values": {"4": 0, "5": 1, "8": 1, "10": 16, "12": 26, "14": 857, "17": 233, "20": 30, "29": 3, "48": 1, "57": 5, "68": 557, "81": 231, "96": 31, "114": 5, "135": 2, "160": 3, "378": 1, "449": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 186761, "range": [1, 10000], "values": {"24": 0, "29": 1, "34": 2, "40": 13, "48": 138, "57": 910, "68": 69, "81": 18, "96": 27, "114": 699, "135": 87, "160": 15, "190": 8, "226": 6, "268": 2, "318": 4, "378": 3, "449": 1, "533": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 10294, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 2, "range": [1, 2], "values": {"0": 10292, "1": 2, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 57939, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 17, "6": 22, "7": 14, "8": 35, "10": 38, "12": 33, "14": 806, "17": 172, "20": 24, "24": 2, "29": 4, "34": 24, "40": 24, "48": 783, "190": 1, "226": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 289, "range": [1, 1000], "values": {"0": 1939, "1": 59, "2": 1, "3": 3, "216": 1, "243": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 68026, "range": [1, 10000], "values": {"0": 1, "2": 1, "6": 8, "7": 22, "8": 538, "10": 566, "12": 19, "14": 10, "17": 2, "29": 1, "34": 2, "48": 4, "57": 495, "68": 295, "81": 27, "96": 7, "114": 2, "135": 3, "160": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 16008, "range": [1, 10000], "values": {"2": 0, "3": 25, "4": 247, "5": 722, "6": 141, "7": 23, "8": 85, "10": 347, "12": 96, "14": 281, "17": 16, "20": 11, "24": 4, "29": 2, "34": 3, "40": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 82, "range": [1, 10000], "values": {"4": 0, "5": 1, "68": 1, "81": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 85381, "range": [1, 10000], "values": {"0": 6242, "1": 28, "2": 47, "3": 56, "4": 88, "5": 116, "6": 46, "7": 23, "8": 134, "10": 398, "12": 147, "14": 1529, "17": 365, "20": 140, "24": 36, "29": 33, "34": 38, "40": 30, "48": 796, "68": 1, "190": 1, "226": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 78414, "range": [1, 100], "values": {"0": 782, "1": 46, "7": 6, "12": 2, "34": 2, "40": 1, "51": 6, "56": 44, "62": 346, "67": 724, "73": 22, "78": 13, "84": 8, "89": 1, "95": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 5013790, "range": [1, 1000000], "values": {"5": 0, "6": 2, "7": 1, "8": 1, "25": 1, "28": 4, "32": 22, "36": 62, "41": 99, "47": 94, "53": 68, "60": 81, "68": 399, "77": 513, "88": 1026, "100": 690, "114": 323, "130": 283, "148": 180, "168": 201, "191": 164, "217": 80, "247": 121, "281": 300, "320": 489, "364": 681, "414": 839, "471": 1456, "536": 1311, "610": 479, "695": 563, "791": 662, "901": 370, "1026": 202, "1168": 106, "1330": 36, "1514": 1, "1724": 4, "1963": 5, "2235": 3, "2545": 2, "2898": 4, "4279": 1, "4872": 1, "5548": 1, "6317": 1, "7193": 2, "8190": 1, "9326": 1, "10619": 1, "12092": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 97562, "range": [1, 2000], "values": {"0": 2074, "1": 85, "2": 28, "3": 25, "4": 39, "5": 47, "6": 43, "7": 36, "8": 44, "9": 41, "10": 51, "11": 81, "13": 49, "15": 5487, "17": 53, "19": 41, "22": 8, "25": 13, "29": 11, "33": 2, "38": 9, "50": 13, "57": 1, "65": 1, "171": 2, "196": 4, "225": 2, "258": 1, "296": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 10294, "range": [1, 200], "values": {"2": 0, "3": 76, "4": 745, "5": 253, "6": 732, "7": 172, "8": 13, "9": 4, "10": 3, "11": 5, "12": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 15194957368, "range": [16, 2147483646], "values": {"0": 0, "16": 17685, "23": 312, "28": 15, "34": 1127, "41": 4534, "50": 43, "61": 157, "74": 40, "90": 26, "109": 165, "132": 3537, "160": 55, "194": 40, "235": 4541, "284": 2731, "344": 928, "416": 4501, "503": 4660, "609": 1147, "737": 3740, "892": 4156, "1080": 1014, "1307": 923, "1582": 3032, "1915": 770, "2318": 815, "2805": 2132, "3395": 1721, "4109": 528, "4973": 2382, "6019": 303, "7284": 359, "8815": 39, "10668": 11, "12911": 168, "15625": 5, "18910": 6, "22886": 2, "27698": 9, "33521": 4, "40569": 14, "49098": 9, "59421": 5, "225968": 13, "273476": 2, "400557": 1, "2231094": 1, "3267857": 4478, "3954901": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 12631362, "range": [1, 2147483646], "values": {"0": 0, "1": 18028, "2": 2, "3": 5931, "5": 104, "8": 3764, "12": 7638, "19": 17295, "30": 9610, "47": 2707, "73": 706, "113": 1612, "176": 1003, "274": 1, "662": 5, "1599": 4478, "6002": 1, "22533": 1, "35021": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 26851684, "range": [1, 150000000], "values": {"0": 605, "1": 1514, "2": 845, "3": 474, "4": 1215, "5": 4163, "6": 4243, "7": 3646, "8": 5884, "10": 5113, "12": 2880, "14": 3459, "17": 3199, "20": 7624, "24": 5265, "29": 4868, "35": 4015, "42": 2888, "50": 1898, "60": 906, "72": 404, "87": 211, "105": 158, "126": 149, "151": 208, "182": 823, "219": 686, "263": 694, "316": 235, "380": 45, "457": 7, "549": 9, "660": 33, "793": 19, "953": 3, "1146": 1, "1378": 2, "1657": 4, "1992": 4, "2395": 37, "2879": 99, "3461": 74, "4161": 50, "5002": 3721, "6013": 467, "7228": 26, "8689": 2, "10445": 4, "12556": 3, "15094": 2, "31521": 2, "37892": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 303121312, "range": [32768, 16777216], "values": {"207574": 0, "221217": 4, "235757": 36, "251252": 48, "267766": 61, "285365": 20, "304121": 11, "324110": 249, "345412": 274, "368115": 189, "392310": 2, "418095": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 474245544, "range": [32768, 16777216], "values": {"474861": 0, "506072": 894, "539334": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 214231928, "range": [32768, 16777216], "values": {"132939": 0, "141677": 3, "150989": 27, "160913": 52, "171489": 8, "182760": 29, "194772": 49, "207574": 4, "221217": 181, "235757": 109, "251252": 249, "267766": 181, "285365": 1, "324110": 1, "345412": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 93288448, "range": [1024, 16777216], "values": {"97683": 0, "102590": 894, "107743": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 894, "1": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1368, "range": [1, 10000], "values": {"0": 16, "1": 116, "2": 115, "3": 15, "4": 13, "5": 14, "6": 8, "7": 14, "8": 20, "10": 13, "12": 6, "14": 14, "24": 3, "40": 1, "48": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 723, "range": [1, 10000], "values": {"0": 9, "1": 56, "2": 58, "3": 8, "4": 5, "5": 8, "6": 4, "7": 7, "8": 10, "10": 7, "12": 3, "14": 7, "24": 2, "40": 1, "48": 0}}, "JS_PAGELOAD_PARSE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 3, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 0}}, "CONTENT_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 45, "1": 3, "4": 1, "5": 0}}, "TIME_TO_NON_BLANK_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 164, "range": [1, 100000], "values": {"68": 0, "75": 1, "83": 1, "92": 0}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 282, "range": [1, 100000], "values": {"92": 0, "102": 1, "154": 1, "171": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 9, "range": [1, 5000], "values": {"1": 0, "2": 3, "3": 1, "4": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 2, "8": 1, "9": 0}}, "TIME_TO_DOM_LOADING_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 85, "range": [1, 50000], "values": {"1": 0, "2": 1, "10": 1, "25": 1, "45": 1, "50": 0}}, "TIME_TO_DOM_INTERACTIVE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 389, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 401, "range": [1, 50000], "values": {"41": 0, "45": 1, "67": 1, "74": 1, "192": 1, "211": 0}}, "TIME_TO_DOM_CONTENT_LOADED_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_DOM_COMPLETE_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_START_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 407, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "TIME_TO_LOAD_EVENT_END_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 409, "range": [1, 50000], "values": {"45": 0, "50": 1, "74": 2, "192": 1, "211": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 212, "range": [1, 50000], "values": {"0": 19, "1": 25, "2": 8, "3": 5, "4": 4, "5": 1, "6": 2, "7": 2, "9": 1, "12": 1, "14": 1, "15": 1, "17": 1, "19": 1, "21": 1, "23": 0}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 860870, "range": [1, 50000], "values": {"9": 0, "10": 2, "12": 3, "13": 1, "14": 2, "15": 2, "17": 1, "19": 1, "21": 3, "37": 1, "45": 1, "119": 1, "131": 19, "144": 105, "158": 832, "174": 1182, "192": 1732, "211": 506, "232": 76, "255": 5, "281": 5, "309": 4, "340": 2, "374": 4, "454": 1, "500": 1, "550": 1, "605": 2, "666": 0}}, "REL_PRELOAD_MISS_RATIO": {"bucket_count": 51, "histogram_type": 5, "sum": 8, "range": [1, 50], "values": {"0": 24, "2": 4, "3": 0}}}, "keyedHistograms": {"NETWORK_ASYNC_OPEN_CHILD_TO_TRANSACTION_PENDING_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 11639, "range": [1, 2000], "values": {"0": 0, "1": 51, "2": 111, "3": 57, "4": 49, "5": 42, "6": 39, "7": 34, "8": 41, "9": 23, "10": 27, "11": 46, "13": 29, "15": 32, "17": 25, "19": 15, "22": 10, "25": 15, "29": 70, "33": 38, "38": 15, "44": 28, "50": 26, "57": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 39, "range": [1, 2000], "values": {"0": 0, "1": 8, "2": 6, "4": 1, "5": 3, "6": 0}}}, "NETWORK_RESPONSE_START_PARENT_TO_CONTENT_EXP_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 6091, "range": [1, 2000], "values": {"0": 54, "1": 208, "2": 116, "3": 92, "4": 67, "5": 56, "6": 55, "7": 26, "8": 25, "9": 20, "10": 9, "11": 21, "13": 13, "15": 13, "17": 9, "19": 5, "22": 3, "25": 2, "29": 2, "33": 8, "38": 4, "44": 1, "50": 3, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "196": 1, "225": 2, "258": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 48, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "13": 1, "15": 0}}}, "NETWORK_RESPONSE_END_PARENT_TO_CONTENT_MS": {"0": {"bucket_count": 50, "histogram_type": 0, "sum": 5217, "range": [1, 2000], "values": {"0": 28, "1": 202, "2": 129, "3": 96, "4": 71, "5": 62, "6": 52, "7": 30, "8": 31, "9": 21, "10": 20, "11": 28, "13": 11, "15": 11, "17": 8, "19": 4, "22": 4, "25": 1, "33": 2, "38": 1, "44": 2, "50": 1, "65": 1, "75": 4, "86": 1, "113": 1, "130": 4, "149": 2, "171": 0}}, "10": {"bucket_count": 50, "histogram_type": 0, "sum": 49, "range": [1, 2000], "values": {"0": 1, "1": 13, "2": 1, "4": 1, "7": 1, "8": 1, "15": 1, "17": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_GET_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 35, "range": [1, 50000], "values": {"14": 0, "15": 1, "19": 1, "21": 0}}, "<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 77, "range": [1, 50000], "values": {"0": 1, "1": 4, "2": 1, "6": 1, "12": 1, "14": 1, "17": 1, "21": 1, "23": 0}}, "customscrollbars@computerwhiz": {"bucket_count": 100, "histogram_type": 0, "sum": 100, "range": [1, 50000], "values": {"0": 18, "1": 21, "2": 7, "3": 5, "4": 4, "5": 1, "6": 1, "7": 2, "9": 1, "10": 0}}}, "WEBEXT_STORAGE_LOCAL_IDB_SET_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 144, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "13": 1, "14": 2, "17": 1, "19": 1, "21": 2, "23": 0}}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 106, "range": [1, 50000], "values": {"9": 0, "10": 1, "12": 1, "37": 1, "45": 1, "50": 0}}, "{3c078156-979c-498b-8990-85f7987dd929}": {"bucket_count": 100, "histogram_type": 0, "sum": 63, "range": [1, 50000], "values": {"11": 0, "12": 1, "15": 2, "21": 1, "23": 0}}}}, "scalars": {"power.total_thread_wakeups": 1197484, "power.total_cpu_time_ms": 1029785}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"extension": 1029785}, "power.wakeups_per_process_type": {"extension": 1197484}}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 195, "power.total_cpu_time_ms": 16}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"socket": 16}, "power.wakeups_per_process_type": {"socket": 195}}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {"power.total_thread_wakeups": 1912866, "power.total_cpu_time_ms": 109069}, "keyedScalars": {"power.cpu_time_per_process_type_ms": {"utility": 109069}, "power.wakeups_per_process_type": {"utility": 1912866}}}}, "histograms": {"ADDON_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 33, "histogram_type": 1, "sum": 510, "range": [1, 32], "values": {"14": 0, "15": 34, "16": 0}}, "CHECKERBOARD_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 85046497, "range": [1, 100000], "values": {"61": 0, "76": 1, "149": 1, "10589": 1, "100000": 1}}, "CHECKERBOARD_PEAK": {"bucket_count": 50, "histogram_type": 0, "sum": 1130594, "range": [1, 66355200], "values": {"42226": 0, "61009": 2, "265859": 1, "554984": 1, "801854": 0}}, "CHECKERBOARD_POTENTIAL_DURATION": {"bucket_count": 50, "histogram_type": 0, "sum": 2734, "range": [1, 1000000], "values": {"113": 0, "149": 4, "1774": 1, "2336": 0}}, "CHECKERBOARD_SEVERITY": {"bucket_count": 50, "histogram_type": 0, "sum": 5781696, "range": [1, 1073741824], "values": {"1303": 0, "1994": 1, "3052": 1, "39254": 1, "4241477": 1, "6492212": 0}}, "CHILD_PROCESS_LAUNCH_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 10781, "range": [1, 64000], "values": {"17": 0, "19": 1, "23": 4, "25": 5, "28": 6, "31": 86, "34": 77, "38": 38, "42": 63, "46": 12, "68": 1, "75": 0}}, "COMPOSITE_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 294968, "range": [1, 1000], "values": {"0": 14688, "1": 4272, "2": 17843, "3": 20289, "4": 18621, "5": 8628, "6": 2810, "7": 1225, "8": 735, "9": 503, "10": 425, "11": 329, "12": 506, "14": 318, "16": 254, "18": 193, "20": 210, "23": 127, "26": 79, "29": 51, "33": 21, "37": 19, "42": 13, "47": 10, "53": 1, "60": 3, "75": 1, "95": 1, "135": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_MAINTHREAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 2181, "range": [1, 64000], "values": {"3": 0, "4": 5, "5": 15, "6": 36, "7": 119, "8": 94, "9": 9, "10": 5, "11": 1, "12": 2, "13": 3, "15": 3, "34": 1, "38": 0}}, "CONTENT_PROCESS_LAUNCH_TOTAL_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 13557, "range": [1, 64000], "values": {"25": 0, "28": 2, "31": 2, "34": 2, "38": 68, "42": 88, "46": 60, "51": 63, "56": 4, "62": 1, "68": 1, "83": 1, "138": 1, "152": 0}}, "CONTENT_PROCESS_LAUNCH_IS_SYNC": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 293, "1": 0}}, "CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 3478, "range": [1, 10000], "values": {"3": 0, "4": 4, "5": 25, "6": 17, "7": 138, "8": 145, "10": 63, "12": 10, "14": 9, "17": 3, "20": 2, "24": 1, "40": 1, "48": 0}}, "GC_ZONE_COUNT": {"bucket_count": 20, "histogram_type": 0, "sum": 68, "range": [1, 100], "values": {"1": 0, "2": 34, "3": 0}}, "GC_ZONES_COLLECTED": {"bucket_count": 20, "histogram_type": 0, "sum": 47, "range": [1, 100], "values": {"0": 0, "1": 21, "2": 13, "3": 0}}, "GC_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1683, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 4, "34": 9, "40": 8, "48": 4, "57": 1, "68": 1, "81": 3, "96": 1, "114": 1, "135": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 3926, "range": [1, 10000], "values": {"48": 0, "57": 1, "68": 7, "81": 11, "96": 6, "114": 2, "135": 1, "190": 2, "226": 4, "268": 0}}, "GC_BUDGET_WAS_INCREASED": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 216, "1": 0}}, "GC_SLICE_WAS_LONG": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 213, "1": 3, "2": 0}}, "GC_MAX_PAUSE_MS_2": {"bucket_count": 50, "histogram_type": 0, "sum": 1034, "range": [1, 10000], "values": {"8": 0, "10": 1, "14": 13, "24": 1, "29": 3, "34": 5, "40": 4, "48": 7, "57": 0}}, "GC_PREPARE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 51, "range": [1, 1000], "values": {"0": 24, "1": 2, "2": 1, "4": 3, "5": 1, "7": 1, "10": 1, "12": 1, "14": 0}}, "GC_MARK_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1085, "range": [1, 10000], "values": {"17": 0, "20": 4, "24": 11, "29": 7, "34": 7, "40": 3, "48": 1, "57": 1, "68": 0}}, "GC_SWEEP_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 467, "range": [1, 10000], "values": {"4": 0, "5": 1, "6": 6, "7": 3, "8": 7, "10": 2, "12": 3, "14": 5, "17": 1, "20": 2, "24": 1, "29": 1, "40": 1, "48": 1, "57": 0}}, "GC_COMPACT_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 129, "range": [1, 10000], "values": {"20": 0, "24": 2, "29": 1, "48": 1, "57": 0}}, "GC_SLICE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1644, "range": [1, 10000], "values": {"0": 120, "1": 9, "2": 2, "3": 3, "4": 5, "6": 4, "7": 4, "8": 9, "10": 6, "12": 3, "14": 25, "17": 1, "20": 1, "24": 3, "29": 4, "34": 5, "40": 4, "48": 8, "57": 0}}, "GC_MMU_50": {"bucket_count": 20, "histogram_type": 1, "sum": 646, "range": [1, 100], "values": {"0": 5, "1": 5, "7": 3, "12": 4, "18": 2, "23": 4, "29": 4, "34": 3, "40": 3, "45": 1, "51": 0}}, "GC_MINOR_US": {"bucket_count": 100, "histogram_type": 0, "sum": 1483938, "range": [1, 1000000], "values": {"2": 0, "3": 1, "4": 1, "6": 1, "9": 1, "10": 1, "11": 1, "13": 6, "15": 2, "17": 4, "19": 6, "22": 3, "25": 5, "28": 1, "32": 5, "36": 4, "41": 7, "47": 15, "53": 47, "60": 71, "68": 106, "77": 236, "88": 461, "100": 867, "114": 1063, "130": 856, "148": 456, "168": 223, "191": 216, "217": 289, "247": 180, "281": 137, "320": 96, "364": 71, "414": 66, "471": 85, "536": 116, "610": 90, "695": 23, "791": 27, "901": 20, "1026": 15, "1168": 4, "1330": 17, "1514": 11, "1724": 10, "1963": 7, "2235": 6, "2545": 11, "2898": 13, "3300": 11, "3758": 4, "4279": 10, "4872": 4, "5548": 6, "6317": 1, "7193": 3, "8190": 2, "9326": 2, "10619": 2, "12092": 1, "13769": 0}}, "GC_TIME_BETWEEN_SLICES_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 2183, "range": [1, 2000], "values": {"0": 63, "1": 12, "2": 3, "4": 2, "6": 1, "9": 2, "10": 1, "11": 1, "13": 2, "15": 80, "17": 3, "22": 3, "38": 2, "44": 1, "50": 2, "75": 1, "99": 2, "149": 1, "171": 0}}, "GC_SLICE_COUNT": {"bucket_count": 50, "histogram_type": 0, "sum": 216, "range": [1, 200], "values": {"2": 0, "3": 1, "4": 11, "5": 5, "6": 8, "7": 4, "8": 1, "9": 1, "12": 1, "16": 1, "23": 1, "25": 0}}, "DESERIALIZE_BYTES": {"bucket_count": 100, "histogram_type": 0, "sum": 95792448, "range": [16, 2147483646], "values": {"0": 0, "16": 90435, "23": 8467, "34": 82, "41": 31, "50": 25, "61": 454, "74": 296, "90": 110, "109": 117, "132": 61, "160": 9, "194": 470, "235": 7346, "284": 5879, "344": 821, "416": 1515, "503": 71310, "609": 6335, "737": 777, "892": 10978, "1080": 16833, "1307": 447, "1582": 72, "1915": 67, "2318": 116, "2805": 48, "3395": 27, "4109": 31, "4973": 20, "6019": 47, "7284": 136, "8815": 2, "10668": 3, "12911": 8, "15625": 1, "18910": 8, "22886": 43, "27698": 2, "33521": 5, "40569": 30, "59421": 1, "71914": 9, "87033": 14, "105331": 1, "154277": 7, "186713": 9, "225968": 3, "330972": 1, "400557": 0}}, "DESERIALIZE_ITEMS": {"bucket_count": 50, "histogram_type": 0, "sum": 2646768, "range": [1, 2147483646], "values": {"0": 0, "1": 98902, "3": 6503, "5": 1098, "8": 77636, "12": 1354, "19": 8270, "30": 28835, "47": 488, "73": 134, "113": 146, "176": 35, "274": 14, "426": 8, "662": 8, "1029": 43, "1599": 1, "2485": 3, "6002": 11, "9328": 5, "14498": 15, "22533": 0}}, "DESERIALIZE_US": {"bucket_count": 100, "histogram_type": 0, "sum": 4106551, "range": [1, 150000000], "values": {"0": 1574, "1": 8348, "2": 11042, "3": 13141, "4": 14028, "5": 16584, "6": 12571, "7": 8023, "8": 20884, "10": 15798, "12": 14797, "14": 25530, "17": 19115, "20": 16435, "24": 8629, "29": 3955, "35": 2759, "42": 2543, "50": 2022, "60": 1221, "72": 964, "87": 793, "105": 632, "126": 441, "151": 381, "182": 357, "219": 150, "263": 107, "316": 73, "380": 72, "457": 68, "549": 77, "660": 64, "793": 53, "953": 47, "1146": 40, "1378": 34, "1657": 31, "1992": 23, "2395": 19, "2879": 28, "3461": 17, "4161": 14, "5002": 6, "6013": 8, "7228": 9, "10445": 1, "12556": 1, "15094": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 564088984, "range": [32768, 16777216], "values": {"324110": 0, "345412": 1, "368115": 3, "392310": 15, "418095": 14, "445575": 54, "474861": 55, "506072": 28, "539334": 61, "574782": 179, "612560": 126, "652821": 83, "695728": 82, "741455": 111, "790188": 79, "842124": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 732929020, "range": [32768, 16777216], "values": {"741455": 0, "790188": 891, "842124": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 2070267332, "range": [32768, 16777216], "values": {"1314908": 0, "1401332": 8, "1493436": 1, "1591594": 4, "1696203": 4, "1807688": 18, "1926500": 79, "2053121": 76, "2188065": 145, "2331878": 304, "2485143": 219, "2648482": 26, "2822556": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 368520620, "range": [32768, 16777216], "values": {"61930": 0, "66000": 2, "70338": 1, "160913": 2, "171489": 2, "194772": 4, "207574": 12, "221217": 9, "235757": 18, "251252": 24, "267766": 40, "285365": 30, "304121": 24, "324110": 42, "345412": 108, "368115": 100, "392310": 92, "418095": 62, "445575": 49, "474861": 62, "506072": 47, "539334": 110, "574782": 51, "612560": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 46233600, "range": [1024, 16777216], "values": {"44591": 0, "46831": 130, "49183": 109, "51654": 483, "54249": 169, "56974": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 891, "1": 0}}, "PROCESS_LIFETIME": {"bucket_count": 24, "histogram_type": 0, "sum": 10205, "range": [15, 86400], "values": {"0": 0, "15": 1, "22": 234, "33": 40, "49": 8, "73": 2, "771": 1, "1142": 0}}, "KEYPRESS_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 566, "range": [1, 200000], "values": {"6": 0, "8": 3, "10": 5, "13": 1, "17": 7, "22": 2, "28": 4, "36": 1, "46": 1, "58": 1, "74": 0}}, "MOUSEUP_FOLLOWED_BY_CLICK_PRESENT_LATENCY": {"bucket_count": 50, "histogram_type": 0, "sum": 4607, "range": [1, 200000], "values": {"2": 0, "3": 1, "13": 5, "17": 10, "22": 12, "28": 7, "36": 6, "46": 3, "58": 4, "74": 8, "94": 1, "119": 1, "151": 3, "310": 2, "394": 1, "501": 1, "637": 0}}, "FONT_FINGERPRINTING_PER_TAB": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 11, "1": 0}}, "HTTP_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 818, "range": [1, 100000], "values": {"0": 23, "1": 8, "5": 1, "16": 2, "20": 2, "25": 6, "31": 7, "39": 1, "233": 1, "292": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 5286, "range": [1, 2], "values": {"0": 57, "1": 5286, "2": 0}}, "HTTP_PAGELOAD_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 19, "range": [1, 2], "values": {"0": 1, "1": 19, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 420, "range": [1, 3], "values": {"0": 449, "2": 210, "3": 0}}, "TLS_EARLY_DATA_ACCEPTED": {"bucket_count": 3, "histogram_type": 2, "sum": 210, "range": [1, 2], "values": {"0": 0, "1": 210, "2": 0}}, "TLS_EARLY_DATA_BYTES_WRITTEN": {"bucket_count": 100, "histogram_type": 0, "sum": 3620, "range": [1, 60000], "values": {"0": 206, "874": 4, "964": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 2738, "range": [1, 16], "values": {"2": 0, "3": 34, "4": 659, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 1356, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 34, "3": 429, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 674, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 655, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 30, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 644, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 98129, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 15, "39": 11, "41": 17, "43": 24, "45": 15, "47": 23, "49": 24, "51": 14, "53": 21, "55": 25, "58": 33, "61": 19, "64": 15, "67": 24, "70": 11, "73": 16, "76": 16, "80": 16, "84": 19, "88": 21, "92": 13, "96": 8, "100": 12, "105": 14, "110": 3, "115": 8, "120": 9, "126": 5, "132": 6, "138": 3, "144": 3, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 6, "207": 3, "217": 15, "227": 32, "237": 16, "248": 13, "259": 14, "271": 5, "283": 2, "296": 4, "310": 7, "324": 3, "339": 13, "355": 4, "371": 6, "388": 11, "406": 7, "425": 15, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 95962, "range": [1, 60000], "values": {"23": 0, "24": 1, "26": 1, "27": 1, "32": 1, "33": 1, "35": 8, "37": 14, "39": 8, "41": 14, "43": 23, "45": 14, "47": 22, "49": 23, "51": 14, "53": 21, "55": 25, "58": 33, "61": 18, "64": 15, "67": 22, "70": 11, "73": 16, "76": 16, "80": 16, "84": 19, "88": 21, "92": 13, "96": 8, "100": 12, "105": 14, "110": 3, "115": 8, "120": 9, "126": 5, "132": 5, "138": 3, "144": 3, "151": 7, "158": 3, "165": 3, "173": 6, "181": 4, "189": 4, "198": 6, "207": 3, "217": 15, "227": 32, "237": 16, "248": 13, "259": 13, "271": 5, "283": 2, "296": 3, "310": 7, "324": 3, "339": 13, "355": 3, "371": 6, "388": 11, "406": 7, "425": 14, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 2020, "range": [1, 60000], "values": {"23": 0, "24": 1, "27": 1, "32": 1, "33": 1, "35": 4, "37": 4, "41": 4, "45": 2, "47": 2, "49": 2, "53": 1, "92": 1, "115": 2, "151": 1, "173": 1, "181": 1, "259": 1, "271": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 96109, "range": [1, 60000], "values": {"25": 0, "26": 1, "35": 4, "37": 11, "39": 11, "41": 13, "43": 24, "45": 13, "47": 21, "49": 22, "51": 14, "53": 20, "55": 25, "58": 33, "61": 19, "64": 15, "67": 24, "70": 11, "73": 16, "76": 16, "80": 16, "84": 19, "88": 21, "92": 12, "96": 8, "100": 12, "105": 14, "110": 3, "115": 6, "120": 9, "126": 5, "132": 6, "138": 3, "144": 3, "151": 6, "158": 3, "165": 3, "173": 5, "181": 3, "189": 4, "198": 6, "207": 3, "217": 15, "227": 32, "237": 16, "248": 13, "259": 13, "271": 5, "283": 2, "296": 4, "310": 7, "324": 3, "339": 13, "355": 4, "371": 6, "388": 11, "406": 7, "425": 15, "445": 1, "531": 3, "555": 4, "608": 2, "696": 1, "762": 1, "872": 1, "1092": 1, "1142": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 1757570, "range": [1, 32000], "values": {"1810": 0, "2105": 1, "2849": 14, "3314": 118, "3855": 24, "4484": 6, "5216": 180, "6067": 23, "8209": 1, "9549": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 326, "range": [1, 2], "values": {"0": 367, "1": 326, "2": 0}}, "CERT_VALIDATION_HTTP_REQUEST_RESULT": {"bucket_count": 17, "histogram_type": 1, "sum": 1, "range": [1, 16], "values": {"0": 0, "1": 1, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 1592, "range": [1, 16], "values": {"3": 0, "4": 336, "8": 31, "9": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 2232, "range": [1, 16], "values": {"3": 0, "4": 94, "8": 232, "9": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 11432, "range": [1, 100000], "values": {"0": 232, "1": 162, "2": 36, "3": 14, "4": 4, "5": 4, "6": 3, "8": 22, "10": 3, "13": 2, "16": 1, "20": 2, "25": 7, "31": 3, "39": 1, "49": 2, "61": 1, "76": 2, "95": 1, "119": 1, "149": 2, "233": 1, "365": 1, "457": 1, "1404": 1, "5399": 1, "6758": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 61603, "range": [1, 16], "values": {"0": 3, "1": 1, "2": 57, "8": 230, "9": 1002, "10": 5054, "11": 6, "12": 2, "13": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 6574, "range": [1, 2], "values": {"0": 9, "1": 6574, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 9124, "range": [1, 5000], "values": {"0": 1296, "1": 9, "2": 7, "3": 7, "5": 2, "7": 6, "8": 1, "10": 2, "11": 4, "12": 4, "13": 4, "14": 6, "15": 10, "16": 5, "17": 7, "18": 2, "19": 6, "20": 3, "21": 4, "23": 5, "25": 5, "27": 4, "29": 5, "31": 2, "33": 2, "35": 4, "38": 4, "41": 3, "47": 4, "50": 2, "54": 3, "58": 1, "66": 2, "71": 2, "87": 3, "93": 2, "100": 1, "115": 2, "123": 1, "132": 5, "163": 4, "175": 2, "188": 1, "202": 1, "233": 1, "250": 1, "268": 2, "287": 1, "308": 2, "1158": 1, "1242": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 8985, "range": [1, 5000], "values": {"0": 147, "1": 5, "2": 2, "3": 5, "10": 1, "11": 2, "12": 6, "13": 4, "14": 9, "15": 5, "16": 4, "17": 6, "18": 7, "19": 1, "20": 6, "21": 9, "23": 5, "25": 3, "27": 8, "29": 3, "31": 1, "33": 3, "35": 3, "38": 1, "41": 1, "44": 2, "62": 1, "66": 10, "71": 2, "76": 1, "81": 1, "87": 1, "93": 1, "115": 2, "123": 2, "132": 2, "142": 1, "163": 3, "188": 2, "202": 3, "217": 2, "233": 1, "287": 1, "308": 2, "330": 2, "1007": 1, "1080": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 34652, "range": [1, 16], "values": {"0": 0, "1": 17054, "2": 669, "6": 2437, "7": 234, "8": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 15188, "range": [1, 60000], "values": {"0": 4, "1": 30, "2": 24, "3": 10, "4": 7, "5": 2, "6": 2, "7": 2, "9": 9, "11": 12, "14": 56, "17": 50, "21": 79, "26": 65, "32": 40, "40": 27, "50": 17, "62": 7, "77": 11, "95": 7, "118": 4, "146": 9, "181": 1, "278": 1, "1012": 1, "1255": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 110047, "range": [1, 60000], "values": {"0": 245, "1": 493, "2": 252, "3": 46, "4": 15, "5": 12, "6": 11, "7": 16, "9": 27, "11": 72, "14": 231, "17": 240, "21": 245, "26": 272, "32": 279, "40": 133, "50": 95, "62": 57, "77": 59, "95": 70, "118": 48, "146": 48, "181": 39, "224": 33, "278": 32, "345": 8, "428": 4, "658": 1, "1012": 3, "1255": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 774, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 467, "range": [1, 60000], "values": {"0": 3686, "1": 114, "2": 45, "3": 16, "4": 8, "6": 1, "7": 1, "11": 2, "14": 1, "17": 3, "32": 2, "40": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 5702, "range": [1, 60000], "values": {"11": 0, "14": 1, "17": 1, "21": 1, "26": 1, "40": 1, "77": 3, "146": 2, "181": 5, "531": 2, "1255": 2, "1556": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 759, "1": 0}}, "DNS_HTTPSSVC_RECORD_RECEIVING_STAGE": {"bucket_count": 51, "histogram_type": 1, "sum": 34041, "range": [1, 50], "values": {"0": 70, "3": 13, "4": 1324, "6": 27, "8": 3568, "9": 0}}, "DNS_HTTPSSVC_CONNECTION_FAILED_REASON": {"bucket_count": 51, "histogram_type": 1, "sum": 0, "range": [1, 50], "values": {"0": 1345, "1": 0}}, "TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 38, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 1, "8": 0}}, "LOADED_TAB_COUNT": {"bucket_count": 100, "histogram_type": 0, "sum": 38, "range": [1, 1000], "values": {"2": 0, "3": 1, "5": 2, "6": 3, "7": 1, "8": 0}}, "STARTUP_CACHE_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 48, "range": [1, 50], "values": {"0": 8, "1": 6, "2": 21, "3": 0}}, "SCRIPT_PRELOADER_REQUESTS": {"bucket_count": 51, "histogram_type": 5, "sum": 64, "range": [1, 50], "values": {"0": 95, "2": 32, "3": 0}}, "NETWORK_ID_ONLINE": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"0": 0, "1": 6, "2": 0}}, "URLCLASSIFIER_LOOKUP_TIME_2": {"bucket_count": 30, "histogram_type": 0, "sum": 8, "range": [1, 5000], "values": {"0": 16, "1": 1, "2": 2, "3": 1, "4": 0}}, "URLCLASSIFIER_CL_CHECK_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 22, "1": 1, "2": 0}}, "URLCLASSIFIER_ASYNC_CLASSIFYLOCAL_TIME": {"bucket_count": 30, "histogram_type": 0, "sum": 2, "range": [1, 60000], "values": {"0": 2, "2": 1, "3": 0}}, "URLCLASSIFIER_VLPS_FILELOAD_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 243, "range": [1, 1000], "values": {"0": 5, "1": 8, "2": 2, "29": 5, "70": 0}}, "URLCLASSIFIER_VLPS_FALLOCATE_TIME": {"bucket_count": 10, "histogram_type": 0, "sum": 7, "range": [1, 1000], "values": {"0": 19, "5": 1, "12": 0}}, "URLCLASSIFIER_VLPS_CONSTRUCT_TIME": {"bucket_count": 15, "histogram_type": 0, "sum": 302, "range": [1, 5000], "values": {"0": 15, "29": 2, "55": 3, "105": 0}}, "URLCLASSIFIER_VLPS_METADATA_CORRUPT": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 25, "1": 0}}, "PLACES_AUTOCOMPLETE_6_FIRST_RESULTS_TIME_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 1353, "range": [50, 1000], "values": {"69": 0, "77": 2, "107": 1, "119": 1, "132": 2, "147": 2, "182": 2, "202": 0}}, "PLACES_FRECENCY_RECALC_CHUNK_TIME_MS": {"bucket_count": 10, "histogram_type": 0, "sum": 116, "range": [50, 10000], "values": {"0": 11, "50": 0}}, "UPDATE_CHECK_CODE_NOTIFY": {"bucket_count": 51, "histogram_type": 1, "sum": 111, "range": [1, 50], "values": {"36": 0, "37": 3, "38": 0}}, "FX_TAB_CLOSE_TIME_ANIM_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 582, "range": [1, 10000], "values": {"81": 0, "96": 1, "114": 4, "135": 0}}, "FX_TAB_SWITCH_UPDATE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 447, "range": [1, 1000], "values": {"4": 0, "6": 1, "13": 8, "19": 8, "27": 1, "39": 1, "56": 1, "80": 0}}, "FX_TAB_SWITCH_TOTAL_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 519, "range": [1, 1000], "values": {"0": 0, "1": 3, "2": 3, "3": 5, "6": 2, "13": 1, "39": 3, "56": 1, "237": 1, "340": 0}}, "FX_TAB_SWITCH_COMPOSITE_E10S_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 1191, "range": [1, 1000], "values": {"27": 0, "39": 1, "56": 5, "80": 1, "115": 4, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 187, "range": [1, 1000], "values": {"115": 0, "165": 1, "237": 0}}, "FX_TAB_SWITCH_SPINNER_VISIBLE_TRIGGER": {"bucket_count": 51, "histogram_type": 5, "sum": 6, "range": [1, 50], "values": {"5": 0, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_ALL_TABS": {"bucket_count": 50, "histogram_type": 0, "sum": 26, "range": [1, 100], "values": {"3": 0, "4": 5, "6": 1, "7": 0}}, "FX_NUMBER_OF_UNIQUE_SITE_ORIGINS_PER_DOCUMENT": {"bucket_count": 50, "histogram_type": 0, "sum": 13, "range": [1, 100], "values": {"0": 0, "1": 9, "2": 2, "3": 0}}, "FX_PAGE_LOAD_MS_2": {"bucket_count": 200, "histogram_type": 1, "sum": 24861, "range": [1, 10000], "values": {"355": 0, "405": 1, "759": 1, "860": 1, "1011": 2, "1466": 2, "1516": 1, "1567": 2, "1668": 1, "1718": 1, "2021": 1, "2324": 2, "2779": 1, "2829": 0}}, "INPUT_EVENT_RESPONSE_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 29106, "range": [1, 10000], "values": {"0": 903, "1": 10719, "2": 4086, "3": 455, "4": 196, "5": 127, "6": 77, "7": 53, "8": 81, "10": 38, "12": 25, "14": 24, "17": 6, "20": 20, "24": 6, "29": 11, "34": 7, "40": 18, "48": 4, "57": 5, "68": 4, "81": 12, "96": 5, "114": 2, "135": 1, "160": 0}}, "INPUT_EVENT_RESPONSE_COALESCED_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 15245, "range": [1, 10000], "values": {"0": 636, "1": 4968, "2": 2430, "3": 270, "4": 123, "5": 76, "6": 46, "7": 31, "8": 43, "10": 25, "12": 16, "14": 13, "17": 3, "20": 11, "24": 1, "29": 7, "34": 2, "40": 9, "48": 3, "57": 3, "68": 1, "81": 1, "96": 1, "114": 1, "135": 1, "160": 2, "190": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 144, "range": [1, 2000], "values": {"0": 219, "1": 17, "2": 5, "3": 7, "5": 2, "7": 3, "15": 1, "35": 1, "52": 0}}, "MIXED_CONTENT_IMAGES": {"bucket_count": 51, "histogram_type": 5, "sum": 4015, "range": [1, 50], "values": {"1": 0, "2": 2006, "3": 1, "4": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 1, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 1768, "range": [1, 10000], "values": {"0": 1470, "1": 1768, "2": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 4828, "range": [1, 10000], "values": {"0": 704, "1": 165, "2": 92, "3": 48, "4": 34, "5": 54, "6": 15, "7": 19, "8": 31, "10": 25, "12": 35, "14": 19, "17": 5, "20": 1, "29": 1, "34": 8, "40": 2, "57": 6, "68": 4, "81": 11, "96": 3, "114": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 12347, "range": [1, 64], "values": {"13": 0, "14": 34, "18": 650, "19": 9, "20": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 9720, "range": [1, 36], "values": {"22": 0, "23": 4, "29": 332, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 2191, "range": [1, 16], "values": {"3": 0, "4": 126, "7": 241, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 2920, "range": [1, 24], "values": {"11": 0, "12": 234, "16": 7, "17": 0}}, "SSL_AUTH_ECDSA_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 2898, "range": [1, 36], "values": {"22": 0, "23": 126, "24": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 17, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 1760, "range": [1, 8], "values": {"0": 0, "1": 326, "2": 17, "4": 350, "5": 0}}, "SSL_OCSP_STAPLING": {"bucket_count": 9, "histogram_type": 1, "sum": 3, "range": [1, 8], "values": {"0": 0, "1": 1, "2": 1, "3": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 404, "range": [1, 24], "values": {"0": 0, "1": 404, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 1151, "range": [1, 10], "values": {"0": 0, "1": 1151, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 1151, "range": [1, 10], "values": {"1": 0, "2": 61, "3": 343, "4": 0}}, "SSL_SCTS_VERIFICATION_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 1243, "range": [1, 10], "values": {"0": 0, "1": 1128, "5": 23, "6": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 404, "range": [1, 10], "values": {"0": 0, "1": 404, "2": 0}}, "CERT_VALIDATION_SUCCESS_BY_CA_2": {"bucket_count": 257, "histogram_type": 1, "sum": 17298, "range": [1, 256], "values": {"13": 0, "14": 22, "15": 110, "20": 168, "60": 2, "61": 8, "89": 19, "116": 34, "119": 8, "145": 33, "146": 0}}, "CERT_PINNING_RESULTS": {"bucket_count": 3, "histogram_type": 2, "sum": 39, "range": [1, 2], "values": {"0": 0, "1": 39, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 221, "range": [1, 512], "values": {"2": 0, "3": 6, "9": 1, "11": 1, "13": 12, "27": 1, "28": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 96, "range": [1, 512], "values": {"16": 0, "17": 2, "31": 2, "32": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 404, "range": [1, 4], "values": {"0": 0, "1": 404, "2": 0}}, "FINGERPRINTERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 69, "1": 0}}, "CRYPTOMINERS_BLOCKED_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 69, "1": 0}}, "TRACKING_PROTECTION_SHIELD": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 69, "1": 0}}, "STORAGE_ACCESS_REMAINING_DAYS": {"bucket_count": 61, "histogram_type": 1, "sum": 75, "range": [1, 60], "values": {"7": 0, "8": 1, "22": 2, "23": 1, "24": 0}}, "QUERY_STRIPPING_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 15, "1": 1, "2": 0}}, "EMAIL_TRACKER_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "SERVICE_WORKER_ISOLATED_LAUNCH_TIME": {"bucket_count": 25, "histogram_type": 0, "sum": 11288, "range": [1, 30000], "values": {"12": 0, "19": 112, "29": 151, "45": 3, "164": 17, "253": 0}}, "WEAVE_DEVICE_COUNT_DESKTOP": {"bucket_count": 11, "histogram_type": 1, "sum": 15, "range": [1, 10], "values": {"0": 0, "1": 15, "2": 0}}, "WEAVE_DEVICE_COUNT_MOBILE": {"bucket_count": 11, "histogram_type": 1, "sum": 0, "range": [1, 10], "values": {"0": 15, "1": 0}}, "CONTENT_FULL_PAINT_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 115004, "range": [1, 1000], "values": {"0": 16, "1": 348, "2": 777, "3": 1507, "4": 1453, "5": 2221, "6": 2324, "7": 1874, "8": 1721, "9": 1233, "10": 918, "11": 804, "12": 644, "14": 192, "16": 108, "18": 94, "20": 100, "23": 55, "26": 40, "29": 36, "33": 19, "37": 10, "42": 9, "47": 1, "53": 2, "60": 1, "75": 2, "84": 1, "95": 0}}, "CONTENT_FRAME_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 413168663, "range": [1, 5000], "values": {"8": 0, "9": 1, "15": 3, "18": 6, "21": 11, "25": 20, "29": 42, "34": 69, "40": 79, "47": 97, "55": 131, "64": 197, "75": 491, "88": 2924, "103": 8921, "120": 1990, "140": 509, "164": 386, "192": 242, "224": 143, "262": 93, "306": 69, "357": 37, "417": 22, "487": 6, "569": 5, "665": 1, "777": 4, "907": 3, "1059": 3, "1237": 1, "3139": 2, "5000": 2}}, "CONTENT_FRAME_TIME_VSYNC": {"bucket_count": 100, "histogram_type": 1, "sum": 421168148, "range": [8, 792], "values": {"8": 0, "16": 2, "32": 3, "40": 8, "48": 11, "56": 7, "64": 5, "72": 5, "80": 3, "88": 3, "96": 95, "104": 2159, "112": 5175, "120": 3579, "128": 1827, "136": 625, "144": 228, "152": 101, "160": 60, "168": 39, "176": 21, "184": 15, "192": 11, "200": 23, "208": 143, "216": 200, "224": 161, "232": 129, "240": 93, "248": 73, "256": 48, "264": 34, "272": 25, "280": 27, "288": 22, "296": 16, "304": 24, "312": 32, "320": 24, "328": 28, "336": 27, "344": 19, "352": 14, "360": 19, "368": 9, "376": 7, "384": 3, "392": 5, "400": 5, "408": 10, "416": 9, "424": 11, "432": 18, "440": 12, "448": 5, "456": 7, "464": 7, "472": 6, "480": 3, "488": 5, "496": 1, "504": 3, "512": 1, "520": 1, "528": 4, "536": 6, "544": 3, "552": 4, "560": 8, "568": 5, "576": 3, "584": 2, "592": 1, "600": 3, "608": 4, "616": 1, "624": 2, "632": 2, "640": 3, "648": 3, "656": 3, "672": 1, "680": 2, "704": 2, "728": 3, "736": 1, "752": 1, "768": 2, "776": 1, "784": 1, "792": 36}}, "CONTENT_FRAME_TIME_WITH_SVG": {"bucket_count": 50, "histogram_type": 0, "sum": 9417107, "range": [1, 5000], "values": {"25": 0, "29": 1, "34": 2, "40": 7, "47": 9, "55": 10, "64": 7, "75": 5, "88": 37, "103": 7128, "120": 5755, "140": 554, "164": 90, "192": 372, "224": 488, "262": 134, "306": 152, "357": 64, "417": 74, "487": 37, "569": 29, "665": 11, "777": 9, "907": 5, "1237": 1, "1445": 2, "1688": 2, "2688": 1, "3139": 2, "3666": 1, "5000": 9}}, "TIME_TO_FIRST_CONTENTFUL_PAINT_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 668, "range": [1, 100000], "values": {"113": 0, "125": 1, "486": 1, "540": 0}}, "INPUT_EVENT_QUEUED_CLICK_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 774, "range": [1, 5000], "values": {"0": 2, "1": 42, "2": 73, "3": 10, "4": 3, "6": 1, "9": 1, "12": 1, "15": 2, "21": 3, "27": 1, "29": 3, "33": 1, "38": 1, "41": 1, "44": 3, "54": 1, "58": 0}}, "INPUT_EVENT_QUEUED_KEYBOARD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 17, "range": [1, 5000], "values": {"0": 117, "8": 1, "9": 1, "10": 0}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}, "BFCACHE_COMBO": {"bucket_count": 51, "histogram_type": 5, "sum": 26, "range": [1, 50], "values": {"1": 0, "2": 6, "3": 1, "11": 1, "12": 0}}, "COOKIE_PURGING_ORIGINS_PURGED": {"bucket_count": 30, "histogram_type": 0, "sum": 1, "range": [1, 500], "values": {"0": 0, "1": 1, "2": 0}}, "COOKIE_PURGING_TRACKERS_WITH_USER_INTERACTION": {"bucket_count": 30, "histogram_type": 0, "sum": 0, "range": [1, 500], "values": {"0": 1, "1": 0}}, "COOKIE_PURGING_DURATION_MS": {"bucket_count": 30, "histogram_type": 0, "sum": 102, "range": [1, 600000], "values": {"53": 0, "85": 1, "136": 0}}, "COOKIE_PURGING_INTERVAL_HOURS": {"bucket_count": 56, "histogram_type": 1, "sum": 24, "range": [1, 168], "values": {"20": 0, "23": 1, "26": 0}}, "REFERRER_POLICY_COUNT": {"bucket_count": 19, "histogram_type": 1, "sum": 33625, "range": [1, 18], "values": {"2": 0, "3": 6, "4": 1443, "8": 9, "9": 9, "12": 1, "14": 2, "17": 1626, "18": 0}}, "ORB_DID_EVER_BLOCK_RESPONSE": {"bucket_count": 3, "histogram_type": 2, "sum": 0, "range": [1, 2], "values": {"0": 40, "1": 0}}, "ORB_BLOCK_REASON": {"bucket_count": 51, "histogram_type": 5, "sum": 1916, "range": [1, 50], "values": {"1": 0, "2": 13, "5": 116, "6": 210, "10": 5, "11": 0}}, "ORB_BLOCK_INITIATOR": {"bucket_count": 51, "histogram_type": 5, "sum": 6770, "range": [1, 50], "values": {"5": 0, "6": 1, "12": 12, "20": 331, "21": 0}}}, "keyedHistograms": {"CANVAS_FINGERPRINTING_PER_TAB": {"unknown": {"bucket_count": 9, "histogram_type": 1, "sum": 0, "range": [1, 8], "values": {"0": 11, "1": 0}}}, "NETWORK_HTTP_REDIRECT_TO_SCHEME": {"https": {"bucket_count": 51, "histogram_type": 5, "sum": 3058, "range": [1, 50], "values": {"0": 4, "1": 3058, "2": 0}}, "vscode": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}}, "NETWORK_DNS_END_TO_CONNECT_START_EXP_MS": {"h3_210": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"1": 0, "2": 1, "3": 1, "4": 0}}, "http/1.1_0": {"bucket_count": 50, "histogram_type": 0, "sum": 27, "range": [1, 2000], "values": {"0": 20, "1": 9, "2": 5, "8": 1, "9": 0}}, "http/1.1_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_210": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}, "http/1.1_1": {"bucket_count": 50, "histogram_type": 0, "sum": 11, "range": [1, 2000], "values": {"10": 0, "11": 1, "13": 0}}, "h2_1": {"bucket_count": 50, "histogram_type": 0, "sum": 3, "range": [1, 2000], "values": {"2": 0, "3": 1, "4": 0}}, "h3_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 1, "2": 1, "3": 0}}, "h2_0": {"bucket_count": 50, "histogram_type": 0, "sum": 159, "range": [1, 2000], "values": {"0": 124, "1": 12, "2": 8, "3": 6, "4": 2, "5": 3, "7": 1, "8": 5, "9": 2, "10": 1, "15": 1, "17": 0}}, "http/1.1_410": {"bucket_count": 50, "histogram_type": 0, "sum": 29, "range": [1, 2000], "values": {"8": 0, "9": 1, "19": 1, "22": 0}}, "h3_10": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"0": 4, "2": 1, "3": 1, "4": 0}}, "h2_10": {"bucket_count": 50, "histogram_type": 0, "sum": 1, "range": [1, 2000], "values": {"0": 133, "1": 1, "2": 0}}, "h3_0": {"bucket_count": 50, "histogram_type": 0, "sum": 57, "range": [1, 2000], "values": {"0": 2, "1": 3, "2": 6, "3": 2, "4": 1, "5": 2, "6": 2, "10": 1, "11": 0}}, "http/1.1_40": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 1, "1": 0}}, "h2_40": {"bucket_count": 50, "histogram_type": 0, "sum": 2, "range": [1, 2000], "values": {"0": 8, "1": 2, "2": 0}}, "h3_8": {"bucket_count": 50, "histogram_type": 0, "sum": 179, "range": [1, 2000], "values": {"0": 2, "1": 96, "2": 23, "3": 4, "4": 4, "9": 1, "10": 0}}, "h2_8": {"bucket_count": 50, "histogram_type": 0, "sum": 42, "range": [1, 2000], "values": {"0": 14, "1": 3, "2": 1, "3": 1, "33": 1, "38": 0}}, "http/1.1_10": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 2000], "values": {"0": 2, "1": 0}}}, "SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 2257, "range": [1, 60000], "values": {"31": 0, "32": 1, "35": 1, "37": 2, "41": 2, "45": 1, "47": 2, "53": 1, "92": 1, "151": 1, "173": 1, "608": 1, "762": 1, "797": 0}}, "mlkem768x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 35247, "range": [1, 60000], "values": {"33": 0, "35": 4, "37": 7, "39": 7, "41": 4, "43": 18, "45": 6, "47": 11, "49": 10, "51": 5, "53": 10, "55": 13, "58": 16, "61": 8, "64": 9, "67": 9, "70": 4, "73": 6, "76": 4, "80": 6, "84": 7, "88": 6, "92": 1, "96": 4, "100": 2, "105": 2, "110": 1, "115": 2, "120": 5, "126": 1, "132": 4, "138": 1, "151": 4, "158": 2, "165": 1, "173": 3, "181": 3, "189": 3, "198": 2, "217": 2, "227": 4, "237": 1, "248": 5, "259": 3, "271": 2, "296": 1, "310": 5, "324": 2, "339": 7, "355": 1, "371": 2, "388": 3, "406": 2, "425": 3, "445": 1, "531": 2, "555": 2, "608": 1, "696": 1, "872": 1, "1092": 1, "1142": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 59760, "range": [1, 60000], "values": {"25": 0, "26": 1, "33": 1, "37": 4, "39": 4, "41": 9, "43": 7, "45": 8, "47": 10, "49": 13, "51": 9, "53": 12, "55": 13, "58": 19, "61": 13, "64": 7, "67": 15, "70": 8, "73": 10, "76": 12, "80": 10, "84": 11, "88": 15, "92": 11, "96": 4, "100": 10, "105": 12, "110": 2, "115": 4, "120": 4, "126": 6, "132": 2, "138": 2, "144": 3, "151": 2, "158": 1, "165": 1, "173": 2, "189": 1, "198": 4, "207": 3, "217": 13, "227": 28, "237": 15, "248": 8, "259": 10, "271": 3, "283": 2, "296": 4, "310": 2, "324": 1, "339": 6, "355": 3, "371": 4, "388": 9, "406": 5, "425": 12, "555": 1, "581": 0}}, "P256": {"bucket_count": 200, "histogram_type": 0, "sum": 1362, "range": [1, 60000], "values": {"80": 0, "84": 1, "165": 1, "531": 1, "555": 1, "581": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 3, "range": [1, 32], "values": {"0": 403, "1": 3, "2": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 7114, "range": [1, 50], "values": {"0": 229, "1": 930, "2": 3079, "3": 6, "4": 2, "5": 0}}, "enabledWont": {"bucket_count": 51, "histogram_type": 5, "sum": 115, "range": [1, 50], "values": {"0": 3, "1": 1, "2": 57, "3": 0}}, "enabledUpgrade": {"bucket_count": 51, "histogram_type": 5, "sum": 4022, "range": [1, 50], "values": {"0": 1, "1": 72, "2": 1975, "3": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"app_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 288, "range": [1, 100], "values": {"17": 0, "18": 16, "19": 0}}, "transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 390, "1": 0}}, "app_closing": {"bucket_count": 101, "histogram_type": 1, "sum": 672, "range": [1, 100], "values": {"41": 0, "42": 16, "43": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1643, "range": [1, 2], "values": {"0": 0, "1": 1643, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 3700, "range": [1, 2], "values": {"0": 9, "1": 3700, "2": 0}}}, "TRR_RELEVANT_SKIP_REASON_TRR_FIRST_TYPE_REC": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 1, "sum": 4620, "range": [1, 50], "values": {"5": 0, "6": 770, "7": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 18649, "range": [1, 50], "values": {"5": 0, "6": 3086, "7": 19, "8": 0}}}, "URLCLASSIFIER_CL_KEYED_UPDATE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 5247, "range": [20, 120000], "values": {"440": 0, "601": 2, "821": 1, "1121": 2, "1531": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_NETWORK_ERROR": {"google4": {"bucket_count": 31, "histogram_type": 1, "sum": 0, "range": [1, 30], "values": {"0": 5, "1": 0}}}, "URLCLASSIFIER_UPDATE_REMOTE_STATUS2": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 5, "range": [1, 16], "values": {"0": 0, "1": 5, "2": 0}}}, "URLCLASSIFIER_UPDATE_SERVER_RESPONSE_TIME": {"google4": {"bucket_count": 30, "histogram_type": 0, "sum": 1059, "range": [1, 100000], "values": {"30": 0, "45": 1, "102": 2, "229": 1, "343": 1, "514": 0}}}, "URLCLASSIFIER_UPDATE_TIMEOUT": {"google4": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 5, "1": 0}}, "mozilla": {"bucket_count": 5, "histogram_type": 1, "sum": 0, "range": [1, 4], "values": {"0": 1, "1": 0}}}, "URLCLASSIFIER_UPDATE_ERROR": {"google4": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 5, "1": 0}}, "mozilla": {"bucket_count": 17, "histogram_type": 1, "sum": 0, "range": [1, 16], "values": {"0": 1, "1": 0}}}, "SEARCH_COUNTS": {"google-b-lm.urlbar": {"bucket_count": 3, "histogram_type": 4, "sum": 1, "range": [1, 2], "values": {"0": 1, "1": 0}}}, "SEARCH_SUGGESTIONS_LATENCY_MS": {"google-b-lm": {"bucket_count": 50, "histogram_type": 0, "sum": 904, "range": [1, 30000], "values": {"43": 0, "52": 2, "63": 2, "77": 3, "115": 3, "140": 0}}}, "EMAIL_TRACKER_EMBEDDED_PER_TAB": {"content_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 1000], "values": {"0": 11, "1": 0}}, "base_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}, "all_normal": {"bucket_count": 100, "histogram_type": 0, "sum": 2, "range": [1, 1000], "values": {"0": 9, "1": 2, "2": 0}}}, "SERVICE_WORKER_FETCH_INTERCEPTION_DURATION_MS_2": {"subresource-other_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_canceled": {"bucket_count": 25, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 1, "1": 0}}, "subresource_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 13377, "range": [1, 60000], "values": {"0": 198, "1": 844, "2": 106, "3": 40, "5": 34, "8": 16, "13": 4, "21": 203, "34": 62, "54": 2, "86": 1, "137": 17, "219": 0}}, "subresource-image_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 392, "range": [1, 60000], "values": {"0": 12, "1": 8, "2": 9, "3": 11, "5": 16, "8": 1, "13": 2, "21": 1, "137": 1, "219": 0}}, "subresource-other_reset": {"bucket_count": 25, "histogram_type": 0, "sum": 12985, "range": [1, 60000], "values": {"0": 186, "1": 836, "2": 97, "3": 29, "5": 18, "8": 15, "13": 2, "21": 202, "34": 62, "54": 2, "86": 1, "137": 16, "219": 0}}, "subresource-image_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}, "subresource_synthesized": {"bucket_count": 25, "histogram_type": 0, "sum": 26, "range": [1, 60000], "values": {"13": 0, "21": 1, "34": 0}}}, "SERVICE_WORKER_FETCH_EVENT_DISPATCH_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 11543, "range": [1, 5000], "values": {"0": 1015, "1": 122, "2": 24, "3": 6, "4": 8, "6": 8, "9": 2, "18": 19, "26": 232, "37": 13, "53": 2, "150": 16, "213": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 11760, "range": [1, 5000], "values": {"0": 1042, "1": 137, "2": 34, "3": 11, "4": 11, "6": 8, "9": 3, "18": 19, "26": 232, "37": 13, "53": 2, "106": 1, "150": 16, "213": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 217, "range": [1, 5000], "values": {"0": 27, "1": 15, "2": 10, "3": 5, "4": 3, "9": 1, "106": 1, "150": 0}}}, "SERVICE_WORKER_FETCH_EVENT_FINISH_SYNTHESIZED_RESPONSE_MS_2": {"subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 19, "range": [1, 5000], "values": {"13": 0, "18": 1, "26": 0}}}, "SERVICE_WORKER_FETCH_EVENT_CHANNEL_RESET_MS_2": {"subresource-other": {"bucket_count": 25, "histogram_type": 0, "sum": 228, "range": [1, 5000], "values": {"0": 1404, "1": 32, "2": 10, "3": 6, "4": 9, "6": 3, "13": 1, "75": 1, "106": 0}}, "subresource": {"bucket_count": 25, "histogram_type": 0, "sum": 366, "range": [1, 5000], "values": {"0": 1425, "1": 50, "2": 17, "3": 14, "4": 13, "6": 3, "13": 2, "18": 2, "75": 1, "106": 0}}, "subresource-image": {"bucket_count": 25, "histogram_type": 0, "sum": 138, "range": [1, 5000], "values": {"0": 21, "1": 18, "2": 7, "3": 8, "4": 4, "13": 1, "18": 2, "26": 0}}}, "WEBEXT_BACKGROUND_PAGE_LOAD_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 161, "range": [1, 60000], "values": {"46": 0, "51": 1, "101": 1, "111": 0}}}, "WEBEXT_BROWSERACTION_POPUP_PRELOAD_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 1, "range": [1, 50], "values": {"0": 0, "1": 1, "2": 0}}}, "WEBEXT_EVENTPAGE_RUNNING_TIME_MS_BY_ADDONID": {"<EMAIL>": {"bucket_count": 100, "histogram_type": 0, "sum": 63345, "range": [1, 60000], "values": {"24762": 0, "27321": 1, "33259": 1, "36696": 0}}}, "WEBEXT_EVENTPAGE_IDLE_RESULT_COUNT_BY_ADDONID": {"<EMAIL>": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}}, "WEBEXT_PAGEACTION_POPUP_OPEN_MS_BY_ADDONID": {"{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"bucket_count": 100, "histogram_type": 0, "sum": 214, "range": [1, 50000], "values": {"67": 0, "74": 1, "131": 1, "144": 0}}}, "QM_FIRST_INITIALIZATION_ATTEMPT": {"TemporaryGroup": {"bucket_count": 3, "histogram_type": 2, "sum": 3, "range": [1, 2], "values": {"0": 0, "1": 3, "2": 0}}, "TemporaryOrigin": {"bucket_count": 3, "histogram_type": 2, "sum": 5, "range": [1, 2], "values": {"0": 0, "1": 5, "2": 0}}}, "HTTP_TRAFFIC_ANALYSIS_3": {"Connection": {"bucket_count": 51, "histogram_type": 5, "sum": 1181, "range": [1, 50], "values": {"0": 151, "1": 337, "2": 422, "3": 0}}, "Transaction": {"bucket_count": 51, "histogram_type": 5, "sum": 8988, "range": [1, 50], "values": {"0": 255, "1": 1394, "2": 3797, "3": 0}}}, "SQLITE_STORE_OPEN": {"indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 375, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 57, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 5, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 24, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 38, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 175, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 13, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 43, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2682, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 40, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 38, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3, "1": 0}}}, "SQLITE_STORE_QUERY": {"bounce-tracking-protection.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 48, "1": 0}}, "caches.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4762, "1": 0}}, "indexedDB-7.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 88, "1": 0}}, "indexedDB-11.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3700, "1": 0}}, "indexedDB-6.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 166, "1": 0}}, "2918063365piupsah.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 300, "1": 0}}, "indexedDB-22.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 160, "1": 0}}, "permissions.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 21, "1": 0}}, "indexedDB-5.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 515, "1": 0}}, "favicons.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 4, "1": 0}}, "indexedDB-16.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 8896, "1": 0}}, "3561288849sdhlie.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 30, "1": 0}}, "indexedDB-12.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 71, "1": 0}}, "data.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 836, "1": 0}}, "indexedDB-20.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 32, "1": 0}}, "cookies.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 3654, "1": 0}}, "content-prefs.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6, "1": 0}}, "indexedDB-13.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 17, "1": 0}}, "indexedDB-4.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 757, "1": 0}}, "indexedDB-23.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 19, "1": 0}}, "indexedDB-1.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 411, "1": 0}}, "protections.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 51, "1": 0}}, "indexedDB-19.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 6012, "1": 0}}, "indexedDB-9.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 111004, "1": 0}}, "places.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 2606, "1": 0}}, "formhistory.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}, "3870112724rsegmnoittet-es.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 1076, "1": 0}}, "indexedDB-8.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 100, "1": 0}}, "webappsstore.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 10, "1": 0}}, "indexedDB-2.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 219, "1": 0}}, "1451318868ntouromlalnodry--epcr.sqlite": {"bucket_count": 51, "histogram_type": 5, "sum": 0, "range": [1, 50], "values": {"0": 33, "1": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 60, "range": [1, 50], "values": {"4": 0, "5": 12, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1440, "range": [1, 50], "values": {"16": 0, "17": 21, "19": 57, "20": 0}}, "11_0_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 254, "range": [1, 50], "values": {"13": 0, "14": 1, "16": 15, "17": 0}}, "30_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 54, "range": [1, 50], "values": {"17": 0, "18": 3, "19": 0}}, "11_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 14, "range": [1, 50], "values": {"13": 0, "14": 1, "15": 0}}, "30_1_0_0_2": {"bucket_count": 51, "histogram_type": 1, "sum": 8, "range": [1, 50], "values": {"1": 0, "2": 4, "3": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 12, "3": 0}}, "30_1_0_2_3": {"bucket_count": 51, "histogram_type": 1, "sum": 466, "range": [1, 50], "values": {"1": 0, "2": 233, "3": 0}}, "30_1_0_6_2": {"bucket_count": 51, "histogram_type": 1, "sum": 98, "range": [1, 50], "values": {"1": 0, "2": 49, "3": 0}}, "30_1_0_1_3": {"bucket_count": 51, "histogram_type": 1, "sum": 128, "range": [1, 50], "values": {"1": 0, "2": 1, "18": 7, "19": 0}}, "30_1_0_2_2": {"bucket_count": 51, "histogram_type": 1, "sum": 6, "range": [1, 50], "values": {"1": 0, "2": 3, "3": 0}}, "11_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 307, "range": [1, 50], "values": {"15": 0, "16": 18, "19": 1, "20": 0}}, "30_1_0_0_3": {"bucket_count": 51, "histogram_type": 1, "sum": 80, "range": [1, 50], "values": {"1": 0, "2": 40, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 36, "range": [1, 50], "values": {"1": 0, "2": 16, "4": 1, "5": 0}}, "11_1_0_1_4": {"bucket_count": 51, "histogram_type": 1, "sum": 778, "range": [1, 50], "values": {"16": 0, "17": 32, "18": 13, "19": 0}}, "11_0_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 19, "range": [1, 50], "values": {"18": 0, "19": 1, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 4375, "range": [1, 50], "values": {"16": 0, "17": 7, "19": 224, "20": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1737, "range": [1, 50], "values": {"3": 0, "4": 4, "16": 9, "19": 83, "20": 0}}, "30_1_0_4_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_3": {"bucket_count": 51, "histogram_type": 1, "sum": 66, "range": [1, 50], "values": {"1": 0, "2": 31, "4": 1, "5": 0}}, "30_1_0_2_4": {"bucket_count": 51, "histogram_type": 1, "sum": 24, "range": [1, 50], "values": {"1": 0, "2": 3, "18": 1, "19": 0}}, "11_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1558, "range": [1, 50], "values": {"3": 0, "4": 171, "16": 27, "17": 26, "18": 0}}}, "ORB_JAVASCRIPT_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 5, "range": [1, 10000], "values": {"0": 1, "1": 3, "2": 1, "3": 0}}}, "ORB_RECEIVE_DATA_FOR_VALIDATION_MS": {"json": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 5, "1": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/c3bba5162c988aa7e1791df8827c1a1575eddcc1", "timezoneOffset": 120, "previousBuildId": null, "sessionId": "3f5bf224-bf45-413a-b265-e7be02d1c39a", "subsessionId": "c79227bf-5040-412b-b88a-3e20412a4cbd", "previousSessionId": "8bcb4b16-0f3d-4ef4-a130-e53ac5a4e69f", "previousSubsessionId": "ea4bbcb4-62a7-4ad9-8c83-82be411f140c", "subsessionCounter": 2, "profileSubsessionCounter": 14, "sessionStartDate": "2025-05-24T12:00:00.0+02:00", "subsessionStartDate": "2025-05-25T00:00:00.0+02:00", "sessionLength": 94175, "subsessionLength": 53675, "addons": "langpack-en-CA%40firefox.mozilla.org:138.0.20250517.143237,langpack-en-GB%40firefox.mozilla.org:138.0.20250517.143237,langpack-cs%40firefox.mozilla.org:138.0.20250517.143237,%7B506e023c-7f2b-40a3-8066-bc5deb40aebe%7D:3.2.13,%7B036a55b4-5e72-4d05-a06c-cba2dfcc134a%7D:********,%7B3c078156-979c-498b-8990-85f7987dd929%7D:5.3.3,private-relay%40firefox.com:2.8.1,keplr-extension%40keplr.app:0.12.219,%7Bb57b832e-f614-4bc0-b98f-1b6c720bec75%7D:1.2resigned1,%7B10c94b5b-9a63-4ae0-8c48-0c7f5f85de25%7D:1.0,customscrollbars%40computerwhiz:4.4,webextension%40metamask.io:12.17.3,jid1-QoFqdK4qzUfGWQ%40jetpack:0.7.6,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:138.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:2.0.0,webcompat%40mozilla.org:138.3.0,default-theme%40mozilla.org:1.4.1"}}, "clientId": "d8c6c0f0-848b-4220-8c58-d154075c592b", "profileGroupId": "51893314-bb6a-4618-9d27-875969894d39", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250421163656", "version": "138.0", "vendor": "Mozilla", "displayVersion": "138.0", "platformVersion": "138.0", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": true}, "partner": {"distributionId": "mint-001", "distributionVersion": "1.0", "partnerId": "mint", "distributor": "mint", "distributorChannel": "wilma", "partnerNames": ["mint"]}, "system": {"memoryMB": 6847, "virtualMaxMB": null, "cpu": {"count": 4, "cores": 2, "pcount": 2, "mcount": 0, "ecount": 0, "vendor": "AuthenticAMD", "name": "AMD Ryzen Embedded R1505G with Radeon Vega Gfx", "family": 23, "model": 24, "stepping": 1, "l2cacheKB": 512, "l3cacheKB": 4096, "speedMHz": 2400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4A", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.8.0-59-generic", "locale": "cs-CZ", "distro": "Linuxmint", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "AMD Radeon Vega 3 Graphics (rade<PERSON>i, raven2, LLVM 19.1.1, DRM 3.57, 6.8.0-59-generic)", "vendorID": "0x1002", "deviceID": "0x15d8", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/radeonsi", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "cs", "intl": {"requestedLocales": ["cs", "en-US"], "availableLocales": ["cs", "en-GB", "en-US", "en-CA"], "appLocales": ["cs", "en-US", "en-GB", "en-CA"], "systemLocales": ["cs-CZ"], "regionalPrefsLocales": ["cs-CZ"], "acceptLanguages": ["cs", "en-us", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": true, "background": true}, "userPrefs": {"browser.search.region": "CZ", "browser.startup.homepage": "<user-set>", "browser.startup.page": 3, "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1746276899, "media.gmp-gmpopenh264.lastDownload": 1746276900, "media.gmp-gmpopenh264.lastUpdate": 1746276900, "media.gmp-manager.lastCheck": 1748083543, "media.gmp-manager.lastEmptyCheck": 1748083543, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 4, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "google-b-lm", "defaultSearchEngineData": {"loadPath": "[app]google", "name": "Google", "submissionURL": "https://www.google.com/search?client=firefox-b-lm&channel=entpr&q="}}, "profile": {"creationDate": 20211, "firstUseDate": 20211}, "addons": {"activeAddons": {"<EMAIL>": {"version": "12.17.3", "scope": 1, "type": "extension", "updateDay": 20232, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ethereum rozšíření prohlížeče", "name": "MetaMask", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{506e023c-7f2b-40a3-8066-bc5deb40aebe}": {"version": "3.2.13", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Ovládejte Váš internetový prohlížeč efektivněji s gesty myší! Doplňek se širokou škálou příkazů spou", "name": "Gesturefy", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{036a55b4-5e72-4d05-a06c-cba2dfcc134a}": {"version": "********", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Přeložte svou stránku v reálném čase pomocí Google nebo Yandex.", "name": "TWP - Translate Web Pages", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{3c078156-979c-498b-8990-85f7987dd929}": {"version": "5.3.3", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Tabs tree, bookmarks and history in a highly configurable sidebar.", "name": "<PERSON><PERSON>", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "{b57b832e-f614-4bc0-b98f-1b6c720bec75}": {"version": "1.2resigned1", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Lets you translate between any languages supported by Google Translate from the Firefox address bar.", "name": "Google Translate (all languages)", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "{10c94b5b-9a63-4ae0-8c48-0c7f5f85de25}": {"version": "1.0", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Automatically changes the dictionary used for spell checking based on the language of text in input ", "name": "Automatic Spell Checking Language Selection", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": false, "quarantineIgnoredByUser": false}, "customscrollbars@computerwhiz": {"version": "4.4", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Give your browser a personal touch with customized scrollbars!", "name": "Custom Scrollbars", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,1]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}, "jid1-QoFqdK4qzUfGWQ@jetpack": {"version": "0.7.6", "scope": 1, "type": "extension", "updateDay": 20211, "isSystem": false, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Makes every page to have light text on dark background (exact colors are customizable)", "name": "Dark Background and Light Text", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "signedState": 2, "signedTypes": "[2,0]", "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "Vzhled s barevným tématem podle nastavení operačního systému.", "name": "Podle systému — automaticky", "userDisabled": false, "appDisabled": false, "version": "1.4.1", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20211, "updateDay": 20211}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "simplified-chatbot-onboarding": {"branch": "treatment-c-short-copy-1-step", "type": "nimbus-nimbus"}, "wnp-seasonal-spring": {"branch": "control", "type": "nimbus-nimbus"}, "upgrade-spotlight-rollout": {"branch": "treatment", "type": "nimbus-rollout"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fpp-floating-point-protection-rollout-linux-only": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "crlite-rollout": {"branch": "rollout", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "https-upgrades-fallback-time-bugfix": {"branch": "rollout", "type": "nimbus-rollout"}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global-v2": {"branch": "control", "type": "nimbus-rollout"}, "tab-groups-50-rollout-no-onboarding": {"branch": "tab-groups", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "shortcuts-visual-refresh-shortcuts-redesign-rollout": {"branch": "shortcuts-redesign", "type": "nimbus-rollout"}, "tab-groups-promotional-onboarding": {"branch": "onboarding", "type": "nimbus-rollout"}, "extensions-migration-in-import-wizard-116-rollout": {"branch": "control", "type": "nimbus-rollout"}, "relay-integration-into-firefox-118-release-for-all-fxa-and-autofill-users": {"branch": "enable-relay-integration", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}}, "services": {"accountEnabled": true, "syncEnabled": true}}}