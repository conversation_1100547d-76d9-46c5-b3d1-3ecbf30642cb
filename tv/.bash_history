            return {}
    except Exception as e:
        print(f"{Fore.RED}Chyba při z<PERSON>ávání tržních dat: {e}{Style.RESET_ALL}")
        return {}


def get_klines(symbol: str, interval: str = "15", limit: int = 200) -> List[Dict[str, Any]]:
    """
    Získá historická data (svíčky) z Bybit API.
    
    Args:
        symbol: Symbol
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet svíček
        
    Returns:
        List[Dict[str, Any]]: Seznam svíček
    """
    try:
        # Odeslání požadavku
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "spot",
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data["retCode"] == 0:
            klines = data.get("result", {}).get("list", [])
            return klines
        else:
            print(f"{Fore.RED}Chyba při z<PERSON>á<PERSON>í historických dat: {data['retMsg']}{Style.RESET_ALL}")
            return []
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání historických dat: {e}{Style.RESET_ALL}")
        return []


def calculate_rsi(klines: List[Dict[str, Any]], period: int = 14) -> float:
    """
    Vypočítá RSI (Relative Strength Index).
    
    Args:
        klines: Seznam svíček
        period: Perioda
        
    Returns:
        float: RSI hodnota
    """
    if len(klines) < period + 1:
        return 50.0
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet změn
    deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
    
    # Výpočet zisků a ztrát
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Výpočet průměrných zisků a ztrát
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Výpočet RS a RSI
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_macd(klines: List[Dict[str, Any]], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, float]:
    """
    Vypočítá MACD (Moving Average Convergence Divergence).
    
    Args:
        klines: Seznam svíček
        fast_period: Rychlá perioda
        slow_period: Pomalá perioda
        signal_period: Signální perioda
        
    Returns:
        Dict[str, float]: MACD hodnoty
    """
    if len(klines) < slow_period + signal_period:
        return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema_values.append((data[i] - ema_values[i-1]) * multiplier + ema_values[i-1])
        
        return ema_values
    
    # Výpočet MACD
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]
    signal_line = ema(macd_line, signal_period)
    histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]
    
    return {
        "macd": macd_line[-1],
        "signal": signal_line[-1],
        "histogram": histogram[-1]
    }


def display_market_data(market_data: Dict[str, Any], symbol: str, indicators: Dict[str, float]):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
        indicators: Technické indikátory
    """
    if not market_data:
        return
    
    ticker = market_data.get("ticker", {})
    
    # Získání hodnot
    last_price = float(ticker.get("lastPrice", 0))
    price_change = float(ticker.get("price24hPcnt", 0)) * 100
    volume = float(ticker.get("volume24h", 0))
    high_24h = float(ticker.get("highPrice24h", 0))
    low_24h = float(ticker.get("lowPrice24h", 0))
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    high_24h_formatted = format_currency(high_24h)
    low_24h_formatted = format_currency(low_24h)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"24h Rozsah: {low_24h_formatted} - {high_24h_formatted}")
    print(f"Objem 24h: {format_currency(volume)}")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN
            if rsi > 70:
                rsi_color = Fore.RED
            elif rsi < 30:
                rsi_color = Fore.YELLOW
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "signal" in indicators:
            macd = indicators["macd"]
            signal = indicators["signal"]
            histogram = indicators.get("histogram", 0)
            
            macd_color = Fore.GREEN if macd > signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {signal:.2f}, Histogram: {histogram:.2f}")
    
    print()


def monitor(symbols: List[str], refresh_interval: int):
    """
    Monitoruje tržní data.
    
    Args:
        symbols: Seznam symbolů
        refresh_interval: Interval obnovení v sekundách
    """
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Market Monitor ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # Zobrazení tržních dat pro každý symbol
            for symbol in symbols:
                market_data = get_market_data(symbol)
                
                # Získání historických dat pro výpočet indikátorů
                klines = get_klines(symbol)
                
                # Výpočet indikátorů
                indicators = {}
                if klines:
                    indicators["rsi"] = calculate_rsi(klines)
                    macd_values = calculate_macd(klines)
                    indicators.update(macd_values)
                
                # Zobrazení dat
                display_market_data(market_data, symbol, indicators)
            
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        print("\nMonitorování ukončeno.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bybit Market Monitor")
    parser.add_argument("--symbols", type=str, default="BTCUSDT,ETHUSDT,SOLUSDT", help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=10, help="Interval obnovení v sekundách")
    
    args = parser.parse_args()
    
    symbols = args.symbols.split(",")
    
    monitor(symbols, args.interval)
EOF

cat > run_monitor.sh << 'EOF'
#!/bin/bash

# Aktivace virtuálního prostředí
source venv/bin/activate

# Spuštění monitorovacího skriptu
python market_monitor.py "$@"
EOF

chmod +x run_monitor.sh
./run_monitor.sh
mkdir -p ~/bybit-trading-assistant/{src,config,logs,tests}\n
mkdir -p ~/bybit-trading-assistant/src/{domain,application,infrastructure,presentation,common}\n
mkdir -p ~/bybit-trading-assistant/src/domain/{models,services,repositories,events,exceptions}\n
mkdir -p ~/bybit-trading-assistant/src/application/{services,dtos,interfaces,commands,queries,handlers}\n
mkdir -p ~/bybit-trading-assistant/src/infrastructure/{persistence,external,messaging,logging,security,configuration}\n
mkdir -p ~/bybit-trading-assistant/src/infrastructure/external/{bybit,tradingview,openai}\n
mkdir -p ~/bybit-trading-assistant/src/presentation/{api,controllers,middlewares,validators}\n
mkdir -p ~/bybit-trading-assistant/src/common/{utils,constants,extensions}\n
find ~/bybit-trading-assistant -type d -exec touch {}/__init__.py \;\n
cd ~/bybit-trading-assistant && python3 -m venv venv\n
source ~/bybit-trading-assistant/venv/bin/activate\n
cat > ~/bybit-trading-assistant/requirements.txt << 'EOF'
# API a integrace
pybit>=2.4.0  # Bybit API klient
openai>=1.0.0  # OpenAI API klient
requests>=2.28.0  # HTTP požadavky
websocket-client>=1.3.2  # WebSocket klient pro realtime data

# Webový framework
fastapi>=0.95.0  # API framework
uvicorn>=0.21.0  # ASGI server
pydantic>=1.10.7  # Validace dat

# Databáze
sqlalchemy>=2.0.0  # ORM
alembic>=1.10.0  # Migrace databáze
pymongo>=4.3.0  # MongoDB klient (pro ukládání historických dat)

# Asynchronní programování
asyncio>=3.4.3  # Asynchronní I/O
aiohttp>=3.8.4  # Asynchronní HTTP klient

# Zpracování dat
pandas>=2.0.0  # Analýza dat
numpy>=1.24.0  # Numerické výpočty
ta>=0.10.0  # Technická analýza

# Logování a monitorování
loguru>=0.6.0  # Logování
prometheus-client>=0.16.0  # Metriky

# Bezpečnost
python-jose>=3.3.0  # JWT
passlib>=1.7.4  # Hashování hesel
python-dotenv>=1.0.0  # Načítání .env souborů

# Testování
pytest>=7.3.0  # Testovací framework
pytest-asyncio>=0.21.0  # Asynchronní testy
pytest-cov>=4.1.0  # Pokrytí kódu testy

# Vývojové nástroje
black>=23.3.0  # Formátování kódu
isort>=5.12.0  # Řazení importů
mypy>=1.2.0  # Statická typová kontrola
flake8>=6.0.0  # Linting
EOF

\n
pip install -r ~/bybit-trading-assistant/requirements.txt\n
sudo apt update && sudo apt install -y python3-pip python3-venv\n
sudo apt install -y python3-venv\n
cd ~/bybit-trading-assistant && source venv/bin/activate
cat > src/application/services/signal_service.py << 'EOF'
"""
Služba pro zpracování signálů.
"""
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.domain.models.signal import Signal, SignalType, SignalMetadata, SignalSource
from src.config.config import config


class SignalService:
    """Služba pro zpracování signálů."""
    
    def __init__(self):
        """Inicializace služby."""
        self.signals = []
    
    def create_signal_from_indicators(self, symbol: str, price: float, indicators: Dict[str, float]) -> Optional[Signal]:
        """
        Vytvoří signál na základě technických indikátorů.
        
        Args:
            symbol: Symbol
            price: Aktuální cena
            indicators: Technické indikátory
            
        Returns:
            Optional[Signal]: Vytvořený signál nebo None, pokud není signál generován
        """
        # Získání hodnot indikátorů
        rsi = indicators.get("rsi", 50.0)
        macd = indicators.get("macd", 0.0)
        macd_signal = indicators.get("signal", 0.0)
        
        # Výchozí hodnoty
        signal_type = SignalType.NEUTRAL
        confidence = 0.0
        
        # Pravidla pro generování signálů
        
        # RSI pravidla
        if rsi < 30:
            # Přeprodaný trh - BUY signál
            signal_type = SignalType.BUY
            confidence += 0.3
        elif rsi > 70:
            # Překoupený trh - SELL signál
            signal_type = SignalType.SELL
            confidence += 0.3
        
        # MACD pravidla
        if macd > macd_signal:
            # MACD nad signální linií - BUY signál
            if signal_type == SignalType.BUY:
                confidence += 0.3
            elif signal_type == SignalType.NEUTRAL:
                signal_type = SignalType.BUY
                confidence += 0.2
        elif macd < macd_signal:
            # MACD pod signální linií - SELL signál
            if signal_type == SignalType.SELL:
                confidence += 0.3
            elif signal_type == SignalType.NEUTRAL:
                signal_type = SignalType.SELL
                confidence += 0.2
        
        # Pokud je signál neutrální nebo má nízkou důvěryhodnost, nevrátíme žádný signál
        if signal_type == SignalType.NEUTRAL or confidence < 0.3:
            return None
        
        # Výpočet stop-loss a take-profit
        stop_loss = None
        take_profit = None
        
        if signal_type == SignalType.BUY:
            stop_loss = price * (1 - config.trading.stop_loss_percentage / 100)
            take_profit = price * (1 + config.trading.take_profit_percentage / 100)
        elif signal_type == SignalType.SELL:
            stop_loss = price * (1 + config.trading.stop_loss_percentage / 100)
            take_profit = price * (1 - config.trading.take_profit_percentage / 100)
        
        # Vytvoření signálu
        signal_id = str(uuid.uuid4())
        metadata = SignalMetadata(
            source=SignalSource.SYSTEM,
            timestamp=datetime.now(),
            correlation_id=None,
            raw_data={"indicators": indicators}
        )
        
        signal = Signal(
            id=signal_id,
            symbol=symbol,
            type=signal_type,
            price=price,
            metadata=metadata,
            confidence=confidence,
            indicators=indicators,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Uložení signálu
        self.signals.append(signal)
        
        return signal
    
    def get_signals(self, symbol: Optional[str] = None, limit: int = 10) -> List[Signal]:
        """
        Získá seznam signálů.
        
        Args:
            symbol: Symbol pro filtrování
            limit: Maximální počet signálů
            
        Returns:
            List[Signal]: Seznam signálů
        """
        # Filtrování podle symbolu
        filtered_signals = self.signals
        if symbol:
            filtered_signals = [s for s in self.signals if s.symbol == symbol]
        
        # Seřazení podle času (nejnovější první)
        sorted_signals = sorted(filtered_signals, key=lambda s: s.metadata.timestamp, reverse=True)
        
        # Omezení počtu signálů
        return sorted_signals[:limit]
    
    def get_latest_signal(self, symbol: str) -> Optional[Signal]:
        """
        Získá nejnovější signál pro daný symbol.
        
        Args:
            symbol: Symbol
            
        Returns:
            Optional[Signal]: Nejnovější signál nebo None, pokud není žádný signál
        """
        signals = self.get_signals(symbol, limit=1)
        return signals[0] if signals else None
EOF

cat > src/application/services/trading_service.py << 'EOF'
"""
Služba pro obchodování.
"""
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from src.domain.models.signal import Signal, SignalType
from src.domain.models.position import Position, PositionSide, PositionStatus
from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.config.config import config


# Nastavení loggeru
logger = logging.getLogger(__name__)


class TradingService:
    """Služba pro obchodování."""
    
    def __init__(self, bybit_client: BybitClient = None, signal_service: SignalService = None):
        """
        Inicializace služby.
        
        Args:
            bybit_client: Klient pro Bybit API
            signal_service: Služba pro zpracování signálů
        """
        self.bybit_client = bybit_client or BybitClient()
        self.signal_service = signal_service or SignalService()
        self.positions = []
    
    def process_signal(self, signal: Signal) -> Optional[Position]:
        """
        Zpracuje signál a vytvoří pozici.
        
        Args:
            signal: Signál
            
        Returns:
            Optional[Position]: Vytvořená pozice nebo None, pokud není pozice vytvořena
        """
        # Kontrola, zda je signál dostatečně silný
        if signal.confidence < 0.5:
            logger.info(f"Signál {signal.id} pro {signal.symbol} má nízkou důvěryhodnost ({signal.confidence}), ignoruji.")
            return None
        
        # Kontrola, zda již nemáme otevřenou pozici pro daný symbol
        open_positions = [p for p in self.positions if p.symbol == signal.symbol and p.status == PositionStatus.OPEN]
        if open_positions:
            logger.info(f"Již existuje otevřená pozice pro {signal.symbol}, ignoruji signál.")
            return None
        
        # Kontrola, zda nepřekročíme maximální počet pozic
        open_positions_count = len([p for p in self.positions if p.status == PositionStatus.OPEN])
        if open_positions_count >= config.trading.max_positions:
            logger.info(f"Dosažen maximální počet pozic ({config.trading.max_positions}), ignoruji signál.")
            return None
        
        # Výpočet množství
        quantity = self._calculate_quantity(signal.symbol, signal.price)
        
        # Vytvoření pozice
        position_id = str(uuid.uuid4())
        position_side = PositionSide.LONG if signal.type == SignalType.BUY else PositionSide.SHORT
        
        position = Position(
            id=position_id,
            symbol=signal.symbol,
            side=position_side,
            entry_price=signal.price,
            quantity=quantity,
            opened_at=datetime.now(),
            status=PositionStatus.OPEN,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            metadata={"signal_id": signal.id}
        )
        
        # Uložení pozice
        self.positions.append(position)
        
        # Vytvoření objednávky na Bybit
        try:
            order_side = "Buy" if position_side == PositionSide.LONG else "Sell"
            order = self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                stop_loss=position.stop_loss,
                take_profit=position.take_profit
            )
            
            # Aktualizace metadat pozice
            position.metadata["order_id"] = order.get("orderId", "")
            
            logger.info(f"Vytvořena nová pozice {position.id} pro {position.symbol} ({position.side.value}).")
            return position
        except Exception as e:
            logger.error(f"Chyba při vytváření objednávky: {e}")
            position.status = PositionStatus.CANCELLED
            return None
    
    def update_positions(self) -> List[Position]:
        """
        Aktualizuje stav otevřených pozic.
        
        Returns:
            List[Position]: Seznam aktualizovaných pozic
        """
        # Získání otevřených pozic
        open_positions = [p for p in self.positions if p.status == PositionStatus.OPEN]
        
        # Aktualizace každé pozice
        for position in open_positions:
            try:
                # Získání aktuální ceny
                market_data = self.bybit_client.get_market_data(position.symbol)
                current_price = float(market_data.get("lastPrice", 0))
                
                # Aktualizace pozice
                position.current_price = current_price
                
                # Výpočet nerealizovaného zisku/ztráty
                if position.side == PositionSide.LONG:
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
                
                # Kontrola stop-loss a take-profit
                if position.stop_loss is not None and position.take_profit is not None:
                    if position.side == PositionSide.LONG:
                        if current_price <= position.stop_loss:
                            self._close_position(position, "stop_loss")
                        elif current_price >= position.take_profit:
                            self._close_position(position, "take_profit")
                    else:
                        if current_price >= position.stop_loss:
                            self._close_position(position, "stop_loss")
                        elif current_price <= position.take_profit:
                            self._close_position(position, "take_profit")
            except Exception as e:
                logger.error(f"Chyba při aktualizaci pozice {position.id}: {e}")
        
        return open_positions
    
    def _close_position(self, position: Position, reason: str) -> bool:
        """
        Uzavře pozici.
        
        Args:
            position: Pozice
            reason: Důvod uzavření
            
        Returns:
            bool: True, pokud byla pozice úspěšně uzavřena
        """
        try:
            # Vytvoření objednávky na Bybit
            order_side = "Sell" if position.side == PositionSide.LONG else "Buy"
            order = self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                reduce_only=True
            )
            
            # Aktualizace pozice
            position.status = PositionStatus.CLOSED
            position.closed_at = datetime.now()
            position.realized_pnl = position.unrealized_pnl
            position.metadata["close_reason"] = reason
            position.metadata["close_order_id"] = order.get("orderId", "")
            
            logger.info(f"Uzavřena pozice {position.id} pro {position.symbol} ({position.side.value}), důvod: {reason}.")
            return True
        except Exception as e:
            logger.error(f"Chyba při uzavírání pozice {position.id}: {e}")
            return False
    
    def _calculate_quantity(self, symbol: str, price: float) -> float:
        """
        Vypočítá množství pro objednávku.
        
        Args:
            symbol: Symbol
            price: Aktuální cena
            
        Returns:
            float: Množství
        """
        # Získání velikosti pozice z konfigurace
        position_size = config.trading.position_size
        
        # Výpočet množství
        quantity = position_size / price
        
        # Zaokrouhlení množství podle pravidel burzy
        if symbol == "BTCUSDT":
            quantity = round(quantity, 6)  # BTC má 6 desetinných míst
        elif symbol == "ETHUSDT":
            quantity = round(quantity, 5)  # ETH má 5 desetinných míst
        else:
            quantity = round(quantity, 4)  # Ostatní mají 4 desetinná místa
        
        return quantity
    
    def get_positions(self, status: Optional[PositionStatus] = None, symbol: Optional[str] = None) -> List[Position]:
        """
        Získá seznam pozic.
        
        Args:
            status: Stav pozice pro filtrování
            symbol: Symbol pro filtrování
            
        Returns:
            List[Position]: Seznam pozic
        """
        # Filtrování podle stavu
        filtered_positions = self.positions
        if status:
            filtered_positions = [p for p in filtered_positions if p.status == status]
        
        # Filtrování podle symbolu
        if symbol:
            filtered_positions = [p for p in filtered_positions if p.symbol == symbol]
        
        # Seřazení podle času (nejnovější první)
        return sorted(filtered_positions, key=lambda p: p.opened_at, reverse=True)
    
    def get_open_positions(self) -> List[Position]:
        """
        Získá seznam otevřených pozic.
        
        Returns:
            List[Position]: Seznam otevřených pozic
        """
        return self.get_positions(status=PositionStatus.OPEN)
    
    def get_position_by_id(self, position_id: str) -> Optional[Position]:
        """
        Získá pozici podle ID.
        
        Args:
            position_id: ID pozice
            
        Returns:
            Optional[Position]: Pozice nebo None, pokud není nalezena
        """
        for position in self.positions:
            if position.id == position_id:
                return position
        return None
EOF

cat > trading_bot.py << 'EOF'
"""
Hlavní skript pro automatické obchodování.
"""
import os
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List

import colorama
from colorama import Fore, Style

from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.application.services.trading_service import TradingService
from src.domain.models.position import PositionStatus
from src.config.config import config


# Inicializace colorama
colorama.init()


# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(config.logging.file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("trading_bot")


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def get_market_data(bybit_client: BybitClient, symbol: str) -> Dict[str, Any]:
    """
    Získá tržní data z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        return bybit_client.get_market_data(symbol)
    except Exception as e:
        logger.error(f"Chyba při získávání tržních dat: {e}")
        return {}


def get_klines(bybit_client: BybitClient, symbol: str, interval: str = "15", limit: int = 200) -> List[Dict[str, Any]]:
    """
    Získá historická data (svíčky) z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet svíček
        
    Returns:
        List[Dict[str, Any]]: Seznam svíček
    """
    try:
        return bybit_client.get_klines(symbol, interval, limit)
    except Exception as e:
        logger.error(f"Chyba při získávání historických dat: {e}")
        return []


def calculate_rsi(klines: List[Dict[str, Any]], period: int = 14) -> float:
    """
    Vypočítá RSI (Relative Strength Index).
    
    Args:
        klines: Seznam svíček
        period: Perioda
        
    Returns:
        float: RSI hodnota
    """
    if len(klines) < period + 1:
        return 50.0
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet změn
    deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
    
    # Výpočet zisků a ztrát
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Výpočet průměrných zisků a ztrát
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Výpočet RS a RSI
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_macd(klines: List[Dict[str, Any]], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, float]:
    """
    Vypočítá MACD (Moving Average Convergence Divergence).
    
    Args:
        klines: Seznam svíček
        fast_period: Rychlá perioda
        slow_period: Pomalá perioda
        signal_period: Signální perioda
        
    Returns:
        Dict[str, float]: MACD hodnoty
    """
    if len(klines) < slow_period + signal_period:
        return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema_values.append((data[i] - ema_values[i-1]) * multiplier + ema_values[i-1])
        
        return ema_values
    
    # Výpočet MACD
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]
    signal_line = ema(macd_line, signal_period)
    histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]
    
    return {
        "macd": macd_line[-1],
        "signal": signal_line[-1],
        "histogram": histogram[-1]
    }


def display_positions(positions: List):
    """
    Zobrazí seznam pozic.
    
    Args:
        positions: Seznam pozic
    """
    if not positions:
        print(f"{Fore.YELLOW}Žádné otevřené pozice{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for position in positions:
        symbol = position.symbol
        side = position.side.value
        side_color = Fore.GREEN if side == "LONG" else Fore.RED
        quantity = position.quantity
        entry_price = position.entry_price
        current_price = position.current_price or entry_price
        unrealized_pnl = position.unrealized_pnl
        profit_percentage = position.profit_percentage
        stop_loss = position.stop_loss
        take_profit = position.take_profit
        opened_at = position.opened_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        side_formatted = f"{side_color}{side}{Style.RESET_ALL}"
        unrealized_pnl_formatted = format_currency(unrealized_pnl)
        profit_percentage_formatted = format_percentage(profit_percentage)
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            side_formatted,
            f"{quantity:.6f}",
            format_currency(entry_price),
            format_currency(current_price),
            unrealized_pnl_formatted,
            profit_percentage_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            opened_at
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Strana", "Množství", "Vstupní cena", "Aktuální cena", "PnL", "PnL %", "Stop-Loss", "Take-Profit", "Otevřeno"]
    print_table(table_data, headers)


def print_table(data, headers):
    """
    Vytiskne tabulku s daty.
    
    Args:
        data: Data pro tabulku
        headers: Hlavičky sloupců
    """
    # Zjištění maximální šířky pro každý sloupec
    col_widths = [len(h) for h in headers]
    for row in data:
        for i, cell in enumerate(row):
            col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # Vytvoření oddělovače
    separator = "+"
    for width in col_widths:
        separator += "-" * (width + 2) + "+"
    
    # Vytisknutí hlavičky
    print(separator)
    header_row = "|"
    for i, header in enumerate(headers):
        header_row += f" {header.ljust(col_widths[i])} |"
    print(header_row)
    print(separator)
    
    # Vytisknutí dat
    for row in data:
        data_row = "|"
        for i, cell in enumerate(row):
            data_row += f" {str(cell).ljust(col_widths[i])} |"
        print(data_row)
    
    print(separator)


def display_signals(signals: List):
    """
    Zobrazí seznam signálů.
    
    Args:
        signals: Seznam signálů
    """
    if not signals:
        print(f"{Fore.YELLOW}Žádné signály{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for signal in signals:
        symbol = signal.symbol
        signal_type = signal.type.value
        type_color = Fore.GREEN if signal_type == "BUY" else Fore.RED
        price = signal.price
        confidence = signal.confidence
        stop_loss = signal.stop_loss
        take_profit = signal.take_profit
        timestamp = signal.metadata.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        type_formatted = f"{type_color}{signal_type}{Style.RESET_ALL}"
        confidence_formatted = f"{confidence:.2f}"
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            type_formatted,
            format_currency(price),
            confidence_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            timestamp
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Typ", "Cena", "Důvěryhodnost", "Stop-Loss", "Take-Profit", "Čas"]
    print_table(table_data, headers)


def display_market_data(market_data: Dict[str, Any], symbol: str, indicators: Dict[str, float]):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
        indicators: Technické indikátory
    """
    if not market_data:
        return
    
    # Získání hodnot
    last_price = float(market_data.get("lastPrice", 0))
    price_change = float(market_data.get("price24hPcnt", 0)) * 100
    volume = float(market_data.get("volume24h", 0))
    high_24h = float(market_data.get("highPrice24h", 0))
    low_24h = float(market_data.get("lowPrice24h", 0))
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    high_24h_formatted = format_currency(high_24h)
    low_24h_formatted = format_currency(low_24h)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"24h Rozsah: {low_24h_formatted} - {high_24h_formatted}")
    print(f"Objem 24h: {format_currency(volume)}")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN
            if rsi > 70:
                rsi_color = Fore.RED
            elif rsi < 30:
                rsi_color = Fore.YELLOW
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "signal" in indicators:
            macd = indicators["macd"]
            signal = indicators["signal"]
            histogram = indicators.get("histogram", 0)
            
            macd_color = Fore.GREEN if macd > signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {signal:.2f}, Histogram: {histogram:.2f}")
    
    print()


def run_trading_bot(symbols: List[str], refresh_interval: int, demo_mode: bool = True):
    """
    Spustí obchodního bota.
    
    Args:
        symbols: Seznam symbolů
        refresh_interval: Interval obnovení v sekundách
        demo_mode: Demo režim (bez skutečných obchodů)
    """
    # Inicializace služeb
    bybit_client = BybitClient()
    signal_service = SignalService()
    trading_service = TradingService(bybit_client, signal_service)
    
    logger.info(f"Spuštěn obchodní bot v {'demo' if demo_mode else 'ostré'} režimu.")
    logger.info(f"Sledované symboly: {', '.join(symbols)}")
    
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Trading Bot {'(Demo)' if demo_mode else '(Ostrý)'} ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Sledované symboly: {', '.join(symbols)}")
            print()
            
            # Aktualizace otevřených pozic
            open_positions = trading_service.update_positions()
            
            # Zobrazení otevřených pozic
            print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
            display_positions(open_positions)
            print()
            
            # Zpracování každého symbolu
            for symbol in symbols:
                # Získání tržních dat
                market_data = get_market_data(bybit_client, symbol)
                if not market_data:
                    continue
                
                # Získání aktuální ceny
                current_price = float(market_data.get("lastPrice", 0))
                
                # Získání historických dat
                klines = get_klines(bybit_client, symbol)
                
                # Výpočet indikátorů
                indicators = {}
                if klines:
                    indicators["rsi"] = calculate_rsi(klines)
                    macd_values = calculate_macd(klines)
                    indicators.update(macd_values)
                
                # Zobrazení tržních dat
                display_market_data(market_data, symbol, indicators)
                
                # Kontrola, zda již nemáme otevřenou pozici pro tento symbol
                symbol_positions = [p for p in open_positions if p.symbol == symbol]
                if symbol_positions:
                    continue
                
                # Vytvoření signálu na základě indikátorů
                signal = signal_service.create_signal_from_indicators(symbol, current_price, indicators)
                
                # Zpracování signálu
                if signal and not demo_mode:
                    position = trading_service.process_signal(signal)
                    if position:
                        logger.info(f"Vytvořena nová pozice pro {symbol} na základě signálu.")
                
                # Zobrazení posledních signálů
                latest_signals = signal_service.get_signals(symbol, limit=1)
                if latest_signals:
                    print(f"{Fore.CYAN}Poslední signál:{Style.RESET_ALL}")
                    display_signals(latest_signals)
                    print()
            
            # Zobrazení informací o intervalu obnovení
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        logger.info("Obchodní bot ukončen uživatelem.")
        print("\nObchodní bot ukončen.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bybit Trading Bot")
    parser.add_argument("--symbols", type=str, default="BTCUSDT,ETHUSDT,SOLUSDT", help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=60, help="Interval obnovení v sekundách")
    parser.add_argument("--demo", action="store_true", help="Spustit v demo režimu (bez skutečných obchodů)")
    
    args = parser.parse_args()
    
    symbols = args.symbols.split(",")
    
    # Vytvoření adresáře pro logy
    os.makedirs(os.path.dirname(config.logging.file), exist_ok=True)
    
    run_trading_bot(symbols, args.interval, args.demo)
EOF

cat > run_trading_bot.sh << 'EOF'
#!/bin/bash

# Aktivace virtuálního prostředí
source venv/bin/activate

# Spuštění obchodního bota
python trading_bot.py "$@"
EOF

chmod +x run_trading_bot.sh
cat > src/application/services/trading_service.py << 'EOF'
"""
Služba pro obchodování.
"""
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from src.domain.models.signal import Signal, SignalType
from src.domain.models.position import Position, PositionSide, PositionStatus
from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.config.config import config


# Nastavení loggeru
logger = logging.getLogger(__name__)


class TradingService:
    """Služba pro obchodování."""
    
    def __init__(self, bybit_client: BybitClient = None, signal_service: SignalService = None):
        """
        Inicializace služby.
        
        Args:
            bybit_client: Klient pro Bybit API
            signal_service: Služba pro zpracování signálů
        """
        self.bybit_client = bybit_client or BybitClient()
        self.signal_service = signal_service or SignalService()
        self.positions = []
    
    def process_signal(self, signal: Signal) -> Optional[Position]:
        """
        Zpracuje signál a vytvoří pozici.
        
        Args:
            signal: Signál
            
        Returns:
            Optional[Position]: Vytvořená pozice nebo None, pokud není pozice vytvořena
        """
        # Kontrola, zda je signál dostatečně silný
        if signal.confidence < 0.5:
            logger.info(f"Signál {signal.id} pro {signal.symbol} má nízkou důvěryhodnost ({signal.confidence}), ignoruji.")
            return None
        
        # Kontrola, zda již nemáme otevřenou pozici pro daný symbol
        open_positions = [p for p in self.positions if p.symbol == signal.symbol and p.status == PositionStatus.OPEN]
        if open_positions:
            logger.info(f"Již existuje otevřená pozice pro {signal.symbol}, ignoruji signál.")
            return None
        
        # Kontrola, zda nepřekročíme maximální počet pozic
        open_positions_count = len([p for p in self.positions if p.status == PositionStatus.OPEN])
        if open_positions_count >= config.trading.max_positions:
            logger.info(f"Dosažen maximální počet pozic ({config.trading.max_positions}), ignoruji signál.")
            return None
        
        # Výpočet množství
        quantity = self._calculate_quantity(signal.symbol, signal.price)
        
        # Vytvoření pozice
        position_id = str(uuid.uuid4())
        position_side = PositionSide.LONG if signal.type == SignalType.BUY else PositionSide.SHORT
        
        position = Position(
            id=position_id,
            symbol=signal.symbol,
            side=position_side,
            entry_price=signal.price,
            quantity=quantity,
            opened_at=datetime.now(),
            status=PositionStatus.OPEN,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            metadata={"signal_id": signal.id}
        )
        
        # Uložení pozice
        self.positions.append(position)
        
        # Vytvoření objednávky na Bybit
        try:
            order_side = "Buy" if position_side == PositionSide.LONG else "Sell"
            order = self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                stop_loss=position.stop_loss,
                take_profit=position.take_profit
            )
            
            # Aktualizace metadat pozice
            position.metadata["order_id"] = order.get("orderId", "")
            
            logger.info(f"Vytvořena nová pozice {position.id} pro {position.symbol} ({position.side.value}).")
            return position
        except Exception as e:
            logger.error(f"Chyba při vytváření objednávky: {e}")
            position.status = PositionStatus.CANCELLED
            return None
    
    def update_positions(self) -> List[Position]:
        """
        Aktualizuje stav otevřených pozic.
        
        Returns:
            List[Position]: Seznam aktualizovaných pozic
        """
        # Získání otevřených pozic
        open_positions = [p for p in self.positions if p.status == PositionStatus.OPEN]
        
        # Aktualizace každé pozice
        for position in open_positions:
            try:
                # Získání aktuální ceny
                market_data = self.bybit_client.get_market_data(position.symbol)
                current_price = float(market_data.get("lastPrice", 0))
                
                # Aktualizace pozice
                position.current_price = current_price
                
                # Výpočet nerealizovaného zisku/ztráty
                if position.side == PositionSide.LONG:
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
                
                # Kontrola stop-loss a take-profit
                if position.stop_loss is not None and position.take_profit is not None:
                    if position.side == PositionSide.LONG:
                        if current_price <= position.stop_loss:
                            self._close_position(position, "stop_loss")
                        elif current_price >= position.take_profit:
                            self._close_position(position, "take_profit")
                    else:
                        if current_price >= position.stop_loss:
                            self._close_position(position, "stop_loss")
                        elif current_price <= position.take_profit:
                            self._close_position(position, "take_profit")
            except Exception as e:
                logger.error(f"Chyba při aktualizaci pozice {position.id}: {e}")
        
        return open_positions
    
    def _close_position(self, position: Position, reason: str) -> bool:
        """
        Uzavře pozici.
        
        Args:
            position: Pozice
            reason: Důvod uzavření
            
        Returns:
            bool: True, pokud byla pozice úspěšně uzavřena
        """
        try:
            # Vytvoření objednávky na Bybit
            order_side = "Sell" if position.side == PositionSide.LONG else "Buy"
            order = self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                reduce_only=True
            )
            
            # Aktualizace pozice
            position.status = PositionStatus.CLOSED
            position.closed_at = datetime.now()
            position.realized_pnl = position.unrealized_pnl
            position.metadata["close_reason"] = reason
            position.metadata["close_order_id"] = order.get("orderId", "")
            
            logger.info(f"Uzavřena pozice {position.id} pro {position.symbol} ({position.side.value}), důvod: {reason}.")
            return True
        except Exception as e:
            logger.error(f"Chyba při uzavírání pozice {position.id}: {e}")
            return False
    
    def _calculate_quantity(self, symbol: str, price: float) -> float:
        """
        Vypočítá množství pro objednávku.
        
        Args:
            symbol: Symbol
            price: Aktuální cena
            
        Returns:
            float: Množství
        """
        # Získání velikosti pozice z konfigurace
        position_size = config.trading.position_size
        
        # Výpočet množství
        quantity = position_size / price
        
        # Zaokrouhlení množství podle pravidel burzy
        if symbol == "BTCUSDT":
            quantity = round(quantity, 6)  # BTC má 6 desetinných míst
        elif symbol == "ETHUSDT":
            quantity = round(quantity, 5)  # ETH má 5 desetinných míst
        else:
            quantity = round(quantity, 4)  # Ostatní mají 4 desetinná místa
        
        return quantity
    
    def get_positions(self, status: Optional[PositionStatus] = None, symbol: Optional[str] = None) -> List[Position]:
        """
        Získá seznam pozic.
        
        Args:
            status: Stav pozice pro filtrování
            symbol: Symbol pro filtrování
            
        Returns:
            List[Position]: Seznam pozic
        """
        # Filtrování podle stavu
        filtered_positions = self.positions
        if status:
            filtered_positions = [p for p in filtered_positions if p.status == status]
        
        # Filtrování podle symbolu
        if symbol:
            filtered_positions = [p for p in filtered_positions if p.symbol == symbol]
        
        # Seřazení podle času (nejnovější první)
        return sorted(filtered_positions, key=lambda p: p.opened_at, reverse=True)
    
    def get_open_positions(self) -> List[Position]:
        """
        Získá seznam otevřených pozic.
        
        Returns:
            List[Position]: Seznam otevřených pozic
        """
        return self.get_positions(status=PositionStatus.OPEN)
    
    def get_position_by_id(self, position_id: str) -> Optional[Position]:
        """
        Získá pozici podle ID.
        
        Args:
            position_id: ID pozice
            
        Returns:
            Optional[Position]: Pozice nebo None, pokud není nalezena
        """
        for position in self.positions:
            if position.id == position_id:
                return position
        return None
EOF

cat > trading_bot.py << 'EOF'
"""
Hlavní skript pro automatické obchodování.
"""
import os
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List

import colorama
from colorama import Fore, Style

from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.application.services.trading_service import TradingService
from src.domain.models.position import PositionStatus
from src.config.config import config


# Inicializace colorama
colorama.init()


# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(config.logging.file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("trading_bot")


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def get_market_data(bybit_client: BybitClient, symbol: str) -> Dict[str, Any]:
    """
    Získá tržní data z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        return bybit_client.get_market_data(symbol)
    except Exception as e:
        logger.error(f"Chyba při získávání tržních dat: {e}")
        return {}


def get_klines(bybit_client: BybitClient, symbol: str, interval: str = "15", limit: int = 200) -> List[Dict[str, Any]]:
    """
    Získá historická data (svíčky) z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet svíček
        
    Returns:
        List[Dict[str, Any]]: Seznam svíček
    """
    try:
        return bybit_client.get_klines(symbol, interval, limit)
    except Exception as e:
        logger.error(f"Chyba při získávání historických dat: {e}")
        return []


def calculate_rsi(klines: List[Dict[str, Any]], period: int = 14) -> float:
    """
    Vypočítá RSI (Relative Strength Index).
    
    Args:
        klines: Seznam svíček
        period: Perioda
        
    Returns:
        float: RSI hodnota
    """
    if len(klines) < period + 1:
        return 50.0
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet změn
    deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
    
    # Výpočet zisků a ztrát
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Výpočet průměrných zisků a ztrát
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Výpočet RS a RSI
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_macd(klines: List[Dict[str, Any]], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, float]:
    """
    Vypočítá MACD (Moving Average Convergence Divergence).
    
    Args:
        klines: Seznam svíček
        fast_period: Rychlá perioda
        slow_period: Pomalá perioda
        signal_period: Signální perioda
        
    Returns:
        Dict[str, float]: MACD hodnoty
    """
    if len(klines) < slow_period + signal_period:
        return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema_values.append((data[i] - ema_values[i-1]) * multiplier + ema_values[i-1])
        
        return ema_values
    
    # Výpočet MACD
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]
    signal_line = ema(macd_line, signal_period)
    histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]
    
    return {
        "macd": macd_line[-1],
        "signal": signal_line[-1],
        "histogram": histogram[-1]
    }


def display_positions(positions: List):
    """
    Zobrazí seznam pozic.
    
    Args:
        positions: Seznam pozic
    """
    if not positions:
        print(f"{Fore.YELLOW}Žádné otevřené pozice{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for position in positions:
        symbol = position.symbol
        side = position.side.value
        side_color = Fore.GREEN if side == "LONG" else Fore.RED
        quantity = position.quantity
        entry_price = position.entry_price
        current_price = position.current_price or entry_price
        unrealized_pnl = position.unrealized_pnl
        profit_percentage = position.profit_percentage
        stop_loss = position.stop_loss
        take_profit = position.take_profit
        opened_at = position.opened_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        side_formatted = f"{side_color}{side}{Style.RESET_ALL}"
        unrealized_pnl_formatted = format_currency(unrealized_pnl)
        profit_percentage_formatted = format_percentage(profit_percentage)
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            side_formatted,
            f"{quantity:.6f}",
            format_currency(entry_price),
            format_currency(current_price),
            unrealized_pnl_formatted,
            profit_percentage_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            opened_at
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Strana", "Množství", "Vstupní cena", "Aktuální cena", "PnL", "PnL %", "Stop-Loss", "Take-Profit", "Otevřeno"]
    print_table(table_data, headers)


def print_table(data, headers):
    """
    Vytiskne tabulku s daty.
    
    Args:
        data: Data pro tabulku
        headers: Hlavičky sloupců
    """
    # Zjištění maximální šířky pro každý sloupec
    col_widths = [len(h) for h in headers]
    for row in data:
        for i, cell in enumerate(row):
            col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # Vytvoření oddělovače
    separator = "+"
    for width in col_widths:
        separator += "-" * (width + 2) + "+"
    
    # Vytisknutí hlavičky
    print(separator)
    header_row = "|"
    for i, header in enumerate(headers):
        header_row += f" {header.ljust(col_widths[i])} |"
    print(header_row)
    print(separator)
    
    # Vytisknutí dat
    for row in data:
        data_row = "|"
        for i, cell in enumerate(row):
            data_row += f" {str(cell).ljust(col_widths[i])} |"
        print(data_row)
    
    print(separator)


def display_signals(signals: List):
    """
    Zobrazí seznam signálů.
    
    Args:
        signals: Seznam signálů
    """
    if not signals:
        print(f"{Fore.YELLOW}Žádné signály{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for signal in signals:
        symbol = signal.symbol
        signal_type = signal.type.value
        type_color = Fore.GREEN if signal_type == "BUY" else Fore.RED
        price = signal.price
        confidence = signal.confidence
        stop_loss = signal.stop_loss
        take_profit = signal.take_profit
        timestamp = signal.metadata.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        type_formatted = f"{type_color}{signal_type}{Style.RESET_ALL}"
        confidence_formatted = f"{confidence:.2f}"
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            type_formatted,
            format_currency(price),
            confidence_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            timestamp
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Typ", "Cena", "Důvěryhodnost", "Stop-Loss", "Take-Profit", "Čas"]
    print_table(table_data, headers)


def display_market_data(market_data: Dict[str, Any], symbol: str, indicators: Dict[str, float]):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
        indicators: Technické indikátory
    """
    if not market_data:
        return
    
    # Získání hodnot
    last_price = float(market_data.get("lastPrice", 0))
    price_change = float(market_data.get("price24hPcnt", 0)) * 100
    volume = float(market_data.get("volume24h", 0))
    high_24h = float(market_data.get("highPrice24h", 0))
    low_24h = float(market_data.get("lowPrice24h", 0))
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    high_24h_formatted = format_currency(high_24h)
    low_24h_formatted = format_currency(low_24h)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"24h Rozsah: {low_24h_formatted} - {high_24h_formatted}")
    print(f"Objem 24h: {format_currency(volume)}")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN
            if rsi > 70:
                rsi_color = Fore.RED
            elif rsi < 30:
                rsi_color = Fore.YELLOW
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "signal" in indicators:
            macd = indicators["macd"]
            signal = indicators["signal"]
            histogram = indicators.get("histogram", 0)
            
            macd_color = Fore.GREEN if macd > signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {signal:.2f}, Histogram: {histogram:.2f}")
    
    print()


def run_trading_bot(symbols: List[str], refresh_interval: int, demo_mode: bool = True):
    """
    Spustí obchodního bota.
    
    Args:
        symbols: Seznam symbolů
        refresh_interval: Interval obnovení v sekundách
        demo_mode: Demo režim (bez skutečných obchodů)
    """
    # Inicializace služeb
    bybit_client = BybitClient()
    signal_service = SignalService()
    trading_service = TradingService(bybit_client, signal_service)
    
    logger.info(f"Spuštěn obchodní bot v {'demo' if demo_mode else 'ostré'} režimu.")
    logger.info(f"Sledované symboly: {', '.join(symbols)}")
    
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Trading Bot {'(Demo)' if demo_mode else '(Ostrý)'} ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Sledované symboly: {', '.join(symbols)}")
            print()
            
            # Aktualizace otevřených pozic
            open_positions = trading_service.update_positions()
            
            # Zobrazení otevřených pozic
            print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
            display_positions(open_positions)
            print()
            
            # Zpracování každého symbolu
            for symbol in symbols:
                # Získání tržních dat
                market_data = get_market_data(bybit_client, symbol)
                if not market_data:
                    continue
                
                # Získání aktuální ceny
                current_price = float(market_data.get("lastPrice", 0))
                
                # Získání historických dat
                klines = get_klines(bybit_client, symbol)
                
                # Výpočet indikátorů
                indicators = {}
                if klines:
                    indicators["rsi"] = calculate_rsi(klines)
                    macd_values = calculate_macd(klines)
                    indicators.update(macd_values)
                
                # Zobrazení tržních dat
                display_market_data(market_data, symbol, indicators)
                
                # Kontrola, zda již nemáme otevřenou pozici pro tento symbol
                symbol_positions = [p for p in open_positions if p.symbol == symbol]
                if symbol_positions:
                    continue
                
                # Vytvoření signálu na základě indikátorů
                signal = signal_service.create_signal_from_indicators(symbol, current_price, indicators)
                
                # Zpracování signálu
                if signal and not demo_mode:
                    position = trading_service.process_signal(signal)
                    if position:
                        logger.info(f"Vytvořena nová pozice pro {symbol} na základě signálu.")
                
                # Zobrazení posledních signálů
                latest_signals = signal_service.get_signals(symbol, limit=1)
                if latest_signals:
                    print(f"{Fore.CYAN}Poslední signál:{Style.RESET_ALL}")
                    display_signals(latest_signals)
                    print()
            
            # Zobrazení informací o intervalu obnovení
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        logger.info("Obchodní bot ukončen uživatelem.")
        print("\nObchodní bot ukončen.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bybit Trading Bot")
    parser.add_argument("--symbols", type=str, default="BTCUSDT,ETHUSDT,SOLUSDT", help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=60, help="Interval obnovení v sekundách")
    parser.add_argument("--demo", action="store_true", help="Spustit v demo režimu (bez skutečných obchodů)")
    
    args = parser.parse_args()
    
    symbols = args.symbols.split(",")
    
    # Vytvoření adresáře pro logy
    os.makedirs(os.path.dirname(config.logging.file), exist_ok=True)
    
    run_trading_bot(symbols, args.interval, args.demo)
EOF

./run_trading_bot.sh --demo --interval 30
./run_monitor.sh
./run_monitor.sh --symbols BTCUSDT,ETHUSDT,SOLUSDT --interval 30
ls
cd bybit-trading-assistant/
./run_monitor.sh
./run_monitor.sh --symbols BTCUSDT,ETHUSDT,SOLUSDT --interval 30
cd ~/lakylukperun2.0 && ls -la
find ~ -name "perun_tradingview_multi.py"
find ~ -name "lakylukperun2.0" -type d
ls -la ~
chmod +x fix_perun_trading_service.py
./fix_perun_trading_service.py
chmod +x find_perun_files.py
./find_perun_files.py
find ~ -name "perun*.py" | grep -v "__pycache__" | sort
find ~ -name "perun*" -type f | grep -v "__pycache__" | sort
find ~ -name "*perun*" -type f | grep -v "__pycache__" | sort
find ~ -name "*trading*" -type f | grep -v "__pycache__" | sort
chmod +x update_openai_api_key_new.py
./update_openai_api_key_new.py "********************************************************************************************************************************************************************"
ls -la
find ~ -name "run_*.sh" | grep -i perun
find ~ -type d -name "*perun*" 2>/dev/null
find ~ -name "perun_*.py" 2>/dev/null | head -10
find ~ -name "perun_tradingview_openai*.py" 2>/dev/null
find ~ -name "perun_taapi_openai*.py" 2>/dev/null
ls -la ~/lakylukperun* 2>/dev/null || echo "Adresář nenalezen"
ls -la ~/lakylukperun2.0 2>/dev/null || echo "Adresář nenalezen"
mkdir -p ~/lakylukperun2.0
chmod +x ~/lakylukperun2.0/run_perun_openai.sh ~/lakylukperun2.0/monitor_perun.sh
pip install openai tradingview-ta
ls -la ~/lakylukperun2.0/
ls -la ~
mkdir -p ~/lakylukperun2.0
cd ~ && ls -la lakylukperun2.0
mkdir -p /home/<USER>/lakylukperun2.0
s -la /home/<USER>
mkdir -p lakylukperun2.0
chmod +x lakylukperun2.0/run_perun_openai.sh lakylukperun2.0/monitor_perun.sh
pip install openai tradingview-ta
s -la lakylukperun2.0/
chmod 755 lakylukperun2.0/*.sh
pip3 install openai tradingview-ta
python3 -m pip install openai tradingview-ta
cd lakylukperun2.0 && touch trading_log.txt
python3 -m pip install --user openai tradingview-ta
cd lakylukperun2.0 && python3 -m pip install --user -r requirements.txt
ls -la ~/lakylukperun2.0/ 2>/dev/null || echo "Adresář lakylukperun2.0 neexistuje"
ls -la ~/bybit-trading-assistant/ 2>/dev/null || echo "Adresář bybit-trading-assistant neexistuje"
find ~ -name "tradingview_adapter.py" 2>/dev/null
find ~ -name "README_TRADINGVIEW.md" 2>/dev/null
sudo mkdir -p /home/<USER>/bybit-trading-assistant
udo mkdir -p /home/<USER>/bybit-trading-assistant
sudo -u tv mkdir -p /home/<USER>/bybit-trading-assistant
sudo mkdir -p /home/<USER>/bybit-trading-assistant
sudo cp tradingview_adapter.* /home/<USER>/bybit-trading-assistant/ 2>/dev/null || echo "Soubory neexistují"
chmod +x bybit_tradingview_integration.py
udo cp bybit_tradingview_integration.py /home/<USER>/bybit-trading-assistant/
sudo cp tradingview_adapter.* install_tradingview.sh README_BYBIT_TRADINGVIEW.md bybit_tradingview_integration.py /home/<USER>/bybit-trading-assistant/ 2>/dev/null || echo "Některé soubory neexistují"
sudo chown -R tv:tv /home/<USER>/bybit-trading-assistant/
udo chmod +x /home/<USER>/bybit-trading-assistant/*.py /home/<USER>/bybit-trading-assistant/*.js /home/<USER>/bybit-trading-assistant/*.sh
sudo -u tv ls -la /home/<USER>/bybit-trading-assistant/
cd
./integrate_openai_api.sh
CLAUDE
npm install -g @anthropic-ai/claude-code --prefix ~/.npm-global
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
CLAUDE
claude
cd ~/lakylukperun2.0
./watch_log.sh
./start_trading_assistant.sh
ls
cd bybit-trading-assistant/
./start_trading_assistant.sh
mc
sudo apt install mc
mc
claude
/home/<USER>/bybit-trading-assistant/run_bot.sh
echo 'Terminal capability test'
