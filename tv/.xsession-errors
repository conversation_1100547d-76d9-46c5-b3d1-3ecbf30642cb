dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
/etc/X11/Xsession.d/30x11-common_xresources: line 16: has_option: command not found
/etc/X11/Xsession.d/75dbus_dbus-launch: line 9: has_option: command not found
dbus-update-activation-environment: setting GTK_MODULES=gail:atk-bridge
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
/etc/X11/Xsession.d/90x11-common_ssh-agent: line 9: has_option: command not found
dbus-update-activation-environment: setting SHELL=/bin/bash
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
dbus-update-activation-environment: setting XDG_CONFIG_DIRS=/etc/xdg/xdg-cinnamon:/etc/xdg
dbus-update-activation-environment: setting LANGUAGE=cs
dbus-update-activation-environment: setting DESKTOP_SESSION=cinnamon
dbus-update-activation-environment: setting GTK_MODULES=gail:atk-bridge
dbus-update-activation-environment: setting PWD=/home/<USER>
dbus-update-activation-environment: setting XDG_SESSION_DESKTOP=cinnamon
dbus-update-activation-environment: setting LOGNAME=tv
dbus-update-activation-environment: setting XDG_SESSION_TYPE=x11
dbus-update-activation-environment: setting GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
dbus-update-activation-environment: setting XDG_GREETER_DATA_DIR=/var/lib/lightdm-data/tv
dbus-update-activation-environment: setting GDM_LANG=cs
dbus-update-activation-environment: setting HOME=/home/<USER>
dbus-update-activation-environment: setting IM_CONFIG_PHASE=1
dbus-update-activation-environment: setting LANG=cs_CZ.UTF-8
dbus-update-activation-environment: setting XDG_SESSION_CLASS=user
dbus-update-activation-environment: setting USER=tv
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting SHLVL=0
dbus-update-activation-environment: setting XDG_RUNTIME_DIR=/run/user/1000
dbus-update-activation-environment: setting GTK3_MODULES=xapp-gtk3-module
dbus-update-activation-environment: setting XDG_DATA_DIRS=/usr/share/cinnamon:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share:/usr/share
dbus-update-activation-environment: setting PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
dbus-update-activation-environment: setting GDMSESSION=cinnamon
dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting _=/usr/bin/dbus-update-activation-environment
discover_other_daemon: 1discover_other_daemon: 1discover_other_daemon: 1[cinnamon-settings-daemon-smartcard] Failed to start: no suitable smartcard driver could be found
[cinnamon-settings-daemon-smartcard] Failed to start: no suitable smartcard driver could be found
dbus-daemon[1312]: Activating service name='org.a11y.atspi.Registry' requested by ':1.0' (uid=1000 pid=1304 comm="/usr/bin/csd-keyboard" label="unconfined")
dbus-daemon[1312]: Successfully activated service 'org.a11y.atspi.Registry'
/usr/bin/cinnamon-launcher:56: DeprecationWarning: This process (pid=1539) is multi-threaded, use of fork() may lead to deadlocks in the child.
  self.cinnamon_pid = os.fork()
Gjs-Message: 10:48:46.787: Profiler is disabled. Not setting up signals.
Gjs-Message: 10:48:47.000: JS LOG: About to start Cinnamon (X11 backend)
Gjs-Message: 10:48:47.090: JS LOG: [LookingGlass/info] Cinnamon.AppSystem.get_default() started in 0 ms
Gjs-Message: 10:48:47.093: JS LOG: [LookingGlass/info] loading user theme: /usr/share/themes/Mint-Y-Dark-Orange/cinnamon/cinnamon.css
Gjs-Message: 10:48:47.112: JS LOG: [LookingGlass/info] added icon directory: /usr/share/themes/Mint-Y-Dark-Orange/cinnamon
Gjs-Message: 10:48:47.308: JS LOG: [LookingGlass/info] loaded at Sun May 18 2025 10:48:47 GMT+0200 (středoevropský letní čas)
Gjs-Message: 10:48:47.308: JS LOG: Cinnamon started at Sun May 18 2025 10:48:47 GMT+0200 (středoevropský letní čas)
Gjs-Message: 10:48:47.321: JS LOG: [LookingGlass/info] ExtensionSystem started in 1 ms
Gjs-Message: 10:48:47.321: JS LOG: [LookingGlass/info] DeskletManager started in 1 ms
Gjs-Message: 10:48:47.321: JS LOG: [LookingGlass/info] SearchProviderManager started in 0 ms
Gjs-Message: 10:48:47.400: JS LOG: [LookingGlass/info] Role locked: tray
Gjs-Message: 10:48:47.405: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 28 ms
Gjs-Message: 10:48:47.410: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 5 ms
Gjs-Message: 10:48:47.428: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 16 ms
Gjs-Message: 10:48:47.435: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 7 ms

** (at-spi2-registryd:1449): WARNING **: 10:48:47.602: Failed to register client: GDBus.Error:org.gnome.SessionManager.AlreadyRegistered: Unable to register client

** (at-spi2-registryd:1449): WARNING **: 10:48:47.602: Unable to register client with session manager
Gjs-Message: 10:48:48.017: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 582 ms
Gjs-Message: 10:48:48.079: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 59 ms
Gjs-Message: 10:48:48.143: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 64 ms
Gjs-Message: 10:48:48.182: JS LOG: [LookingGlass/info] Role locked: notifications
Gjs-Message: 10:48:48.227: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 81 ms
Gjs-Message: 10:48:48.255: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 28 ms
Gjs-Message: 10:48:48.414: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 153 ms
Gjs-Message: 10:48:48.549: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 133 ms
Gjs-Message: 10:48:48.626: JS LOG: [LookingGlass/info] Role locked: panellauncher
Gjs-Message: 10:48:48.626: JS LOG: [LookingGlass/info] Role locked: windowattentionhandler
Gjs-Message: 10:48:48.633: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 84 ms
Gjs-Message: 10:48:48.643: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: nm-applet (:1.50/org/x/StatusIcon/Icon)

** (csd-power:1323): CRITICAL **: 10:48:48.677: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 10:48:48.716: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 83 ms
Gjs-Message: 10:48:48.764: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet (:1.50/org/x/StatusIcon/Icon)
Gjs-Message: 10:48:48.782: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 65 ms
Nemo-Message: 10:48:48.872: nemo-desktop: session is cinnamon, establishing proxy
blueman-applet 10.48.48 WARNING  PluginManager:150 __load_plugin: Not loading PPPSupport because its conflict has higher priority
blueman-applet 10.48.48 WARNING  PluginManager:150 __load_plugin: Not loading DhcpClient because its conflict has higher priority
Gjs-Message: 10:48:49.032: JS LOG: [LookingGlass/info] <NAME_EMAIL> in 250 ms
Gjs-Message: 10:48:49.033: JS LOG: [LookingGlass/info] AppletManager started in 1724 ms
Gjs-Message: 10:48:49.041: JS LOG: [LookingGlass/info] Cinnamon took 2040 ms to start
Gjs-Message: 10:48:49.070: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 10:48:49.109: JS LOG: Unknown network device type, is 32
Gjs-Message: 10:48:49.298: JS LOG: [LookingGlass/info] GPU offload supported: false
Gjs-Message: 10:48:51.914: JS LOG: Enabling WindowAttentionHandler
/usr/lib/linuxmint/mintUpdate/mintUpdate.py:1394: DeprecationWarning: Gdk.threads_init is deprecated
  Gdk.threads_init()
/usr/lib/linuxmint/mintUpdate/mintUpdate.py:1778: DeprecationWarning: Gdk.threads_enter is deprecated
  Gdk.threads_enter()
/usr/lib/linuxmint/mintUpdate/mintUpdate.py:817: DeprecationWarning: Gdk.threads_leave is deprecated
  Gdk.threads_leave()
Gjs-Message: 10:49:08.923: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
blueman-applet 10.49.14 WARNING  DiscvManager:122 update_menuitems: warning: Adapter is None
Gjs-Message: 10:49:28.276: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 10:49:28.282: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 12:56:47.372: JS LOG: Unknown network device type, is 16
Gjs-Message: 13:05:50.260: JS LOG: Removing a network device that was not added
Gjs-Message: 13:05:50.892: JS LOG: Unknown network device type, is 16

** (cinnamon:1575): CRITICAL **: 15:30:06.967: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 15:30:07.022: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.023: JS LOG: [LookingGlass/info] Adding systray: network (16x16px)
Gjs-Message: 15:30:07.024: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.047: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.048: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.049: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.050: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.051: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.051: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.063: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.064: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.064: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.065: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.066: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.066: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.067: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.068: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.068: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.069: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.070: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.070: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.071: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.072: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.072: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.073: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.074: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.074: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.075: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.076: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.076: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.078: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.078: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.079: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.080: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.080: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.081: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.082: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.082: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.083: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.083: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.084: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.084: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.085: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.086: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.086: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.087: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.087: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.088: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.089: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.089: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.090: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.090: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.091: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.091: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.092: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.093: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.094: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.094: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.095: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.095: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.096: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.097: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.097: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.098: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.099: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.099: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.100: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.100: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.101: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.121: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.122: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.122: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.123: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:07.123: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:07.124: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 15:30:07.226: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 15:30:07.260: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 15:30:07.265: JS LOG: Unknown network device type, is 32
Gjs-Message: 15:30:07.272: JS LOG: Unknown network device type, is 16
Gjs-Message: 15:30:07.298: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 15:30:07.298: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 15:30:07.304: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 15:30:07.314: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)

(cinnamon:1575): Gjs-WARNING **: 15:30:07.327: JS ERROR: Exception in callback for signal: status-icon-removed: TypeError: parent is null
_onTrayIconRemoved@/usr/share/cinnamon/applets/<EMAIL>/applet.js:218:9
_callHandlers@resource:///org/gnome/gjs/modules/core/_signals.js:130:42
_emit@resource:///org/gnome/gjs/modules/core/_signals.js:119:10
_onTrayIconRemoved@/usr/share/cinnamon/js/ui/statusIconDispatcher.js:69:14

Gjs-Message: 15:45:45.289: JS LOG: Removing a network device that was not added
Gjs-Message: 15:45:45.291: JS LOG: Removing a network device that was not added
Gjs-Message: 15:45:46.035: JS LOG: Unknown network device type, is 16
Gjs-Message: 15:45:46.035: JS LOG: Unknown network device type, is 16

(cinnamon:1575): Clutter-WARNING **: 16:03:26.539: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:03:36.311: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:28:02.091: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:51:34.587: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.
Window manager warning: last_user_time (21130015) is greater than comparison timestamp (16064978).  This most likely represents a buggy client sending inaccurate timestamps in messages such as _NET_ACTIVE_WINDOW.  Trying to work around...
Window manager warning: 0x260007d appears to be one of the offending windows with a timestamp of 21130015.  Working around...

** (cinnamon:1575): CRITICAL **: 21:56:17.307: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 21:56:17.559: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 21:56:17.583: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 21:56:17.590: JS LOG: Unknown network device type, is 32
Gjs-Message: 21:56:17.601: JS LOG: Unknown network device type, is 16
Gjs-Message: 21:56:17.610: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 21:56:17.611: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 21:56:17.616: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 21:56:17.620: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)

** (cinnamon:1575): CRITICAL **: 17:32:49.508: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 17:32:49.753: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 17:32:49.793: JS LOG: Unknown network device type, is 32
Gjs-Message: 17:32:49.799: JS LOG: Unknown network device type, is 16
Gjs-Message: 17:32:49.801: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 17:32:49.802: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 17:32:49.807: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 17:32:49.811: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)

** (csd-power:1323): CRITICAL **: 17:32:49.815: abs_to_percentage: assertion 'max > min' failed

** (cinnamon:1575): CRITICAL **: 21:58:37.448: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 21:58:37.672: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 21:58:37.701: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 21:58:37.711: JS LOG: Unknown network device type, is 32
Gjs-Message: 21:58:37.717: JS LOG: Unknown network device type, is 16
Gjs-Message: 21:58:37.722: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 21:58:37.726: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 21:58:37.729: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 21:58:37.733: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (cinnamon:1575): CRITICAL **: 22:02:55.574: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 22:02:55.796: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 22:02:55.818: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 22:02:55.824: JS LOG: Unknown network device type, is 32
Gjs-Message: 22:02:55.830: JS LOG: Unknown network device type, is 16
Gjs-Message: 22:02:55.836: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 22:02:55.840: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 22:02:55.844: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 22:02:55.848: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (cinnamon:1575): CRITICAL **: 16:49:43.226: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 16:49:43.430: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 16:49:43.451: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 16:49:43.461: JS LOG: Unknown network device type, is 32
Gjs-Message: 16:49:43.468: JS LOG: Unknown network device type, is 16
Gjs-Message: 16:49:43.472: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 16:49:43.475: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 16:49:43.480: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 16:49:43.483: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)

** (cinnamon:1575): CRITICAL **: 12:55:16.738: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 12:55:16.956: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 12:55:16.966: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 12:55:16.973: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)

** (csd-power:1323): CRITICAL **: 12:55:16.975: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 12:55:16.976: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 12:55:16.994: JS LOG: Unknown network device type, is 32
Gjs-Message: 12:55:17.000: JS LOG: Unknown network device type, is 16
Gjs-Message: 12:55:17.002: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (cinnamon:1575): CRITICAL **: 21:58:08.310: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 21:58:08.535: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 21:58:08.562: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 21:58:08.569: JS LOG: Unknown network device type, is 32
Gjs-Message: 21:58:08.578: JS LOG: Unknown network device type, is 16
Gjs-Message: 21:58:08.585: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 21:58:08.590: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 21:58:08.592: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 21:58:08.596: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (cinnamon:1575): CRITICAL **: 20:04:14.216: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 20:04:14.441: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 20:04:14.459: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 20:04:14.462: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 20:04:14.468: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 20:04:14.474: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 20:04:14.502: JS LOG: Unknown network device type, is 32
Gjs-Message: 20:04:14.511: JS LOG: Unknown network device type, is 16
Gjs-Message: 20:04:14.516: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (cinnamon:1575): CRITICAL **: 20:47:43.247: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 20:47:43.495: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 20:47:43.510: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 20:47:43.518: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)

** (csd-power:1323): CRITICAL **: 20:47:43.519: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 20:47:43.530: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 20:47:43.536: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 20:47:43.564: JS LOG: Unknown network device type, is 32
Gjs-Message: 20:47:43.574: JS LOG: Unknown network device type, is 16

** (cinnamon:1575): CRITICAL **: 11:01:06.299: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 11:01:06.561: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 11:01:06.595: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 11:01:06.600: JS LOG: Unknown network device type, is 32
Gjs-Message: 11:01:06.605: JS LOG: Unknown network device type, is 16
Gjs-Message: 11:01:06.613: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 11:01:06.618: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 11:01:06.622: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 11:01:06.625: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet

** (csd-color:1305): WARNING **: 12:40:56.223: failed to reset xrandr-LG Electronics-LG TV SSCR2-16843009 gamma tables: gamma size is zero

** (csd-color:1305): WARNING **: 12:40:56.223: failed to reset xrandr-LG Electronics-LG TV SSCR2-16843009 gamma tables: gamma size is zero

** (cinnamon:1575): CRITICAL **: 12:40:56.423: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 12:40:56.676: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 12:40:56.742: JS LOG: Unknown network device type, is 32
Gjs-Message: 12:40:56.749: JS LOG: Unknown network device type, is 16
Gjs-Message: 12:40:56.752: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 12:40:56.752: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 12:40:56.757: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 12:40:56.761: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)

** (csd-power:1323): CRITICAL **: 12:40:56.782: abs_to_percentage: assertion 'max > min' failed

(cinnamon:1575): Clutter-WARNING **: 12:44:42.612: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

** (cinnamon:1575): CRITICAL **: 15:30:08.897: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 15:30:08.961: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:08.962: JS LOG: [LookingGlass/info] Adding systray: network (16x16px)
Gjs-Message: 15:30:08.963: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:08.985: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:08.986: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:08.986: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:08.988: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:08.988: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:08.989: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.001: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.002: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.002: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.003: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.004: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.004: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.005: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.006: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.006: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.007: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.008: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.009: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.010: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.010: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.011: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.012: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.012: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.013: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.014: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.014: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.015: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.016: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.017: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.017: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.018: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.019: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.019: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.020: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.021: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.021: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.022: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.023: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.023: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.024: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.024: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.025: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.026: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.026: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.027: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.028: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.029: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.029: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.030: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.031: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.032: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.033: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.033: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.034: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.035: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.036: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.036: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.037: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.038: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.038: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.040: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.040: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.041: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.042: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.042: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.043: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.071: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.071: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.071: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.072: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 15:30:09.073: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 15:30:09.073: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (1x1px)
Gjs-Message: 15:30:09.196: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 15:30:09.217: JS LOG: Unknown network device type, is 32
Gjs-Message: 15:30:09.225: JS LOG: Unknown network device type, is 16
Gjs-Message: 15:30:09.237: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 15:30:09.238: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)

** (csd-power:1323): CRITICAL **: 15:30:09.246: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 15:30:09.247: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)
Gjs-Message: 15:30:09.251: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)

(cinnamon:1575): Gjs-WARNING **: 15:30:09.320: JS ERROR: Exception in callback for signal: status-icon-removed: TypeError: parent is null
_onTrayIconRemoved@/usr/share/cinnamon/applets/<EMAIL>/applet.js:218:9
_callHandlers@resource:///org/gnome/gjs/modules/core/_signals.js:130:42
_emit@resource:///org/gnome/gjs/modules/core/_signals.js:119:10
_onTrayIconRemoved@/usr/share/cinnamon/js/ui/statusIconDispatcher.js:69:14


(cinnamon:1575): Clutter-WARNING **: 15:46:43.836: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 15:47:04.411: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 15:48:12.214: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:10:37.752: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:10:50.567: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:11:06.241: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:16:57.973: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:17:04.692: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:40:45.801: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:41:06.927: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:41:14.711: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:46:41.100: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:50:46.215: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:50:49.248: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:50:50.874: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:51:14.198: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:51:18.205: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:51:19.382: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 16:51:26.673: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

(cinnamon:1575): Clutter-WARNING **: 18:34:44.818: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.

** (cinnamon:1575): CRITICAL **: 21:29:50.437: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 21:29:50.510: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.511: JS LOG: [LookingGlass/info] Adding systray: network (16x16px)
Gjs-Message: 21:29:50.512: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.531: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.532: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.532: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.533: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.534: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.534: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.545: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.546: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.547: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.548: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.549: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.549: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.550: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.551: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.552: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.553: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.554: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.554: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.555: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.556: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.556: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.557: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.558: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.558: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.559: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.560: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.560: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.561: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.562: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.562: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.563: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.564: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.564: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.565: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.566: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.566: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.567: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.568: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.568: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.569: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.570: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.570: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.571: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.572: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.572: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.573: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.574: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.574: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.575: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.575: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.576: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.577: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.577: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.578: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.579: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.580: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.580: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.581: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.582: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.582: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.583: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.584: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.584: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.585: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.586: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.586: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.617: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.617: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.617: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.618: JS LOG: [LookingGlass/info] Adding systray: mintupdate.py (16x16px)
Gjs-Message: 21:29:50.619: JS LOG: [LookingGlass/info] Hiding systray: network
Gjs-Message: 21:29:50.619: JS LOG: [LookingGlass/info] Adding systray: tray.py (16x16px)
Gjs-Message: 21:29:50.743: JS LOG: <EMAIL>: Calendar events supported.

** (csd-power:1323): CRITICAL **: 21:29:50.804: abs_to_percentage: assertion 'max > min' failed
Gjs-Message: 21:29:50.812: JS LOG: Unknown network device type, is 32
Gjs-Message: 21:29:50.819: JS LOG: Unknown network device type, is 16
Gjs-Message: 21:29:50.823: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 21:29:50.832: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 21:29:50.832: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 21:29:50.838: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)

(cinnamon:1575): Gjs-WARNING **: 21:29:50.858: JS ERROR: Exception in callback for signal: status-icon-removed: TypeError: parent is null
_onTrayIconRemoved@/usr/share/cinnamon/applets/<EMAIL>/applet.js:218:9
_callHandlers@resource:///org/gnome/gjs/modules/core/_signals.js:130:42
_emit@resource:///org/gnome/gjs/modules/core/_signals.js:119:10
_onTrayIconRemoved@/usr/share/cinnamon/js/ui/statusIconDispatcher.js:69:14


** (cinnamon:1575): CRITICAL **: 10:49:39.640: na_tray_manager_manage_screen: assertion 'manager->screen == NULL' failed
Gjs-Message: 10:49:39.909: JS LOG: <EMAIL>: Calendar events supported.
Gjs-Message: 10:49:39.976: JS LOG: Unknown network device type, is 32
Gjs-Message: 10:49:39.985: JS LOG: Unknown network device type, is 16
Gjs-Message: 10:49:39.991: JS LOG: [LookingGlass/info] Hiding XAppStatusIcon (we have an applet): nm-applet
Gjs-Message: 10:49:39.991: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintUpdate.py (:1.74/org/x/StatusIcon/Icon)
Gjs-Message: 10:49:39.997: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: mintreport (:1.79/org/x/StatusIcon/Icon)
Gjs-Message: 10:49:40.003: JS LOG: [LookingGlass/info] Adding XAppStatusIcon: process-monitor (:1.79/org/x/StatusIcon/Icon_1)

** (csd-power:1323): CRITICAL **: 10:49:40.022: abs_to_percentage: assertion 'max > min' failed
Window manager warning: Buggy client sent a _NET_ACTIVE_WINDOW message with a timestamp of 0 for 0x5800003

(cinnamon:1575): Clutter-WARNING **: 12:48:44.957: Attempting to remove actor of type 'StIcon' from group of class 'ClutterBox', but the container is not the actor's parent.
