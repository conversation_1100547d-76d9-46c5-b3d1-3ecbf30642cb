#!/bin/bash

echo "=== Bybit Trading Bot & Monitor ==="
cd /home/<USER>/bybit-trading-assistant

# Aktivace virtuálního prostředí
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "CHYBA: Virtuální prostředí nenalezeno!"
    exit 1
fi

# Spustit strategie a monitoring v pozadí
echo "Spouštím obchodní strategie v pozadí..."
./run_strategies.sh &
STRATEGIES_PID=$!

# Počkat 3 sekundy pro inicializaci
sleep 3

# Spustit dashboard
echo "Spouštím dashboard pro monitoring..."
streamlit run dashboard.py

# Při ukončení dashboardu ukončit i obchodní strategie
kill $STRATEGIES_PID
echo "Aplikace byla ukončena."