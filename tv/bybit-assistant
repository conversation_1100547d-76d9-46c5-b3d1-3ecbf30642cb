#!/bin/bash

echo "=== Bybit Trading Assistant ==="
echo "Spouštím aplikaci..."
cd /home/<USER>/bybit-trading-assistant

# <PERSON><PERSON><PERSON><PERSON>, zda je virtuální prostředí
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "CHYBA: Virtuální prostředí nenalezeno!"
    echo "Spustím setup.sh pro vytvoření prostředí."
    ./setup.sh
    source venv/bin/activate
fi

# Zkontrolovat, zda je nainstalován streamlit
if ! command -v streamlit &> /dev/null; then
    echo "CHYBA: Streamlit není nainstalován!"
    echo "Instaluji streamlit..."
    pip install streamlit
fi

# Zkontrolovat, zda existuje dashboard.py
if [ ! -f "dashboard.py" ]; then
    echo "CHYBA: Soubor dashboard.py nenalezen!"
    exit 1
fi

# Spustit dashboard
echo "Spouštím dashboard pro monitoring aplikace..."
streamlit run dashboard.py