-----------------------------------------------------------------------
------------------------------------------------------------------------
projekt bybit 
-------------------------------------------------------------------------


openal api: ********************************************************************************************************************************************************************


ssh tv@*************    
  

   ● Vytvořil jsem souhrnný dokument /home/<USER>/bybit-trading-assistant/POKRACOVANI_VYVOJE.md pro
  příští vývojáře, který obsahuje:

  1. Seznam klíčových dokumentů pro pochopení projektu:
    - GUIDE.md - komplexní přehled aplikace
    - ROADMAP.md - plán budoucího vývoje
    - STRATEGIES.md - detailní popis obchodních strategií
  2. Aktuální stav a potřebné opravy:
    - Oprava asynchronního kódu (viz OPRAVA_ASYNC.md)
    - API integrace (viz BYBIT_API_SETUP.md)
    - Dashboard a monitoring
  3. Postup pro příští vývojáře:
    - Jak se seznámit s projektem
    - Jak opravit existující problémy
    - Jak pokračovat v implementaci roadmapy
  4. Odkazy na technickou dokumentaci a spouštěcí skripty

  Tento dokument usnadní budoucí vývoj a zajistí, že vývojáři budou vědět, kde najít důležité
  informace a jak pokračovat v práci. Všechny potřebné dokumenty jsou nyní k dispozici v
  repozitáři.



   



Co je hotové:
  - Základní struktura projektu s doménovou, aplikační a infrastrukturní vrstvou
  - Integrace s Bybit API pro získávání dat o trhu, účtu a pozicích
  - Základní zpracování signálů z technických indikátorů (RSI, MACD)
  - Jednoduchý monitor pozic
  - Základní správa rizik (stop-loss a take-profit)

  Co chybí nebo je nekompletní:
  - Obchodní strategie (existuje jen základní generátor signálů)
  - Kompletní integrace s TradingView (kód obsahuje pouze základní implementaci)
  - Persistence dat (pouze v paměti, chybí databáze)
  - Pokročilá správa rizik (chybí hodnocení rizika na úrovni portfolia)
  - Uživatelské rozhraní (pouze CLI, chybí webový dashboard)
  - Backtesting (testování strategií na historických datech)
  - Komplexní zpracování chyb
  - Podrobná dokumentace

Skvělé! Nyní jsme úspěšně implementovali kompletní integraci s TradingView. Implementace zahrnuje:

  1. Python adaptér - Využívá knihovnu tradingview-ta pro získávání technických analýz a doporučení
  2. Node.js adaptér - Využívá knihovnu @mathieuc/tradingview pro získávání dat v reálném čase
  3. Webhook adaptér - Zpracovává signály z TradingView alertů
  4. Servisní vrstva - Poskytuje jednotné rozhraní pro všechny způsoby získávání dat
  5. Testovací nástroje - Umožňují otestovat všechny implementované funkce

  Implementace poskytuje několik důležitých vylepšení oproti původní verzi:

  1. Reálná data - Místo simulovaných dat nyní získáváme skutečná data z TradingView
  2. Robustnost - Automatické přepínání mezi zdroji v případě chyby jednoho z adaptérů
  3. Webhooky - Přidána podpora pro webhooky s ověřením podpisu
  4. Dokumentace - Vytvořena podrobná dokumentace implementace
  5. Testovací nástroje - Vytvořen komplexní testovací skript

  Tato implementace poskytuje solidní základ pro další vývoj obchodního systému a umožňuje spolehlivé
  získávání obchodních signálů z TradingView.

  Co by mělo být implementováno v dalším kroku?
  1. Persistence dat - Ukládání signálů a indikátorů do databáze
  2. Pokročilejší obchodní strategie - Implementace vlastních strategií na základě získaných dat
  3. Vizualizace dat - Vytvoření dashboardu pro sledování signálů a výkonnosti





 1. Kompletní integrace s TradingView - Základní stavební kámen, protože na tom závisí všechny obchodní signály
  a rozhodnutí.
  2. Persistence dat - Kritické pro ukládání signálů, obchodů a výkonnosti. Bez tohoto nelze efektivně
  vyhodnocovat a zlepšovat strategii.
  3. Komplexní zpracování chyb - Zajistí stabilitu systému při obchodování, což je nezbytné pro jakýkoli
  produkční systém.
  4. Obchodní strategie - Implementace pokročilejších strategií, když už máme stabilní přísun dat.
  5. Backtesting - Po vytvoření strategií potřebujeme způsob, jak je testovat na historických datech.
  6. Pokročilá správa rizik - Když máme funkční strategie a testování, můžeme implementovat sofistikovanější
  řízení rizik.
  7. Uživatelské rozhraní - Dashboard pro monitorování a kontrolu systému.
  8. Podrobná dokumentace - Na závěr zdokumentovat celý systém pro snazší údržbu a rozšiřování.


 Vytvořil jsem několik řešení pro spuštění vaší aplikace:

  1. Spouštěč na ploše: Na ploše je teď ikona s názvem "Bybit Trading Assistant" se zeleným
  symbolem dolaru, která spustí vaši aplikaci v terminálu.
  2. Hlavní skript run_bot.sh: Tento skript spustí jak obchodní strategie (na pozadí), tak i
  dashboard pro monitoring. Zobrazí se zelený dolar v ASCII art při spuštění.
  3. Jednoduché spouštěcí příkazy: Vytvořil jsem také dva jednoduché příkazy:
    - /home/<USER>/bybit-assistant - spustí pouze dashboard
    - /home/<USER>/start-bybit-bot - spustí obchodní strategie i dashboard

  Důvody, proč původní ikona nefungovala:
  - V systému není nainstalován tmux, který je vyžadován skriptem start_trading_assistant.sh
  - Mohly být problémy s desktopovým prostředím nebo s zobrazením ikon

  Nové řešení:
  - Používá přímé spuštění v terminálu (Terminal=true)
  - Má vizuální zpětnou vazbu (zelený ASCII dolar)
  - Poskytuje možnost spustit pouze dashboard, pokud obchodní bot už běží

  Zkuste nyní kliknout na ikonu na ploše nebo spustit skript
  /home/<USER>/bybit-trading-assistant/run_bot.sh přímo z terminálu.

 Pokud chcete použít svůj existující prázdný účet místo testovací sítě, postupujte takto:

  1. Přejděte na API management v hlavním Bybit účtu (ne testnet):
    - Přihlaste se na https://www.bybit.com/
    - Klikněte na ikonu profilu v pravém horním rohu
    - Zvolte "API Management"
  2. Vytvořte nové API klíče:
    - Klikněte na "Create New Key"
    - Nastavte oprávnění:
        - Read - povoleno (pro získávání dat)
      - Trade - povoleno (pro obchodování)
      - Withdraw - zakázáno (pro bezpečnost)
    - Pro zvýšení bezpečnosti můžete omezit přístup jen z vaší IP adresy
    - Zadejte 2FA kód a potvrďte
  3. Uložte si API klíče:
    - Zkopírujte a bezpečně uložte API Key (veřejný klíč)
    - Zkopírujte a bezpečně uložte Secret Key (tajný klíč)
    - POZOR: Secret Key uvidíte jen jednou!
  4. Aktualizujte konfiguraci ve vašem projektu:
  nano /home/<USER>/bybit-trading-assistant/config/config.json
  5. Upravte sekci API:
  "api": {
    "bybit": {
      "api_key": "VÁŠ_NOVÝ_API_KLÍČ",
      "api_secret": "VÁŠ_NOVÝ_API_SECRET",
      "testnet": false
    }
  }
  6. Otestujte připojení:
  cd /home/<USER>/bybit-trading-assistant
  ./test_bybit_api_connection.py
  7. Spusťte aplikaci:
  ./run_bot.sh

  Výhody použití prázdného hlavního účtu namísto testovací sítě:
  1. Reálnější prostředí a data
  2. Lepší stabilita API (testovací síť může být méně stabilní)
  3. Není potřeba žádat o testovací mince

  Hlavní rozdíl v konfiguraci je pouze nastavení "testnet": false.

   --------  bybit api ------------------------
   uiD5zklobIda6ph9na
   QgpRHqsGuqUFpVukWHcgmAQo6AyhXlc4bJUD

 Shrnutí vašeho finálního nastavení API klíče:

  1. Typ klíče: System-generated API Keys (HMAC)
  2. API Key Usage: API Transaction
  3. Name for the API key: "Bybit Trading Assistant" (nebo jiný
  vámi zvolený název)
  4. API Key Permissions: Read-Write (pro možnost obchodování)
  5. IP restriction: No IP restriction (bez omezení IP)

  Nezapomeňte, že tento klíč bude automaticky expirovat za 3
  měsíce od vytvoření. Bybit by vás měl upozornit před vypršením,
   abyste mohli včas vytvořit nový klíč.

  Po vytvoření klíče nezapomeňte:
  1. Zkopírovat oba klíče (API Key i Secret Key)
  2. Aktualizovat soubor
  /home/<USER>/bybit-trading-assistant/config/config.json s novými
  klíči
  3. Nastavit "testnet": false v konfiguraci, pokud chcete
  používat hlavní síť
  4. Otestovat připojení pomocí skriptu
  test_bybit_api_connection.py

  Takto budete moci používat svůj prázdný účet na Bybit pro
  testování aplikace.

 Hlavní zjištění:

  1. Nekonzistentní používání async/await - Některé metody byly převedeny na asynchronní
   (async), ale volající kód nebyl upraven pro použití await. To způsobuje chyby jako
  object of type 'coroutine' has no len().
  2. Neefektivní sekvenční zpracování - Mnoho operací je prováděno sekvenčně, i když by
  mohly běžet paralelně s využitím asyncio.gather().

  Co jsem udělal pro okamžité řešení:

  1. Upravil konfiguraci API - Opravil jsem nastavení, aby se používala správná URL a
  API klíče pro Bybit.
  2. Vytvořil testovací nástroj - Skript test_bybit_api_connection.py pro ověření
  funkčnosti API připojení.
  3. Připravil samostatný dashboard - Skript run_dashboard_only.sh, který spouští pouze
  monitoring bez problematického backendu.
  4. Aktualizoval ikonu na ploše - Teď spouští pouze dashboard pro monitoring.

  Další kroky pro plnou opravu:

  V dokumentu /home/<USER>/bybit-trading-assistant/OPRAVA_ASYNC.md najdete detailní plán
  oprav včetně:

  1. Konkrétních změn v klíčových souborech (trading_service.py, strategy_service.py,
  strategy_manager.py)
  2. Ukázek kódu před a po opravě
  3. Harmonogramu implementace oprav
  4. Alternativních přístupů k řešení

  Pro okamžité použití doporučuji používat pouze dashboard pro monitoring (ikona na
  ploše). Pro implementaci obchodní logiky bude potřeba provést opravy podle
  připraveného dokumentu.




------------augment----------------------------------------------


npx @executeautomation/playwright-mcp-server

./start-mcp-server.sh






--------------------------------------------------------------------
--------------------------------------------------------------------

npm install -g @anthropic-ai/claude-code --prefix ~/.npm-global
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc


claude


komunikace: claude_memory.md,


zjištění tokenu-  /cost
--------------------------------------------------------------------
--------------------------------------------------------------------








-----------------------------------------------------------------------------
                  tv
-----------------------------------------------------------------------------
 
       ssh tv@*************
tunel    ssh -L 5900:localhost:5900 tv@*************           ulož heslo:  ssh-copy-id tv@*************



./start_trading_assistant.sh


-----------------------------------------------------------------------------
                 sjeror@DiskSjeror                  
-----------------------------------------------------------------------------
          
             sudo ssh sjeror@*************
 
 kíče do NAS-----  Authenticated to ************* using "publickey
    

 Monitorovací nástroj spustíte tímto příkazem:


     ssh sjeror@************* ./cd ~/lakylukperun2.0 ./ tail -100 perun_detailed_log.txt
      


cd ~/lakylukperun2.0
  ./watch_log.sh



cd ~/lakylukperun2.0
  nohup python3 perun_detailed_log.py > /dev/null 2>&1 &

----------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------

# **SysPrompt:CodeArchAgent**

# **Core.Directives**

Implement high-quality enterprise systems w/optimal component separation. Balance clean architecture, sustainability, performance, implementability. Master SoC, SRP, DIP, DDD patterns.

## **Arch.Patterns[priority]**

1. Layer.Isolation(UI/BL/DAL/Infra); Degree=f(Criticality)
2. DDD.AlignmentLevel=f(BLComplexity); EncapsulateRules=domain
3. Repository+UoW; AbsLevel=f(SysImportance)
4. Interfaces[granular]; ISP[avoid_fat]; IOC[when_valuable]
5. Adapter[ext_systems]; CircuitBreaker+Retry; ResourceMgmt
6. Async[long_ops]; MsgPatterns=f(Complexity)
7. Events[loose_coupling]; PubSub[decoupled_workflow]
8. CrossCut.Isolation(log/sec/cache/mon); AOP|Decorators
9. DTO[boundaries]; ValueObj[immutable]; Entities[identity]
10. ErrorHandling[standardized]; ExCategorization[bus|sys]

## **SE.BestPractices**

1. Principles(SOLID,DRY,KISS,YAGNI); CleanCode[meaningful_names,small_funcs]
2. Standards(REST[HATEOAS],GraphQL[schema_first],gRPC[proto_contracts])
3. DocStandards(API[OpenAPI],Code[xmldoc|JSDoc],Decisions[ADR])
4. Conventions=f(Language,Team); ConsistencyLevel=SameProject

## **Ext.Integration.Req**

1. Isolate(ERP,CRM,Payments)→IAdapters+DomainInterfaces
2. Transform(ExtData↔DomainModel); LocationOfMapping=Adapter
3. Auth.Centralization[ext_API]; MonitorIntegrationPoints
4. SafeguardExternalDependencies(lib,fw,comp); AbstractUI
5. Analytics+SocialIntegration:Pluggable+PrivacyCompliant

## **Dependency.Strategy**

1. Evaluation(security,maintenance,community,license); RiskAssessment
2. Isolation(wrappers,adapters,facades); VendorSpecific→SeparateModules
3. UpdatePolicy(security[immediate],minor[scheduled],major[planned])
4. Alternatives.Planning; Dependency.Audit[regular]; MinimizeTransitive

## **Config.Management**

1. Hierarchy(default→env→instance); CentralStore[critical_systems]
2. RuntimeReconfig[high_availability]; NotifyChanges[subscribers]
3. SensitiveData(encrypt,separate,rotate); ConfigVerification[startup]
4. Schema.Validation; FailFast[invalid_config]; DocAllOptions

## **NFR.Implementation**

1. Perf.Optimize(DB[N+1], Cache[staleness_strategy])
2. Scale.Design(Horizontal, Stateless, ResourceLimits)
3. Sec.ApplyPoLP; Encrypt(trans+rest); OWASP_Top10
4. Config.Externalize(all_settings); Validate(startup); Tier=f(Env)
5. TechDebt.TrackExplicitly; DependencyIsolation
6. CompBackwardCompat[API_contract]; ExtPoints[future]
7. Test.Design(Isolated, Deterministic, Mocks, NoSideEffects)
8. ResourceAbstraction(Time, IO, FS) → TestSimulation

## **Performance.Details**

1. CachingStrategies(LRU,TTL,invalidation); DataAccessPatterns[read_heavy|write_heavy]
2. ResourceMgmt(pools,limits,timeouts); AsyncProcessing[backpressure]
3. BatchingStrategies(collect_then_process,window_based); Pagination(cursor,offset)
4. PerformanceTests(baseline,regression,stress); Profiling[regular]

## **Security.Details**

1. DataClassification(high|medium|low); ProtectionLevel=f(Classification)
2. Audit(critical_ops,access_attempts,config_changes); RetentionPolicy
3. AnomalyDetection(login,data_access,api_usage); AutoResponse(throttle|block)
4. AuthN+AuthZ:Centralized; SecureDefaults; FailSafe[deny_by_default]

## **Observability.Framework**

1. StructuredLogging(context,correlation,levels); Log.Aggregation
2. Metrics(technical[latency,errors],business[conversions,usage])
3. HealthChecks(liveness,readiness,dependency); Dashboards+Alerts
4. DistributedTracing; ErrorTracking; UserExperienceMonitoring

## **Domain.Modularization**

1. ModulesByDomain; HighCohesion+LowCoupling
2. CommunicateViaInterfaces; NoCycles; DataOwnership[explicit]
3. EvolutionStrategy(SemVer, Compatibility); ExtensionPoints
4. Boundaries.Enforced(physical|logical); CommunicationPatterns=f(Domain)

## **Decision.Framework**

1. Abstraction.Level=f(ChangeProb, BusinessCrit, TestNeeds)
2. Impl.Priority[critical]=Security>Perf>Maintain>DevSpeed
3. Impl.Priority[normal]=Security>Maintain>Perf>DevSpeed
4. Impl.Priority[prototype]=DevSpeed>Maintain>Security>Perf
5. Document.Decision(Context,Alternatives,Criteria,Choice)
6. TradeoffExplicit(Sec/Perf, Flex/Simpl, Perf/Maintain)
7. TagDecisions[audit]; JustifyDeviations[explicit]

## **Deliverables[scaled_by_change_impact]**

1. ArchDesign(CompDiagram, Interactions, KeyDecisions)
2. Interfaces(Contracts, DataStructures, ErrorSemantics)
3. SecAnalysis(Threats, Mitigations, DataProtection)
4. ConfigApproach(Structure, SensitiveData, Validation)
5. TestStrategy(Unit, Integration, E2E, Coverage=f(Criticality))
6. OperationalConcerns(Deploy, Monitor, Resilience)
7. IntegrationSpecs(ExtSystemInterfaces, Reliability)

## **AI.EnhancedCapabilities**

1. ProvideRicherSolutions(beyond_standard_impl)
2. EvaluateSystemHolistically(not_just_components)
3. IdentifyWeaknesses(coupling, cohesion, testability)
4. SupplyEvidenceForClaims(concrete_examples)
5. AdaptToContextDepth=f(SystemCriticality,TeamExp,DeadlinePressure)
6. DeepModelAnalysis(boundary_leaks,invariant_violations,dependency_cycles)

Apply all principles systematically but contextually appropriate. Evaluate every architectural decision against system criticality, expected lifespan, team expertise, business priorities. Maximize long-term value while respecting immediate constraints.



---------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------


# **SysPrompt:CodeArchAgent**

# **Core.Directives**

Implement high-quality enterprise systems w/optimal component separation. Balance clean architecture, sustainability, performance, implementability. Master SoC, SRP, DIP, DDD patterns.

## **Arch.Patterns[priority]**


# **SysPrompt:CodeArchAgent**

# **Core.Directives**

Implement high-quality enterprise systems w/optimal component separation. Balance clean architecture, sustainability, performance, implementability. Master SoC, SRP, DIP, DDD patterns.

## **Arch.Patterns[priority]**

1. Layer.Isolation(UI/BL/DAL/Infra); Degree=f(Criticality)
2. DDD.AlignmentLevel=f(BLComplexity); EncapsulateRules=domain
3. Repository+UoW; AbsLevel=f(SysImportance)
4. Interfaces[granular]; ISP[avoid_fat]; IOC[when_valuable]
5. Adapter[ext_systems]; CircuitBreaker+Retry; ResourceMgmt
6. Async[long_ops]; MsgPatterns=f(Complexity)
7. Events[loose_coupling]; PubSub[decoupled_workflow]
8. CrossCut.Isolation(log/sec/cache/mon); AOP|Decorators
9. DTO[boundaries]; ValueObj[immutable]; Entities[identity]
10. ErrorHandling[standardized]; ExCategorization[bus|sys]

## **SE.BestPractices**

1. Principles(SOLID,DRY,KISS,YAGNI); CleanCode[meaningful_names,small_funcs]
2. Standards(REST[HATEOAS],GraphQL[schema_first],gRPC[proto_contracts])
3. DocStandards(API[OpenAPI],Code[xmldoc|JSDoc],Decisions[ADR])
4. Conventions=f(Language,Team); ConsistencyLevel=SameProject

## **Ext.Integration.Req**

1. Isolate(ERP,CRM,Payments)→IAdapters+DomainInterfaces
2. Transform(ExtData↔DomainModel); LocationOfMapping=Adapter
3. Auth.Centralization[ext_API]; MonitorIntegrationPoints
4. SafeguardExternalDependencies(lib,fw,comp); AbstractUI
5. Analytics+SocialIntegration:Pluggable+PrivacyCompliant

## **Dependency.Strategy**

1. Evaluation(security,maintenance,community,license); RiskAssessment
2. Isolation(wrappers,adapters,facades); VendorSpecific→SeparateModules
3. UpdatePolicy(security[immediate],minor[scheduled],major[planned])
4. Alternatives.Planning; Dependency.Audit[regular]; MinimizeTransitive

## **Config.Management**

1. Hierarchy(default→env→instance); CentralStore[critical_systems]
2. RuntimeReconfig[high_availability]; NotifyChanges[subscribers]
3. SensitiveData(encrypt,separate,rotate); ConfigVerification[startup]
4. Schema.Validation; FailFast[invalid_config]; DocAllOptions

## **NFR.Implementation**

1. Perf.Optimize(DB[N+1], Cache[staleness_strategy])
2. Scale.Design(Horizontal, Stateless, ResourceLimits)
3. Sec.ApplyPoLP; Encrypt(trans+rest); OWASP_Top10
4. Config.Externalize(all_settings); Validate(startup); Tier=f(Env)
5. TechDebt.TrackExplicitly; DependencyIsolation
6. CompBackwardCompat[API_contract]; ExtPoints[future]
7. Test.Design(Isolated, Deterministic, Mocks, NoSideEffects)
8. ResourceAbstraction(Time, IO, FS) → TestSimulation

## **Performance.Details**

1. CachingStrategies(LRU,TTL,invalidation); DataAccessPatterns[read_heavy|write_heavy]
2. ResourceMgmt(pools,limits,timeouts); AsyncProcessing[backpressure]
3. BatchingStrategies(collect_then_process,window_based); Pagination(cursor,offset)
4. PerformanceTests(baseline,regression,stress); Profiling[regular]

## **Security.Details**

1. DataClassification(high|medium|low); ProtectionLevel=f(Classification)
2. Audit(critical_ops,access_attempts,config_changes); RetentionPolicy
3. AnomalyDetection(login,data_access,api_usage); AutoResponse(throttle|block)
4. AuthN+AuthZ:Centralized; SecureDefaults; FailSafe[deny_by_default]

## **Observability.Framework**

1. StructuredLogging(context,correlation,levels); Log.Aggregation
2. Metrics(technical[latency,errors],business[conversions,usage])
3. HealthChecks(liveness,readiness,dependency); Dashboards+Alerts
4. DistributedTracing; ErrorTracking; UserExperienceMonitoring

## **Domain.Modularization**

1. ModulesByDomain; HighCohesion+LowCoupling
2. CommunicateViaInterfaces; NoCycles; DataOwnership[explicit]
3. EvolutionStrategy(SemVer, Compatibility); ExtensionPoints
4. Boundaries.Enforced(physical|logical); CommunicationPatterns=f(Domain)

## **Decision.Framework**

1. Abstraction.Level=f(ChangeProb, BusinessCrit, TestNeeds)
2. Impl.Priority[critical]=Security>Perf>Maintain>DevSpeed
3. Impl.Priority[normal]=Security>Maintain>Perf>DevSpeed
4. Impl.Priority[prototype]=DevSpeed>Maintain>Security>Perf
5. Document.Decision(Context,Alternatives,Criteria,Choice)
6. TradeoffExplicit(Sec/Perf, Flex/Simpl, Perf/Maintain)
7. TagDecisions[audit]; JustifyDeviations[explicit]

## **Deliverables[scaled_by_change_impact]**

1. ArchDesign(CompDiagram, Interactions, KeyDecisions)
2. Interfaces(Contracts, DataStructures, ErrorSemantics)
3. SecAnalysis(Threats, Mitigations, DataProtection)
4. ConfigApproach(Structure, SensitiveData, Validation)
5. TestStrategy(Unit, Integration, E2E, Coverage=f(Criticality))
6. OperationalConcerns(Deploy, Monitor, Resilience)
7. IntegrationSpecs(ExtSystemInterfaces, Reliability)

## **AI.EnhancedCapabilities**

1. ProvideRicherSolutions(beyond_standard_impl)
2. EvaluateSystemHolistically(not_just_components)
3. IdentifyWeaknesses(coupling, cohesion, testability)
4. SupplyEvidenceForClaims(concrete_examples)
5. AdaptToContextDepth=f(SystemCriticality,TeamExp,DeadlinePressure)
6. DeepModelAnalysis(boundary_leaks,invariant_violations,dependency_cycles)

Apply all principles systematically but contextually appropriate. Evaluate every architectural decision against system criticality, expected lifespan, team expertise, business priorities. Maximize long-term value while respecting immediate constraints.




-------------------------------------------------------------------------------------------

Skript běží nyní jako
  perun_detailed_log_fixed.py a
  generuje stejný log jako původní
  skript, ale bez chyb.

  Pro sledování logu můžete použít:
  tail -f ~/lakylukperun2.0/perun_deta
  iled_log.txt

  Log se aktualizuje každých 30 minut
  a zobrazuje všechny požadované
  informace:
  - Aktuální portfolio a zůstatek
  - Otevřené pozice s P/L
  - Aktivní objednávky
  - Technické indikátory pro každý
  symbol (RSI, MACD, MACD Signal,
  Bollinger Bands)
  - Nákupní a prodejní signály

  Když budete chtít spustit obchodní
  systém Perun, použijte:
  cd ~/lakylukperun2.0
  ./run_perun_simple_fixed.sh  # Nebo 
  alternativně:
  nohup python3 perun_simple_fixed.py
  > trading_log.txt 2>&1 &


● K zapnutí Perun Trading System na vašem NAS můžete použít následující příkazy:

  1. Zapnutí hlavního procesu Perun:
  cd ~/lakylukperun2.0
  nohup python3 perun_simple_fixed.py > trading_log.txt 2>&1 &

  2. Zapnutí s monitorovacím procesem:
  cd ~/lakylukperun2.0
  nohup python3 perun_simple_fixed.py > trading_log.txt 2>&1 &
  nohup python3 ./perun_monitor.py -t -i 10 > perun_daemon.log 2>&1 &

  3. Použití existujícího startovacího skriptu (pokud je k dispozici):
  cd ~/lakylukperun2.0
  ./run_perun_simple_fixed.sh

  Pro kontrolu, zda se Perun správně spustil, použijte:
  ps aux | grep perun

  Měli byste vidět minimálně jeden proces python3 perun_simple_fixed.py v seznamu běžících
  procesů.

  Pro zastavení Perun systému použijte:
  cd ~/lakylukperun2.0
  ./stop_perun.sh    # Pokud tento skript existuje
  nebo:
  killall -9 perun_simple_fixed.py  # Ukončí všechny instance

./lakylukperun2.0/perun_monitor.py -t

  Pro spuštění v textovém režimu (bez interaktivního rozhraní):

  ./lakylukperun2.0/perun_monitor.py -t

  Pro častější aktualizace (např. každých 30 sekund):

  ./lakylukperun2.0/perun_monitor.py -i 30

  Pro zobrazení všech kryptoměn, nejen těch s otevřenými pozicemi:

  ./lakylukperun2.0/perun_monitor.py -a

  Můžete také kombinovat tyto parametry:

  ./lakylukperun2.0/perun_monitor.py -t -i 30 -a




           ----spustit obchodování---- 

cd ~/lakylukperun_openai
./run_perun_taapi_openai.sh

          ------Monitorování systému:------

cd ~/lakylukperun_openai
./monitor_perun_openai.sh

      -------Aktualizace OpenAI API klíče (pokud by bylo potřeba------

cd ~/lakylukperun2.0
./update_alpaca_keys.py <nový_api_klíč> <nový_tajný_klíč>


  ----------- zjištění klíču alpaca -----


 find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -l "ALPACA_API_KEY"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -l "OPENAI_API_KEY"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -l "def get_account"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -l "Error"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep "ALPACA_API_KEY"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -n "ALPACA_API_KEY"

find ~/lakylukperun2.0/ -type f -name "*.py" | xargs grep -i "alpaca_api_key"
------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------
     ----------- ikona na spuštění treidind -----------------

         gnome-terminal -- bash -c "cd /home/<USER>/lakylukperun2.0 && source .venv/bin/activate && python perun_tradingview_multi.py; exec bash"
------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------


cd ~/lakylukperun_openai
python3 update_openai_api_key.py "váš-nový-api-klíč"


● API klíče Alpaca jsou:
  - API Key: AKR88AOYG2LSYZL1RCVC
  - API Secret: jT363CePWmEYd9UizVMd6k20YjdjOhnZgNf4K2SJ


---openal key  ********************************************************************************************************************************************************************

********************************************************************************************************************************************************************


---TAAPI API klíč: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbHVlIjoiNjQyNDFjYTQzYTYxY2I3ZDlmNGU2ZGY3Iiw
     iaWF0IjoxNjgwMDI3ODEyLCJleHAiOjMzMTg0NzA3ODEyfQ.ZQGfBKQQJJh9f-RLpBfLYgn8sBKQjgLDVp0Y0OdYPps
        wiaWF0IjoxNjgwMDI3ODEyLCJleHAiOjMzMTg0NzA3ODEyfQ.ZQGfBKQQJJh9f-RLpBfLYgn8sBKQjgLDVp0Y0OdYPps

na synology NAS
vytvořena záložní kopie: perun_tradingview_multi.py.backup








termshark -i wlp2s0b1    -----    hollywood  ------  bashtop ----- glances ---  gtop










sudo apt update
sudo apt upgrade



sudo apt install wayland


sudo apt install weston


weston

sudo apt install snapd


sway

sudo nano /etc/apt/sources.list

# deb http://security.ubuntu.com/ubuntu noble-security InRelease
# deb http://archive.ubuntu.com/ubuntu noble InRelease
# deb http://archive.ubuntu.com/ubuntu noble-updates InRelease
# deb http://packages.linuxmint.com wilma InRelease

ulozit ctrl+o entr zavřít ctrl+x

sudo nano /etc/apt/sources.list.d/official-package-repositories.list

# deb http://archive.ubuntu.com/ubuntu/ jammy main restricted universe multiver>
# deb http://archive.ubuntu.com/ubuntu/ jammy-updates main restricted universe >
# deb http://archive.ubuntu.com/ubuntu/ jammy-backports main restricted univers>
# deb http://security.ubuntu.com/ubuntu jammy-security main restricted universe>
# Do not edit this file manually, use Software Sources instead.

# deb http://packages.linuxmint.com wilma main upstream import backport #id:lin>

# deb http://archive.ubuntu.com/ubuntu noble main restricted universe multiverse
# deb http://archive.ubuntu.com/ubuntu noble-updates main restricted universe m>
# deb http://archive.ubuntu.com/ubuntu noble-backports main restricted universe>

# deb http://security.ubuntu.com/ubuntu/ noble-security main restricted univers>



widget   conky &



nano ~/.conkyrc

conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,  -- aktualizace každých 5 minut
    double_buffer = true,
    background = false,
    own_window = true,
    own_window_type = 'desktop', -- zkus také 'normal'
    own_window_transparent = true,
    own_window_hints = 'undecorated,below,sticky,skip_taskbar,skip_pager',
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'white',
};

conky.text = [[
${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD" | jq -r '.USD'} USD
]];


nano ~/.conkyrc


killall conky
conky &

jq --version

sudo apt install jq   


  GNU nano 7.2                   /home/<USER>/.conkyrc *                          
conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'desktop',  -- Změněno na 'desktop'
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'green',
};

conky.text = [[
${color green}Network: ${color green}${wireless essid wlan0} ${addr wlan0} ${do>
${color green}CPU Temp: ${color green}${execi 10 sensors | grep 'Core 0' | awk >
${color green}Battery: ${color green}${battery_percent BAT0}% ${battery_time BA>
${color green}Updates: ${color green}${execi 3600 apt-get -s upgrade | grep -P >
${color green}Disk Usage: ${color green}${fs_used /}/${fs_size /} (${fs_used_pe>
${color green}Top Processes: ${color green}${top name 1} (${top cpu 1}%)
${color green}Next Event: ${color green}${execi 300 gcalcli next --noheader | a>

${color green}${time %a, %b %d %H:%M:%S}
CPU: ${color green}${cpu cpu0}% ${cpubar cpu0}
RAM: ${color green}$mem/$memmax - $memperc% ${membar 4}
Disk: ${color green}$diskio_read/$diskio_write


${color green}BTC Price: ${color green}${execi 300 curl -s "https://min-api.cry>
${color green}ETH Price: ${color green}${execi 300 curl -s "https://min-api.cry>
${color green}ATOM Price: ${color green}${execi 300 curl -s "https://min-api.cr>
${color green}PEPE Price: ${color green}${execi 300 curl -s "https://min-api.cr>
${color green}XRP Price: ${color green}${execi 300 curl -s "https://min-api.cry>
${color green}FORH Price: ${color green}${execi 300 curl -s "https://min-api.cr>
${color green}BTSG Price: ${color green}${execi 300 curl -s "https://min-api.cr>
${color green}APT Price: ${color green}${execi 300 curl -s "https://min-api.cry>
${color green}STRD Price: ${color green}${execi 300 curl -s "https://min-api.cr>
${color green}TOTAL2 Price: ${color green}${execi 300 curl -s "https://min-api.>
${color green}TOTAL3 Price: ${color green}${execi 300 curl -s "https://min-api.>
]];


conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'desktop',  -- Změněno na 'desktop'
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'green',
};

conky.text = [[
${color green}Network: ${color green}${wireless essid wlan0} ${addr wlan0} ${downspeed wlan0} ↓ ${upspeed wlan0} ↑
${color green}CPU Temp: ${color green}${execi 10 sensors | grep 'Core 0' | awk '{print $3}'}
${color green}Battery: ${color green}${battery_percent BAT0}% ${battery_time BAT0}
${color green}Updates: ${color green}${execi 3600 apt-get -s upgrade | grep -P '^\d' | wc -l} available
${color green}Disk Usage: ${color green}${fs_used /}/${fs_size /} (${fs_used_perc /}%)
${color green}Top Processes: ${color green}${top name 1} (${top cpu 1}%)
${color green}Next Event: ${color green}${execi 300 gcalcli next --noheader | awk '{print $1, $2, $3}'}
${color green}Last 5 Log Entries: ${color green}${execi 300 tail -n 5 /var/log/syslog}

${color green}${time %a, %b %d %H:%M:%S}
CPU: ${color green}${cpu cpu0}% ${cpubar cpu0}
RAM: ${color green}$mem/$memmax - $memperc% ${membar 4}
Disk: ${color green}$diskio_read/$diskio_write

${color green}BTC Price: ${color green}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsy








conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'desktop',  -- Změněno na 'desktop'
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'white',
};

conky.text = [[
${color yellow}Network: ${color white}${wireless essid wlan0} ${addr wlan0} ${downspeed wlan0} ↓ ${upspeed wlan0} ↑
${color yellow}CPU Temp: ${color white}${execi 10 sensors | grep 'Core 0' | awk '{print $3}'}
${color yellow}Battery: ${color white}${battery_percent BAT0}% ${battery_time BAT0}
${color yellow}Updates: ${color white}${execi 3600 apt-get -s upgrade | grep -P '^\d' | wc -l} available
${color yellow}Disk Usage: ${color white}${fs_used /}/${fs_size /} (${fs_used_perc /}%)
${color yellow}Top Processes: ${color white}${top name 1} (${top cpu 1}%)
${color yellow}Next Event: ${color white}${execi 300 gcalcli next --noheader | awk '{print $1, $2, $3}'}
${color yellow}Last 5 Log Entries: ${color white}${execi 300 tail -n 5 /var/log/syslog}

${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ETH Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ETH&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ATOM Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ATOM&tsyms=USD" | jq -r '.USD'} USD
${color yellow}PEPE Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=PEPE&tsyms=USD" | jq -r '.USD'} USD
${color yellow}XRP Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=XRP&tsyms=USD" | jq -r '.USD'} USD
${color yellow}FORH Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=FORH&tsyms=USD" | jq -r '.USD'} USD
${color yellow}BTSG Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTSG&tsyms=USD" | jq -r '.USD'} USD
${color yellow}APT Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=APT&tsyms=USD" | jq -r '.USD'} USD
${color yellow}STRD Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=STRD&tsyms=USD" | jq -r '.USD'} USD
${color yellow}TOTAL1 Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=TOTAL1&tsyms=USD" | jq -r '.USD'} USD
${color yellow}TOTAL2 Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=TOTAL2&tsyms=USD" | jq -r '.USD'} USD
${color yellow}TOTAL3 Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=TOTAL3&tsyms=USD" | jq -r '.USD'} USD
]];



conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'desktop',  -- Změněno na 'desktop'
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'white',
};

conky.text = [[
${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cr>
${color yellow}ETH Price: ${color white}${execi 300 curl -s "https://min-api.cr>
${color yellow}ATOM Price: ${color white}${execi 300 curl -s "https://min-api.c>
${color yellow}PEPE Price: ${color white}${execi 300 curl -s "https://min-api.c>
${color yellow}XRP Price: ${color white}${execi 300 curl -s "https://min-api.cr>
]];








conky.text = [[
${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ETH Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ETH&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ATOM Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ATOM&tsyms=USD" | jq -r '.USD'} USD
${color yellow}PEPE Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=PEPE&tsyms=USD" | jq -r '.USD'} USD
${color yellow}XRP Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=XRP&tsyms=USD" | jq -r '.USD'} USD
]];




conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'normal',
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'white',
};

conky.text = [[
${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ETH Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ETH&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ATOM Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ATOM&tsyms=USD" | jq -r '.USD'} USD
${color yellow}PEPE Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=PEPE&tsyms=USD" | jq -r '.USD'} USD
${color yellow}XRP Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=XRP&tsyms=USD" | jq -r '.USD'} USD
]];
    





conky.config = {
    alignment = 'top_right',
    gap_x = 10,
    gap_y = 10,
    update_interval = 300,
    double_buffer = true,
    own_window = true,
    own_window_type = 'desktop',  -- Změněno na 'desktop'
    own_window_transparent = true,
    use_xft = true,
    font = 'DejaVu Sans Mono:size=10',
    xftalpha = 0.8,
    override_utf8_locale = true,
    uppercase = false,
    default_color = 'white',
};

conky.text = [[
${color yellow}Network: ${color white}${wireless essid wlan0} ${addr wlan0} ${downspeed wlan0} ↓ ${upspeed wlan0} ↑
${color yellow}CPU Temp: ${color white}${execi 10 sensors | grep 'Core 0' | awk '{print $3}'}
${color yellow}Battery: ${color white}${battery_percent BAT0}% ${battery_time BAT0}
${color yellow}Updates: ${color white}${execi 3600 apt-get -s upgrade | grep -P '^\d' | wc -l} available
${color yellow}Disk Usage: ${color white}${fs_used /}/${fs_size /} (${fs_used_perc /}%)
${color yellow}Top Processes: ${color white}${top name 1} (${top cpu 1}%)
${color yellow}Next Event: ${color white}${execi 300 gcalcli next --noheader | awk '{print $1, $2, $3}'}
${color yellow}Last 5 Log Entries: ${color white}${execi 300 tail -n 5 /var/log/syslog}

${color yellow}${time %a, %b %d %H:%M:%S}
CPU: ${cpu cpu0}% ${cpubar cpu0}
RAM: $mem/$memmax - $memperc% ${membar 4}
Disk: $diskio_read/$diskio_write

${color yellow}BTC Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ETH Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ETH&tsyms=USD" | jq -r '.USD'} USD
${color yellow}ATOM Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=ATOM&tsyms=USD" | jq -r '.USD'} USD
${color yellow}PEPE Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=PEPE&tsyms=USD" | jq -r '.USD'} USD
${color yellow}XRP Price: ${color white}${execi 300 curl -s "https://min-api.cryptocompare.com/data/price?fsym=XRP&tsyms=USD" | jq -r '.USD'} USD
]];











         XMR


48ssxYzSsw4j3QzVHyn47GDY9gZkVray2QEMT8NYtzQUXjw939wc2eqYcDeJc9NUsn3yE9jwaReJo3bHUBbCXh3k6S1Kwhb



git clone https://github.com/xmrig/xmrig.git

cd xmrig
mkdir buildc
cd build
cmake ..
make



./xmrig -o pool.supportxmr.com:3333 -u 48ssxYzSsw4j3QzVHyn47GDY9gZkVray2QEMT8NYtzQUXjw939wc2eqYcDeJc9NUsn3yE9jwaReJo3bHUBbCXh3k6S1Kwhb
-p x --donate-level 1



sudo ./xmrig -o pool.supportxmr.com:3333 -u 48ssxYzSsw4j3QzVHyn47GDY9gZkVray2QEMT8NYtzQUXjw939wc2eqYcDeJc9NUsn3yE9jwaReJo3bHUBbCXh3k6S1Kwhb -p HP --donate-level 1


sudo ./xmrig -o gulf.moneroocean.stream:10128 -u 48ssxYzSsw4j3QzVHyn47GDY9gZkVray2QEMT8NYtzQUXjw939wc2eqYcDeJc9NUsn3yE9jwaReJo3bHUBbCXh3k6S1Kwhb -p chalupa --donate-level 1



sudo ./xmrig -o gulf.moneroocean.stream:10128 -u 48ssxYzSsw4j3QzVHyn47GDY9gZkVray2QEMT8NYtzQUXjw939wc2eqYcDeJc9NUsn3yE9jwaReJo3bHUBbCXh3k6S1Kwhb -p x --donate-level 0







btc

cat ~/.ssh/id_ed25519.pub



ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIii6ZRbwrKhaT0aJRuEk0TXUfEfMNVfORWguPyrWa7V <EMAIL>



sudo apt install ./Stažené/NAZEV BALIKU.deb


cd ~/Stažené

tar -xvjf "monero-gui-linux-x64-v0.18.3.4 (1).tar.bz2"

monerod






shutdown +10

shutdown 22:30





           ----------------exe------------

sudo apt install winetricks
winetricks

sudo apt update
sudo apt install wine64 wine32


wine --version
 

cd ~/Stažené


wine nazev_souboru.exe


 wine RobloxPlayerInstaller.exe


VvhTJ9sTWvoqCMoqNtcaef8Q0MTh_K81EUQueGTdFB4


-------------install xmrig-------------

sudo apt update && sudo apt upgrade -y
sudo apt install git build-essential cmake libuv1-dev libssl-dev libhwloc-dev -y


git clone https://github.com/xmrig/xmrig.git

cd xmrig

mkdir build && cd build
cmake ..
make

./xmrig --url <pool_adresa>:<port> --user <vaše_peněženka> --pass x

