{"numStartups": 2, "theme": "light", "customApiKeyResponses": {"approved": ["JG9Hty6uTOA-BwHXHwAA"], "rejected": []}, "tipsHistory": {"new-user-warmup": 1}, "promptQueueUseCount": 1, "firstStartTime": "2025-05-19T20:28:46.098Z", "userID": "5bdef4c5a87447080ae472110d9fc7109a8f383f5a5f6af795b4b69c8bac3a4d", "statsigModel": {"bedrock": "us.anthropic.claude-3-7-sonnet-********-v1:0", "vertex": "claude-3-7-sonnet@********", "firstParty": "claude-3-7-sonnet-********"}, "oauthAccount": {"accountUuid": "2d969b67-0b8e-487a-b0c9-57ce9f7932e8", "emailAddress": "<EMAIL>", "organizationUuid": "61bff5e3-a9a5-487b-9828-9203963c2bb3", "organizationRole": "admin", "workspaceRole": "workspace_developer", "organizationName": "ahoj’s Individual Org"}, "primaryApiKey": "************************************************************************************************************", "isQualifiedForDataSharing": false, "hasCompletedOnboarding": true, "lastOnboardingVersion": "0.2.122", "projects": {"/home/<USER>": {"allowedTools": [], "history": [{"display": "ulož to pro příští praci a pokračování do našeho :  Pro příští pokračování vývoje jsou klíčové tyto dokumenty:\n  1. GUIDE.md - pomůže rychle pochopit strukturu a fungování aplikace\n  2. ROADMAP.md - poskytuje plán dalšího vývoje a prioritizaci úkolů\n  3. STRATEGIES.md - detailní popis implementovaných obchodních strategií", "pastedContents": {}}, {"display": "do<PERSON><PERSON><PERSON>š analizovat co by se mělo opravit  Pro plnou funkčnost obchodování by\n   by<PERSON> pot<PERSON><PERSON><PERSON> prov<PERSON>t komplexnějš<PERSON> opravy asynchronn<PERSON><PERSON> kódu", "pastedContents": {}}, {"display": "vlož nové kliče uiD5zklobIda6ph9na   QgpRHqsGuqUFpVukWHcgmAQo6AyhXlc4bJUD", "pastedContents": {}}, {"display": "použijem Nepoužít IP omezení", "pastedContents": {}}, {"display": " 2. Nepoužít IP omezení  tady zase vyprši klič u bybit? takže budu api menit po 3 mesících?", "pastedContents": {}}, {"display": "statickou adresu nemam .asi.jak to zjist<PERSON>?", "pastedContents": {}}, {"display": "da<PERSON><PERSON><PERSON> možnosti nastavení :   Create New Key\nAPI Key Usage\nAPI Transaction\nConnect to Third-Party Applications\nName for the API key\nAPI Key Permissions\nRead-Write\nRead-Only\nOnly IPs with permissions granted are allowed to access the OpenAPI\nNo IP restriction\nYour account is at risk. Fiat trading and withdrawal services are restricted. If an API key isn't linked to an IP address, it will expire in 3 months.\n", "pastedContents": {}}, {"display": "dve mo<PERSON><PERSON><PERSON>", "pastedContents": {}}, {"display": "mam tam tabulku u noveho li<PERSON>,    posilam okopirovyny text:   Select Your API Key Type\nSystem-generated API Keys\nThe API key generated by the Bybit system operates with HMAC encryption. You will be provided with a pair of public and private keys. Please treat this pair of keys as passwords and keep them safe.\nSelf-generated API Keys\nSelf-generated API keys operate with RSA encryption. You must create your public and private keys through the software, and then only provide the public key to Bybit, we will never hold your private key.API v3 and v5 are currently supported.\n", "pastedContents": {}}, {"display": "je potřeba vyrabět testovaci verzi?pro případ  mam i jiny učet na bybit kde zadne penize nejsou ani otevřené pozice.  Vče otevřu tento navod? /home/<USER>/bybit-trading-assistant/BYBIT_API_SETUP.md\n", "pastedContents": {}}, {"display": "1. Chyba připojení k Bybit API: Chyba při připojení k Bybit API: Expecting value: line 1 \n  column 1 (char 0) - Pravděpodobně neplatné API klíče nebo chybí správná konfigurace.\n  To bude ta chyba na bybit maji nekolik možnosti kliču ,nevim ktery a jak nastavit,pohledej internet,dokumentace api bybit", "pastedContents": {}}, {"display": "[Pasted text #1 +89 lines]", "pastedContents": {"1": {"id": 1, "type": "text", "content": "tv@tv-HP-t640-Thin-Client:~$ /home/<USER>/bybit-trading-assistant/run_bot.sh\n\n                        \n  $$$$$$$$$$$$$$$$$$   \n  $$              $$   \n  $$  $$$$$$$$    $$   \n  $$  $$      $$  $$   \n  $$  $$      $$  $$   \n  $$  $$$$$$$$    $$   \n  $$        $$    $$   \n  $$        $$    $$   \n  $$  $$$$$$      $$   \n  $$              $$   \n  $$$$$$$$$$$$$$$$$$   \n                        \n=== Bybit Trading Assistant ===\nSpouštím obchodního bota a monitoring...\nSpouštím obchodní strategie...\nPoužívám virtuální prostředí...\nSpouštím obchodního bota s pokročilými strategiemi...\n2025-05-24 11:20:32,450 - src.infrastructure.persistence.repository_factory - INFO - Vytvářím SQL repozitář pro signály\n2025-05-24 11:20:32,450 - src.infrastructure.persistence.repository_factory - INFO - Vytvářím SQL repozitář pro indikátory\n2025-05-24 11:20:32,450 - src.strategies.strategy_manager - INFO - Inicializace správce strategií\n2025-05-24 11:20:32,450 - src.strategies.base_strategy - INFO - Inicializace strategie: TrendFollowingStrategy\n2025-05-24 11:20:32,450 - src.strategies.trend_following_strategy - INFO - Inicializace TrendFollowingStrategy s rychlým EMA (9) a pomalým EMA (21)\n2025-05-24 11:20:32,450 - src.strategies.strategy_manager - INFO - Registrována strategie: trend_following s váhou 1.0\n2025-05-24 11:20:32,451 - src.strategies.base_strategy - INFO - Inicializace strategie: RsiMacdStrategy\n2025-05-24 11:20:32,451 - src.strategies.rsi_macd_strategy - INFO - Inicializace RsiMacdStrategy (RSI: 14, MACD: 12/26/9)\n2025-05-24 11:20:32,451 - src.strategies.strategy_manager - INFO - Registrována strategie: rsi_macd s váhou 1.2\n2025-05-24 11:20:32,451 - src.strategies.base_strategy - INFO - Inicializace strategie: BreakoutStrategy\n2025-05-24 11:20:32,451 - src.strategies.breakout_strategy - INFO - Inicializace BreakoutStrategy (lookback: 20, threshold: 0.003)\n2025-05-24 11:20:32,451 - src.strategies.strategy_manager - INFO - Registrována strategie: breakout s váhou 0.8\n2025-05-24 11:20:32,451 - src.strategies.base_strategy - INFO - Inicializace strategie: VolumeStrategy\n2025-05-24 11:20:32,451 - src.strategies.volume_strategy - INFO - Inicializace VolumeStrategy (období: 20, práh objemu: 2.0)\n2025-05-24 11:20:32,451 - src.strategies.strategy_manager - INFO - Registrována strategie: volume s váhou 0.9\n2025-05-24 11:20:32,451 - src.strategies.strategy_manager - INFO - Registrováno 4 výchozích strategií\n2025-05-24 11:20:32,451 - __main__ - INFO - Dostupné strategie: trend_following, rsi_macd, breakout, volume\n2025-05-24 11:20:33,261 - __main__ - ERROR - Chyba při připojení k Bybit API: Expecting value: line 1 column 1 (char 0)\n2025-05-24 11:20:33,261 - __main__ - WARNING - Bot bude pokračovat, ale nebude možné obchodovat.\n2025-05-24 11:20:33,262 - __main__ - INFO - Spouštění obchodního bota pro symboly: BTCUSDT, ETHUSDT, SOLUSDT\n2025-05-24 11:20:33,262 - __main__ - INFO - Interval kontroly: 60 sekund\n2025-05-24 11:20:33,262 - __main__ - INFO - Začátek kontroly: 2025-05-24 11:20:33.262910\n2025-05-24 11:20:33,263 - __main__ - INFO - Počet otevřených pozic: 0\n2025-05-24 11:20:33,263 - __main__ - INFO - Analýza trhu pro BTCUSDT\nObchodní strategie běží na pozadí (PID: 75796)\nSpouštím dashboard pro monitoring...\n\nCollecting usage statistics. To deactivate, set browser.gatherUsageStats to false.\n\n2025-05-24 11:20:35,681 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro BTCUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:20:35,682 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:35,682 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:35,682 - __main__ - WARNING - Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:35,682 - __main__ - INFO - Analýza trhu pro ETHUSDT\n\n  You can now view your Streamlit app in your browser.\n\n  Local URL: http://localhost:8501\n  Network URL: http://************:8501\n  External URL: http://***************:8501\n\n2025-05-24 11:20:37,634 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro ETHUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:20:37,634 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:37,634 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:37,634 - __main__ - WARNING - Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:37,635 - __main__ - INFO - Analýza trhu pro SOLUSDT\n2025-05-24 11:20:39,392 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro SOLUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:20:39,392 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:39,393 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:39,393 - __main__ - WARNING - Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:20:39,393 - __main__ - INFO - Kontrola dokončena za 6.13 s. Další kontrola za 53.87 s.\n2025-05-24 11:21:33,319 - __main__ - INFO - Začátek kontroly: 2025-05-24 11:21:33.319198\n2025-05-24 11:21:33,319 - __main__ - INFO - Počet otevřených pozic: 0\n2025-05-24 11:21:33,319 - __main__ - INFO - Analýza trhu pro BTCUSDT\n2025-05-24 11:21:35,791 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro BTCUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:21:35,791 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:35,791 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:35,792 - __main__ - WARNING - Chyba při analýze trhu pro BTCUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:35,792 - __main__ - INFO - Analýza trhu pro ETHUSDT\n2025-05-24 11:21:37,863 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro ETHUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:21:37,863 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:37,863 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:37,863 - __main__ - WARNING - Chyba při analýze trhu pro ETHUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:37,864 - __main__ - INFO - Analýza trhu pro SOLUSDT\n2025-05-24 11:21:39,559 - src.application.services.market_data_service - ERROR - Chyba při získávání knihy objednávek pro SOLUSDT: 'BybitClient' object has no attribute 'get_orderbook'\n2025-05-24 11:21:39,560 - src.application.services.trading_service - ERROR - Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:39,560 - src.application.services.notification_service - WARNING - [NotificationType.ERROR] Chyba při analýze trhu: Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:39,560 - __main__ - WARNING - Chyba při analýze trhu pro SOLUSDT: object dict can't be used in 'await' expression\n2025-05-24 11:21:39,560 - __main__ - INFO - Kontrola dokončena za 6.24 s. Další kontrola za 53.76 s.\n"}}}, {"display": "ikona ne<PERSON>gu<PERSON> ,nic se nespustí.otestuj ikon<PERSON> (spouštěč aplikace) a zjisti chyby aplikace", "pastedContents": {}}, {"display": "aplikace by mela fungovat ale nemam ji jak monitorovat.potřeboval bych ji spouštět přez ikonu na ploše ve tvaru zeleneho dolaru.", "pastedContents": {}}, {"display": " Vytvořil jsem průvodce aplikací a roadmapu pro budoucí vývoj. V GUIDE.md najdete komplexní přehled celé aplikace\n  včetně jejích komponent, architektury a způsobu použití. ROADMAP.md pak obsahuje podrobný plán budoucího vývoje\n  rozdělený do pěti fází s odhadem časové náročnosti.\nPro příští pokračování vývoje jsou klíčové tyto dokumenty:\n  1. GUIDE.md - pomůže rychle pochopit strukturu a fungování aplikace\n  2. ROADMAP.md - poskytuje plán dalšího vývoje a prioritizaci úkolů\n  3. STRATEGIES.md - detailní popis implementovaných obchodních strategií\n   \n", "pastedContents": {}}, {"display": "spust program a ukaž mi nejakou logu pro sledovaní aktualních cen otevřeých pozic a analizi z rsi a jine indikatory", "pastedContents": {}}, {"display": "nenapsal jsi mi co se stalo?funguje?je nejaky problem?je potřeba se na neco zameřit?", "pastedContents": {}}, {"display": "spust program a otestuje<PERSON> jej<PERSON>,budu potřebovat ogu na sledovaní obchodování a o otevřených pozic,na bybit mam otevřené pozice i vložený kapital.", "pastedContents": {}}, {"display": " pridej api do projektu z bybit:api b23FvuJ6P6JIKtulnE    tajný api mDX0mTlNDZ0WFyrj5EAphRAufEo5DjfCTRwq", "pastedContents": {}}, {"display": "v<PERSON>ž tento klič do prokjektu ,jsem si vědom ryzik", "pastedContents": {}}, {"display": "openal api: ********************************************************************************************************************************************************************", "pastedContents": {}}, {"display": "da se program spustit a otestovak co nefunguje?", "pastedContents": {}}, {"display": "co je ted hotové a jaky je ted plan?", "pastedContents": {}}, {"display": "udelej mi pruvodce aplikaci ,pro prípad příštího pokračování", "pastedContents": {}}, {"display": "k<PERSON><PERSON> z těch to dvou ma byt první implementovana do projektu?\n  1. **Pokročilejší obchodní strategie** - Implementace vlastních strategií na základě získaných dat\n  2. **Vizualizace dat** - Vytvoření dashboardu pro sledování signálů a výkonnosti\n", "pastedContents": {}}, {"display": "  Co by m<PERSON><PERSON> b<PERSON>t implementováno v dalším kroku?\n  1. Persistence dat - Ukládání signálů a indikátorů do databáze\n  2. Pokročilejší obchodní strategie - Implementace vlastních strategií na základě získaných dat\n  3. Vizualizace dat - Vytvoření dashboardu pro sledování signálů a výkonnosti\n  ok budem pokračovat ,muž<PERSON>š <PERSON>t", "pastedContents": {}}, {"display": "začni pracovat popořade,dnes mužem začít       1. Kompletní integrace s TradingView - Základní stavební k<PERSON>, protože na tom závisí všechny obchodní signály\n  a rozhodnutí", "pastedContents": {}}, {"display": "na čem by se melo delat jako první seřad mi to podle toho jak by jsme meli potupovat:   Co chybí nebo je nekompletní:\n  - Obchodní strategie (existuje jen základní generátor sign<PERSON>)\n  - Kompletní integrace s TradingView (kód obsahuje pouze základní implementaci)\n  - Persistence dat (pouze v paměti, chybí databáze)\n  - Pokročilá správa rizik (chybí hodnocení rizika na úrovni portfolia)\n  - Uživatelské rozhraní (pouze CLI, chybí webový dashboard)\n  - Backtesting (testování strategií na historických datech)\n  - Komplexní zpracování chyb\n  - Podrobná dokumentace\n\n", "pastedContents": {}}, {"display": "komunikuj č<PERSON>ky", "pastedContents": {}}, {"display": "Pro orientaci v projektu použij vytvořený průvodce PRUVODCE_PROJEKTEM.md,potřebuju stebou pracovat na tomto projektu analizuj ho co je hotove jak by mel fungovat a co by mu me<PERSON> chybet,popripade najdi nejakou dokumentaci na internetu tře na github je dost projektu z kterych čerpam informace ", "pastedContents": {}}], "dontCrawlDirectory": true, "mcpContextUris": [], "mcpServers": {}, "enabledMcpjsonServers": [], "disabledMcpjsonServers": [], "enableAllProjectMcpServers": false, "hasTrustDialogAccepted": false, "ignorePatterns": [], "projectOnboardingSeenCount": 4, "hasClaudeMdExternalIncludesApproved": false, "hasClaudeMdExternalIncludesWarningShown": false}}, "cachedChangelog": "# Changelog\n\n## 1.0.1\n\n- Added `DISABLE_INTERLEAVED_THINKING` to give users the option to opt out of interleaved thinking.\n- Improved model references to show provider-specific names (Sonnet 3.7 for Bedrock, Sonnet 4 for Console)\n- Updated documentation links and OAuth process descriptions\n\n## 1.0.0\n\n- Claude Code is now generally available\n- Introducing Sonnet 4 and Opus 4 models\n\n## 0.2.125\n\n- Breaking change: Bedrock ARN passed to `ANTHROPIC_MODEL` or `ANTHROPIC_SMALL_FAST_MODEL` should no longer contain an escaped slash (specify `/` instead of `%2F`)\n- Removed `DEBUG=true` in favor of `ANTHROPIC_LOG=debug`, to log all requests\n\n## 0.2.117\n\n- Breaking change: --print JSON output now returns nested message objects, for forwards-compatibility as we introduce new metadata fields\n- Introduced settings.cleanupPeriodDays\n- Introduced CLAUDE_CODE_API_KEY_HELPER_TTL_MS env var\n- Introduced --debug mode\n\n## 0.2.108\n\n- You can now send messages to <PERSON> while it works to steer <PERSON> in real-time\n- Introduced BASH_DEFAULT_TIMEOUT_MS and BASH_MAX_TIMEOUT_MS env vars\n- Fixed a bug where thinking was not working in -p mode\n- Fixed a regression in /cost reporting\n- Deprecated MCP wizard interface in favor of other MCP commands\n- Lots of other bugfixes and improvements\n\n## 0.2.107\n\n- CLAUDE.md files can now import other files. Add @path/to/file.md to ./CLAUDE.md to load additional files on launch\n\n## 0.2.106\n\n- MCP SSE server configs can now specify custom headers\n- Fixed a bug where MCP permission prompt didn't always show correctly\n\n## 0.2.105\n\n- Claude can now search the web\n- Moved system & account status to /status\n- Added word movement keybindings for Vim\n- Improved latency for startup, todo tool, and file edits\n\n## 0.2.102\n\n- Improved thinking triggering reliability\n- Improved @mention reliability for images and folders\n- You can now paste multiple large chunks into one prompt\n\n## 0.2.100\n\n- Fixed a crash caused by a stack overflow error\n- Made db storage optional; missing db support disables --continue and --resume\n\n## 0.2.98\n\n- Fixed an issue where auto-compact was running twice\n\n## 0.2.96\n\n- Claude Code can now also be used with a Claude Max subscription (https://claude.ai/upgrade)\n\n## 0.2.93\n\n- Resume conversations from where you left off from with \"claude --continue\" and \"claude --resume\"\n- Claude now has access to a Todo list that helps it stay on track and be more organized\n\n## 0.2.82\n\n- Added support for --disallowedTools\n- Renamed tools for consistency: LSTool -> LS, View -> Read, etc.\n\n## 0.2.75\n\n- Hit Enter to queue up additional messages while Claude is working\n- Drag in or copy/paste image files directly into the prompt\n- @-mention files to directly add them to context\n- Run one-off MCP servers with `claude --mcp-config <path-to-file>`\n- Improved performance for filename auto-complete\n\n## 0.2.74\n\n- Added support for refreshing dynamically generated API keys (via apiKeyHelper), with a 5 minute TTL\n- Task tool can now perform writes and run bash commands\n\n## 0.2.72\n\n- Updated spinner to indicate tokens loaded and tool usage\n\n## 0.2.70\n\n- Network commands like curl are now available for Claude to use\n- Claude can now run multiple web queries in parallel\n- Pressing ESC once immediately interrupts Claude in Auto-accept mode\n\n## 0.2.69\n\n- Fixed UI glitches with improved Select component behavior\n- Enhanced terminal output display with better text truncation logic\n\n## 0.2.67\n\n- Shared project permission rules can be saved in .claude/settings.json\n\n## 0.2.66\n\n- Print mode (-p) now supports streaming output via --output-format=stream-json\n- Fixed issue where pasting could trigger memory or bash mode unexpectedly\n\n## 0.2.63\n\n- Fixed an issue where MCP tools were loaded twice, which caused tool call errors\n\n## 0.2.61\n\n- Navigate menus with vim-style keys (j/k) or bash/emacs shortcuts (Ctrl+n/p) for faster interaction\n- Enhanced image detection for more reliable clipboard paste functionality\n- Fixed an issue where ESC key could crash the conversation history selector\n\n## 0.2.59\n\n- Copy+paste images directly into your prompt\n- Improved progress indicators for bash and fetch tools\n- Bugfixes for non-interactive mode (-p)\n\n## 0.2.54\n\n- Quickly add to Memory by starting your message with '#'\n- Press ctrl+r to see full output for long tool results\n- Added support for MCP SSE transport\n\n## 0.2.53\n\n- New web fetch tool lets Claude view URLs that you paste in\n- Fixed a bug with JPEG detection\n\n## 0.2.50\n\n- New MCP \"project\" scope now allows you to add MCP servers to .mcp.json files and commit them to your repository\n\n## 0.2.49\n\n- Previous MCP server scopes have been renamed: previous \"project\" scope is now \"local\" and \"global\" scope is now \"user\"\n\n## 0.2.47\n\n- Press Tab to auto-complete file and folder names\n- Press Shift + Tab to toggle auto-accept for file edits\n- Automatic conversation compaction for infinite conversation length (toggle with /config)\n\n## 0.2.44\n\n- Ask Claude to make a plan with thinking mode: just say 'think' or 'think harder' or even 'ultrathink'\n\n## 0.2.41\n\n- MCP server startup timeout can now be configured via MCP_TIMEOUT environment variable\n- MCP server startup no longer blocks the app from starting up\n\n## 0.2.37\n\n- New /release-notes command lets you view release notes at any time\n- `claude config add/remove` commands now accept multiple values separated by commas or spaces\n\n## 0.2.36\n\n- Import MCP servers from Claude Desktop with `claude mcp add-from-claude-desktop`\n- Add MCP servers as JSON strings with `claude mcp add-json <n> <json>`\n\n## 0.2.34\n\n- Vim bindings for text input - enable with /vim or /config\n\n## 0.2.32\n\n- Interactive MCP setup wizard: Run \"claude mcp add\" to add MCP servers with a step-by-step interface\n- Fix for some PersistentShell issues\n\n## 0.2.31\n\n- Custom slash commands: Markdown files in .claude/commands/ directories now appear as custom slash commands to insert prompts into your conversation\n- MCP debug mode: Run with --mcp-debug flag to get more information about MCP server errors\n\n## 0.2.30\n\n- Added ANSI color theme for better terminal compatibility\n- Fixed issue where slash command arguments weren't being sent properly\n- (Mac-only) API keys are now stored in macOS Keychain\n\n## 0.2.26\n\n- New /approved-tools command for managing tool permissions\n- Word-level diff display for improved code readability\n- Fuzzy matching for slash commands\n\n## 0.2.21\n\n- Fuzzy matching for /commands\n", "changelogLastFetched": 1748076165380, "maxSubscriptionNoticeCount": 0, "hasAvailableMaxSubscription": false, "hasAcknowledgedCostThreshold": true}