"""
Hlavní skript pro automatické obchodování.
"""
import os
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List

import colorama
from colorama import Fore, Style

from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.application.services.trading_service import TradingService
from src.domain.models.position import PositionStatus
from src.config.config import config


# Inicializace colorama
colorama.init()


# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(config.logging.file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("trading_bot")


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def get_market_data(bybit_client: BybitClient, symbol: str) -> Dict[str, Any]:
    """
    Získá tržní data z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        return bybit_client.get_market_data(symbol)
    except Exception as e:
        logger.error(f"Chyba při získávání tržních dat: {e}")
        return {}


def get_klines(bybit_client: BybitClient, symbol: str, interval: str = "15", limit: int = 200) -> List[Dict[str, Any]]:
    """
    Získá historická data (svíčky) z Bybit API.
    
    Args:
        bybit_client: Klient pro Bybit API
        symbol: Symbol
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet svíček
        
    Returns:
        List[Dict[str, Any]]: Seznam svíček
    """
    try:
        return bybit_client.get_klines(symbol, interval, limit)
    except Exception as e:
        logger.error(f"Chyba při získávání historických dat: {e}")
        return []


def calculate_rsi(klines: List[Dict[str, Any]], period: int = 14) -> float:
    """
    Vypočítá RSI (Relative Strength Index).
    
    Args:
        klines: Seznam svíček
        period: Perioda
        
    Returns:
        float: RSI hodnota
    """
    if len(klines) < period + 1:
        return 50.0
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet změn
    deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
    
    # Výpočet zisků a ztrát
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Výpočet průměrných zisků a ztrát
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Výpočet RS a RSI
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_macd(klines: List[Dict[str, Any]], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, float]:
    """
    Vypočítá MACD (Moving Average Convergence Divergence).
    
    Args:
        klines: Seznam svíček
        fast_period: Rychlá perioda
        slow_period: Pomalá perioda
        signal_period: Signální perioda
        
    Returns:
        Dict[str, float]: MACD hodnoty
    """
    if len(klines) < slow_period + signal_period:
        return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema_values.append((data[i] - ema_values[i-1]) * multiplier + ema_values[i-1])
        
        return ema_values
    
    # Výpočet MACD
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]
    signal_line = ema(macd_line, signal_period)
    histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]
    
    return {
        "macd": macd_line[-1],
        "signal": signal_line[-1],
        "histogram": histogram[-1]
    }


def display_positions(positions: List):
    """
    Zobrazí seznam pozic.
    
    Args:
        positions: Seznam pozic
    """
    if not positions:
        print(f"{Fore.YELLOW}Žádné otevřené pozice{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for position in positions:
        symbol = position.symbol
        side = position.side.value
        side_color = Fore.GREEN if side == "LONG" else Fore.RED
        quantity = position.quantity
        entry_price = position.entry_price
        current_price = position.current_price or entry_price
        unrealized_pnl = position.unrealized_pnl
        profit_percentage = position.profit_percentage
        stop_loss = position.stop_loss
        take_profit = position.take_profit
        opened_at = position.opened_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        side_formatted = f"{side_color}{side}{Style.RESET_ALL}"
        unrealized_pnl_formatted = format_currency(unrealized_pnl)
        profit_percentage_formatted = format_percentage(profit_percentage)
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            side_formatted,
            f"{quantity:.6f}",
            format_currency(entry_price),
            format_currency(current_price),
            unrealized_pnl_formatted,
            profit_percentage_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            opened_at
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Strana", "Množství", "Vstupní cena", "Aktuální cena", "PnL", "PnL %", "Stop-Loss", "Take-Profit", "Otevřeno"]
    print_table(table_data, headers)


def print_table(data, headers):
    """
    Vytiskne tabulku s daty.
    
    Args:
        data: Data pro tabulku
        headers: Hlavičky sloupců
    """
    # Zjištění maximální šířky pro každý sloupec
    col_widths = [len(h) for h in headers]
    for row in data:
        for i, cell in enumerate(row):
            col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # Vytvoření oddělovače
    separator = "+"
    for width in col_widths:
        separator += "-" * (width + 2) + "+"
    
    # Vytisknutí hlavičky
    print(separator)
    header_row = "|"
    for i, header in enumerate(headers):
        header_row += f" {header.ljust(col_widths[i])} |"
    print(header_row)
    print(separator)
    
    # Vytisknutí dat
    for row in data:
        data_row = "|"
        for i, cell in enumerate(row):
            data_row += f" {str(cell).ljust(col_widths[i])} |"
        print(data_row)
    
    print(separator)


def display_signals(signals: List):
    """
    Zobrazí seznam signálů.
    
    Args:
        signals: Seznam signálů
    """
    if not signals:
        print(f"{Fore.YELLOW}Žádné signály{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for signal in signals:
        symbol = signal.symbol
        signal_type = signal.type.value
        type_color = Fore.GREEN if signal_type == "BUY" else Fore.RED
        price = signal.price
        confidence = signal.confidence
        stop_loss = signal.stop_loss
        take_profit = signal.take_profit
        timestamp = signal.metadata.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        # Formátování hodnot
        type_formatted = f"{type_color}{signal_type}{Style.RESET_ALL}"
        confidence_formatted = f"{confidence:.2f}"
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulku
        table_data.append([
            symbol,
            type_formatted,
            format_currency(price),
            confidence_formatted,
            stop_loss_formatted,
            take_profit_formatted,
            timestamp
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Typ", "Cena", "Důvěryhodnost", "Stop-Loss", "Take-Profit", "Čas"]
    print_table(table_data, headers)


def display_market_data(market_data: Dict[str, Any], symbol: str, indicators: Dict[str, float]):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
        indicators: Technické indikátory
    """
    if not market_data:
        return
    
    # Získání hodnot
    last_price = float(market_data.get("lastPrice", 0))
    price_change = float(market_data.get("price24hPcnt", 0)) * 100
    volume = float(market_data.get("volume24h", 0))
    high_24h = float(market_data.get("highPrice24h", 0))
    low_24h = float(market_data.get("lowPrice24h", 0))
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    high_24h_formatted = format_currency(high_24h)
    low_24h_formatted = format_currency(low_24h)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"24h Rozsah: {low_24h_formatted} - {high_24h_formatted}")
    print(f"Objem 24h: {format_currency(volume)}")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN
            if rsi > 70:
                rsi_color = Fore.RED
            elif rsi < 30:
                rsi_color = Fore.YELLOW
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "signal" in indicators:
            macd = indicators["macd"]
            signal = indicators["signal"]
            histogram = indicators.get("histogram", 0)
            
            macd_color = Fore.GREEN if macd > signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {signal:.2f}, Histogram: {histogram:.2f}")
    
    print()


def run_trading_bot(symbols: List[str], refresh_interval: int, demo_mode: bool = True):
    """
    Spustí obchodního bota.
    
    Args:
        symbols: Seznam symbolů
        refresh_interval: Interval obnovení v sekundách
        demo_mode: Demo režim (bez skutečných obchodů)
    """
    # Inicializace služeb
    bybit_client = BybitClient()
    signal_service = SignalService()
    trading_service = TradingService(bybit_client, signal_service)
    
    logger.info(f"Spuštěn obchodní bot v {'demo' if demo_mode else 'ostré'} režimu.")
    logger.info(f"Sledované symboly: {', '.join(symbols)}")
    
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Trading Bot {'(Demo)' if demo_mode else '(Ostrý)'} ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Sledované symboly: {', '.join(symbols)}")
            print()
            
            # Aktualizace otevřených pozic
            open_positions = trading_service.update_positions()
            
            # Zobrazení otevřených pozic
            print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
            display_positions(open_positions)
            print()
            
            # Zpracování každého symbolu
            for symbol in symbols:
                # Získání tržních dat
                market_data = get_market_data(bybit_client, symbol)
                if not market_data:
                    continue
                
                # Získání aktuální ceny
                current_price = float(market_data.get("lastPrice", 0))
                
                # Získání historických dat
                klines = get_klines(bybit_client, symbol)
                
                # Výpočet indikátorů
                indicators = {}
                if klines:
                    indicators["rsi"] = calculate_rsi(klines)
                    macd_values = calculate_macd(klines)
                    indicators.update(macd_values)
                
                # Zobrazení tržních dat
                display_market_data(market_data, symbol, indicators)
                
                # Kontrola, zda již nemáme otevřenou pozici pro tento symbol
                symbol_positions = [p for p in open_positions if p.symbol == symbol]
                if symbol_positions:
                    continue
                
                # Vytvoření signálu na základě indikátorů
                signal = signal_service.create_signal_from_indicators(symbol, current_price, indicators)
                
                # Zpracování signálu
                if signal and not demo_mode:
                    position = trading_service.process_signal(signal)
                    if position:
                        logger.info(f"Vytvořena nová pozice pro {symbol} na základě signálu.")
                
                # Zobrazení posledních signálů
                latest_signals = signal_service.get_signals(symbol, limit=1)
                if latest_signals:
                    print(f"{Fore.CYAN}Poslední signál:{Style.RESET_ALL}")
                    display_signals(latest_signals)
                    print()
            
            # Zobrazení informací o intervalu obnovení
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        logger.info("Obchodní bot ukončen uživatelem.")
        print("\nObchodní bot ukončen.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bybit Trading Bot")
    parser.add_argument("--symbols", type=str, default="BTCUSDT,ETHUSDT,SOLUSDT", help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=60, help="Interval obnovení v sekundách")
    parser.add_argument("--demo", action="store_true", help="Spustit v demo režimu (bez skutečných obchodů)")
    
    args = parser.parse_args()
    
    symbols = args.symbols.split(",")
    
    # Vytvoření adresáře pro logy
    os.makedirs(os.path.dirname(config.logging.file), exist_ok=True)
    
    run_trading_bot(symbols, args.interval, args.demo)
