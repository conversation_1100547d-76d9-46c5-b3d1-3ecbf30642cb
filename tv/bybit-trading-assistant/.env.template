# Bybit API konfigurace
BYBIT_API_KEY=
BYBIT_API_SECRET=
BYBIT_TESTNET=true

# TradingView konfigurace
TRADINGVIEW_WEBHOOK_PORT=8000
TRADINGVIEW_WEBHOOK_PATH=/webhook
TRADINGVIEW_WEBHOOK_SECRET=
TRADINGVIEW_USERNAME=
TRADINGVIEW_PASSWORD=
TRADINGVIEW_USE_API=true
TRADINGVIEW_CHECK_INTERVAL=60
TRADINGVIEW_INDICATORS=RSI,MACD,BB

# OpenAI API konfigurace
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4o
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=1000

# Trading konfigurace
TRADING_SYMBOLS=BTCUSDT,ETHUSDT
TRADING_MAX_POSITIONS=2
TRADING_POSITION_SIZE_USD=25.0
TRADING_MAX_RISK_PER_TRADE_PERCENT=1.0
TRADING_STOP_LOSS_PERCENT=2.0
TRADING_TAKE_PROFIT_PERCENT=4.0
TRADING_USE_TRAILING_STOP=false
TRADING_TRAILING_STOP_PERCENT=1.0

# Database konfigurace
DATABASE_URL=sqlite:///./trading_assistant.db
DATABASE_USE_MONGODB=false
DATABASE_MONGODB_URL=
DATABASE_MONGODB_DB=trading_assistant

# Logging konfigurace
LOGGING_LEVEL=INFO
LOGGING_LOG_TO_FILE=true
LOGGING_LOG_FILE=logs/trading_assistant.log
LOGGING_ROTATION=1 day
LOGGING_RETENTION=1 month

# Debug režim
DEBUG=false
