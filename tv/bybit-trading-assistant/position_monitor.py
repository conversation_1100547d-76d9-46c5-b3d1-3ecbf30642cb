"""
Monitorovací skript pro sledování aktuáln<PERSON>ch otevřených pozic.
"""
import os
import time
import json
import argparse
import hmac
import hashlib
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional
from tabulate import tabulate
import colorama
from colorama import Fore, Style


# Inicializace colorama
colorama.init()


# Načtení konfigurace
def load_config():
    """Načte konfiguraci z config.json."""
    try:
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "config.json")
        with open(config_path, "r") as f:
            return json.load(f)
    except Exception as e:
        print(f"{Fore.RED}Chyba při načítání konfigurace: {e}{Style.RESET_ALL}")
        return {}


# Načtení API klíčů z konfigurace
config = load_config()
API_KEY = config.get("api", {}).get("bybit", {}).get("api_key", "")
API_SECRET = config.get("api", {}).get("bybit", {}).get("api_secret", "")

if not API_KEY or not API_SECRET:
    print(f"{Fore.RED}Chyba: API klíče nejsou nastaveny v konfiguraci.{Style.RESET_ALL}")


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def format_time(timestamp: int) -> str:
    """Formátuje časové razítko."""
    dt = datetime.fromtimestamp(timestamp / 1000)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def get_signature(timestamp: int, api_key: str, recv_window: int, api_secret: str) -> str:
    """
    Vytvoří podpis pro Bybit API.

    Args:
        timestamp: Časové razítko
        api_key: API klíč
        recv_window: Okno pro příjem
        api_secret: API tajný klíč

    Returns:
        str: Podpis
    """
    param_str = f"api_key={api_key}&recv_window={recv_window}&timestamp={timestamp}"
    return hmac.new(
        bytes(api_secret, "utf-8"),
        bytes(param_str, "utf-8"),
        digestmod=hashlib.sha256
    ).hexdigest()


def get_positions() -> List[Dict[str, Any]]:
    """
    Získá seznam otevřených pozic z Bybit API.

    Returns:
        List[Dict[str, Any]]: Seznam otevřených pozic
    """
    try:
        timestamp = int(time.time() * 1000)
        recv_window = 5000

        # Vytvoření podpisu
        signature = get_signature(timestamp, API_KEY, recv_window, API_SECRET)

        # Nastavení hlaviček
        headers = {
            "X-BAPI-API-KEY": API_KEY,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

        # Odeslání požadavku
        url = "https://api.bybit.com/v5/position/list"
        params = {
            "category": "spot",
            "settleCoin": "USDT"
        }

        response = requests.get(url, headers=headers, params=params)
        data = response.json()

        if data["retCode"] == 0:
            positions = data.get("result", {}).get("list", [])
            return positions
        else:
            print(f"{Fore.RED}Chyba při získávání pozic: {data['retMsg']}{Style.RESET_ALL}")
            return []
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání pozic: {e}{Style.RESET_ALL}")
        return []


def get_account_info() -> Dict[str, Any]:
    """
    Získá informace o účtu z Bybit API.

    Returns:
        Dict[str, Any]: Informace o účtu
    """
    try:
        timestamp = int(time.time() * 1000)
        recv_window = 5000

        # Vytvoření podpisu
        signature = get_signature(timestamp, API_KEY, recv_window, API_SECRET)

        # Nastavení hlaviček
        headers = {
            "X-BAPI-API-KEY": API_KEY,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

        # Odeslání požadavku
        url = "https://api.bybit.com/v5/account/wallet-balance"
        params = {
            "accountType": "UNIFIED"
        }

        response = requests.get(url, headers=headers, params=params)
        data = response.json()

        if data["retCode"] == 0:
            account_info = data.get("result", {})
            return account_info
        else:
            print(f"{Fore.RED}Chyba při získávání informací o účtu: {data['retMsg']}{Style.RESET_ALL}")
            return {"error": data["retMsg"]}
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání informací o účtu: {e}{Style.RESET_ALL}")
        return {"error": str(e)}


def get_market_data(symbol: str) -> Dict[str, Any]:
    """
    Získá tržní data z Bybit API.

    Args:
        symbol: Symbol

    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        # Odeslání požadavku
        url = "https://api.bybit.com/v5/market/tickers"
        params = {
            "category": "spot",
            "symbol": symbol
        }

        response = requests.get(url, params=params)
        data = response.json()

        if data["retCode"] == 0:
            tickers = data.get("result", {}).get("list", [])
            if tickers:
                ticker = tickers[0]
                return {
                    "ticker": ticker,
                    "indicators": {}  # Indikátory nejsou dostupné přes Bybit API
                }
            else:
                return {}
        else:
            print(f"{Fore.RED}Chyba při získávání tržních dat: {data['retMsg']}{Style.RESET_ALL}")
            return {}
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání tržních dat: {e}{Style.RESET_ALL}")
        return {}


def display_positions(positions: List[Dict[str, Any]]):
    """
    Zobrazí seznam otevřených pozic.

    Args:
        positions: Seznam otevřených pozic
    """
    if not positions:
        print(f"{Fore.YELLOW}Žádné otevřené pozice{Style.RESET_ALL}")
        return

    # Příprava dat pro tabulku
    table_data = []
    for position in positions:
        symbol = position.get("symbol", "")
        side = position.get("side", "")
        side_color = Fore.GREEN if side == "Buy" else Fore.RED
        quantity = float(position.get("size", 0))
        entry_price = float(position.get("avgPrice", 0))
        current_price = float(position.get("markPrice", 0))
        unrealized_pnl = float(position.get("unrealisedPnl", 0))

        # Výpočet procentuálního zisku/ztráty
        if entry_price > 0:
            if side == "Buy":
                profit_percentage = ((current_price - entry_price) / entry_price) * 100
            else:
                profit_percentage = ((entry_price - current_price) / entry_price) * 100
        else:
            profit_percentage = 0

        # Formátování hodnot
        side_formatted = f"{side_color}{side}{Style.RESET_ALL}"
        unrealized_pnl_formatted = format_currency(unrealized_pnl)
        profit_percentage_formatted = format_percentage(profit_percentage)

        # Přidání řádku do tabulky
        table_data.append([
            symbol,
            side_formatted,
            f"{quantity:.6f}",
            format_currency(entry_price),
            format_currency(current_price),
            unrealized_pnl_formatted,
            profit_percentage_formatted
        ])

    # Zobrazení tabulky
    headers = ["Symbol", "Strana", "Množství", "Vstupní cena", "Aktuální cena", "PnL", "PnL %"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))


def display_account_info(account_info: Dict[str, Any]):
    """
    Zobrazí informace o účtu.

    Args:
        account_info: Informace o účtu
    """
    if "error" in account_info:
        print(f"{Fore.RED}Chyba: {account_info['error']}{Style.RESET_ALL}")
        return

    # Získání hodnot
    coins = account_info.get("list", [{}])[0].get("coin", [])
    total_equity = 0
    total_available_balance = 0

    for coin in coins:
        if coin.get("coin") == "USDT":
            total_equity = float(coin.get("equity", 0))
            total_available_balance = float(coin.get("availableToWithdraw", 0))

    # Formátování hodnot
    total_equity_formatted = format_currency(total_equity)
    total_available_balance_formatted = format_currency(total_available_balance)

    # Zobrazení informací
    print(f"{Fore.CYAN}=== Informace o účtu ==={Style.RESET_ALL}")
    print(f"Celková hodnota: {total_equity_formatted}")
    print(f"Dostupný zůstatek: {total_available_balance_formatted}")
    print()


def display_market_data(market_data: Dict[str, Any], symbol: str):
    """
    Zobrazí tržní data pro daný symbol.

    Args:
        market_data: Tržní data
        symbol: Symbol
    """
    if not market_data:
        return

    ticker = market_data.get("ticker", {})

    # Získání hodnot
    last_price = float(ticker.get("lastPrice", 0))
    price_change = float(ticker.get("price24hPcnt", 0)) * 100
    volume = float(ticker.get("volume24h", 0))

    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)

    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"Objem 24h: {format_currency(volume)}")
    print()


def monitor(refresh_interval: int):
    """
    Monitoruje stav obchodního asistenta.

    Args:
        refresh_interval: Interval obnovení v sekundách
    """
    try:
        while True:
            clear_screen()

            print(f"{Fore.YELLOW}=== Bybit Trading Assistant - Monitor pozic ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()

            # Získání dat
            positions = get_positions()
            account_info = get_account_info()

            # Zobrazení dat
            display_account_info(account_info)

            print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
            display_positions(positions)
            print()

            # Zobrazení tržních dat pro každý symbol s otevřenou pozicí
            position_symbols = [p.get("symbol", "") for p in positions]
            for symbol in position_symbols:
                if symbol:
                    market_data = get_market_data(symbol)
                    display_market_data(market_data, symbol)

            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        print("\nMonitorování ukončeno.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Monitor pozic pro Bybit Trading Assistant")
    parser.add_argument("--interval", type=int, default=10, help="Interval obnovení v sekundách")

    args = parser.parse_args()

    monitor(args.interval)
