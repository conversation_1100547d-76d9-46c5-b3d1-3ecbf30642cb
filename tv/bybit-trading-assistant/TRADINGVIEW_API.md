# Použití TradingView API v Bybit Trading Assistant

Tento dokument popisuje, jak používat TradingView API v projektu Bybit Trading Assistant.

## Přehled

Bybit Trading Assistant podporuje dva způsoby získávání signálů z TradingView:

1. **TradingView API** - Přímé získávání dat z TradingView pomocí knihoven
2. **TradingView Webhooky** - Příjem signálů z TradingView přes webhooky

## TradingView API

Projekt používá dvě různé implementace TradingView API:

### 1. Python implementace (tradingview-ta)

Používá oficiální Python knihovnu `tradingview-ta`, která poskytuje přístup k technickým indikátorům a doporučením z TradingView.

```python
from tradingview_ta import TA_Handler, Interval

handler = TA_Handler(
    symbol="BTCUSDT",
    exchange="BINANCE",
    screener="crypto",
    interval=Interval.INTERVAL_1_HOUR
)

analysis = handler.get_analysis()
recommendation = analysis.summary["RECOMMENDATION"]
```

### 2. Node.js implementace (@mathieuc/tradingview)

Používá neoficiální Node.js knihovnu `@mathieuc/tradingview`, která poskytuje přístup k více funkcím TradingView, včetně vlastních indikátorů.

```javascript
const TradingView = require('@mathieuc/tradingview');

const client = new TradingView.Client();
const chart = new client.Session.Chart();

chart.setMarket('BINANCE:BTCUSDT', {
  timeframe: '60',  // 1h
});

const indicator = chart.createIndicator('RSI', {
  length: 14
});

chart.onUpdate(() => {
  const value = indicator.getValue();
  console.log(`RSI: ${value}`);
});
```

## Konfigurace

Konfigurace TradingView API se provádí v souboru `.env`:

```
# TradingView konfigurace
TRADINGVIEW_WEBHOOK_PORT=8000
TRADINGVIEW_WEBHOOK_PATH=/webhook
TRADINGVIEW_WEBHOOK_SECRET=
TRADINGVIEW_USERNAME=
TRADINGVIEW_PASSWORD=
TRADINGVIEW_USE_API=true
TRADINGVIEW_CHECK_INTERVAL=60
TRADINGVIEW_INDICATORS=RSI,MACD,BB
```

- `TRADINGVIEW_USE_API` - Určuje, zda se má používat TradingView API (true) nebo webhooky (false)
- `TRADINGVIEW_CHECK_INTERVAL` - Interval kontroly signálů v sekundách
- `TRADINGVIEW_INDICATORS` - Seznam indikátorů pro sledování

## Instalace

Pro použití TradingView API je potřeba nainstalovat potřebné závislosti:

```bash
# Instalace Python knihovny
pip install tradingview-ta

# Instalace Node.js knihovny
npm install @mathieuc/tradingview
```

Nebo můžete použít připravený instalační skript:

```bash
./install_tradingview.sh
```

## Použití v kódu

### Získání signálů

```python
from src.application.services.tradingview_service import TradingViewService

# Inicializace služby
tradingview_service = TradingViewService()

# Získání signálů
signals = await tradingview_service.get_signals()

# Zpracování signálů
for signal in signals:
    print(f"Signál: {signal.type.value} pro {signal.symbol} na ceně {signal.price}")
```

### Získání technických indikátorů

```python
from src.application.services.tradingview_service import TradingViewService

# Inicializace služby
tradingview_service = TradingViewService()

# Získání technických indikátorů
indicators = await tradingview_service.get_technical_indicators("BTCUSDT")

# Výpis indikátorů
print(f"RSI: {indicators.get('rsi')}")
print(f"MACD: {indicators.get('macd')}")
print(f"MACD Signal: {indicators.get('macd_signal')}")
```

## Testování

Pro testování TradingView API můžete použít připravený testovací skript:

```bash
python test_tradingview.py --symbol BTCUSDT --interval 1h
```

## Příklady

V adresáři `examples` najdete příklady použití TradingView API:

- `tradingview_python_example.py` - Příklad použití Python knihovny
- `tradingview_node_example.js` - Příklad použití Node.js knihovny

## Řešení problémů

### Chyba "No data found, symbol may be delisted"

Tato chyba může nastat, pokud symbol není dostupný na dané burze. Zkuste změnit burzu nebo symbol.

### Chyba "Timeout"

Tato chyba může nastat, pokud je TradingView API přetížené nebo nedostupné. Zkuste zvýšit timeout nebo počkat chvíli a zkusit to znovu.

### Chyba "Invalid credentials"

Tato chyba může nastat, pokud jsou neplatné přihlašovací údaje k TradingView. Zkontrolujte své uživatelské jméno a heslo.

## Omezení

- TradingView API má omezení na počet požadavků za minutu
- Neoficiální knihovna `@mathieuc/tradingview` může přestat fungovat při změnách v TradingView API
- Pro některé funkce je potřeba mít TradingView Pro účet
