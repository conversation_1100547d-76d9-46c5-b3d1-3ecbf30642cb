#!/bin/bash

# Skript pro testování integrace s TradingView
# Použití: ./run_tradingview_test.sh [symbol] [interval]

# Aktivace virtuálního prostředí
source venv/bin/activate

# Nastavení parametrů
SYMBOL=${1:-"BTCUSDT"}
INTERVAL=${2:-"1h"}

# Spuštění testu
echo "Spouštění testu TradingView integrace pro symbol $SYMBOL ($INTERVAL)..."
python test_tradingview.py --symbol $SYMBOL --interval $INTERVAL

# Deaktivace virtuálního prostředí
deactivate