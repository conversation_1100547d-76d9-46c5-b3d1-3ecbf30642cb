#!/usr/bin/env python3
"""
Skript pro spuštění obchodního bota s pokročilými strategiemi.
"""
import os
import asyncio
import logging
import argparse
from datetime import datetime

from src.strategies.strategy_manager import StrategyManager
from src.application.services.market_data_service import MarketDataService
from src.application.services.trading_service import TradingService
from src.application.services.strategy_service import StrategyService
from src.application.services.signal_service import SignalService
from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.config.config import config


# Nastavení loggeru
logging.basicConfig(
    level=getattr(logging, config.logging.level),
    format=config.logging.format,
    handlers=[
        logging.FileHandler(config.logging.file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


async def main():
    """Hlavní funkce."""
    # Zpracování argumentů
    parser = argparse.ArgumentParser(description="Ob<PERSON>dn<PERSON> bot s pokročilými strategiemi")
    parser.add_argument("--symbols", type=str, default=",".join(config.trading.symbols),
                       help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=config.trading.check_interval,
                       help="Interval kontroly v sekundách")
    parser.add_argument("--testnet", action="store_true", default=config.bybit.testnet,
                       help="Použití testovacího prostředí")
    
    args = parser.parse_args()
    
    # Nastavení seznamu symbolů
    symbols = args.symbols.split(",")
    
    # Vytvoření klienta pro Bybit API
    bybit_client = BybitClient(api_key=config.bybit.api_key, 
                              api_secret=config.bybit.api_secret,
                              testnet=args.testnet)
    
    # Vytvoření služeb
    market_data_service = MarketDataService(bybit_client)
    signal_service = SignalService()
    strategy_service = StrategyService()
    trading_service = TradingService(
        bybit_client=bybit_client,
        signal_service=signal_service,
        strategy_service=strategy_service,
        market_data_service=market_data_service
    )
    
    # Výpis dostupných strategií
    strategies = strategy_service.get_available_strategies()
    logger.info(f"Dostupné strategie: {', '.join(strategies.keys())}")
    
    # Kontrola, zda máme nastaveny API klíče
    if not config.bybit.api_key or not config.bybit.api_secret:
        logger.warning("Nejsou nastaveny API klíče pro Bybit. Bot bude běžet v režimu simulace.")
    
    # Ověření spojení s Bybit API
    try:
        account_info = await bybit_client.get_account_info()
        logger.info(f"Připojeno k Bybit API. Zůstatek účtu: {account_info.get('totalEquity', 'N/A')}")
    except Exception as e:
        logger.error(f"Chyba při připojení k Bybit API: {e}")
        logger.warning("Bot bude pokračovat, ale nebude možné obchodovat.")
    
    # Hlavní smyčka
    logger.info(f"Spouštění obchodního bota pro symboly: {', '.join(symbols)}")
    logger.info(f"Interval kontroly: {args.interval} sekund")
    
    try:
        while True:
            start_time = datetime.now()
            logger.info(f"Začátek kontroly: {start_time}")
            
            # Aktualizace otevřených pozic
            await trading_service.update_positions()
            open_positions = trading_service.get_open_positions()
            logger.info(f"Počet otevřených pozic: {len(open_positions)}")
            
            # Analýza trhu pro každý symbol
            for symbol in symbols:
                try:
                    logger.info(f"Analýza trhu pro {symbol}")
                    result = await trading_service.analyze_market(symbol)
                    
                    if "error" in result:
                        logger.warning(f"Chyba při analýze trhu pro {symbol}: {result['error']}")
                    elif result.get("signal"):
                        logger.info(f"Vygenerován signál pro {symbol}: {result['signal']['type']}")
                    else:
                        logger.info(f"Žádný signál pro {symbol}")
                        
                except Exception as e:
                    logger.error(f"Chyba při analýze trhu pro {symbol}: {e}")
            
            # Výpočet času do další kontroly
            elapsed_time = (datetime.now() - start_time).total_seconds()
            sleep_time = max(1, args.interval - elapsed_time)
            
            logger.info(f"Kontrola dokončena za {elapsed_time:.2f} s. Další kontrola za {sleep_time:.2f} s.")
            await asyncio.sleep(sleep_time)
            
    except KeyboardInterrupt:
        logger.info("Bot byl ukončen uživatelem.")
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())