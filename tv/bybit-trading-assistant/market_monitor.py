"""
Monitorovací skript pro sledování tržních dat z Bybit (veřejné API).
"""
import os
import time
import argparse
import requests
from datetime import datetime
from typing import Dict, Any, List
import colorama
from colorama import Fore, Style


# Inicializace colorama
colorama.init()


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def get_market_data(symbol: str) -> Dict[str, Any]:
    """
    Získá tržní data z Bybit API.
    
    Args:
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        # Odeslání požadavku
        url = "https://api.bybit.com/v5/market/tickers"
        params = {
            "category": "spot",
            "symbol": symbol
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data["retCode"] == 0:
            tickers = data.get("result", {}).get("list", [])
            if tickers:
                ticker = tickers[0]
                return {
                    "ticker": ticker,
                    "indicators": {}  # Indikátory nejsou dostupné přes Bybit API
                }
            else:
                return {}
        else:
            print(f"{Fore.RED}Chyba při získávání tržních dat: {data['retMsg']}{Style.RESET_ALL}")
            return {}
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání tržních dat: {e}{Style.RESET_ALL}")
        return {}


def get_klines(symbol: str, interval: str = "15", limit: int = 200) -> List[Dict[str, Any]]:
    """
    Získá historická data (svíčky) z Bybit API.
    
    Args:
        symbol: Symbol
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet svíček
        
    Returns:
        List[Dict[str, Any]]: Seznam svíček
    """
    try:
        # Odeslání požadavku
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "spot",
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if data["retCode"] == 0:
            klines = data.get("result", {}).get("list", [])
            return klines
        else:
            print(f"{Fore.RED}Chyba při získávání historických dat: {data['retMsg']}{Style.RESET_ALL}")
            return []
    except Exception as e:
        print(f"{Fore.RED}Chyba při získávání historických dat: {e}{Style.RESET_ALL}")
        return []


def calculate_rsi(klines: List[Dict[str, Any]], period: int = 14) -> float:
    """
    Vypočítá RSI (Relative Strength Index).
    
    Args:
        klines: Seznam svíček
        period: Perioda
        
    Returns:
        float: RSI hodnota
    """
    if len(klines) < period + 1:
        return 50.0
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet změn
    deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
    
    # Výpočet zisků a ztrát
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Výpočet průměrných zisků a ztrát
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    # Výpočet RS a RSI
    if avg_loss == 0:
        return 100.0
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def calculate_macd(klines: List[Dict[str, Any]], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, float]:
    """
    Vypočítá MACD (Moving Average Convergence Divergence).
    
    Args:
        klines: Seznam svíček
        fast_period: Rychlá perioda
        slow_period: Pomalá perioda
        signal_period: Signální perioda
        
    Returns:
        Dict[str, float]: MACD hodnoty
    """
    if len(klines) < slow_period + signal_period:
        return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
    
    # Získání cen
    prices = [float(kline[4]) for kline in klines]  # Uzavírací ceny
    prices.reverse()  # Nejnovější data první
    
    # Výpočet EMA
    def ema(data, period):
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema_values.append((data[i] - ema_values[i-1]) * multiplier + ema_values[i-1])
        
        return ema_values
    
    # Výpočet MACD
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]
    signal_line = ema(macd_line, signal_period)
    histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]
    
    return {
        "macd": macd_line[-1],
        "signal": signal_line[-1],
        "histogram": histogram[-1]
    }


def display_market_data(market_data: Dict[str, Any], symbol: str, indicators: Dict[str, float]):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
        indicators: Technické indikátory
    """
    if not market_data:
        return
    
    ticker = market_data.get("ticker", {})
    
    # Získání hodnot
    last_price = float(ticker.get("lastPrice", 0))
    price_change = float(ticker.get("price24hPcnt", 0)) * 100
    volume = float(ticker.get("volume24h", 0))
    high_24h = float(ticker.get("highPrice24h", 0))
    low_24h = float(ticker.get("lowPrice24h", 0))
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    high_24h_formatted = format_currency(high_24h)
    low_24h_formatted = format_currency(low_24h)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    print(f"24h Rozsah: {low_24h_formatted} - {high_24h_formatted}")
    print(f"Objem 24h: {format_currency(volume)}")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN
            if rsi > 70:
                rsi_color = Fore.RED
            elif rsi < 30:
                rsi_color = Fore.YELLOW
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "signal" in indicators:
            macd = indicators["macd"]
            signal = indicators["signal"]
            histogram = indicators.get("histogram", 0)
            
            macd_color = Fore.GREEN if macd > signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {signal:.2f}, Histogram: {histogram:.2f}")
    
    print()


def monitor(symbols: List[str], refresh_interval: int):
    """
    Monitoruje tržní data.
    
    Args:
        symbols: Seznam symbolů
        refresh_interval: Interval obnovení v sekundách
    """
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Market Monitor ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # Zobrazení tržních dat pro každý symbol
            for symbol in symbols:
                market_data = get_market_data(symbol)
                
                # Získání historických dat pro výpočet indikátorů
                klines = get_klines(symbol)
                
                # Výpočet indikátorů
                indicators = {}
                if klines:
                    indicators["rsi"] = calculate_rsi(klines)
                    macd_values = calculate_macd(klines)
                    indicators.update(macd_values)
                
                # Zobrazení dat
                display_market_data(market_data, symbol, indicators)
            
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        print("\nMonitorování ukončeno.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bybit Market Monitor")
    parser.add_argument("--symbols", type=str, default="BTCUSDT,ETHUSDT,SOLUSDT", help="Seznam symbolů oddělených čárkou")
    parser.add_argument("--interval", type=int, default=10, help="Interval obnovení v sekundách")
    
    args = parser.parse_args()
    
    symbols = args.symbols.split(",")
    
    monitor(symbols, args.interval)
