import streamlit as st
import requests
import pandas as pd
import matplotlib.pyplot as plt
import time
from datetime import datetime, timedelta
import threading
import json
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import strategy performance analyzer
from src.strategy_performance import StrategyPerformanceAnalyzer, plot_equity_curve, plot_win_loss_distribution, plot_strategy_comparison

# Configure the page
st.set_page_config(
    page_title="Bybit Trading Assistant Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Global variables
API_BASE_URL = "http://localhost:8000"  # Adjust based on your FastAPI server port
REFRESH_INTERVAL = 30  # in seconds

# API functions
def get_account_info():
    try:
        response = requests.get(f"{API_BASE_URL}/account")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching account info: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

def get_positions():
    try:
        response = requests.get(f"{API_BASE_URL}/positions")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching positions: {response.status_code}")
            return {"positions": []}
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return {"positions": []}

def get_orders():
    try:
        response = requests.get(f"{API_BASE_URL}/orders")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching orders: {response.status_code}")
            return {"orders": []}
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return {"orders": []}

def get_market_data(symbol):
    try:
        response = requests.get(f"{API_BASE_URL}/market-data/{symbol}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching market data: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

def get_market_analysis(symbol):
    try:
        response = requests.get(f"{API_BASE_URL}/market-analysis/{symbol}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching market analysis: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

def get_tradingview_indicators(symbol):
    try:
        response = requests.get(f"{API_BASE_URL}/tradingview/indicators/{symbol}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching TradingView indicators: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None


def get_notifications(limit=20, notification_type=None, min_priority=None):
    try:
        params = {"limit": limit}
        if notification_type:
            params["notification_type"] = notification_type
        if min_priority:
            params["min_priority"] = min_priority
            
        response = requests.get(f"{API_BASE_URL}/notifications", params=params)
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching notifications: {response.status_code}")
            return {"notifications": []}
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return {"notifications": []}


def create_notification(title, message, notification_type="INFO", priority="MEDIUM", data=None):
    try:
        payload = {
            "title": title,
            "message": message,
            "type": notification_type,
            "priority": priority,
            "data": data or {}
        }
        
        response = requests.post(f"{API_BASE_URL}/notifications", json=payload)
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error creating notification: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None


def clear_notifications():
    try:
        response = requests.delete(f"{API_BASE_URL}/notifications")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error clearing notifications: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None


def get_strategy_trades(strategy_name, days=30):
    """
    Get trades for a specific strategy.
    
    Args:
        strategy_name: Name of the strategy
        days: Number of days to look back
        
    Returns:
        List of trades
    """
    try:
        # This would be replaced with an actual API call
        # For now, generate some sample trades for demo purposes
        import random
        from datetime import datetime, timedelta
        
        sample_trades = []
        num_trades = random.randint(20, 50)
        
        start_date = datetime.now() - timedelta(days=days)
        end_date = datetime.now()
        
        # Initial capital
        capital = 10000
        current_price = 50000  # For BTC/USDT
        
        for i in range(num_trades):
            # Random dates within range
            trade_date = start_date + (end_date - start_date) * random.random()
            close_date = trade_date + timedelta(hours=random.randint(1, 48))
            
            # Side (slightly biased to match strategy characteristics)
            side_bias = {
                "trend_following": 0.6,  # Trend following tends to have more longs in bull markets
                "rsi_macd": 0.5,         # RSI MACD is balanced
                "breakout": 0.55,        # Breakout slightly favors longs
                "volume": 0.5            # Volume is balanced
            }
            
            bias = side_bias.get(strategy_name, 0.5)
            side = "LONG" if random.random() < bias else "SHORT"
            
            # Entry and exit prices (with some bias based on strategy and side)
            price_change_pct = random.uniform(-0.05, 0.05)
            
            # Adjust win rates based on strategy
            win_rate = {
                "trend_following": 0.45,
                "rsi_macd": 0.55,
                "breakout": 0.40,
                "volume": 0.52
            }.get(strategy_name, 0.5)
            
            # Adjust price change based on win rate
            if (side == "LONG" and random.random() < win_rate) or (side == "SHORT" and random.random() > win_rate):
                price_change_pct = abs(price_change_pct)  # Winning trade
            else:
                price_change_pct = -abs(price_change_pct)  # Losing trade
            
            if side == "LONG":
                entry_price = current_price * (1 - random.uniform(0.005, 0.02))
                exit_price = entry_price * (1 + price_change_pct)
            else:  # SHORT
                entry_price = current_price * (1 + random.uniform(0.005, 0.02))
                exit_price = entry_price * (1 - price_change_pct)
            
            # Position size (0.001 to 0.1 BTC)
            quantity = round(random.uniform(0.001, 0.01), 3)
            
            # Create trade object
            trade = {
                "id": f"trade_{i}",
                "strategy": strategy_name,
                "symbol": "BTCUSDT",
                "side": side,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "quantity": quantity,
                "opened_at": trade_date.isoformat(),
                "closed_at": close_date.isoformat(),
                "status": "CLOSED",
                "pnl": (exit_price - entry_price) * quantity if side == "LONG" else (entry_price - exit_price) * quantity
            }
            
            sample_trades.append(trade)
            
            # Update current price for next trade
            current_price = exit_price
        
        return sample_trades
    except Exception as e:
        st.error(f"Error getting strategy trades: {e}")
        return []

def cancel_order(order_id):
    try:
        response = requests.delete(f"{API_BASE_URL}/orders/{order_id}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error canceling order: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

def close_position(position_id):
    try:
        response = requests.delete(f"{API_BASE_URL}/positions/{position_id}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error closing position: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

# Helper functions
def format_currency(value):
    return f"${value:.2f}" if value is not None else "N/A"

def format_percentage(value):
    return f"{value:.2f}%" if value is not None else "N/A"

def color_profit_percentage(val):
    if val is None or pd.isna(val):
        return ''
    
    val = float(val.strip('%'))
    if val > 0:
        return 'background-color: rgba(0, 255, 0, 0.2)'
    elif val < 0:
        return 'background-color: rgba(255, 0, 0, 0.2)'
    else:
        return ''

# Main dashboard
def main():
    # Sidebar
    st.sidebar.title("Bybit Trading Assistant")
    refresh_rate = st.sidebar.slider("Refresh rate (seconds)", min_value=5, max_value=60, value=REFRESH_INTERVAL)
    st.sidebar.info(f"Dashboard refreshes every {refresh_rate} seconds")
    
    # Manual refresh button
    if st.sidebar.button("Refresh Now"):
        st.experimental_rerun()
    
    # Navigation
    page = st.sidebar.radio("Navigate", ["Dashboard", "Positions", "Orders", "Market Analysis", "Strategy Performance", "Notifications", "Settings"])
    
    if page == "Dashboard":
        show_dashboard()
    elif page == "Positions":
        show_positions_page()
    elif page == "Orders":
        show_orders_page()
    elif page == "Market Analysis":
        show_market_analysis()
    elif page == "Strategy Performance":
        show_strategy_performance()
    elif page == "Notifications":
        show_notifications_page()
    elif page == "Settings":
        show_settings()
    
    # Auto-refresh
    time_placeholder = st.empty()
    last_refresh = datetime.now()
    time_placeholder.text(f"Last refreshed: {last_refresh.strftime('%H:%M:%S')}")

def show_dashboard():
    st.title("Trading Dashboard")
    
    # Recent important notifications
    notifications_data = get_notifications(limit=3, min_priority="HIGH")
    important_notifications = notifications_data.get("notifications", [])
    
    if important_notifications:
        st.warning("Important Notifications")
        for notification in important_notifications:
            st.info(f"**{notification['title']}**: {notification['message']}")
        st.markdown("---")
    
    # Account summary
    account_info = get_account_info()
    if account_info:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Balance", format_currency(account_info.get("total_equity", 0)))
        
        with col2:
            st.metric("Available", format_currency(account_info.get("available_balance", 0)))
        
        with col3:
            daily_pnl = account_info.get("daily_pnl", 0)
            st.metric("Daily P&L", format_currency(daily_pnl), format_percentage(account_info.get("daily_pnl_percentage", 0)))
        
        with col4:
            st.metric("Margin Used", format_percentage(account_info.get("margin_used_percentage", 0)))
    
    # Open positions summary
    positions_data = get_positions()
    positions = positions_data.get("positions", [])
    
    if positions:
        st.subheader(f"Open Positions ({len(positions)})")
        
        # Create a dataframe for display
        positions_df = pd.DataFrame(positions)
        if not positions_df.empty:
            # Select and rename columns for better display
            display_columns = {
                "symbol": "Symbol",
                "side": "Side",
                "quantity": "Size",
                "entry_price": "Entry",
                "current_price": "Current",
                "profit_percentage": "P&L %",
                "unrealized_pnl": "Unrealized P&L",
                "value": "Position Value"
            }
            
            # Check if columns exist
            available_columns = [col for col in display_columns.keys() if col in positions_df.columns]
            positions_df_display = positions_df[available_columns].copy()
            
            # Rename columns
            positions_df_display.columns = [display_columns[col] for col in available_columns]
            
            # Format numeric columns
            for col in ["Entry", "Current", "Unrealized P&L", "Position Value"]:
                if col in positions_df_display.columns:
                    positions_df_display[col] = positions_df_display[col].apply(lambda x: f"${x:.2f}" if x is not None else "N/A")
            
            if "P&L %" in positions_df_display.columns:
                positions_df_display["P&L %"] = positions_df_display["P&L %"].apply(lambda x: f"{x:.2f}%" if x is not None else "N/A")
            
            # Apply styling
            styled_df = positions_df_display.style.applymap(color_profit_percentage, subset=["P&L %"])
            st.dataframe(styled_df, use_container_width=True)
            
            # Summary metrics for positions
            total_value = sum(p.get("value", 0) for p in positions)
            total_pnl = sum(p.get("unrealized_pnl", 0) for p in positions)
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Position Value", format_currency(total_value))
            with col2:
                st.metric("Total Unrealized P&L", format_currency(total_pnl))
    else:
        st.info("No open positions")
    
    # Recent orders
    orders_data = get_orders()
    orders = orders_data.get("orders", [])
    
    if orders:
        st.subheader(f"Recent Orders ({len(orders)})")
        
        orders_df = pd.DataFrame(orders)
        if not orders_df.empty:
            # Select and rename columns for better display
            display_columns = {
                "symbol": "Symbol",
                "side": "Side",
                "type": "Type",
                "quantity": "Size",
                "price": "Price",
                "status": "Status",
                "created_at": "Created"
            }
            
            # Check if columns exist
            available_columns = [col for col in display_columns.keys() if col in orders_df.columns]
            orders_df_display = orders_df[available_columns].copy()
            
            # Rename columns
            orders_df_display.columns = [display_columns[col] for col in available_columns]
            
            # Format numeric columns
            if "Price" in orders_df_display.columns:
                orders_df_display["Price"] = orders_df_display["Price"].apply(lambda x: f"${x:.2f}" if x is not None else "Market")
            
            # Format dates
            if "Created" in orders_df_display.columns:
                orders_df_display["Created"] = pd.to_datetime(orders_df_display["Created"]).dt.strftime('%Y-%m-%d %H:%M')
            
            st.dataframe(orders_df_display, use_container_width=True)
    else:
        st.info("No recent orders")

def show_positions_page():
    st.title("Open Positions")
    
    positions_data = get_positions()
    positions = positions_data.get("positions", [])
    
    if not positions:
        st.info("No open positions")
        return
    
    # Create dataframe
    positions_df = pd.DataFrame(positions)
    
    # Format and display
    if not positions_df.empty:
        # Convert timestamps
        if "opened_at" in positions_df.columns:
            positions_df["opened_at"] = pd.to_datetime(positions_df["opened_at"])
        
        # Display each position as a card
        for i, position in positions_df.iterrows():
            col1, col2 = st.columns([3, 1])
            
            with col1:
                symbol = position.get("symbol", "Unknown")
                side = position.get("side", "Unknown")
                entry_price = position.get("entry_price", 0)
                current_price = position.get("current_price", 0)
                quantity = position.get("quantity", 0)
                profit_pct = position.get("profit_percentage", 0)
                unrealized_pnl = position.get("unrealized_pnl", 0)
                
                # Position header with colored badge for side
                side_color = "green" if side == "LONG" else "red"
                st.markdown(f"### {symbol} <span style='color:{side_color};'>{side}</span>", unsafe_allow_html=True)
                
                # Position details
                col_a, col_b, col_c, col_d = st.columns(4)
                col_a.metric("Entry Price", format_currency(entry_price))
                col_b.metric("Current Price", format_currency(current_price))
                col_c.metric("Size", f"{quantity}")
                col_d.metric("P&L", format_currency(unrealized_pnl), format_percentage(profit_pct))
                
                # Risk management
                st.subheader("Risk Management")
                sl_col, tp_col = st.columns(2)
                
                with sl_col:
                    stop_loss = position.get("stop_loss")
                    if stop_loss:
                        sl_distance = abs(((stop_loss / entry_price) - 1) * 100)
                        st.metric("Stop Loss", format_currency(stop_loss), f"{sl_distance:.2f}% from entry")
                    else:
                        st.warning("No stop loss set")
                
                with tp_col:
                    take_profit = position.get("take_profit")
                    if take_profit:
                        tp_distance = abs(((take_profit / entry_price) - 1) * 100)
                        st.metric("Take Profit", format_currency(take_profit), f"{tp_distance:.2f}% from entry")
                    else:
                        st.warning("No take profit set")
            
            with col2:
                st.subheader("Actions")
                position_id = position.get("id", "")
                
                # Close position button
                if st.button("Close Position", key=f"close_{position_id}"):
                    with st.spinner("Closing position..."):
                        result = close_position(position_id)
                        if result and result.get("status") == "success":
                            st.success(result.get("message", "Position closed successfully"))
                            time.sleep(1)
                            st.experimental_rerun()
                
                # Modify stop loss and take profit
                st.subheader("Modify")
                new_sl = st.number_input("New Stop Loss", min_value=0.0, value=float(position.get("stop_loss", 0)), key=f"sl_{position_id}")
                new_tp = st.number_input("New Take Profit", min_value=0.0, value=float(position.get("take_profit", 0)), key=f"tp_{position_id}")
                
                if st.button("Update", key=f"update_{position_id}"):
                    st.warning("API endpoint for updating SL/TP not implemented yet")
            
            st.markdown("---")

def show_orders_page():
    st.title("Open Orders")
    
    orders_data = get_orders()
    orders = orders_data.get("orders", [])
    
    if not orders:
        st.info("No open orders")
        return
    
    # Create dataframe
    orders_df = pd.DataFrame(orders)
    
    # Format and display
    if not orders_df.empty:
        # Convert timestamps
        if "created_at" in orders_df.columns:
            orders_df["created_at"] = pd.to_datetime(orders_df["created_at"])
        
        # Display each order as a card
        for i, order in orders_df.iterrows():
            col1, col2 = st.columns([3, 1])
            
            with col1:
                symbol = order.get("symbol", "Unknown")
                side = order.get("side", "Unknown")
                order_type = order.get("type", "Unknown")
                price = order.get("price", 0)
                quantity = order.get("quantity", 0)
                status = order.get("status", "Unknown")
                created_at = order.get("created_at", "Unknown")
                
                # Order header with colored badge for side
                side_color = "green" if side == "BUY" else "red"
                st.markdown(f"### {symbol} <span style='color:{side_color};'>{side}</span> - {order_type}", unsafe_allow_html=True)
                
                # Order details
                col_a, col_b, col_c, col_d = st.columns(4)
                col_a.metric("Price", format_currency(price) if price else "Market")
                col_b.metric("Size", f"{quantity}")
                col_c.metric("Status", status)
                if isinstance(created_at, str):
                    created_at_display = created_at
                else:
                    created_at_display = created_at.strftime("%Y-%m-%d %H:%M:%S") if created_at else "Unknown"
                col_d.metric("Created", created_at_display)
            
            with col2:
                st.subheader("Actions")
                order_id = order.get("id", "")
                
                # Cancel order button
                if st.button("Cancel Order", key=f"cancel_{order_id}"):
                    with st.spinner("Cancelling order..."):
                        result = cancel_order(order_id)
                        if result and result.get("status") == "success":
                            st.success(result.get("message", "Order cancelled successfully"))
                            time.sleep(1)
                            st.experimental_rerun()
            
            st.markdown("---")

def show_market_analysis():
    st.title("Market Analysis")
    
    # Symbol selection
    symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT", "XRPUSDT"]
    selected_symbol = st.selectbox("Select Symbol", symbols)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Market Data")
        market_data = get_market_data(selected_symbol)
        
        if market_data and market_data.get("status") == "success":
            data = market_data.get("market_data", {})
            
            # Display market data
            md_col1, md_col2 = st.columns(2)
            
            with md_col1:
                st.metric("Price", format_currency(data.get("last_price", 0)))
                st.metric("24h High", format_currency(data.get("high_price_24h", 0)))
                st.metric("24h Low", format_currency(data.get("low_price_24h", 0)))
            
            with md_col2:
                st.metric("24h Volume", format_currency(data.get("volume_24h", 0)))
                st.metric("24h Change", format_percentage(data.get("price_change_percent_24h", 0)))
                st.metric("Funding Rate", format_percentage(data.get("funding_rate", 0)))
        else:
            st.warning("Unable to fetch market data")
    
    with col2:
        st.subheader("Technical Indicators")
        indicators = get_tradingview_indicators(selected_symbol)
        
        if indicators and indicators.get("status") == "success":
            indicators_data = indicators.get("indicators", {})
            
            # Display indicators
            for category, values in indicators_data.items():
                st.write(f"**{category}**")
                
                if isinstance(values, dict):
                    for indicator, value in values.items():
                        st.metric(indicator, value)
                else:
                    st.write(values)
        else:
            st.warning("Unable to fetch technical indicators")
    
    # Market analysis
    st.subheader("Market Analysis")
    analysis = get_market_analysis(selected_symbol)
    
    if analysis and analysis.get("status") == "success":
        analysis_data = analysis.get("analysis", {})
        
        if isinstance(analysis_data, dict):
            for section, content in analysis_data.items():
                st.write(f"**{section}**")
                st.write(content)
        else:
            st.write(analysis_data)
    else:
        st.warning("Unable to fetch market analysis")

def show_notifications_page():
    st.title("Notifications & Alerts")
    
    # Get notifications
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("Recent Notifications")
    
    with col2:
        # Clear notifications button
        if st.button("Clear All Notifications"):
            with st.spinner("Clearing notifications..."):
                result = clear_notifications()
                if result and result.get("status") == "success":
                    st.success("Notifications cleared")
                    time.sleep(1)
                    st.experimental_rerun()
    
    # Filter options
    col1, col2, col3 = st.columns(3)
    
    with col1:
        notification_type = st.selectbox(
            "Filter by Type",
            ["All Types", "SIGNAL", "ORDER", "POSITION", "ERROR", "INFO", "WARNING"]
        )
    
    with col2:
        priority = st.selectbox(
            "Filter by Priority",
            ["All Priorities", "LOW", "MEDIUM", "HIGH", "CRITICAL"]
        )
    
    with col3:
        limit = st.slider("Number of notifications", min_value=5, max_value=100, value=20, step=5)
    
    # Apply filters
    filter_type = None if notification_type == "All Types" else notification_type
    filter_priority = None if priority == "All Priorities" else priority
    
    notifications_data = get_notifications(limit=limit, notification_type=filter_type, min_priority=filter_priority)
    notifications = notifications_data.get("notifications", [])
    
    if not notifications:
        st.info("No notifications found")
        
        # Add a sample notification for testing
        if st.button("Create Test Notification"):
            create_notification(
                title="Test Notification",
                message="This is a test notification to verify the system is working correctly.",
                notification_type="INFO",
                priority="MEDIUM"
            )
            time.sleep(1)
            st.experimental_rerun()
    else:
        # Display notifications
        for notification in notifications:
            # Determine color based on type and priority
            color = "blue"
            if notification["type"] == "ERROR":
                color = "red"
            elif notification["type"] == "WARNING":
                color = "orange"
            elif notification["type"] == "SIGNAL":
                color = "green"
            
            # Increase color intensity based on priority
            intensity = "100"
            if notification["priority"] == "HIGH":
                intensity = "300"
            elif notification["priority"] == "CRITICAL":
                intensity = "500"
            
            # Create a card-like container for each notification
            with st.container():
                st.markdown(f"""
                <div style="border-left: 5px solid {color}; padding-left: 10px; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: {color}{intensity};">{notification["title"]}</h4>
                    <p>{notification["message"]}</p>
                    <p style="font-size: 0.8em; color: gray;">
                        Type: {notification["type"]} | Priority: {notification["priority"]} | 
                        Time: {pd.to_datetime(notification["timestamp"]).strftime('%Y-%m-%d %H:%M:%S')}
                    </p>
                </div>
                """, unsafe_allow_html=True)
    
    # Create new notification form
    st.markdown("---")
    st.subheader("Create New Notification")
    
    with st.form("create_notification_form"):
        notification_title = st.text_input("Title")
        notification_message = st.text_area("Message")
        
        col1, col2 = st.columns(2)
        with col1:
            new_type = st.selectbox("Type", ["INFO", "WARNING", "ERROR", "SIGNAL", "ORDER", "POSITION"])
        with col2:
            new_priority = st.selectbox("Priority", ["LOW", "MEDIUM", "HIGH", "CRITICAL"])
        
        # Submit button
        submitted = st.form_submit_button("Create Notification")
        if submitted:
            if not notification_title or not notification_message:
                st.error("Title and Message are required")
            else:
                with st.spinner("Creating notification..."):
                    result = create_notification(
                        title=notification_title,
                        message=notification_message,
                        notification_type=new_type,
                        priority=new_priority
                    )
                    
                    if result and result.get("status") == "success":
                        st.success("Notification created successfully")
                        time.sleep(1)
                        st.experimental_rerun()


def show_strategy_performance():
    st.title("Strategy Performance")
    
    # Strategy selection
    available_strategies = ["trend_following", "rsi_macd", "breakout", "volume"]
    selected_strategy = st.selectbox("Select Strategy", available_strategies)
    
    # Time period selection
    col1, col2 = st.columns(2)
    with col1:
        time_period = st.selectbox(
            "Time Period",
            ["7 days", "30 days", "90 days", "Year to date", "1 year", "All time"],
            index=1
        )
    
    with col2:
        compare_strategies = st.checkbox("Compare with other strategies")
    
    # Convert time period to days
    days_lookup = {
        "7 days": 7,
        "30 days": 30,
        "90 days": 90,
        "Year to date": (datetime.now() - datetime(datetime.now().year, 1, 1)).days,
        "1 year": 365,
        "All time": 1000  # Just a large number for demo
    }
    days = days_lookup.get(time_period, 30)
    
    # Get trades for selected strategy
    trades = get_strategy_trades(selected_strategy, days)
    
    if not trades:
        st.warning(f"No trades found for {selected_strategy} in the selected time period")
        return
    
    # Initialize the analyzer
    analyzer = StrategyPerformanceAnalyzer()
    
    # Calculate metrics
    metrics = analyzer.calculate_metrics(trades)
    
    # Display summary metrics
    st.subheader("Performance Summary")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Trades", metrics["total_trades"])
        st.metric("Win Rate", f"{metrics['win_rate']:.2%}")
    
    with col2:
        st.metric("Net Profit", f"${metrics['net_profit']:.2f}")
        st.metric("Profit Factor", f"{metrics['profit_factor']:.2f}")
    
    with col3:
        st.metric("Max Drawdown", f"{metrics['max_drawdown_percentage']:.2f}%")
        avg_hold = metrics["average_holding_time"].total_seconds() / 3600  # Convert to hours
        st.metric("Avg Holding Time", f"{avg_hold:.1f} hours")
    
    with col4:
        st.metric("Sharpe Ratio", f"{metrics['sharpe_ratio']:.2f}")
        st.metric("Annualized Return", f"{metrics['annualized_return']:.2f}%")
    
    # Create equity curve
    st.subheader("Equity Curve")
    equity_data = analyzer.create_equity_curve(trades)
    fig = plot_equity_curve(equity_data, f"{selected_strategy.replace('_', ' ').title()} Equity Curve")
    st.pyplot(fig)
    
    # Win/loss distribution
    st.subheader("Win/Loss Distribution")
    dist_fig = plot_win_loss_distribution(trades, f"{selected_strategy.replace('_', ' ').title()} Win/Loss Distribution")
    st.pyplot(dist_fig)
    
    # Compare with other strategies if selected
    if compare_strategies:
        st.subheader("Strategy Comparison")
        
        # Get trades for all strategies
        all_strategy_trades = {}
        for strategy in available_strategies:
            all_strategy_trades[strategy] = get_strategy_trades(strategy, days)
        
        # Compare strategies
        comparison_results = analyzer.compare_strategies(all_strategy_trades)
        
        # Select metrics to compare
        metrics_to_compare = [
            "win_rate", 
            "net_profit", 
            "profit_factor", 
            "max_drawdown_percentage",
            "sharpe_ratio"
        ]
        
        # Plot comparison
        comparison_fig = plot_strategy_comparison(comparison_results, metrics_to_compare)
        st.pyplot(comparison_fig)
        
        # Display detailed comparison table
        st.subheader("Detailed Comparison")
        
        # Create comparison dataframe
        comparison_data = {}
        for strategy, metrics in comparison_results.items():
            strategy_data = {
                "Total Trades": metrics["total_trades"],
                "Win Rate": f"{metrics['win_rate']:.2%}",
                "Net Profit": f"${metrics['net_profit']:.2f}",
                "Profit Factor": f"{metrics['profit_factor']:.2f}",
                "Max Drawdown": f"{metrics['max_drawdown_percentage']:.2f}%",
                "Sharpe Ratio": f"{metrics['sharpe_ratio']:.2f}",
                "Annualized Return": f"{metrics['annualized_return']:.2f}%"
            }
            comparison_data[strategy] = strategy_data
        
        comparison_df = pd.DataFrame(comparison_data).T
        st.dataframe(comparison_df)
    
    # Trade details
    st.subheader("Trade Details")
    
    # Convert trades to DataFrame
    trades_df = pd.DataFrame(trades)
    
    # Format columns
    if not trades_df.empty:
        # Format timestamps
        trades_df["opened_at"] = pd.to_datetime(trades_df["opened_at"])
        trades_df["closed_at"] = pd.to_datetime(trades_df["closed_at"])
        
        # Calculate holding time
        trades_df["holding_time"] = trades_df["closed_at"] - trades_df["opened_at"]
        trades_df["holding_time_str"] = trades_df["holding_time"].apply(
            lambda x: f"{x.total_seconds() / 3600:.1f} hours"
        )
        
        # Format monetary values
        trades_df["entry_price"] = trades_df["entry_price"].apply(lambda x: f"${x:.2f}")
        trades_df["exit_price"] = trades_df["exit_price"].apply(lambda x: f"${x:.2f}")
        trades_df["pnl"] = trades_df["pnl"].apply(lambda x: f"${x:.2f}")
        
        # Select and rename columns
        display_columns = {
            "opened_at": "Entry Time",
            "closed_at": "Exit Time",
            "symbol": "Symbol",
            "side": "Side",
            "entry_price": "Entry Price",
            "exit_price": "Exit Price",
            "quantity": "Size",
            "pnl": "P&L",
            "holding_time_str": "Holding Time"
        }
        
        display_df = trades_df[[col for col in display_columns.keys() if col in trades_df.columns]]
        display_df.columns = [display_columns[col] for col in display_df.columns]
        
        # Sort by entry time descending
        display_df = display_df.sort_values("Entry Time", ascending=False)
        
        # Display table
        st.dataframe(display_df)


def show_settings():
    st.title("Settings")
    
    st.subheader("API Configuration")
    st.info("These settings are for display only. Update the config files to change them.")
    
    # Create placeholders for settings
    api_settings = {
        "Bybit API URL": "https://api.bybit.com",
        "TradingView Webhook Port": "8000",
        "Dashboard Refresh Rate": f"{REFRESH_INTERVAL} seconds"
    }
    
    # Display settings
    for setting, value in api_settings.items():
        col1, col2 = st.columns([1, 3])
        col1.write(setting)
        col2.code(value)
    
    st.subheader("Trading Settings")
    
    # Risk management settings
    st.write("Default Risk Management")
    
    col1, col2 = st.columns(2)
    with col1:
        risk_per_trade = st.slider("Risk per trade (%)", min_value=0.1, max_value=5.0, value=1.0, step=0.1)
        sl_percentage = st.slider("Default Stop Loss (%)", min_value=0.5, max_value=10.0, value=2.0, step=0.1)
    
    with col2:
        leverage = st.slider("Default Leverage", min_value=1, max_value=20, value=3)
        tp_percentage = st.slider("Default Take Profit (%)", min_value=0.5, max_value=20.0, value=4.0, step=0.1)
    
    # Save button
    if st.button("Save Settings"):
        st.warning("Settings update endpoint not implemented yet")
        st.info("This would save the settings to the configuration file")

if __name__ == "__main__":
    main()