#!/bin/bash

# Barvy
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# ASCII art zeleného dolaru
echo -e "${GREEN}"
cat /home/<USER>/bybit-trading-assistant/dollar-ascii.txt
echo -e "${NC}"

echo -e "${BLUE}=== Bybit Trading Assistant - Dashboard ===${NC}"
echo -e "${YELLOW}Spouštím pouze dashboard bez obchodního bota...${NC}"
cd /home/<USER>/bybit-trading-assistant

# Aktivace virtuálního prostředí
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "CHYBA: Virtuální prostředí nenalezeno!"
    exit 1
fi

# Spustit dashboard
echo "Spouštím dashboard pro monitoring..."
streamlit run dashboard.py