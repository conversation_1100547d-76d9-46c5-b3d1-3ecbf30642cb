# Bybit Trading Assistant Dashboard

This dashboard provides a user-friendly interface to monitor your trading activities, open positions, and market data.

## Features

- **Dashboard Overview**: Quick summary of account balance, open positions, and recent orders
- **Positions Management**: Detailed view of all open positions with the ability to close positions
- **Orders Management**: View and cancel open orders
- **Market Analysis**: Technical indicators and market data for various symbols
- **Strategy Performance**: Performance metrics and visualizations for trading strategies
- **Notifications**: Real-time alerts and notifications for important trading events
- **Settings**: Configure trading parameters

## How to Run

1. Make sure the FastAPI backend is running:
   ```
   python src/app.py
   ```

2. Run the dashboard in a separate terminal:
   ```
   ./run_dashboard.sh
   ```
   
   Alternatively, you can run it directly with:
   ```
   source venv/bin/activate
   streamlit run dashboard.py
   ```

3. Open your browser and navigate to:
   ```
   http://localhost:8501
   ```

## Dashboard Pages

### Dashboard Overview
Provides a quick summary of your account including:
- Account balance and available funds
- Daily P&L
- Open positions overview
- Recent orders

### Positions
Detailed view of each open position including:
- Entry and current price
- Position size
- Profit/loss metrics
- Stop loss and take profit levels
- Actions to close or modify positions

### Orders
Manage your open orders:
- View order details (symbol, type, price, etc.)
- Cancel orders directly from the interface

### Market Analysis
Analyze market conditions:
- Current market data (price, volume, funding rate)
- Technical indicators from TradingView
- AI-powered market analysis

### Strategy Performance
Analyze and compare trading strategy performance:
- View key performance metrics (win rate, profit factor, drawdown, etc.)
- Interactive equity curve visualization
- Win/loss distribution analysis
- Compare multiple strategies side-by-side
- Detailed trade history

### Notifications
Stay informed about important trading events:
- Real-time alerts for trades, positions, and system events
- Customizable notification priority levels
- Filter notifications by type and priority
- Create custom notifications

### Settings
Configure trading parameters:
- Risk management settings
- Default stop loss and take profit levels
- Leverage settings

## Troubleshooting

- If the dashboard shows connection errors, make sure the FastAPI backend is running
- Check that the API_BASE_URL in dashboard.py matches your FastAPI server address
- Ensure you have all required dependencies installed