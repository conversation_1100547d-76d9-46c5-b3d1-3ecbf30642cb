# Bybit Trading Assistant

Asistent pro sledování a správu obchodů na platformě Bybit s integrací TradingView.

## Instalace

1. Naklonujte repozitář:
   ```
   git clone https://github.com/username/bybit-trading-assistant.git
   cd bybit-trading-assistant
   ```

2. Vytvořte a aktivujte virtuální prostředí:
   ```
   python -m venv venv
   source venv/bin/activate  # Na Windows: venv\Scripts\activate
   ```

3. Nainstalujte závislosti:
   ```
   pip install -r requirements.txt
   ```

4. Nainstalujte Node.js závislosti:
   ```
   cd external/bybit-api
   npm install
   cd ../..
   
   cd external/TradingView-API
   npm install
   cd ../..
   ```

5. Vytvořte konfigurační soubor:
   ```
   mkdir -p config
   cp config/config.example.json config/config.json
   cp .env.example .env
   ```

6. Upravte konfigurační soubory `config/config.json` a `.env` a přidejte své API klíče.

## Použití

### Monitor pozic

Spusťte monitor pozic:

```
./run.sh
```

Pro použití testovacího prostředí (testnet):

```
./run.sh --testnet
```

### Analýza trhu

Spusťte analýzu trhu s využitím dat z TradingView:

```
./run_market_analyzer.sh
```

### Příklady

Podívejte se na příklady v adresáři `examples`:

```
# Příklad použití Bybit API
./run_example.sh

# Příklad použití TradingView API
./run_tradingview_example.sh
```

## Konfigurace

Konfigurační soubor `config/config.json` obsahuje následující nastavení:

```json
{
  "api": {
    "bybit": {
      "api_key": "VÁŠ_API_KLÍČ",
      "api_secret": "VÁŠ_API_SECRET",
      "testnet": false
    },
    "tradingview": {
      "username": "VÁŠ_USERNAME",
      "password": "VAŠE_HESLO"
    }
  },
  "trading": {
    "default_symbols": ["BTCUSDT", "ETHUSDT", "SOLUSDT"],
    "refresh_interval": 10,
    "risk_management": {
      "max_position_size_usd": 1000,
      "max_daily_loss_usd": 100,
      "stop_loss_percentage": 2.0,
      "take_profit_percentage": 5.0
    },
    "indicators": {
      "RSI": {"length": 14},
      "MACD": {"fast_length": 12, "slow_length": 26, "signal_length": 9}
    }
  },
  "logging": {
    "level": "INFO",
    "file": "../logs/trading_assistant.log",
    "console": true
  },
  "database": {
    "enabled": true,
    "type": "sqlite",
    "path": "../data/trading.db"
  }
}
```

Soubor `.env` obsahuje následující proměnné prostředí:

```
# Bybit API klíče
BYBIT_API_KEY=váš_api_klíč
BYBIT_API_SECRET=váš_api_secret
BYBIT_TESTNET=false

# TradingView přihlašovací údaje
TRADINGVIEW_USERNAME=váš_username
TRADINGVIEW_PASSWORD=vaše_heslo

# Nastavení prostředí
ENVIRONMENT=production  # production, development, test
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Nastavení databáze
DB_TYPE=sqlite
DB_PATH=data/trading.db
```

## Struktura projektu

```
bybit-trading-assistant/
├── config/                  # Konfigurační soubory
│   └── config.json          # Hlavní konfigurační soubor
├── data/                    # Data
├── examples/                # Příklady použití
├── external/                # Externí knihovny
│   ├── bybit-api/           # Bybit API knihovna
│   └── TradingView-API/     # TradingView API knihovna
├── logs/                    # Logy
├── src/                     # Zdrojový kód
│   ├── api/                 # API klienti
│   │   └── wrappers/        # Wrappery pro externí knihovny
│   ├── database/            # Databázová vrstva
│   ├── models/              # Datové modely
│   ├── services/            # Služby
│   ├── strategies/          # Obchodní strategie
│   └── utils/               # Utility
├── tests/                   # Testy
│   ├── integration/         # Integrační testy
│   └── unit/                # Unit testy
├── .env                     # Proměnné prostředí
├── .gitignore               # Git ignore soubor
├── README.md                # Dokumentace
└── requirements.txt         # Seznam závislostí
```

## Licence

MIT
# bybit-trading-assistant
