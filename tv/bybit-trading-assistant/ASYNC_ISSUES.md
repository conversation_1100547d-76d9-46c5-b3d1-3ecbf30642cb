# Problémy s asynchronním kódem v aplikaci

Během testování byly zjištěny problémy s asynchronním kódem v aplikaci. Toto je shrnutí problémů a potřebných oprav.

## Zjištěné problémy

1. **V MarketDataService byly funkcím p<PERSON> `async` modifikátory**, ale volající kód nebyl upraven pro použití `await`.

2. **Chyby v běhu**:
   - `object of type 'coroutine' has no len()` - Tato chyba nastává, když se asynchronní funkce volá bez `await`, což vrací objekt coroutine místo očekávaného výsledku.
   - Různé Runtime<PERSON>arning: `coroutine 'MarketDataService.get_candles' was never awaited`

## Doporučené opravy

Aplikace vyžaduje komplexní opravu asynchronn<PERSON><PERSON>, ale pro okamžitou funkčnost je nejlepší použít pouze dashboard bez obchodní logiky. Proto jsme:

1. Vyt<PERSON><PERSON><PERSON> skript `run_dashboard_only.sh` pro spuštění pouze dashboardu
2. Upravili zástupce na ploše tak, aby spouštěl pouze dashboard

## Další kroky pro plnou opravu

Pro plnou opravu asynchronního zpracování v aplikaci je potřeba:

1. Buď konzistentně používat asynchronní kód s `async/await` ve všech vrstvách aplikace
2. Nebo odstranit `async` modifikátory a používat synchronní kód

### Specifické opravy:

1. **StrategyManager**:
   - Metoda `evaluate_strategy` by měla být `async` a používat `await` pro volání strategie.analyze
   - Všechny strategie by měly mít konzistentní rozhraní (buď všechny `async` nebo žádná)

2. **TradingService**:
   - Metoda `analyze_market` by měla správně používat `await` při volání jiných asynchronních funkcí

3. **MarketDataService**:
   - Metody jako `get_ticker`, `get_candles` a `get_orderbook` by měly být konzistentní - buď všechny synchronní, nebo všechny asynchronní

## Upozornění

Plná oprava by vyžadovala podrobnou analýzu celé aplikace a pravděpodobně by znamenala přepsání značné části kódu. Pro tuto chvíli je doporučeno používat jen dashboard pro monitoring a postupně opravit asynchronní chyby.