"""
Testovací skript pro Bybit API.
"""
import json
from position_monitor import get_positions, get_account_info

def main():
    """
    Hlavní funkce.
    """
    print("=== Test Bybit API ===")
    print("\n=== Testování get_positions() ===")
    positions = get_positions()
    print(f"Počet pozic: {len(positions)}")
    print(json.dumps(positions, indent=2))

    print("\n=== Testování get_account_info() ===")
    account_info = get_account_info()
    print(json.dumps(account_info, indent=2))

if __name__ == "__main__":
    main()
