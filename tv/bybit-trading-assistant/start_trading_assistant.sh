#!/bin/bash

# Activate the virtual environment
source venv/bin/activate

# Check if tmux is installed
if ! command -v tmux &> /dev/null; then
    echo "tmux is not installed. Please install it with: sudo apt-get install tmux"
    exit 1
fi

# Start a new tmux session detached
tmux new-session -d -s trading_assistant

# Split the window horizontally
tmux split-window -h -t trading_assistant

# Run the FastAPI backend in the left pane
tmux send-keys -t trading_assistant:0.0 "cd $(pwd) && source venv/bin/activate && python src/app.py" C-m

# Run the dashboard in the right pane
tmux send-keys -t trading_assistant:0.1 "cd $(pwd) && source venv/bin/activate && streamlit run dashboard.py" C-m

# Attach to the session
tmux attach-session -t trading_assistant

# Note: To detach from the session without stopping it, press Ctrl+b followed by d
# To reattach to a detached session, run: tmux attach-session -t trading_assistant