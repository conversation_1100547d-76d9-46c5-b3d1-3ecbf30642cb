#!/usr/bin/env python3
"""
Skript pro testování obchodních strategií na historických datech.
"""
import os
import json
import logging
import argparse
from datetime import datetime, timedelta

from src.strategies.strategy_manager import StrategyManager
from src.strategies.strategy_tester import StrategyTester
from src.application.services.market_data_service import MarketDataService
from src.config.config import config


# Nastavení loggeru
logging.basicConfig(
    level=getattr(logging, config.logging.level),
    format=config.logging.format,
    handlers=[
        logging.FileHandler(config.logging.file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Hlavní funkce."""
    # Zpracování argumentů
    parser = argparse.ArgumentParser(description="Testování obchodních strategií")
    parser.add_argument("--strategy", type=str, help="Název strategie (pokud nen<PERSON>, testuj<PERSON> se v<PERSON>)")
    parser.add_argument("--symbol", type=str, default="BTCUSDT", help="Symbol pro testování")
    parser.add_argument("--timeframe", type=str, default="1h", help="Časový rámec")
    parser.add_argument("--days", type=int, default=30, help="Počet dní pro testování")
    parser.add_argument("--balance", type=float, default=10000.0, help="Počáteční zůstatek")
    parser.add_argument("--output", type=str, help="Cesta k výstupnímu souboru (JSON)")
    
    args = parser.parse_args()
    
    # Vytvoření instance testeru
    strategy_manager = StrategyManager()
    market_data_service = MarketDataService()
    strategy_tester = StrategyTester(strategy_manager, market_data_service)
    
    # Nastavení časového období
    end_date = datetime.now()
    start_date = end_date - timedelta(days=args.days)
    
    logger.info(f"Spouštění testu pro období {start_date} - {end_date}")
    
    # Testování strategií
    if args.strategy:
        # Testování jedné strategie
        logger.info(f"Testování strategie: {args.strategy}")
        results = strategy_tester.test_strategy(
            strategy_name=args.strategy,
            symbol=args.symbol,
            timeframe=args.timeframe,
            start_date=start_date,
            end_date=end_date,
            initial_balance=args.balance
        )
    else:
        # Testování všech strategií
        logger.info("Testování všech dostupných strategií")
        results = strategy_tester.test_all_strategies(
            symbol=args.symbol,
            timeframe=args.timeframe,
            start_date=start_date,
            end_date=end_date,
            initial_balance=args.balance
        )
    
    # Výpis výsledků
    if isinstance(results, dict) and "error" in results:
        logger.error(f"Chyba při testování: {results['error']}")
        return
    
    # Formátování výsledků
    if args.strategy:
        # Jedna strategie
        print_results(args.strategy, results)
    else:
        # Všechny strategie
        for strategy_name, result in results.items():
            print_results(strategy_name, result)
            print("\n" + "-" * 80 + "\n")
    
    # Uložení výsledků do souboru
    if args.output:
        logger.info(f"Ukládání výsledků do souboru: {args.output}")
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)


def print_results(strategy_name: str, results: dict):
    """
    Vypíše výsledky testu strategie.
    
    Args:
        strategy_name: Název strategie
        results: Výsledky testu
    """
    if "error" in results:
        print(f"Chyba při testování strategie {strategy_name}: {results['error']}")
        return
    
    print(f"Výsledky testu strategie: {strategy_name}")
    print(f"Symbol: {results['symbol']}")
    print(f"Timeframe: {results['timeframe']}")
    print(f"Období: {results['start_date']} - {results['end_date']}")
    print(f"Počáteční zůstatek: ${results['initial_balance']:.2f}")
    print(f"Konečný zůstatek: ${results['final_balance']:.2f}")
    print(f"Zisk/ztráta: ${results['profit_loss']:.2f} ({results['profit_loss_percent']:.2f}%)")
    print(f"Celkem obchodů: {results['total_trades']}")
    
    if results['total_trades'] > 0:
        print(f"Úspěšnost: {results['win_rate']:.2f}%")
        print(f"Průměrný zisk: ${results['average_win']:.2f}")
        print(f"Průměrná ztráta: ${results['average_loss']:.2f}")
        print(f"Profit faktor: {results['profit_factor']:.2f}")
        print(f"Maximální drawdown: {results['max_drawdown']:.2f}%")
    
    # Výpis obchodů
    if 'trades' in results and results['trades']:
        print("\nPoslední obchody:")
        for i, trade in enumerate(results['trades'][-5:], 1):
            print(f"{i}. {trade['side']} - Vstup: ${trade['entry_price']:.2f}, Výstup: ${trade['exit_price']:.2f}, "
                  f"Zisk/Ztráta: ${trade['profit_loss']:.2f}, Důvod: {trade['reason']}")


if __name__ == "__main__":
    main()