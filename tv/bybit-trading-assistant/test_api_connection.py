"""
Testovací skript pro ověření připojení k Bybit API.
"""
import os
import json
from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.config.config import config

def main():
    """
    Hlavní funkce.
    """
    # Získání API klíčů z konfigurace
    api_key = config.bybit.api_key
    api_secret = config.bybit.api_secret
    testnet = config.bybit.testnet
    
    print(f"=== Test připojení k Bybit API ===")
    print(f"API klíč: {api_key[:5]}...{api_key[-5:]}")
    print(f"API tajný klíč: {api_secret[:5]}...{api_secret[-5:]}")
    print(f"Testnet: {testnet}")
    print()
    
    # Inicializace klienta
    client = BybitClient(api_key, api_secret, testnet)
    
    # Test připojení - získání informací o účtu
    print("Získávání informací o účtu...")
    try:
        account_info = client.get_account_info()
        
        print("Připojení k Bybit API je úspěšné!")
        print("\nInformace o účtu:")
        print(json.dumps(account_info, indent=2))
        
    except Exception as e:
        print(f"Chyba při připojení k Bybit API: {e}")
    
    # Test získání tržních dat
    print("\nZískávání tržních dat...")
    try:
        market_data = client.get_market_data("BTCUSDT")
        
        print("Získávání tržních dat je úspěšné!")
        print(f"\nSymbol: {market_data.get('symbol')}")
        print(f"Poslední cena: {market_data.get('lastPrice')}")
        print(f"24h změna: {market_data.get('price24hPcnt')}")
        print(f"24h objem: {market_data.get('volume24h')}")
        
    except Exception as e:
        print(f"Chyba při získávání tržních dat: {e}")
    
    # Test získání svíček
    print("\nZískávání svíček...")
    try:
        klines = client.get_klines("BTCUSDT", "60", 5)  # 60 = 1h
        
        print("Získávání svíček je úspěšné!")
        print(f"\nPočet svíček: {len(klines)}")
        
        for i, kline in enumerate(klines[:3]):
            print(f"\nSvíčka {i+1}:")
            print(f"Čas: {kline[0]}")
            print(f"Open: {kline[1]}")
            print(f"High: {kline[2]}")
            print(f"Low: {kline[3]}")
            print(f"Close: {kline[4]}")
            print(f"Volume: {kline[5]}")
        
    except Exception as e:
        print(f"Chyba při získávání svíček: {e}")


if __name__ == "__main__":
    main()