#!/usr/bin/env python
"""
Testovací skript pro práci s databází.

Tento skript testuje funkčnost databázové vrstvy.
"""
import asyncio
import argparse
import logging
import uuid
from datetime import datetime
import json
import os
import sys

# Přidání projektu do Python cesty
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.infrastructure.persistence.database.database import db
from src.infrastructure.persistence.repository_factory import repository_factory
from src.domain.models.signal import Signal, SignalType, SignalMetadata, SignalSource
from src.domain.models.order import Order, OrderType, OrderSide, OrderStatus
from src.domain.models.position import Position, PositionSide, PositionStatus


def setup_logging():
    """Nastavení logování."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )


async def test_signal_repository():
    """Test repozitáře pro signály."""
    logger = logging.getLogger("test_signal_repository")
    logger.info("Testování repozitáře pro signály")
    
    # Vytvoření repozitáře
    repository = repository_factory.create_signal_repository()
    
    # Vytvoření testovacího signálu
    signal_id = str(uuid.uuid4())
    signal = Signal(
        id=signal_id,
        symbol="BTCUSDT",
        type=SignalType.BUY,
        price=50000.0,
        metadata=SignalMetadata(
            source=SignalSource.SYSTEM,
            timestamp=datetime.now(),
            correlation_id=None,
            raw_data={"test": True}
        ),
        confidence=0.8,
        indicators={
            "rsi": 45.0,
            "macd": 100.0,
            "macd_signal": 90.0
        },
        stop_loss=47500.0,
        take_profit=55000.0
    )
    
    # Uložení signálu
    logger.info(f"Ukládám signál s ID {signal_id}")
    await repository.save(signal)
    
    # Získání signálu podle ID
    saved_signal = await repository.get_by_id(signal_id)
    logger.info(f"Načten signál: {saved_signal.id}, {saved_signal.symbol}, {saved_signal.type}")
    
    # Získání všech signálů
    all_signals = await repository.get_all()
    logger.info(f"Počet všech signálů: {len(all_signals)}")
    
    # Získání signálů pro daný symbol
    symbol_signals = await repository.get_by_symbol("BTCUSDT", limit=5)
    logger.info(f"Počet signálů pro BTCUSDT: {len(symbol_signals)}")
    
    logger.info("Test repozitáře pro signály dokončen")


async def test_order_repository():
    """Test repozitáře pro objednávky."""
    logger = logging.getLogger("test_order_repository")
    logger.info("Testování repozitáře pro objednávky")
    
    # Vytvoření repozitáře
    repository = repository_factory.create_order_repository()
    
    # Vytvoření testovací objednávky
    order_id = str(uuid.uuid4())
    order = Order(
        id=order_id,
        symbol="BTCUSDT",
        side=OrderSide.BUY,
        type=OrderType.MARKET,
        quantity=0.01,
        price=None,
        status=OrderStatus.CREATED,
        created_at=datetime.now(),
        signal_id=None,
        stop_loss=47500.0,
        take_profit=55000.0
    )
    
    # Uložení objednávky
    logger.info(f"Ukládám objednávku s ID {order_id}")
    await repository.save(order)
    
    # Získání objednávky podle ID
    saved_order = await repository.get_by_id(order_id)
    logger.info(f"Načtena objednávka: {saved_order.id}, {saved_order.symbol}, {saved_order.side}")
    
    # Získání všech objednávek
    all_orders = await repository.get_all()
    logger.info(f"Počet všech objednávek: {len(all_orders)}")
    
    # Získání objednávek podle statusu
    status_orders = await repository.get_by_status(OrderStatus.CREATED)
    logger.info(f"Počet objednávek se statusem CREATED: {len(status_orders)}")
    
    logger.info("Test repozitáře pro objednávky dokončen")


async def test_position_repository():
    """Test repozitáře pro pozice."""
    logger = logging.getLogger("test_position_repository")
    logger.info("Testování repozitáře pro pozice")
    
    # Vytvoření repozitáře
    repository = repository_factory.create_position_repository()
    
    # Vytvoření testovací pozice
    position_id = str(uuid.uuid4())
    position = Position(
        id=position_id,
        symbol="BTCUSDT",
        side=PositionSide.LONG,
        entry_price=50000.0,
        quantity=0.01,
        opened_at=datetime.now(),
        status=PositionStatus.OPEN,
        current_price=51000.0,
        unrealized_pnl=10.0,
        stop_loss=47500.0,
        take_profit=55000.0
    )
    
    # Uložení pozice
    logger.info(f"Ukládám pozici s ID {position_id}")
    await repository.save(position)
    
    # Získání pozice podle ID
    saved_position = await repository.get_by_id(position_id)
    logger.info(f"Načtena pozice: {saved_position.id}, {saved_position.symbol}, {saved_position.side}")
    
    # Získání všech pozic
    all_positions = await repository.get_all()
    logger.info(f"Počet všech pozic: {len(all_positions)}")
    
    # Získání aktivních pozic
    active_positions = await repository.get_all_active()
    logger.info(f"Počet aktivních pozic: {len(active_positions)}")
    
    logger.info("Test repozitáře pro pozice dokončen")


async def test_indicator_repository():
    """Test repozitáře pro indikátory."""
    logger = logging.getLogger("test_indicator_repository")
    logger.info("Testování repozitáře pro indikátory")
    
    # Vytvoření repozitáře
    repository = repository_factory.create_indicator_repository()
    
    # Vytvoření testovacích indikátorů
    indicators = {
        "symbol": "BTCUSDT",
        "timestamp": datetime.now(),
        "interval": "1h",
        "price": 50000.0,
        "rsi": 45.0,
        "macd": 100.0,
        "macd_signal": 90.0,
        "bb_upper": 52000.0,
        "bb_middle": 50000.0,
        "bb_lower": 48000.0
    }
    
    # Uložení indikátorů
    logger.info(f"Ukládám indikátory pro {indicators['symbol']}")
    record_id = await repository.save(indicators)
    logger.info(f"Indikátory byly uloženy s ID {record_id}")
    
    # Získání nejnovějších indikátorů
    latest_indicators = await repository.get_latest(indicators["symbol"], indicators["interval"])
    if latest_indicators:
        logger.info(f"Načteny nejnovější indikátory pro {indicators['symbol']}: {latest_indicators['rsi']}, {latest_indicators['macd']}")
    else:
        logger.warning(f"Nepodařilo se načíst nejnovější indikátory pro {indicators['symbol']}")
    
    # Získání historie indikátorů
    history = await repository.get_history(indicators["symbol"], indicators["interval"], 5)
    logger.info(f"Počet historických indikátorů: {len(history)}")
    
    logger.info("Test repozitáře pro indikátory dokončen")


async def run_tests(test_names=None):
    """
    Spustí testy repozitářů.
    
    Args:
        test_names: Seznam názvů testů k spuštění
    """
    # Inicializace databáze
    db.create_all()
    
    # Dostupné testy
    tests = {
        "signal": test_signal_repository,
        "order": test_order_repository,
        "position": test_position_repository,
        "indicator": test_indicator_repository,
        "all": None
    }
    
    # Pokud nejsou specifikovány testy, spustíme všechny
    if not test_names:
        test_names = ["all"]
    
    # Spuštění testů
    if "all" in test_names:
        await test_signal_repository()
        await test_order_repository()
        await test_position_repository()
        await test_indicator_repository()
    else:
        for test_name in test_names:
            if test_name in tests and tests[test_name]:
                await tests[test_name]()


def main():
    """Hlavní funkce."""
    parser = argparse.ArgumentParser(description="Testování databáze")
    parser.add_argument("--tests", nargs="*", choices=["signal", "order", "position", "indicator", "all"],
                      help="Testy k spuštění")
    
    args = parser.parse_args()
    
    setup_logging()
    
    asyncio.run(run_tests(args.tests))


if __name__ == "__main__":
    main()