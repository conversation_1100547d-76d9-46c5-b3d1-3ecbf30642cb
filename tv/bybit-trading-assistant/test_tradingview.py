"""
Testovací skript pro integraci s TradingView.

Tento skript testuje různé způsoby získávání dat z TradingView:
1. Pomocí Python knihovny tradingview-ta
2. Pomocí Node.js knihovny @mathieuc/tradingview
3. Simulaci webhooků

Použití:
python test_tradingview.py --symbol BTCUSDT --interval 1h
"""
import asyncio
import argparse
import json
import logging
from datetime import datetime

from src.infrastructure.external.tradingview.tradingview_api_adapter import TradingViewApiAdapter
from src.infrastructure.external.tradingview.tradingview_node_adapter import TradingViewNodeAdapter
from src.application.services.tradingview_service import TradingViewService
from src.domain.models.signal import SignalType


async def test_tradingview_api(symbol: str, interval: str):
    """
    Testuje integraci s TradingView pomocí Python knihovny.
    
    Args:
        symbol: Symbol (např. BTCUSDT)
        interval: Časový interval (např. 1h)
    """
    print("\n=== Test TradingView API (Python) ===")
    
    adapter = TradingViewApiAdapter()
    
    # Získání analýzy
    print(f"Získávání analýzy pro {symbol} ({interval})...")
    analysis = await adapter.get_analysis(symbol, interval)
    
    print("\nShrnutí:")
    print(f"Doporučení: {analysis['summary']['RECOMMENDATION']}")
    print(f"BUY: {analysis['summary']['BUY']}")
    print(f"NEUTRAL: {analysis['summary']['NEUTRAL']}")
    print(f"SELL: {analysis['summary']['SELL']}")
    
    print("\nIndikátory:")
    for name, value in analysis['indicators'].items():
        print(f"{name}: {value}")
    
    # Získání signálů
    print("\nZískávání signálů...")
    signals = await adapter.get_signals([symbol], interval)
    
    if signals:
        print(f"\nVytvořeno {len(signals)} signálů:")
        for signal in signals:
            print(f"Signál: {signal.type.value} pro {signal.symbol} na ceně {signal.price}")
            print(f"Confidence: {signal.confidence}")
            print(f"Stop-loss: {signal.stop_loss}")
            print(f"Take-profit: {signal.take_profit}")
    else:
        print("Žádné signály nebyly vytvořeny.")


async def test_tradingview_node(symbol: str, interval: str):
    """
    Testuje integraci s TradingView pomocí Node.js knihovny.
    
    Args:
        symbol: Symbol (např. BTCUSDT)
        interval: Časový interval (např. 1h)
    """
    print("\n=== Test TradingView API (Node.js) ===")
    
    adapter = TradingViewNodeAdapter()
    
    # Získání technických indikátorů
    print(f"Získávání technických indikátorů pro {symbol} ({interval})...")
    indicators = await adapter.get_technical_indicators(symbol, interval)
    
    print("\nIndikátory:")
    for name, value in indicators.items():
        print(f"{name}: {value}")
    
    # Získání signálů
    print("\nZískávání signálů...")
    signals = await adapter.get_signals([symbol], interval)
    
    if signals:
        print(f"\nVytvořeno {len(signals)} signálů:")
        for signal in signals:
            print(f"Signál: {signal.type.value} pro {signal.symbol} na ceně {signal.price}")
            print(f"Confidence: {signal.confidence}")
            print(f"Stop-loss: {signal.stop_loss}")
            print(f"Take-profit: {signal.take_profit}")
    else:
        print("Žádné signály nebyly vytvořeny.")


async def test_tradingview_service(symbol: str, interval: str):
    """
    Testuje integraci s TradingView pomocí služby.
    
    Args:
        symbol: Symbol (např. BTCUSDT)
        interval: Časový interval (např. 1h)
    """
    print("\n=== Test TradingView Service ===")
    
    service = TradingViewService()
    service.symbols = [symbol]
    service.interval = interval
    
    # Získání signálů
    print(f"Získávání signálů pro {symbol} ({interval})...")
    signals = await service.get_signals()
    
    if signals:
        print(f"\nVytvořeno {len(signals)} signálů:")
        for signal in signals:
            print(f"Signál: {signal.type.value} pro {signal.symbol} na ceně {signal.price}")
            print(f"Confidence: {signal.confidence}")
            print(f"Stop-loss: {signal.stop_loss}")
            print(f"Take-profit: {signal.take_profit}")
    else:
        print("Žádné signály nebyly vytvořeny.")
    
    # Získání technických indikátorů
    print(f"\nZískávání technických indikátorů pro {symbol} ({interval})...")
    indicators = await service.get_technical_indicators(symbol)
    
    print("\nIndikátory:")
    for name, value in indicators.items():
        print(f"{name}: {value}")


async def simulate_webhook(symbol: str, price: float):
    """
    Simuluje webhook z TradingView.
    
    Args:
        symbol: Symbol (např. BTCUSDT)
        price: Cena
    """
    print("\n=== Simulace Webhooků z TradingView ===")
    
    from src.infrastructure.external.tradingview.tradingview_webhook_adapter import TradingViewWebhookAdapter
    from fastapi import Request
    
    # Vytvoření dat pro webhook
    webhook_data = {
        "symbol": symbol,
        "action": "BUY",
        "price": price,
        "confidence": 0.8,
        "indicators": {
            "rsi": 35.5,
            "macd": 105.0,
            "macd_signal": 95.0
        }
    }
    
    print(f"Simulace webhooků pro {symbol} s daty:")
    print(json.dumps(webhook_data, indent=2))
    
    # Simulace požadavku
    class MockRequest:
        async def json(self):
            return webhook_data
        
        async def body(self):
            return json.dumps(webhook_data).encode()
    
    # Zpracování webhooků
    adapter = TradingViewWebhookAdapter()
    signal = await adapter.parse_webhook(MockRequest())
    
    print("\nVytvořený signál:")
    print(f"ID: {signal.id}")
    print(f"Symbol: {signal.symbol}")
    print(f"Typ: {signal.type.value}")
    print(f"Cena: {signal.price}")
    print(f"Confidence: {signal.confidence}")
    print(f"Stop-loss: {signal.stop_loss}")
    print(f"Take-profit: {signal.take_profit}")
    
    # Získání průvodce nastavením
    guide = adapter.create_webhook_setup_guide()
    
    print("\nPrůvodce nastavením webhooků:")
    print(f"Webhook URL: {guide['webhook_url']}")
    print("\nKroky nastavení:")
    for step in guide['setup_steps']:
        print(step)


async def main():
    """Hlavní funkce."""
    # Nastavení logování
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Zpracování argumentů
    parser = argparse.ArgumentParser(description="Test TradingView integrace")
    parser.add_argument("--symbol", type=str, default="BTCUSDT", help="Symbol (např. BTCUSDT)")
    parser.add_argument("--interval", type=str, default="1h", help="Časový interval (např. 1h)")
    parser.add_argument("--price", type=float, default=50000.0, help="Cena pro simulaci webhooků")
    
    args = parser.parse_args()
    
    print(f"=== Test TradingView Integrace ===")
    print(f"Symbol: {args.symbol}")
    print(f"Interval: {args.interval}")
    print(f"Čas: {datetime.now()}")
    
    # Testování API adaptéru (Python)
    await test_tradingview_api(args.symbol, args.interval)
    
    # Testování API adaptéru (Node.js)
    await test_tradingview_node(args.symbol, args.interval)
    
    # Testování služby
    await test_tradingview_service(args.symbol, args.interval)
    
    # Simulace webhooků
    await simulate_webhook(args.symbol, args.price)
    
    print("\n=== Test dokončen ===")


if __name__ == "__main__":
    asyncio.run(main())