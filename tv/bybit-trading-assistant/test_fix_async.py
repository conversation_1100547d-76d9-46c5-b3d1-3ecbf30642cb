"""
Skript pro opravu async/await chyb v kódu.
"""
import os
import re

def scan_py_files(directory):
    """
    Projde všechny Python soubory v adresáři a hledá potenciální problémy s async/await.
    """
    issues = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                    # Hledání await na objektech, které nejsou awaitable
                    await_dict_pattern = r'await\s+(?:\{|\[|dict\(|list\()'
                    await_dict_matches = re.findall(await_dict_pattern, content)
                    
                    if await_dict_matches:
                        issues.append({
                            'file': file_path,
                            'type': 'await_on_non_awaitable',
                            'matches': await_dict_matches
                        })
    
    return issues

def main():
    src_dir = "src"
    print(f"Hledám potenciální async/await problémy v adresáři {src_dir}...")
    
    issues = scan_py_files(src_dir)
    
    if issues:
        print(f"Nalezeno {len(issues)} souborů s potenciálními problémy:")
        for issue in issues:
            print(f"\nSoubor: {issue['file']}")
            print(f"Typ problému: {issue['type']}")
            print("Nalezené výskyty:")
            for match in issue['matches']:
                print(f"  - {match}")
    else:
        print("Nebyly nalezeny žádné potenciální problémy s async/await.")

if __name__ == "__main__":
    main()