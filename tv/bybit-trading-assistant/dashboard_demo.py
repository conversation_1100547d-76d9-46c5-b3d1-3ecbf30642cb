import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Nastavení stránky
st.set_page_config(
    page_title="Bybit Trading Assistant - Demo",
    page_icon="📈",
    layout="wide"
)

# Simulovaná data pro ukázku
def generate_price_data(days=30, symbol="BTCUSDT"):
    np.random.seed(42)  # Pro konzistentnost
    
    # Výchozí cena
    if symbol == "BTCUSDT":
        base_price = 50000
        volatility = 0.02
    elif symbol == "ETHUSDT":
        base_price = 3000
        volatility = 0.025
    else:
        base_price = 1000
        volatility = 0.03
    
    # Generování časové řady
    dates = [datetime.now() - timedelta(days=i) for i in range(days)]
    dates.reverse()
    
    # Simulace pohybu ceny jako geometrický náhodný pohyb
    returns = np.random.normal(0.001, volatility, days)
    price = [base_price]
    
    for ret in returns:
        price.append(price[-1] * (1 + ret))
    
    price = price[1:]  # Odstranění počáteční ceny
    
    # Generování high, low, open, close, volume
    high = [p * (1 + np.random.uniform(0.001, 0.015)) for p in price]
    low = [p * (1 - np.random.uniform(0.001, 0.015)) for p in price]
    open_price = [l + (h-l) * np.random.random() for h, l in zip(high, low)]
    close = price
    volume = [np.random.uniform(500, 5000) * p for p in price]
    
    # Vytvoření DataFrame
    df = pd.DataFrame({
        'date': dates,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    })
    
    return df

# Výpočet indikátorů
def calculate_indicators(df):
    # SMA
    df['SMA_20'] = df['close'].rolling(window=20).mean()
    df['SMA_50'] = df['close'].rolling(window=50).mean()
    
    # EMA
    df['EMA_12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['EMA_26'] = df['close'].ewm(span=26, adjust=False).mean()
    
    # MACD
    df['MACD'] = df['EMA_12'] - df['EMA_26']
    df['MACD_signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
    df['MACD_hist'] = df['MACD'] - df['MACD_signal']
    
    # RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()
    
    rs = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # Bollinger Bands
    df['BB_middle'] = df['close'].rolling(window=20).mean()
    df['BB_std'] = df['close'].rolling(window=20).std()
    df['BB_upper'] = df['BB_middle'] + 2 * df['BB_std']
    df['BB_lower'] = df['BB_middle'] - 2 * df['BB_std']
    
    return df

# Zobrazení grafu ceny a indikátorů
def plot_price_and_indicators(df, title="BTC/USDT"):
    fig = plt.figure(figsize=(12, 10))
    
    # Nastavení rozložení grafů
    gs = fig.add_gridspec(3, 1, height_ratios=[3, 1, 1])
    
    # Graf ceny
    ax1 = fig.add_subplot(gs[0])
    ax1.plot(df['date'], df['close'], label='Close', color='black')
    ax1.plot(df['date'], df['SMA_20'], label='SMA 20', color='blue', alpha=0.6)
    ax1.plot(df['date'], df['SMA_50'], label='SMA 50', color='red', alpha=0.6)
    ax1.plot(df['date'], df['BB_upper'], label='BB Upper', color='gray', linestyle='--', alpha=0.5)
    ax1.plot(df['date'], df['BB_middle'], label='BB Middle', color='gray', alpha=0.5)
    ax1.plot(df['date'], df['BB_lower'], label='BB Lower', color='gray', linestyle='--', alpha=0.5)
    ax1.fill_between(df['date'], df['BB_upper'], df['BB_lower'], color='gray', alpha=0.1)
    ax1.set_title(f'{title} Price Chart with Indicators')
    ax1.set_ylabel('Price (USDT)')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # MACD
    ax2 = fig.add_subplot(gs[1], sharex=ax1)
    ax2.plot(df['date'], df['MACD'], label='MACD', color='blue')
    ax2.plot(df['date'], df['MACD_signal'], label='Signal', color='red')
    ax2.bar(df['date'], df['MACD_hist'], label='Histogram', color=['green' if x > 0 else 'red' for x in df['MACD_hist']], alpha=0.5)
    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    ax2.set_ylabel('MACD')
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # RSI
    ax3 = fig.add_subplot(gs[2], sharex=ax1)
    ax3.plot(df['date'], df['RSI'], label='RSI', color='purple')
    ax3.axhline(y=70, color='red', linestyle='--', alpha=0.5)
    ax3.axhline(y=30, color='green', linestyle='--', alpha=0.5)
    ax3.fill_between(df['date'], 70, df['RSI'].where(df['RSI'] > 70, 70), color='red', alpha=0.2)
    ax3.fill_between(df['date'], 30, df['RSI'].where(df['RSI'] < 30, 30), color='green', alpha=0.2)
    ax3.set_ylim(0, 100)
    ax3.set_ylabel('RSI')
    ax3.set_xlabel('Date')
    ax3.legend(loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

# Simulované pozice
def generate_positions():
    positions = [
        {
            "id": "pos_1",
            "symbol": "BTCUSDT",
            "side": "LONG",
            "entry_price": 49850.25,
            "quantity": 0.12,
            "current_price": 50750.75,
            "unrealized_pnl": 108.06,
            "status": "OPEN",
            "stop_loss": 48500.00,
            "take_profit": 52000.00,
            "opened_at": (datetime.now() - timedelta(days=2)).isoformat(),
            "value": 6090.09,
            "profit_percentage": 1.81
        },
        {
            "id": "pos_2",
            "symbol": "ETHUSDT",
            "side": "SHORT",
            "entry_price": 3150.80,
            "quantity": 1.5,
            "current_price": 3080.25,
            "unrealized_pnl": 105.83,
            "status": "OPEN",
            "stop_loss": 3250.00,
            "take_profit": 2950.00,
            "opened_at": (datetime.now() - timedelta(hours=18)).isoformat(),
            "value": 4620.38,
            "profit_percentage": 2.24
        }
    ]
    return positions

# Simulované objednávky
def generate_orders():
    orders = [
        {
            "id": "ord_1",
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": 0.05,
            "price": 49500.00,
            "status": "SUBMITTED",
            "created_at": (datetime.now() - timedelta(hours=3)).isoformat()
        },
        {
            "id": "ord_2",
            "symbol": "ETHUSDT",
            "side": "SELL",
            "type": "STOP",
            "quantity": 0.8,
            "price": 3250.00,
            "status": "SUBMITTED",
            "created_at": (datetime.now() - timedelta(hours=6)).isoformat()
        }
    ]
    return orders

# Simulované signály a indikátory
def generate_indicators(symbol="BTCUSDT"):
    if symbol == "BTCUSDT":
        indicators = {
            "Trend Indicators": {
                "EMA (20)": "Bullish",
                "SMA (50)": "Bullish",
                "MACD": "Bullish (Crossover)",
                "Ichimoku Cloud": "Above Cloud"
            },
            "Momentum Indicators": {
                "RSI (14)": "62.5 (Neutral)",
                "Stochastic (14,3,3)": "75.3 (Overbought)",
                "CCI (20)": "124.5 (Overbought)"
            },
            "Volatility Indicators": {
                "Bollinger Bands": "Upper Band Touch",
                "ATR (14)": "High Volatility"
            },
            "Overall Signal": "Buy (Strength: 7/10)"
        }
    elif symbol == "ETHUSDT":
        indicators = {
            "Trend Indicators": {
                "EMA (20)": "Bearish",
                "SMA (50)": "Neutral",
                "MACD": "Bearish (Crossunder)",
                "Ichimoku Cloud": "Below Cloud"
            },
            "Momentum Indicators": {
                "RSI (14)": "36.8 (Neutral)",
                "Stochastic (14,3,3)": "22.7 (Oversold)",
                "CCI (20)": "-112.3 (Oversold)"
            },
            "Volatility Indicators": {
                "Bollinger Bands": "Lower Band Touch",
                "ATR (14)": "Medium Volatility"
            },
            "Overall Signal": "Sell (Strength: 6/10)"
        }
    else:
        indicators = {
            "Trend Indicators": {
                "EMA (20)": "Neutral",
                "SMA (50)": "Neutral",
                "MACD": "Neutral",
                "Ichimoku Cloud": "In Cloud"
            },
            "Momentum Indicators": {
                "RSI (14)": "51.2 (Neutral)",
                "Stochastic (14,3,3)": "48.5 (Neutral)",
                "CCI (20)": "5.3 (Neutral)"
            },
            "Volatility Indicators": {
                "Bollinger Bands": "Middle Band",
                "ATR (14)": "Low Volatility"
            },
            "Overall Signal": "Neutral (Strength: 3/10)"
        }
    
    return indicators

# Hlavní funkce dashboardu
def main():
    st.title("Bybit Trading Assistant - Analýza a Monitoring")
    
    # Sidebar
    st.sidebar.title("Navigace")
    page = st.sidebar.radio("Vyberte stránku", ["Dashboard", "Analýza Trhu", "Otevřené Pozice", "Objednávky"])
    
    st.sidebar.title("Nastavení")
    selected_symbol = st.sidebar.selectbox("Symbol", ["BTCUSDT", "ETHUSDT", "SOLUSDT"])
    
    if page == "Dashboard":
        show_dashboard(selected_symbol)
    elif page == "Analýza Trhu":
        show_market_analysis(selected_symbol)
    elif page == "Otevřené Pozice":
        show_positions()
    elif page == "Objednávky":
        show_orders()

def show_dashboard(symbol):
    st.header("Dashboard Přehled")
    
    # Account summary
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Zůstatek", "$24,685.32")
    
    with col2:
        st.metric("Dostupné", "$15,912.58")
    
    with col3:
        st.metric("Denní P&L", "$324.86", "+1.32%")
    
    with col4:
        st.metric("Využitý Margin", "35.12%")
    
    # Open positions
    st.subheader("Otevřené Pozice")
    positions = generate_positions()
    
    if positions:
        # Convert to DataFrame
        positions_df = pd.DataFrame(positions)
        
        # Select and rename columns
        display_columns = {
            "symbol": "Symbol",
            "side": "Strana",
            "entry_price": "Vstupní Cena",
            "current_price": "Aktuální Cena",
            "quantity": "Množství",
            "profit_percentage": "P&L %",
            "unrealized_pnl": "Nerealizovaný P&L",
            "value": "Hodnota Pozice"
        }
        
        df_display = positions_df[list(display_columns.keys())].copy()
        df_display.columns = list(display_columns.values())
        
        # Format columns
        df_display["Vstupní Cena"] = df_display["Vstupní Cena"].apply(lambda x: f"${x:.2f}")
        df_display["Aktuální Cena"] = df_display["Aktuální Cena"].apply(lambda x: f"${x:.2f}")
        df_display["P&L %"] = df_display["P&L %"].apply(lambda x: f"{x:.2f}%")
        df_display["Nerealizovaný P&L"] = df_display["Nerealizovaný P&L"].apply(lambda x: f"${x:.2f}")
        df_display["Hodnota Pozice"] = df_display["Hodnota Pozice"].apply(lambda x: f"${x:.2f}")
        
        st.dataframe(df_display)
    else:
        st.info("Žádné otevřené pozice")
    
    # Price chart
    st.subheader(f"Cenový Graf - {symbol}")
    df = generate_price_data(30, symbol)
    df = calculate_indicators(df)
    
    fig = plot_price_and_indicators(df, symbol)
    st.pyplot(fig)
    
    # Market indicators
    st.subheader("Technické Indikátory")
    indicators = generate_indicators(symbol)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Trendové Indikátory**")
        for key, value in indicators["Trend Indicators"].items():
            st.write(f"{key}: {value}")
        
        st.write("**Momentové Indikátory**")
        for key, value in indicators["Momentum Indicators"].items():
            st.write(f"{key}: {value}")
    
    with col2:
        st.write("**Indikátory Volatility**")
        for key, value in indicators["Volatility Indicators"].items():
            st.write(f"{key}: {value}")
        
        st.write("**Celkový Signál**")
        signal = indicators["Overall Signal"]
        
        if "Buy" in signal:
            st.success(signal)
        elif "Sell" in signal:
            st.error(signal)
        else:
            st.info(signal)

def show_market_analysis(symbol):
    st.header(f"Analýza Trhu - {symbol}")
    
    # Get data
    df = generate_price_data(60, symbol)
    df = calculate_indicators(df)
    
    # Show current price and basic info
    current_price = df['close'].iloc[-1]
    day_change = ((df['close'].iloc[-1] / df['close'].iloc[-2]) - 1) * 100
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Aktuální Cena", f"${current_price:.2f}", f"{day_change:.2f}%")
    
    with col2:
        volume = df['volume'].iloc[-1]
        volume_change = ((df['volume'].iloc[-1] / df['volume'].iloc[-2]) - 1) * 100
        st.metric("Objem (24h)", f"${volume:.2f}", f"{volume_change:.2f}%")
    
    with col3:
        volatility = df['BB_std'].iloc[-1] / df['close'].iloc[-1] * 100
        st.metric("Volatilita", f"{volatility:.2f}%")
    
    # Technical indicators
    st.subheader("RSI & MACD Analýza")
    
    col1, col2 = st.columns(2)
    
    with col1:
        rsi = df['RSI'].iloc[-1]
        st.metric("RSI (14)", f"{rsi:.2f}")
        
        if rsi > 70:
            st.warning("RSI indikuje přeprodaný trh (>70)")
        elif rsi < 30:
            st.warning("RSI indikuje překoupený trh (<30)")
        else:
            st.info("RSI je v neutrální zóně (30-70)")
    
    with col2:
        macd = df['MACD'].iloc[-1]
        signal = df['MACD_signal'].iloc[-1]
        st.metric("MACD", f"{macd:.2f}", f"Signal: {signal:.2f}")
        
        if macd > signal:
            st.success("MACD je nad signální linií (Bullish)")
        else:
            st.error("MACD je pod signální linií (Bearish)")
    
    # Moving averages
    st.subheader("Klouzavé Průměry")
    
    col1, col2 = st.columns(2)
    
    with col1:
        sma20 = df['SMA_20'].iloc[-1]
        st.metric("SMA 20", f"${sma20:.2f}")
        
        if current_price > sma20:
            st.success("Cena je nad SMA 20 (Bullish)")
        else:
            st.error("Cena je pod SMA 20 (Bearish)")
    
    with col2:
        sma50 = df['SMA_50'].iloc[-1]
        st.metric("SMA 50", f"${sma50:.2f}")
        
        if current_price > sma50:
            st.success("Cena je nad SMA 50 (Bullish)")
        else:
            st.error("Cena je pod SMA 50 (Bearish)")
    
    # Chart
    st.subheader("Cenový Graf s Indikátory")
    fig = plot_price_and_indicators(df, symbol)
    st.pyplot(fig)
    
    # Trading signals
    st.subheader("Obchodní Signály")
    indicators = generate_indicators(symbol)
    
    signal = indicators["Overall Signal"]
    if "Buy" in signal:
        st.success(f"**{signal}**")
        st.write("Doporučení: Zvažte otevření LONG pozice.")
    elif "Sell" in signal:
        st.error(f"**{signal}**")
        st.write("Doporučení: Zvažte otevření SHORT pozice nebo uzavření LONG pozic.")
    else:
        st.info(f"**{signal}**")
        st.write("Doporučení: Vyčkejte na jasnější tržní podmínky.")
    
    # Support and resistance levels
    st.subheader("Úrovně Supportu a Resistance")
    
    support1 = current_price * 0.95
    support2 = current_price * 0.90
    resistance1 = current_price * 1.05
    resistance2 = current_price * 1.10
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Support**")
        st.write(f"Support 1: ${support1:.2f}")
        st.write(f"Support 2: ${support2:.2f}")
    
    with col2:
        st.write("**Resistance**")
        st.write(f"Resistance 1: ${resistance1:.2f}")
        st.write(f"Resistance 2: ${resistance2:.2f}")

def show_positions():
    st.header("Otevřené Pozice")
    
    positions = generate_positions()
    
    if not positions:
        st.info("Žádné otevřené pozice")
        return
    
    for position in positions:
        col1, col2 = st.columns([3, 1])
        
        with col1:
            symbol = position["symbol"]
            side = position["side"]
            entry_price = position["entry_price"]
            current_price = position["current_price"]
            quantity = position["quantity"]
            pnl = position["unrealized_pnl"]
            pnl_pct = position["profit_percentage"]
            
            # Position header
            side_color = "green" if side == "LONG" else "red"
            st.markdown(f"### {symbol} <span style='color:{side_color};'>{side}</span>", unsafe_allow_html=True)
            
            # Position details
            col_a, col_b, col_c, col_d = st.columns(4)
            col_a.metric("Vstupní Cena", f"${entry_price:.2f}")
            col_b.metric("Aktuální Cena", f"${current_price:.2f}")
            col_c.metric("Množství", f"{quantity}")
            col_d.metric("P&L", f"${pnl:.2f}", f"{pnl_pct:.2f}%")
            
            # Risk management
            st.subheader("Risk Management")
            sl_col, tp_col = st.columns(2)
            
            with sl_col:
                stop_loss = position.get("stop_loss")
                if stop_loss:
                    sl_distance = abs(((stop_loss / entry_price) - 1) * 100)
                    st.metric("Stop Loss", f"${stop_loss:.2f}", f"{sl_distance:.2f}% od vstupu")
                else:
                    st.warning("Není nastaven Stop Loss")
            
            with tp_col:
                take_profit = position.get("take_profit")
                if take_profit:
                    tp_distance = abs(((take_profit / entry_price) - 1) * 100)
                    st.metric("Take Profit", f"${take_profit:.2f}", f"{tp_distance:.2f}% od vstupu")
                else:
                    st.warning("Není nastaven Take Profit")
        
        with col2:
            st.subheader("Akce")
            
            # Close position button
            st.button("Uzavřít Pozici", key=f"close_{position['id']}")
            
            # Modify stop loss and take profit
            st.subheader("Upravit")
            
            new_sl = st.number_input(
                "Nový Stop Loss", 
                min_value=0.0, 
                value=float(position.get("stop_loss", 0)), 
                key=f"sl_{position['id']}"
            )
            
            new_tp = st.number_input(
                "Nový Take Profit", 
                min_value=0.0, 
                value=float(position.get("take_profit", 0)), 
                key=f"tp_{position['id']}"
            )
            
            st.button("Aktualizovat", key=f"update_{position['id']}")
        
        st.markdown("---")

def show_orders():
    st.header("Aktivní Objednávky")
    
    orders = generate_orders()
    
    if not orders:
        st.info("Žádné aktivní objednávky")
        return
    
    for order in orders:
        col1, col2 = st.columns([3, 1])
        
        with col1:
            symbol = order["symbol"]
            side = order["side"]
            order_type = order["type"]
            price = order["price"]
            quantity = order["quantity"]
            status = order["status"]
            
            # Order header
            side_color = "green" if side == "BUY" else "red"
            st.markdown(f"### {symbol} <span style='color:{side_color};'>{side}</span> - {order_type}", unsafe_allow_html=True)
            
            # Order details
            col_a, col_b, col_c, col_d = st.columns(4)
            col_a.metric("Cena", f"${price:.2f}")
            col_b.metric("Množství", f"{quantity}")
            col_c.metric("Status", status)
            
            created_at = datetime.fromisoformat(order["created_at"])
            col_d.metric("Vytvořeno", created_at.strftime("%Y-%m-%d %H:%M"))
        
        with col2:
            st.subheader("Akce")
            
            # Cancel order button
            st.button("Zrušit Objednávku", key=f"cancel_{order['id']}")
        
        st.markdown("---")

if __name__ == "__main__":
    main()