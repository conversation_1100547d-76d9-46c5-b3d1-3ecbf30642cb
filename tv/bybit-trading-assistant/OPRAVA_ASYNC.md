# Oprava asynchronního kódu v Bybit Trading Assistant

Pro zajištění plné funkčnosti obchodování je potřeba provést komplexní opravy asynchronního kódu v aplikaci. Zde jsou detailní informace o problémech a potřebných změnách.

## Zjištěné problémy

Při analýze kódu byly identifikovány tyto hlavní problémy:

1. **Nekonzistentní použ<PERSON> async/await**: Některé metody jsou označeny jako `async`, ale volající kód nepoužívá `await`.
2. **Chyby při běhu**: Chybové zprávy jako `object of type 'coroutine' has no len()` a `coroutine was never awaited`.
3. **Neefektivní sekvenční zpracování**: Některé operace jsou prováděny sekvenčně, i když by mohly b<PERSON><PERSON>et paralelně.

## Potřebné opravy

### 1. Úpravy v souboru `trading_service.py`

#### Změna 1: Notifikační služba
```python
# Původní kód:
self.notification_service.create_notification(
    title=f"Nový obchodní signál pro {symbol}",
    message=f"Byl vygenerován nový {signal.type.value} signál s důvěryhodností {signal.confidence:.2f}",
    type=NotificationType.SIGNAL,
    priority=NotificationPriority.MEDIUM,
    data={"signal_id": signal.id, "symbol": symbol, "type": signal.type.value}
)

# Nový kód:
await self.notification_service.create_notification(
    title=f"Nový obchodní signál pro {symbol}",
    message=f"Byl vygenerován nový {signal.type.value} signál s důvěryhodností {signal.confidence:.2f}",
    type=NotificationType.SIGNAL,
    priority=NotificationPriority.MEDIUM,
    data={"signal_id": signal.id, "symbol": symbol, "type": signal.type.value}
)
```

#### Změna 2: Paralelní aktualizace pozic
```python
# Původní kód:
open_positions = [p for p in self.positions if p.status == PositionStatus.OPEN]
for position in open_positions:
    try:
        # Aktualizace pozice...

# Nový kód:
open_positions = [p for p in self.positions if p.status == PositionStatus.OPEN]
update_tasks = [self._update_position(position) for position in open_positions]
await asyncio.gather(*update_tasks)

async def _update_position(self, position):
    try:
        # Získání aktuální ceny
        ticker = await self.market_data_service.get_ticker(position.symbol)
        # zbytek kódu pro aktualizaci pozice...
```

### 2. Úpravy v souboru `strategy_service.py`

#### Změna 3: Asynchronní vyhodnocení strategií
```python
# Původní kód:
strategy_results = self.strategy_manager.evaluate_all_strategies(
    candles=candles,
    ticker=ticker,
    positions=positions or []
)

# Nový kód:
strategy_results = await self.strategy_manager.evaluate_all_strategies(
    candles=candles,
    ticker=ticker,
    positions=positions or []
)
```

### 3. Úpravy v souboru `strategy_manager.py`

#### Změna 4: Paralelní vyhodnocení strategií
```python
# Původní kód:
def evaluate_all_strategies(self, candles: List[Candle], 
                          ticker: Optional[Ticker] = None,
                          positions: Optional[List[Position]] = None) -> Dict[str, Dict[str, Any]]:
    results = {}
    for name in self.strategies:
        results[name] = self.evaluate_strategy(name, candles, ticker, positions)
    return results

# Nový kód:
async def evaluate_all_strategies(self, candles: List[Candle], 
                               ticker: Optional[Ticker] = None,
                               positions: Optional[List[Position]] = None) -> Dict[str, Dict[str, Any]]:
    tasks = {}
    for name in self.strategies:
        tasks[name] = self.evaluate_strategy(name, candles, ticker, positions)
    
    results = {}
    for name, task in tasks.items():
        results[name] = await task
    
    return results
```

#### Změna 5: Asynchronní vyhodnocení strategií
```python
# Původní kód:
def evaluate_strategy(self, name: str, candles: List[Candle], 
                    ticker: Optional[Ticker] = None,
                    positions: Optional[List[Position]] = None) -> Dict[str, Any]:
    # ...
    analysis = strategy.analyze(candles, ticker)
    signal = strategy.generate_signal(analysis, positions)
    # ...

# Nový kód:
async def evaluate_strategy(self, name: str, candles: List[Candle], 
                         ticker: Optional[Ticker] = None,
                         positions: Optional[List[Position]] = None) -> Dict[str, Any]:
    # ...
    analysis = await strategy.analyze(candles, ticker)
    signal = await strategy.generate_signal(analysis, positions)
    # ...
```

### 4. Úpravy v souborech strategií

#### Změna 6: Aktualizace základní strategie v `base_strategy.py`
```python
# Původní kód:
def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
    raise NotImplementedError("Metoda analyze() musí být implementována v odvozené třídě")

def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
    raise NotImplementedError("Metoda generate_signal() musí být implementována v odvozené třídě")

# Nový kód:
async def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
    raise NotImplementedError("Metoda analyze() musí být implementována v odvozené třídě")

async def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
    raise NotImplementedError("Metoda generate_signal() musí být implementována v odvozené třídě")
```

#### Změna 7: Aktualizace všech strategií
Podobné změny by měly být provedeny ve všech strategiích:
- trend_following_strategy.py
- rsi_macd_strategy.py
- breakout_strategy.py
- volume_strategy.py

### 5. Úpravy v souboru `run_trading_strategies.py`

#### Změna 8: Paralelní zpracování symbolů
```python
# Původní kód:
for symbol in symbols:
    try:
        logger.info(f"Analýza trhu pro {symbol}")
        result = await trading_service.analyze_market(symbol)
        # ...
    except Exception as e:
        logger.error(f"Chyba při analýze trhu pro {symbol}: {e}")

# Nový kód:
async def analyze_symbol(trading_service, symbol, logger):
    try:
        logger.info(f"Analýza trhu pro {symbol}")
        result = await trading_service.analyze_market(symbol)
        # ...
    except Exception as e:
        logger.error(f"Chyba při analýze trhu pro {symbol}: {e}")

tasks = [analyze_symbol(trading_service, symbol, logger) for symbol in symbols]
await asyncio.gather(*tasks)
```

## Další doporučení

1. **Doplnění importů**: Ke všem souborům, které používají asyncio, je třeba přidat:
   ```python
   import asyncio
   ```

2. **Tracing a debugging**: Přidat lepší chybové hlášky a diagnostické nástroje pro sledování asynchronního chování.

3. **Testování**: Vytvořit jednotkové testy specificky zaměřené na správnost asynchronního kódu.

4. **Dokumentace**: Jasně zdokumentovat, které metody jsou asynchronní a jak je správně volat.

## Harmonogram implementace

Implementace těchto oprav by měla probíhat v následujícím pořadí:

1. Nejprve aktualizovat základní třídy (BaseStrategy, StrategyManager)
2. Poté aktualizovat všechny strategie
3. Následně aktualizovat služby (MarketDataService, TradingService, StrategyService)
4. Nakonec aktualizovat hlavní smyčku v run_trading_strategies.py

Toto umožní postupné testování a odhalení případných dalších problémů v každé vrstvě.

## Alternativní přístup

Pokud by implementace plně asynchronního kódu byla příliš složitá, je možné zvážit alternativní přístup:
- Odstranit všechny `async/await` a implementovat synchronní verzi
- Místo asyncio použít threading nebo multiprocessing pro paralelismus
- Zaměřit se na optimalizaci kritických částí kódu