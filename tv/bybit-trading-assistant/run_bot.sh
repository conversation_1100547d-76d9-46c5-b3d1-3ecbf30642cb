#!/bin/bash

# Barvy
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# ASCII art zeleného dolaru
echo -e "${GREEN}"
cat /home/<USER>/bybit-trading-assistant/dollar-ascii.txt
echo -e "${NC}"

echo -e "${BLUE}=== Bybit Trading Assistant ===${NC}"
echo -e "${YELLOW}Spouštím obchodního bota a monitoring...${NC}"
cd /home/<USER>/bybit-trading-assistant

# Aktivace virtuálního prostředí
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "CHYBA: Virtuální prostředí nenalezeno!"
    exit 1
fi

# Kontrola, jestli už běží nějaká instance
pgrep -f "python.*run_trading_strategies.py" > /dev/null
if [ $? -eq 0 ]; then
    echo "VAROVÁNÍ: Obchodní bot již běž<PERSON>!"
    echo "Chcete pokračovat pouze s dashboardem? (A/N)"
    read -n 1 odpoved
    if [[ "$odpoved" =~ ^[Aa]$ ]]; then
        echo -e "\nSpouštím pouze dashboard..."
    else
        echo -e "\nUkončuji spouštění."
        exit 0
    fi
else
    # Spustit strategie v pozadí
    echo "Spouštím obchodní strategie..."
    ./run_strategies.sh &
    STRATEGIES_PID=$!

    # Počkat 3 sekundy pro inicializaci
    sleep 3
    echo "Obchodní strategie běží na pozadí (PID: $STRATEGIES_PID)"
fi

# Spustit dashboard
echo "Spouštím dashboard pro monitoring..."
streamlit run dashboard.py

# Při ukončení dashboardu se zeptat, jestli chceme ukončit i strategie
if [ -n "$STRATEGIES_PID" ]; then
    echo "Dashboard byl ukončen. Chcete ukončit i obchodní strategie? (A/N)"
    read -n 1 odpoved
    if [[ "$odpoved" =~ ^[Aa]$ ]]; then
        kill $STRATEGIES_PID
        echo -e "\nObchodní strategie byly ukončeny."
    else
        echo -e "\nObchodní strategie dále běží na pozadí (PID: $STRATEGIES_PID)."
    fi
fi

echo "Aplikace Bybit Trading Assistant byla ukončena."