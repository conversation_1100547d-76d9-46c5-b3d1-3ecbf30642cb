"""
Wrapper pro bybit-api knihovnu.
"""
import os
import json
import logging
import subprocess
from typing import Dict, Any, List, Optional, Union

# Nastavení loggeru
logger = logging.getLogger(__name__)


class BybitApiWrapper:
    """
    Wrapper pro bybit-api knihovnu.
    """

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None, testnet: bool = False):
        """
        Inicializace wrapperu.

        Args:
            api_key: API klíč pro Bybit API
            api_secret: API tajný klíč pro Bybit API
            testnet: Zda používat testnet (vývojové prostředí)
        """
        self.api_key = api_key or os.getenv("BYBIT_API_KEY", "")
        self.api_secret = api_secret or os.getenv("BYBIT_API_SECRET", "")
        self.testnet = testnet

        if not self.api_key or not self.api_secret:
            logger.warning("API klíče nejsou nastaveny. Některé funkce nebudou dostupné.")

        # Cesta k Node.js skriptům
        self.scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "node_scripts")
        
        # Vytvoření adresáře pro Node.js skripty, pokud neexistuje
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Vytvoření základních Node.js skriptů
        self._create_node_scripts()

    def _create_node_scripts(self):
        """
        Vytvoří Node.js skripty pro komunikaci s bybit-api knihovnou.
        """
        # Skript pro získání informací o účtu
        account_info_script = os.path.join(self.scripts_dir, "get_account_info.js")
        with open(account_info_script, "w") as f:
            f.write("""
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Získání informací o účtu
client.getWalletBalance({ accountType: 'UNIFIED' })
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
""")

        # Skript pro získání pozic
        positions_script = os.path.join(self.scripts_dir, "get_positions.js")
        with open(positions_script, "w") as f:
            f.write("""
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';
const category = args[3] || 'spot';
const symbol = args[4] || '';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Získání pozic
const params = { category };
if (symbol) {
  params.symbol = symbol;
}

client.getPositionInfo(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
""")

        # Skript pro získání tržních dat
        market_data_script = os.path.join(self.scripts_dir, "get_market_data.js")
        with open(market_data_script, "w") as f:
            f.write("""
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const category = args[0] || 'spot';
const symbol = args[1] || '';

const client = new RestClientV5();

// Získání tržních dat
const params = { category };
if (symbol) {
  params.symbol = symbol;
}

client.getTickers(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
""")

        # Skript pro vytvoření objednávky
        place_order_script = os.path.join(self.scripts_dir, "place_order.js")
        with open(place_order_script, "w") as f:
            f.write("""
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';
const category = args[3];
const symbol = args[4];
const side = args[5];
const orderType = args[6];
const qty = args[7];
const price = args[8] || '';
const timeInForce = args[9] || 'GTC';
const reduceOnly = args[10] === 'true';
const closeOnTrigger = args[11] === 'true';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Vytvoření objednávky
const params = {
  category,
  symbol,
  side,
  orderType,
  qty,
  timeInForce,
  reduceOnly,
  closeOnTrigger
};

if (price && orderType.toLowerCase() === 'limit') {
  params.price = price;
}

client.submitOrder(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
""")

    def _run_node_script(self, script_name: str, *args) -> Dict[str, Any]:
        """
        Spustí Node.js skript a vrátí výsledek.

        Args:
            script_name: Název skriptu
            *args: Argumenty pro skript

        Returns:
            Dict[str, Any]: Výsledek skriptu
        """
        script_path = os.path.join(self.scripts_dir, script_name)
        
        # Sestavení příkazu
        command = ["node", script_path]
        command.extend(args)
        
        try:
            # Spuštění skriptu
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            
            # Parsování výstupu
            return json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"Chyba při spuštění skriptu {script_name}: {e}")
            try:
                return json.loads(e.stdout)
            except:
                return {"error": str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"Chyba při parsování výstupu skriptu {script_name}: {e}")
            return {"error": "Neplatný JSON výstup"}
        except Exception as e:
            logger.error(f"Neočekávaná chyba při spuštění skriptu {script_name}: {e}")
            return {"error": str(e)}

    def get_account_info(self) -> Dict[str, Any]:
        """
        Získá informace o účtu.

        Returns:
            Dict[str, Any]: Informace o účtu
        """
        return self._run_node_script(
            "get_account_info.js",
            self.api_key,
            self.api_secret,
            str(self.testnet).lower()
        )

    def get_positions(self, category: str = "spot", symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Získá seznam otevřených pozic.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)

        Returns:
            Dict[str, Any]: Seznam otevřených pozic
        """
        args = [
            self.api_key,
            self.api_secret,
            str(self.testnet).lower(),
            category
        ]
        
        if symbol:
            args.append(symbol)
            
        return self._run_node_script("get_positions.js", *args)

    def get_market_data(self, category: str = "spot", symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Získá tržní data.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)

        Returns:
            Dict[str, Any]: Tržní data
        """
        args = [category]
        
        if symbol:
            args.append(symbol)
            
        return self._run_node_script("get_market_data.js", *args)

    def place_order(self, category: str, symbol: str, side: str, order_type: str, 
                   qty: Union[float, str], price: Optional[Union[float, str]] = None, 
                   time_in_force: str = "GTC", reduce_only: bool = False, 
                   close_on_trigger: bool = False) -> Dict[str, Any]:
        """
        Vytvoří novou objednávku.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)
            side: Strana (Buy, Sell)
            order_type: Typ objednávky (Limit, Market)
            qty: Množství
            price: Cena (povinná pro Limit objednávky)
            time_in_force: Časová platnost (GTC, IOC, FOK, PostOnly)
            reduce_only: Pouze snížit pozici
            close_on_trigger: Zavřít při spuštění

        Returns:
            Dict[str, Any]: Výsledek vytvoření objednávky
        """
        args = [
            self.api_key,
            self.api_secret,
            str(self.testnet).lower(),
            category,
            symbol,
            side,
            order_type,
            str(qty)
        ]
        
        if price:
            args.append(str(price))
        else:
            args.append("")
            
        args.append(time_in_force)
        args.append(str(reduce_only).lower())
        args.append(str(close_on_trigger).lower())
        
        return self._run_node_script("place_order.js", *args)
