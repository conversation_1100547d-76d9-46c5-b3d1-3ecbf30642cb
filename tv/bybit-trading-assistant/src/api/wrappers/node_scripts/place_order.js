
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';
const category = args[3];
const symbol = args[4];
const side = args[5];
const orderType = args[6];
const qty = args[7];
const price = args[8] || '';
const timeInForce = args[9] || 'GTC';
const reduceOnly = args[10] === 'true';
const closeOnTrigger = args[11] === 'true';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Vytvoření objedn<PERSON>vky
const params = {
  category,
  symbol,
  side,
  orderType,
  qty,
  timeInForce,
  reduceOnly,
  closeOnTrigger
};

if (price && orderType.toLowerCase() === 'limit') {
  params.price = price;
}

client.submitOrder(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
