
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';
const category = args[3] || 'spot';
const symbol = args[4] || '';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Získání pozic
const params = { category };
if (symbol) {
  params.symbol = symbol;
}

client.getPositionInfo(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
