
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const category = args[0] || 'spot';
const symbol = args[1] || '';

const client = new RestClientV5();

// Získání tržních dat
const params = { category };
if (symbol) {
  params.symbol = symbol;
}

client.getTickers(params)
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
