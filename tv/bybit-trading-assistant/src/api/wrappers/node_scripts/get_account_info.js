
const { RestClientV5 } = require('../../../external/bybit-api');

// Získání argumentů z příkazové řádky
const args = process.argv.slice(2);
const apiKey = args[0];
const apiSecret = args[1];
const testnet = args[2] === 'true';

const client = new RestClientV5({
  key: apiKey,
  secret: apiSecret,
  testnet: testnet
});

// Získání informací o účtu
client.getWalletBalance({ accountType: 'UNIFIED' })
  .then(response => {
    console.log(JSON.stringify(response));
  })
  .catch(error => {
    console.error(JSON.stringify({ error: error.message }));
  });
