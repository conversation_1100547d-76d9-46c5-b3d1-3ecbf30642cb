"""
Bybit API klient pro komunikaci s Bybit API.
"""
import os
import time
import hmac
import hashlib
import json
import logging
from typing import Dict, Any, List, Optional, Union
import requests
import websocket
from dotenv import load_dotenv

# Načtení proměnných prostředí
load_dotenv()

# Nastavení loggeru
logger = logging.getLogger(__name__)


class BybitClient:
    """
    Klient pro komunikaci s Bybit API.
    """

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None, testnet: bool = False):
        """
        Inicializace klienta.

        Args:
            api_key: API klíč pro Bybit API
            api_secret: API tajný klíč pro Bybit API
            testnet: Zda používat testnet (vývojové prostředí)
        """
        self.api_key = api_key or os.getenv("BYBIT_API_KEY", "")
        self.api_secret = api_secret or os.getenv("BYBIT_API_SECRET", "")
        self.testnet = testnet

        if not self.api_key or not self.api_secret:
            logger.warning("API klíče nejsou nastaveny. Některé funkce nebudou dostupné.")

        # Nastavení základních URL
        self.base_url = "https://api-testnet.bybit.com" if testnet else "https://api.bybit.com"
        self.ws_url = "wss://stream-testnet.bybit.com" if testnet else "wss://stream.bybit.com"

    def _generate_signature(self, timestamp: int, recv_window: int) -> str:
        """
        Generuje podpis pro Bybit API.

        Args:
            timestamp: Časové razítko
            recv_window: Okno pro příjem

        Returns:
            str: Podpis
        """
        param_str = f"api_key={self.api_key}&recv_window={recv_window}&timestamp={timestamp}"
        return hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(param_str, "utf-8"),
            digestmod=hashlib.sha256
        ).hexdigest()

    def _get_headers(self) -> Dict[str, str]:
        """
        Získá hlavičky pro HTTP požadavky.

        Returns:
            Dict[str, str]: Hlavičky
        """
        timestamp = int(time.time() * 1000)
        recv_window = 5000
        signature = self._generate_signature(timestamp, recv_window)

        return {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

    def get_server_time(self) -> Dict[str, Any]:
        """
        Získá čas serveru.

        Returns:
            Dict[str, Any]: Odpověď serveru
        """
        url = f"{self.base_url}/v5/market/time"
        response = requests.get(url)
        return response.json()

    def get_account_info(self) -> Dict[str, Any]:
        """
        Získá informace o účtu.

        Returns:
            Dict[str, Any]: Informace o účtu
        """
        url = f"{self.base_url}/v5/account/wallet-balance"
        params = {
            "accountType": "UNIFIED"
        }
        headers = self._get_headers()
        response = requests.get(url, headers=headers, params=params)
        return response.json()

    def get_positions(self, category: str = "spot", symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Získá seznam otevřených pozic.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)

        Returns:
            Dict[str, Any]: Seznam otevřených pozic
        """
        url = f"{self.base_url}/v5/position/list"
        params = {
            "category": category
        }
        if symbol:
            params["symbol"] = symbol

        headers = self._get_headers()
        response = requests.get(url, headers=headers, params=params)
        return response.json()

    def get_market_data(self, category: str = "spot", symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Získá tržní data.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)

        Returns:
            Dict[str, Any]: Tržní data
        """
        url = f"{self.base_url}/v5/market/tickers"
        params = {
            "category": category
        }
        if symbol:
            params["symbol"] = symbol

        response = requests.get(url, params=params)
        return response.json()

    def get_klines(self, category: str, symbol: str, interval: str, limit: int = 200, 
                  start_time: Optional[int] = None, end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        Získá historická data svíček.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)
            interval: Interval (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 12h, 1d, 1w, 1M)
            limit: Počet svíček (max 1000)
            start_time: Počáteční čas v milisekundách
            end_time: Koncový čas v milisekundách

        Returns:
            Dict[str, Any]: Historická data svíček
        """
        url = f"{self.base_url}/v5/market/kline"
        params = {
            "category": category,
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        if start_time:
            params["start"] = start_time
        if end_time:
            params["end"] = end_time

        response = requests.get(url, params=params)
        return response.json()

    def place_order(self, category: str, symbol: str, side: str, order_type: str, 
                   qty: Union[float, str], price: Optional[Union[float, str]] = None, 
                   time_in_force: str = "GTC", reduce_only: bool = False, 
                   close_on_trigger: bool = False) -> Dict[str, Any]:
        """
        Vytvoří nový objednávku.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)
            side: Strana (Buy, Sell)
            order_type: Typ objednávky (Limit, Market)
            qty: Množství
            price: Cena (povinná pro Limit objednávky)
            time_in_force: Časová platnost (GTC, IOC, FOK, PostOnly)
            reduce_only: Pouze snížit pozici
            close_on_trigger: Zavřít při spuštění

        Returns:
            Dict[str, Any]: Výsledek vytvoření objednávky
        """
        url = f"{self.base_url}/v5/order/create"
        headers = self._get_headers()
        
        data = {
            "category": category,
            "symbol": symbol,
            "side": side,
            "orderType": order_type,
            "qty": str(qty),
            "timeInForce": time_in_force,
            "reduceOnly": reduce_only,
            "closeOnTrigger": close_on_trigger
        }
        
        if price and order_type.lower() == "limit":
            data["price"] = str(price)
            
        response = requests.post(url, headers=headers, json=data)
        return response.json()

    def cancel_order(self, category: str, symbol: str, order_id: Optional[str] = None, 
                    order_link_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Zruší objednávku.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)
            order_id: ID objednávky
            order_link_id: ID propojení objednávky

        Returns:
            Dict[str, Any]: Výsledek zrušení objednávky
        """
        url = f"{self.base_url}/v5/order/cancel"
        headers = self._get_headers()
        
        data = {
            "category": category,
            "symbol": symbol
        }
        
        if order_id:
            data["orderId"] = order_id
        elif order_link_id:
            data["orderLinkId"] = order_link_id
        else:
            raise ValueError("Musí být zadáno buď order_id nebo order_link_id")
            
        response = requests.post(url, headers=headers, json=data)
        return response.json()

    def get_order_history(self, category: str, symbol: Optional[str] = None, 
                         limit: int = 50) -> Dict[str, Any]:
        """
        Získá historii objednávek.

        Args:
            category: Kategorie (spot, linear, inverse)
            symbol: Symbol (např. BTCUSDT)
            limit: Počet záznamů (max 50)

        Returns:
            Dict[str, Any]: Historie objednávek
        """
        url = f"{self.base_url}/v5/order/history"
        headers = self._get_headers()
        
        params = {
            "category": category,
            "limit": limit
        }
        
        if symbol:
            params["symbol"] = symbol
            
        response = requests.get(url, headers=headers, params=params)
        return response.json()

    def subscribe_to_websocket(self, topics: List[str], callback):
        """
        Přihlásí se k odběru dat přes WebSocket.

        Args:
            topics: Seznam témat k odběru
            callback: Funkce pro zpracování zpráv
        """
        def on_message(ws, message):
            data = json.loads(message)
            callback(data)

        def on_error(ws, error):
            logger.error(f"WebSocket chyba: {error}")

        def on_close(ws, close_status_code, close_msg):
            logger.info(f"WebSocket spojení uzavřeno: {close_status_code} - {close_msg}")

        def on_open(ws):
            logger.info("WebSocket spojení otevřeno")
            # Přihlášení k odběru témat
            for topic in topics:
                ws.send(json.dumps({
                    "op": "subscribe",
                    "args": [topic]
                }))

        # Vytvoření WebSocket spojení
        ws_app = websocket.WebSocketApp(
            f"{self.ws_url}/v5/public",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        # Spuštění WebSocket spojení v novém vlákně
        import threading
        wst = threading.Thread(target=ws_app.run_forever)
        wst.daemon = True
        wst.start()
        
        return ws_app
