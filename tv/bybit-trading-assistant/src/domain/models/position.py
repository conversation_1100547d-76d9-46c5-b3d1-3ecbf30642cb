"""
Model pro pozice.
"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional


class PositionSide(str, Enum):
    """Strana pozice."""
    LONG = "LONG"
    SHORT = "SHORT"


class PositionStatus(str, Enum):
    """Stav pozice."""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PENDING = "PENDING"
    CANCELLED = "CANCELLED"


@dataclass
class Position:
    """Model pro pozici."""
    id: str
    symbol: str
    side: PositionSide
    entry_price: float
    quantity: float
    opened_at: datetime
    status: PositionStatus = PositionStatus.OPEN
    current_price: Optional[float] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    closed_at: Optional[datetime] = None
    exit_price: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def value(self) -> float:
        """
        Vypočítá hodnotu pozice.
        
        Returns:
            float: Hodnota pozice
        """
        price = self.current_price if self.current_price is not None else self.entry_price
        return price * self.quantity
    
    @property
    def profit_percentage(self) -> float:
        """
        Vypočítá procentuální zisk/ztrátu.
        
        Returns:
            float: Procentuální zisk/ztráta
        """
        if self.entry_price == 0:
            return 0.0
        
        if self.current_price is None:
            return 0.0
        
        if self.side == PositionSide.LONG:
            return ((self.current_price - self.entry_price) / self.entry_price) * 100
        else:
            return ((self.entry_price - self.current_price) / self.entry_price) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Převede pozici na slovník.
        
        Returns:
            Dict[str, Any]: Slovník s daty pozice
        """
        return {
            "id": self.id,
            "symbol": self.symbol,
            "side": self.side.value,
            "entry_price": self.entry_price,
            "quantity": self.quantity,
            "current_price": self.current_price,
            "unrealized_pnl": self.unrealized_pnl,
            "realized_pnl": self.realized_pnl,
            "status": self.status.value,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "opened_at": self.opened_at.isoformat(),
            "closed_at": self.closed_at.isoformat() if self.closed_at else None,
            "exit_price": self.exit_price,
            "value": self.value,
            "profit_percentage": self.profit_percentage,
            "metadata": self.metadata
        }