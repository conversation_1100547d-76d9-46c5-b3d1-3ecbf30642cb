"""
Model pro objednávky.
"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List


class OrderType(str, Enum):
    """Typ objednávky."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderSide(str, Enum):
    """Strana objednávky."""
    BUY = "BUY"
    SELL = "SELL"


class OrderStatus(str, Enum):
    """Status objednávky."""
    CREATED = "CREATED"
    SUBMITTED = "SUBMITTED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELED = "CANCELED"
    REJECTED = "REJECTED"


@dataclass
class Order:
    """Model pro objednávku."""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float]
    status: OrderStatus
    created_at: datetime
    signal_id: Optional[str] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    external_id: Optional[str] = None
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_active(self) -> bool:
        """
        Ověří, zda je objednávka aktivní.
        
        Returns:
            bool: True, pokud je objednávka aktivní, jinak False
        """
        return self.status in [OrderStatus.CREATED, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
    
    def is_completed(self) -> bool:
        """
        Ověří, zda je objednávka dokončena.
        
        Returns:
            bool: True, pokud je objednávka dokončena, jinak False
        """
        return self.status in [OrderStatus.FILLED, OrderStatus.CANCELED, OrderStatus.REJECTED]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Převede objednávku na slovník.
        
        Returns:
            Dict[str, Any]: Slovník s daty objednávky
        """
        return {
            "id": self.id,
            "symbol": self.symbol,
            "side": self.side.value,
            "type": self.type.value,
            "quantity": self.quantity,
            "price": self.price,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "signal_id": self.signal_id,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "external_id": self.external_id,
            "filled_at": self.filled_at.isoformat() if self.filled_at else None,
            "filled_price": self.filled_price,
            "filled_quantity": self.filled_quantity,
            "metadata": self.metadata
        }
