"""
Model pro signály.
"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional


class SignalType(str, Enum):
    """Typ signálu."""
    BUY = "BUY"
    SELL = "SELL"
    NEUTRAL = "NEUTRAL"


class SignalSource(str, Enum):
    """Zdroj signálu."""
    TRADINGVIEW = "TRADINGVIEW"
    OPENAI = "OPENAI"
    SYSTEM = "SYSTEM"
    USER = "USER"


@dataclass
class SignalMetadata:
    """Metadata signálu."""
    source: SignalSource
    timestamp: datetime
    correlation_id: Optional[str] = None
    raw_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Signal:
    """Model pro signál."""
    id: str
    symbol: str
    type: SignalType
    price: float
    metadata: SignalMetadata
    confidence: float = 0.0
    indicators: Dict[str, float] = field(default_factory=dict)
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Převede signál na slovník.
        
        Returns:
            Dict[str, Any]: Slovník s daty signálu
        """
        return {
            "id": self.id,
            "symbol": self.symbol,
            "type": self.type.value,
            "price": self.price,
            "confidence": self.confidence,
            "indicators": self.indicators,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "metadata": {
                "source": self.metadata.source.value,
                "timestamp": self.metadata.timestamp.isoformat(),
                "correlation_id": self.metadata.correlation_id,
                "raw_data": self.metadata.raw_data
            }
        }
