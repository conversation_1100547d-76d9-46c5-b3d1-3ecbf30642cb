"""
Hlavní aplikační soubor pro Bybit Trading Assistant.
"""
import asyncio
import logging
import os
from typing import Dict, Any, List
from datetime import datetime

import uvicorn
from fastapi import FastAPI, Request, Depends, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from src.config.config import config
from src.infrastructure.external.bybit.bybit_api_adapter import BybitApiAdapter
from src.infrastructure.external.openai.openai_api_adapter import OpenAIApiAdapter
from src.infrastructure.external.tradingview.tradingview_webhook_adapter import TradingViewWebhookAdapter
from src.domain.services.risk_manager import RiskManager
from src.domain.services.signal_analyzer import SignalAnalyzer
from src.application.services.trading_service import TradingService
from src.application.services.tradingview_service import TradingViewService
from src.application.services.notification_service import NotificationService, NotificationType, NotificationPriority
from src.infrastructure.persistence.memory.memory_repositories import (
    MemorySignalRepository,
    MemoryOrderRepository,
    MemoryPositionRepository,
    MemoryAnalysisRepository
)


# Nastavení logování
def setup_logging():
    """Nastavení logování."""
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "trading_assistant.log")
    
    logging.basicConfig(
        level=getattr(logging, config.logging.level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file) if config.logging.log_to_file else logging.NullHandler()
        ]
    )


# Inicializace aplikace
app = FastAPI(
    title="Bybit Trading Assistant",
    description="Obchodní asistent pro Bybit s využitím TradingView a OpenAI",
    version="1.0.0"
)

# Povolení CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inicializace repozitářů
signal_repository = MemorySignalRepository()
order_repository = MemoryOrderRepository()
position_repository = MemoryPositionRepository()
analysis_repository = MemoryAnalysisRepository()

# Inicializace služeb
exchange_service = BybitApiAdapter()
ai_service = OpenAIApiAdapter()
risk_manager = RiskManager()
signal_analyzer = SignalAnalyzer()
tradingview_webhook_adapter = TradingViewWebhookAdapter()
tradingview_service = TradingViewService()
notification_service = NotificationService(max_notifications=200)

# Inicializace obchodní služby
trading_service = TradingService(
    order_repository=order_repository,
    position_repository=position_repository,
    signal_repository=signal_repository,
    exchange_service=exchange_service,
    ai_service=ai_service,
    risk_manager=risk_manager
)


# Middleware pro logování požadavků
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware pro logování požadavků."""
    start_time = datetime.now()
    response = await call_next(request)
    process_time = (datetime.now() - start_time).total_seconds() * 1000
    logging.info(f"Požadavek: {request.method} {request.url.path} - Doba zpracování: {process_time:.2f}ms")
    return response


# Závislost pro získání TradingView webhook adaptéru
def get_tradingview_webhook_adapter():
    """Závislost pro získání TradingView webhook adaptéru."""
    return tradingview_webhook_adapter


# Závislost pro získání obchodní služby
def get_trading_service():
    """Závislost pro získání obchodní služby."""
    return trading_service


# Závislost pro získání notifikační služby
def get_notification_service():
    """Závislost pro získání notifikační služby."""
    return notification_service


# Endpoint pro TradingView webhook
@app.post("/webhook")
async def webhook(
    request: Request,
    tradingview_webhook_adapter=Depends(get_tradingview_webhook_adapter),
    trading_service=Depends(get_trading_service)
):
    """
    Endpoint pro TradingView webhook.
    
    Args:
        request: HTTP požadavek
        tradingview_webhook_adapter: TradingView webhook adaptér
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Odpověď
    """
    try:
        # Parsování webhooků
        signal = await tradingview_webhook_adapter.parse_webhook(request)
        
        # Zpracování signálu
        order = await trading_service.process_signal(signal)
        
        if order:
            return {
                "status": "success",
                "message": f"Signál byl úspěšně zpracován a vytvořena objednávka {order.id}",
                "signal_id": signal.id,
                "order_id": order.id
            }
        else:
            return {
                "status": "warning",
                "message": "Signál byl úspěšně zpracován, ale nebyla vytvořena žádná objednávka",
                "signal_id": signal.id
            }
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code,
            content={"status": "error", "message": e.detail}
        )
    except Exception as e:
        logging.error(f"Chyba při zpracování webhooků: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání stavu účtu
@app.get("/account")
async def get_account(trading_service=Depends(get_trading_service)):
    """
    Endpoint pro získání stavu účtu.
    
    Args:
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Stav účtu
    """
    try:
        account_summary = await trading_service.get_account_summary()
        return account_summary
    except Exception as e:
        logging.error(f"Chyba při získávání stavu účtu: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání otevřených pozic
@app.get("/positions")
async def get_positions(trading_service=Depends(get_trading_service)):
    """
    Endpoint pro získání otevřených pozic.
    
    Args:
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Otevřené pozice
    """
    try:
        positions = await position_repository.get_all_active()
        return {
            "status": "success",
            "positions": [position.to_dict() for position in positions]
        }
    except Exception as e:
        logging.error(f"Chyba při získávání otevřených pozic: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání otevřených objednávek
@app.get("/orders")
async def get_orders(trading_service=Depends(get_trading_service)):
    """
    Endpoint pro získání otevřených objednávek.
    
    Args:
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Otevřené objednávky
    """
    try:
        orders = await order_repository.get_by_status("SUBMITTED")
        return {
            "status": "success",
            "orders": [order.to_dict() for order in orders]
        }
    except Exception as e:
        logging.error(f"Chyba při získávání otevřených objednávek: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro zrušení objednávky
@app.delete("/orders/{order_id}")
async def cancel_order(order_id: str, trading_service=Depends(get_trading_service)):
    """
    Endpoint pro zrušení objednávky.
    
    Args:
        order_id: ID objednávky
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Odpověď
    """
    try:
        success = await trading_service.cancel_order(order_id)
        if success:
            return {
                "status": "success",
                "message": f"Objednávka {order_id} byla úspěšně zrušena"
            }
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "message": f"Objednávka {order_id} nemohla být zrušena"
                }
            )
    except Exception as e:
        logging.error(f"Chyba při rušení objednávky {order_id}: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro uzavření pozice
@app.delete("/positions/{position_id}")
async def close_position(position_id: str, trading_service=Depends(get_trading_service)):
    """
    Endpoint pro uzavření pozice.
    
    Args:
        position_id: ID pozice
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Odpověď
    """
    try:
        position = await position_repository.get_by_id(position_id)
        if not position:
            return JSONResponse(
                status_code=404,
                content={
                    "status": "error",
                    "message": f"Pozice {position_id} nebyla nalezena"
                }
            )
        
        order = await trading_service._close_position(position)
        return {
            "status": "success",
            "message": f"Pozice {position_id} byla úspěšně uzavřena",
            "order_id": order.id
        }
    except Exception as e:
        logging.error(f"Chyba při uzavírání pozice {position_id}: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání tržních dat
@app.get("/market-data/{symbol}")
async def get_market_data(symbol: str, trading_service=Depends(get_trading_service)):
    """
    Endpoint pro získání tržních dat.
    
    Args:
        symbol: Symbol
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    try:
        market_data = await exchange_service.get_market_data(symbol)
        return {
            "status": "success",
            "market_data": market_data
        }
    except Exception as e:
        logging.error(f"Chyba při získávání tržních dat pro {symbol}: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání analýzy trhu
@app.get("/market-analysis/{symbol}")
async def get_market_analysis(symbol: str, trading_service=Depends(get_trading_service)):
    """
    Endpoint pro získání analýzy trhu.
    
    Args:
        symbol: Symbol
        trading_service: Obchodní služba
        
    Returns:
        Dict[str, Any]: Analýza trhu
    """
    try:
        market_data = await exchange_service.get_market_data(symbol)
        analysis = await ai_service.evaluate_market_conditions(symbol, market_data)
        return {
            "status": "success",
            "analysis": analysis
        }
    except Exception as e:
        logging.error(f"Chyba při získávání analýzy trhu pro {symbol}: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání technických indikátorů z TradingView
@app.get("/tradingview/indicators/{symbol}")
async def get_tradingview_indicators(symbol: str):
    """
    Endpoint pro získání technických indikátorů z TradingView.
    
    Args:
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Technické indikátory
    """
    try:
        indicators = await tradingview_service.get_technical_indicators(symbol)
        return {
            "status": "success",
            "indicators": indicators
        }
    except Exception as e:
        logging.error(f"Chyba při získávání indikátorů z TradingView pro {symbol}: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro získání signálů z TradingView
@app.get("/tradingview/signals")
async def get_tradingview_signals():
    """
    Endpoint pro získání signálů z TradingView.
    
    Returns:
        Dict[str, Any]: Signály
    """
    try:
        signals = await tradingview_service.get_signals()
        return {
            "status": "success",
            "signals": [signal.to_dict() for signal in signals]
        }
    except Exception as e:
        logging.error(f"Chyba při získávání signálů z TradingView: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro zdravotní kontrolu
@app.get("/health")
async def health_check():
    """
    Endpoint pro zdravotní kontrolu.
    
    Returns:
        Dict[str, Any]: Stav aplikace
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }


# Endpoint pro získání notifikací
@app.get("/notifications")
async def get_notifications(
    limit: int = 20,
    notification_type: str = None,
    min_priority: str = None,
    notification_service=Depends(get_notification_service)
):
    """
    Endpoint pro získání notifikací.
    
    Args:
        limit: Maximální počet notifikací
        notification_type: Filtr podle typu notifikace
        min_priority: Minimální priorita notifikací
        notification_service: Notifikační služba
        
    Returns:
        Dict[str, Any]: Seznam notifikací
    """
    try:
        # Převod řetězcových hodnot na enum
        type_filter = NotificationType(notification_type.upper()) if notification_type else None
        priority_filter = NotificationPriority(min_priority.upper()) if min_priority else None
        
        notifications = notification_service.get_notifications(
            limit=limit,
            type_filter=type_filter,
            min_priority=priority_filter
        )
        
        return {
            "status": "success",
            "notifications": [notification.to_dict() for notification in notifications]
        }
    except Exception as e:
        logging.error(f"Chyba při získávání notifikací: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro vytvoření notifikace
@app.post("/notifications")
async def create_notification(
    request: Request,
    notification_service=Depends(get_notification_service)
):
    """
    Endpoint pro vytvoření notifikace.
    
    Args:
        request: HTTP požadavek
        notification_service: Notifikační služba
        
    Returns:
        Dict[str, Any]: Vytvořená notifikace
    """
    try:
        data = await request.json()
        
        title = data.get("title")
        message = data.get("message")
        notification_type = data.get("type", "INFO")
        priority = data.get("priority", "MEDIUM")
        extra_data = data.get("data", {})
        
        if not title or not message:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Chybí povinné parametry 'title' a 'message'"}
            )
        
        # Převod řetězcových hodnot na enum
        type_enum = NotificationType(notification_type.upper())
        priority_enum = NotificationPriority(priority.upper())
        
        notification = notification_service.create_notification(
            title=title,
            message=message,
            type=type_enum,
            priority=priority_enum,
            data=extra_data
        )
        
        return {
            "status": "success",
            "notification": notification.to_dict()
        }
    except Exception as e:
        logging.error(f"Chyba při vytváření notifikace: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Endpoint pro smazání notifikací
@app.delete("/notifications")
async def clear_notifications(
    notification_service=Depends(get_notification_service)
):
    """
    Endpoint pro smazání všech notifikací.
    
    Args:
        notification_service: Notifikační služba
        
    Returns:
        Dict[str, Any]: Výsledek operace
    """
    try:
        notification_service.clear_notifications()
        return {
            "status": "success",
            "message": "Všechny notifikace byly smazány"
        }
    except Exception as e:
        logging.error(f"Chyba při mazání notifikací: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# Úloha pro aktualizaci pozic
async def update_positions_task():
    """Úloha pro aktualizaci pozic."""
    while True:
        try:
            await trading_service.update_positions()
        except Exception as e:
            logging.error(f"Chyba při aktualizaci pozic: {e}")
        
        # Čekání 60 sekund
        await asyncio.sleep(60)


# Úloha pro monitorování signálů z TradingView
async def tradingview_signals_task():
    """Úloha pro monitorování signálů z TradingView."""
    async def process_signals(signals: List):
        """Zpracování signálů."""
        for signal in signals:
            try:
                await trading_service.process_signal(signal)
            except Exception as e:
                logging.error(f"Chyba při zpracování signálu {signal.id}: {e}")
    
    await tradingview_service.start_signal_monitoring(process_signals)


# Spuštění aplikace
if __name__ == "__main__":
    # Nastavení logování
    setup_logging()
    
    # Spuštění úloh
    loop = asyncio.get_event_loop()
    loop.create_task(update_positions_task())
    loop.create_task(tradingview_signals_task())
    
    # Spuštění aplikace
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=config.tradingview.webhook_port,
        reload=config.debug
    )
