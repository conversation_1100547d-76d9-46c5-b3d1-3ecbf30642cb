"""
Adaptér pro TradingView API.
"""
from typing import Dict, Any, List, Optional
import uuid
import logging
from datetime import datetime
from tradingview_ta import TA_Handler, Interval, Exchange

from src.domain.models.signal import Signal, SignalType, SignalSource, SignalMetadata


class TradingViewApiAdapter:
    """Adaptér pro TradingView API."""
    
    def __init__(self):
        """Inicializace adaptéru."""
        self.logger = logging.getLogger(__name__)
        
        # Mapování TradingView intervalů
        self.interval_map = {
            "1m": Interval.INTERVAL_1_MINUTE,
            "5m": Interval.INTERVAL_5_MINUTES,
            "15m": Interval.INTERVAL_15_MINUTES,
            "30m": Interval.INTERVAL_30_MINUTES,
            "1h": Interval.INTERVAL_1_HOUR,
            "2h": Interval.INTERVAL_2_HOURS,
            "4h": Interval.INTERVAL_4_HOURS,
            "1d": Interval.INTERVAL_1_DAY,
            "1W": Interval.INTERVAL_1_WEEK,
            "1M": Interval.INTERVAL_1_MONTH
        }
        
        # Výchozí hodnoty
        self.default_exchange = "BINANCE"
        self.default_screener = "crypto"
    
    async def get_analysis(self, symbol: str, interval: str) -> Dict[str, Any]:
        """
        Získá analýzu z TradingView.
        
        Args:
            symbol: Symbol
            interval: Časový interval
            
        Returns:
            Dict[str, Any]: Analýza
        """
        try:
            # Převedení intervalu
            tv_interval = self.interval_map.get(interval, Interval.INTERVAL_1_HOUR)
            
            # Vytvoření handleru
            handler = TA_Handler(
                symbol=symbol,
                exchange=self.default_exchange,
                screener=self.default_screener,
                interval=tv_interval,
                timeout=10
            )
            
            # Získání analýzy
            analysis = handler.get_analysis()
            
            # Převedení do našeho formátu
            result = {
                "summary": {
                    "RECOMMENDATION": analysis.summary["RECOMMENDATION"],
                    "BUY": analysis.summary["BUY"],
                    "SELL": analysis.summary["SELL"],
                    "NEUTRAL": analysis.summary["NEUTRAL"]
                },
                "indicators": {}
            }
            
            # Převedení indikátorů
            for name, value in analysis.indicators.items():
                if name == "RSI":
                    result["indicators"]["RSI"] = value
                elif name == "RSI[1]":
                    result["indicators"]["RSI_1"] = value
                elif name == "MACD.macd":
                    result["indicators"]["MACD.macd"] = value
                elif name == "MACD.signal":
                    result["indicators"]["MACD.signal"] = value
                elif name == "BB.upper":
                    result["indicators"]["BB.upper"] = value
                elif name == "BB.lower":
                    result["indicators"]["BB.lower"] = value
                elif name == "BB.middle":
                    result["indicators"]["BB.middle"] = value
                else:
                    result["indicators"][name] = value
            
            self.logger.info(f"Získána analýza pro {symbol} ({interval})")
            return result
        except Exception as e:
            self.logger.error(f"Chyba při získávání analýzy pro {symbol} ({interval}): {e}")
            
            # Vrátíme simulovaná data v případě chyby
            self.logger.warning(f"Použití simulovaných dat pro {symbol}")
            return {
                "summary": {
                    "RECOMMENDATION": "BUY",
                    "BUY": 12,
                    "SELL": 4,
                    "NEUTRAL": 10
                },
                "indicators": {
                    "RSI": 55.0,
                    "MACD.macd": 100.0,
                    "MACD.signal": 90.0,
                    "BB.upper": 52000.0,
                    "BB.lower": 48000.0,
                    "BB.middle": 50000.0
                }
            }
    
    async def get_signals(self, symbols: List[str], interval: str) -> List[Signal]:
        """
        Získá signály z TradingView.
        
        Args:
            symbols: Seznam symbolů
            interval: Časový interval
            
        Returns:
            List[Signal]: Seznam signálů
        """
        signals = []
        
        for symbol in symbols:
            try:
                # Získání analýzy
                analysis = await self.get_analysis(symbol, interval)
                
                # Získání aktuální ceny (pokud je dostupná v indikátorech)
                price = analysis["indicators"].get("close", 0.0)
                if not price or price == 0.0:
                    # Simulace ceny, pokud není dostupná
                    price = 50000.0 if "BTC" in symbol else 2000.0 if "ETH" in symbol else 100.0
                
                # Vytvoření signálu na základě analýzy
                recommendation = analysis["summary"]["RECOMMENDATION"]
                if recommendation in ["STRONG_BUY", "BUY"]:
                    signal_type = SignalType.BUY
                    confidence = 0.8 if recommendation == "STRONG_BUY" else 0.6
                elif recommendation in ["STRONG_SELL", "SELL"]:
                    signal_type = SignalType.SELL
                    confidence = 0.8 if recommendation == "STRONG_SELL" else 0.6
                else:
                    # Neutrální doporučení - žádný signál
                    continue
                
                # Výpočet stop-loss a take-profit
                if signal_type == SignalType.BUY:
                    stop_loss = price * 0.95  # 5% pod cenou
                    take_profit = price * 1.1  # 10% nad cenou
                else:  # SELL
                    stop_loss = price * 1.05  # 5% nad cenou
                    take_profit = price * 0.9  # 10% pod cenou
                
                # Vytvoření metadat
                metadata = SignalMetadata(
                    source=SignalSource.TRADINGVIEW,
                    timestamp=datetime.now(),
                    correlation_id=str(uuid.uuid4()),
                    raw_data=analysis
                )
                
                # Vytvoření signálu
                signal = Signal(
                    id=str(uuid.uuid4()),
                    symbol=symbol,
                    type=signal_type,
                    price=price,
                    metadata=metadata,
                    confidence=confidence,
                    indicators=analysis["indicators"],
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                
                signals.append(signal)
                self.logger.info(f"Vytvořen signál pro {symbol}: {signal_type.value}")
            except Exception as e:
                self.logger.error(f"Chyba při vytváření signálu pro {symbol}: {e}")
        
        return signals