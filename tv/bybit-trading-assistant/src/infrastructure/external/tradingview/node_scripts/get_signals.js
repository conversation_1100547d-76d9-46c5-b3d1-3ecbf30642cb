/**
 * Skript pro získání obchodních signálů z TradingView
 * 
 * Použití:
 * node get_signals.js <symbols> <interval>
 * 
 * Příklad:
 * node get_signals.js BTCUSDT,ETHUSDT,SOLUSDT 1h
 */

const TradingView = require('@mathieuc/tradingview');

// Získání parametrů z příkazové řádky
const symbolsStr = process.argv[2];
const interval = process.argv[3] || '1h';

// Kontrola vstupních parametrů
if (!symbolsStr) {
  console.error('Chybí parametr symbols');
  process.exit(1);
}

// Rozdělení symbolů
const symbols = symbolsStr.split(',');

// Mapování intervalů pro TradingView
const intervalMap = {
  '1m': '1',
  '5m': '5',
  '15m': '15',
  '30m': '30',
  '1h': '60',
  '2h': '120',
  '4h': '240',
  '1d': 'D',
  '1W': 'W',
  '1M': 'M'
};

// Funkce pro získání správného formátu symbolu
function getMarket(symbol) {
  // Defaultně předpokládáme BINANCE
  return `BINANCE:${symbol}`;
}

// Funkce pro generování signálů na základě indikátorů
function generateSignal(symbol, price, indicators) {
  const { rsi, macd, macd_signal, bb_upper, bb_lower } = indicators;
  
  // Výchozí neutrální doporučení
  let recommendation = "NEUTRAL";
  let confidence = 0.5;
  
  // Pravidla pro generování signálů
  
  // RSI pravidla
  if (rsi < 30) {
    // Přeprodaný trh - BUY signál
    recommendation = "BUY";
    confidence = 0.6;
  } else if (rsi > 70) {
    // Překoupený trh - SELL signál
    recommendation = "SELL";
    confidence = 0.6;
  }
  
  // MACD pravidla
  if (macd > macd_signal) {
    // MACD nad signální linií - BUY signál
    if (recommendation === "BUY") {
      confidence += 0.2;
      recommendation = "STRONG_BUY";
    } else if (recommendation === "NEUTRAL") {
      recommendation = "BUY";
      confidence = 0.5;
    }
  } else if (macd < macd_signal) {
    // MACD pod signální linií - SELL signál
    if (recommendation === "SELL") {
      confidence += 0.2;
      recommendation = "STRONG_SELL";
    } else if (recommendation === "NEUTRAL") {
      recommendation = "SELL";
      confidence = 0.5;
    }
  }
  
  // Bollingerova pásma
  if (price <= bb_lower) {
    // Cena pod dolním pásmem - potenciální BUY
    if (recommendation === "BUY" || recommendation === "STRONG_BUY") {
      confidence += 0.1;
    } else if (recommendation === "NEUTRAL") {
      recommendation = "BUY";
      confidence = 0.5;
    }
  } else if (price >= bb_upper) {
    // Cena nad horním pásmem - potenciální SELL
    if (recommendation === "SELL" || recommendation === "STRONG_SELL") {
      confidence += 0.1;
    } else if (recommendation === "NEUTRAL") {
      recommendation = "SELL";
      confidence = 0.5;
    }
  }
  
  // Omezení confidence na 0.9
  confidence = Math.min(confidence, 0.9);
  
  return {
    recommendation,
    confidence
  };
}

// Hlavní funkce
async function main() {
  try {
    // Vytvoření TradingView klienta
    const client = new TradingView.Client();
    
    // Pole pro ukládání výsledků
    const results = [];
    
    // Zpracování každého symbolu
    for (const symbol of symbols) {
      try {
        // Vytvoření session a grafu
        const chart = new client.Session.Chart();
        
        // Nastavení trhu a intervalu
        chart.setMarket(getMarket(symbol), {
          timeframe: intervalMap[interval] || '60', // Defaultně 1h
        });
        
        // Indikátory k získání
        const indicators = {
          rsi: chart.createIndicator('RSI', { 
            length: 14 
          }),
          macd: chart.createIndicator('MACD', { 
            fast_length: 12,
            slow_length: 26,
            source: 'close',
            signal_length: 9
          }),
          bb: chart.createIndicator('BB', {
            length: 20,
            src: 'close',
            mult: 2
          })
        };
        
        // Čekání na data
        await new Promise((resolve) => {
          // Timeout pro případ, že data nejsou k dispozici
          const timeout = setTimeout(() => {
            console.error(`Timeout při získávání dat pro ${symbol}`);
            resolve();
          }, 10000);
          
          // Callback pro aktualizaci grafu
          chart.onUpdate(() => {
            // Kontrola, zda jsou data k dispozici
            const price = chart.symbolInfo?.last_price;
            if (!price) return;
            
            // Kontrola indikátorů
            for (const [key, indicator] of Object.entries(indicators)) {
              if (!indicator.isReady()) return;
            }
            
            clearTimeout(timeout);
            
            // Získání hodnot
            const price = chart.symbolInfo.last_price;
            const rsi = indicators.rsi.getValue();
            const macdData = indicators.macd.getValue();
            const bbData = indicators.bb.getValue();
            
            // Výpočet indikátorů
            const indicatorsData = {
              rsi: rsi,
              macd: macdData.macd,
              macd_signal: macdData.signal,
              macd_histogram: macdData.hist,
              bb_upper: bbData.upper,
              bb_middle: bbData.basis,
              bb_lower: bbData.lower
            };
            
            // Generování signálu
            const signal = generateSignal(symbol, price, indicatorsData);
            
            // Výpočet stop-loss a take-profit
            let stopLoss = null;
            let takeProfit = null;
            
            if (signal.recommendation === "BUY" || signal.recommendation === "STRONG_BUY") {
              stopLoss = price * 0.95;  // 5% pod cenou
              takeProfit = price * 1.1;  // 10% nad cenou
            } else if (signal.recommendation === "SELL" || signal.recommendation === "STRONG_SELL") {
              stopLoss = price * 1.05;  // 5% nad cenou
              takeProfit = price * 0.9;  // 10% pod cenou
            }
            
            // Přidání výsledku
            results.push({
              symbol,
              price,
              indicators: indicatorsData,
              signal: {
                recommendation: signal.recommendation,
                confidence: signal.confidence,
                stopLoss,
                takeProfit
              }
            });
            
            resolve();
          });
        });
      } catch (error) {
        console.error(`Chyba při zpracování symbolu ${symbol}: ${error.message}`);
      }
    }
    
    // Ukončení klienta
    client.end();
    
    // Výstup ve formátu JSON
    console.log(JSON.stringify(results));
  } catch (error) {
    console.error(JSON.stringify({ error: error.message }));
    process.exit(1);
  }
}

// Spuštění hlavní funkce
main();