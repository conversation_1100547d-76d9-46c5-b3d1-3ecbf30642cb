/**
 * Skript pro získání technických indikátorů z TradingView
 * 
 * Použití:
 * node get_indicators.js <symbol> <interval>
 * 
 * Příklad:
 * node get_indicators.js BTCUSDT 1h
 */

const TradingView = require('@mathieuc/tradingview');

// Získání parametrů z příkazové řádky
const symbol = process.argv[2];
const interval = process.argv[3] || '1h';

// Kontrola vstupních parametrů
if (!symbol) {
  console.error('Chybí parametr symbol');
  process.exit(1);
}

// Mapování intervalů pro TradingView
const intervalMap = {
  '1m': '1',
  '5m': '5',
  '15m': '15',
  '30m': '30',
  '1h': '60',
  '2h': '120',
  '4h': '240',
  '1d': 'D',
  '1W': 'W',
  '1M': 'M'
};

// Funkce pro získání správného formátu symbolu
function getMarket(symbol) {
  // Defaultně předpokládáme BINANCE
  return `BINANCE:${symbol}`;
}

// Hlavní funkce
async function main() {
  try {
    // Vytvoření TradingView klienta
    const client = new TradingView.Client();
    
    // Vytvoření session a grafu
    const chart = new client.Session.Chart();
    
    // Nastavení trhu a intervalu
    chart.setMarket(getMarket(symbol), {
      timeframe: intervalMap[interval] || '60', // Defaultně 1h
    });
    
    // Indikátory k získání
    const indicators = {
      rsi: chart.createIndicator('RSI', { 
        length: 14 
      }),
      macd: chart.createIndicator('MACD', { 
        fast_length: 12,
        slow_length: 26,
        source: 'close',
        signal_length: 9
      }),
      bb: chart.createIndicator('BB', {
        length: 20,
        src: 'close',
        mult: 2
      })
    };
    
    // Čekání na data
    await new Promise(resolve => {
      // Funkce pro získání aktuálních dat
      const checkData = () => {
        // Kontrola, zda jsou data k dispozici
        const price = chart.symbolInfo?.last_price;
        if (!price) return false;
        
        // Kontrola indikátorů
        for (const [key, indicator] of Object.entries(indicators)) {
          if (!indicator.isReady()) return false;
        }
        
        return true;
      };
      
      // Timeout pro případ, že data nejsou k dispozici
      const timeout = setTimeout(() => {
        client.end();
        console.error(JSON.stringify({ error: 'Timeout při získávání dat' }));
        process.exit(1);
      }, 15000);
      
      // Callback pro aktualizaci grafu
      chart.onUpdate(() => {
        if (checkData()) {
          clearTimeout(timeout);
          
          // Získání hodnot
          const price = chart.symbolInfo.last_price;
          const rsi = indicators.rsi.getValue();
          const macdData = indicators.macd.getValue();
          const bbData = indicators.bb.getValue();
          
          // Vytvoření výstupního objektu
          const result = {
            symbol,
            price,
            indicators: {
              rsi: rsi,
              macd: macdData.macd,
              macd_signal: macdData.signal,
              macd_histogram: macdData.hist,
              bb_upper: bbData.upper,
              bb_middle: bbData.basis,
              bb_lower: bbData.lower
            }
          };
          
          // Výstup ve formátu JSON
          console.log(JSON.stringify(result));
          
          // Ukončení klienta
          client.end();
          resolve();
        }
      });
    });
  } catch (error) {
    console.error(JSON.stringify({ error: error.message }));
    process.exit(1);
  }
}

// Spuštění hlavní funkce
main();