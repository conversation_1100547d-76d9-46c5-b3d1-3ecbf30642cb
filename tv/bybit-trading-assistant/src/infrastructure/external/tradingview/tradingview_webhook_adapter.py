"""
Adaptér pro TradingView webhooky.
"""
from typing import Dict, Any, List, Optional
import uuid
import logging
import hashlib
import hmac
from fastapi import Request, HTTPException, Header
from datetime import datetime

from src.domain.models.signal import Signal, SignalType, SignalSource, SignalMetadata
from src.config.config import config


class TradingViewWebhookAdapter:
    """Adaptér pro TradingView webhooky."""
    
    def __init__(self):
        """Inicializace adaptéru."""
        self.logger = logging.getLogger(__name__)
        self.webhook_secret = config.tradingview.webhook_secret
    
    def _verify_signature(self, payload: bytes, signature: str) -> bool:
        """
        Ově<PERSON>í podpis webhooků z TradingView.
        
        Args:
            payload: Obsah požadavku
            signature: Podpis z hlavičky
            
        Returns:
            bool: True, pokud je podpis platný
        """
        if not self.webhook_secret or not signature:
            return False
        
        # Výpočet očekávan<PERSON><PERSON> podpisu
        expected_signature = hmac.new(
            self.webhook_secret.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Porovnání podpisů
        return hmac.compare_digest(expected_signature, signature)
    
    async def parse_webhook(self, request: Request, signature: Optional[str] = Header(None, alias="X-Tradingview-Signature")) -> Signal:
        """
        Parsuje webhook z TradingView.
        
        Args:
            request: HTTP požadavek
            signature: Podpis z hlavičky (volitelný)
            
        Returns:
            Signal: Signál
            
        Raises:
            HTTPException: Pokud je podpis neplatný nebo obsah požadavku neplatný
        """
        try:
            # Načtení obsahu požadavku
            payload = await request.body()
            
            # Ověření podpisu (pokud je nastaven)
            if self.webhook_secret and not self._verify_signature(payload, signature):
                self.logger.warning("Neplatný podpis webhooků z TradingView")
                raise HTTPException(status_code=401, detail="Neplatný podpis")
            
            # Parsování dat z požadavku
            data = await request.json()
            
            # Kontrola povinných polí
            required_fields = ["symbol", "action"]
            for field in required_fields:
                if field not in data:
                    raise HTTPException(status_code=400, detail=f"Chybí povinné pole: {field}")
            
            # Získání hodnot
            symbol = data.get("symbol", "").upper()
            signal_type_str = data.get("action", "").upper()
            
            # Kontrola hodnot
            if signal_type_str not in ["BUY", "SELL"]:
                raise HTTPException(status_code=400, detail=f"Neplatná hodnota action: {signal_type_str}")
            
            # Mapování hodnot
            signal_type = SignalType.BUY if signal_type_str == "BUY" else SignalType.SELL
            price = float(data.get("price", 0.0))
            
            # Získání stop-loss a take-profit
            stop_loss = float(data.get("stop_loss")) if "stop_loss" in data else None
            take_profit = float(data.get("take_profit")) if "take_profit" in data else None
            
            # Pokud nejsou zadány, vypočítáme je
            if price > 0:
                if stop_loss is None:
                    stop_loss = price * 0.95 if signal_type == SignalType.BUY else price * 1.05
                
                if take_profit is None:
                    take_profit = price * 1.1 if signal_type == SignalType.BUY else price * 0.9
            
            # Získání indikátorů
            indicators = data.get("indicators", {})
            
            # Vytvoření metadat
            metadata = SignalMetadata(
                source=SignalSource.WEBHOOK,
                timestamp=datetime.now(),
                correlation_id=data.get("correlation_id", str(uuid.uuid4())),
                raw_data=data
            )
            
            # Vytvoření signálu
            signal = Signal(
                id=str(uuid.uuid4()),
                symbol=symbol,
                type=signal_type,
                price=price,
                metadata=metadata,
                confidence=float(data.get("confidence", 0.8)),
                indicators=indicators,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            self.logger.info(f"Přijat webhook pro {symbol}: {signal_type.value}")
            return signal
        except HTTPException:
            # Přeposíláme HTTP výjimky
            raise
        except Exception as e:
            self.logger.error(f"Chyba při parsování webhooků z TradingView: {e}")
            raise HTTPException(status_code=500, detail=f"Chyba při zpracování webhooků: {str(e)}")
    
    def create_webhook_setup_guide(self) -> Dict[str, Any]:
        """
        Vytvoří průvodce nastavením webhooků pro TradingView.
        
        Returns:
            Dict[str, Any]: Průvodce nastavením webhooků
        """
        # Zjištění URL pro webhook
        webhook_url = f"{config.server.base_url}{config.tradingview.webhook_path}"
        
        # Vytvoření Pine skriptu
        pine_script = """
// TradingView Pine Script pro odesílání webhooků

//@version=4
strategy("Webhook Signal", overlay=true)

// Indikátory
rsi = rsi(close, 14)
[macd, signal, hist] = macd(close, 12, 26, 9)

// Podmínky pro BUY signál
buyCondition = crossover(macd, signal) and rsi < 70

// Podmínky pro SELL signál
sellCondition = crossunder(macd, signal) and rsi > 30

// Stop-loss a Take-profit v procentech
stopLossPercent = 5.0
takeProfitPercent = 10.0

// Výpočet stop-loss a take-profit úrovní
stopLoss = buyCondition ? close * (1 - stopLossPercent / 100) : close * (1 + stopLossPercent / 100)
takeProfit = buyCondition ? close * (1 + takeProfitPercent / 100) : close * (1 - takeProfitPercent / 100)

// Strategie
strategy.entry("Buy", strategy.long, when=buyCondition)
strategy.entry("Sell", strategy.short, when=sellCondition)

// Nastavení stop-loss a take-profit
strategy.exit("Exit Buy", "Buy", stop=stopLoss, limit=takeProfit)
strategy.exit("Exit Sell", "Sell", stop=stopLoss, limit=takeProfit)

// Odeslání webhooků
if buyCondition
    strategy.close("Sell")
    alert(
        message="{\"symbol\": \"{{ticker}}\", \"action\": \"BUY\", \"price\": {{close}}, \"stop_loss\": " + tostring(stopLoss) + ", \"take_profit\": " + tostring(takeProfit) + ", \"indicators\": {\"rsi\": " + tostring(rsi) + ", \"macd\": " + tostring(macd) + ", \"macd_signal\": " + tostring(signal) + "}}",
        freq=alert.freq_once_per_bar_close
    )

if sellCondition
    strategy.close("Buy")
    alert(
        message="{\"symbol\": \"{{ticker}}\", \"action\": \"SELL\", \"price\": {{close}}, \"stop_loss\": " + tostring(stopLoss) + ", \"take_profit\": " + tostring(takeProfit) + ", \"indicators\": {\"rsi\": " + tostring(rsi) + ", \"macd\": " + tostring(macd) + ", \"macd_signal\": " + tostring(signal) + "}}",
        freq=alert.freq_once_per_bar_close
    )
"""
        
        # Vytvoření návodu
        guide = {
            "webhook_url": webhook_url,
            "pine_script": pine_script,
            "setup_steps": [
                "1. Otevřete TradingView a přejděte na graf požadovaného symbolu.",
                "2. Klikněte na 'Pine Editor' a vložte výše uvedený Pine Script.",
                "3. Klikněte na 'Save' a poté na 'Add to Chart'.",
                "4. V grafu klikněte na 'Alerts' a vytvořte nový alert.",
                "5. Nastavte podmínku alertu na 'Buy' nebo 'Sell' signál.",
                "6. V sekci 'Notification' zaškrtněte 'Webhook URL' a vložte uvedenou webhook URL.",
                "7. Uložte alert.",
                "8. Opakujte kroky 4-7 pro druhý typ signálu (Buy/Sell)."
            ]
        }
        
        return guide