"""
Klient pro Bybit API.
"""
import hmac
import hashlib
import time
import json
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from src.config.config import config


class BybitClient:
    """Klient pro Bybit API."""
    
    def __init__(self, api_key: str = None, api_secret: str = None, testnet: bool = None):
        """
        Inicializace klienta.
        
        Args:
            api_key: API klíč
            api_secret: API tajný klíč
            testnet: Použití testovací sítě
        """
        self.api_key = api_key or config.bybit.api_key
        self.api_secret = api_secret or config.bybit.api_secret
        self.testnet = testnet if testnet is not None else config.bybit.testnet
        
        # Nastavení základní URL
        testnet_url = "https://api-testnet.bybit.com"
        main_url = "https://api.bybit.com"
        self.base_url = testnet_url if self.testnet else (config.bybit.base_url or main_url)
    
    def _get_signature(self, params: Dict[str, Any]) -> str:
        """
        Vytvoří podpis pro Bybit API.
        
        Args:
            params: Parametry požadavku
            
        Returns:
            str: Podpis
        """
        # Seřazení parametrů podle klíče
        sorted_params = sorted(params.items())
        
        # Vytvoření řetězce parametrů
        param_str = "&".join([f"{key}={value}" for key, value in sorted_params])
        
        # Vytvoření podpisu
        return hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(param_str, "utf-8"),
            digestmod=hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict[str, Any] = None, auth: bool = False) -> Dict[str, Any]:
        """
        Odešle požadavek na Bybit API.
        
        Args:
            method: HTTP metoda
            endpoint: Koncový bod API
            params: Parametry požadavku
            auth: Vyžaduje autentizaci
            
        Returns:
            Dict[str, Any]: Odpověď z API
        """
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        if auth:
            timestamp = int(time.time() * 1000)
            recv_window = 5000
            
            # Parametry pro podpis
            auth_params = {
                "api_key": self.api_key,
                "timestamp": str(timestamp),
                "recv_window": str(recv_window)
            }
            
            # Přidání parametrů požadavku
            if params:
                auth_params.update(params)
            
            # Vytvoření podpisu
            signature = self._get_signature(auth_params)
            
            # Nastavení hlaviček
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window),
                "Content-Type": "application/json"
            }
        
        # Odeslání požadavku
        if method == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=params)
        else:
            raise ValueError(f"Nepodporovaná HTTP metoda: {method}")
        
        # Zpracování odpovědi
        data = response.json()
        
        if data["retCode"] != 0:
            raise Exception(f"Chyba API: {data['retMsg']}")
        
        return data
    
    def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        Získá tržní data pro daný symbol.
        
        Args:
            symbol: Symbol
            
        Returns:
            Dict[str, Any]: Tržní data
        """
        endpoint = "/v5/market/tickers"
        params = {
            "category": "spot",
            "symbol": symbol
        }
        
        data = self._make_request("GET", endpoint, params)
        
        tickers = data.get("result", {}).get("list", [])
        if tickers:
            return tickers[0]
        
        return {}
    
    def get_klines(self, symbol: str, interval: str = "15", limit: int = 200) -> List[List[Any]]:
        """
        Získá historická data (svíčky) pro daný symbol.
        
        Args:
            symbol: Symbol
            interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
            limit: Počet svíček
            
        Returns:
            List[List[Any]]: Seznam svíček
        """
        endpoint = "/v5/market/kline"
        params = {
            "category": "spot",
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        
        data = self._make_request("GET", endpoint, params)
        
        return data.get("result", {}).get("list", [])
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Získá seznam otevřených pozic.
        
        Returns:
            List[Dict[str, Any]]: Seznam otevřených pozic
        """
        endpoint = "/v5/position/list"
        params = {
            "category": "spot",
            "settleCoin": "USDT"
        }
        
        data = self._make_request("GET", endpoint, params, auth=True)
        
        return data.get("result", {}).get("list", [])
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        Získá informace o účtu.
        
        Returns:
            Dict[str, Any]: Informace o účtu
        """
        endpoint = "/v5/account/wallet-balance"
        params = {
            "accountType": "UNIFIED"
        }
        
        data = self._make_request("GET", endpoint, params, auth=True)
        
        return data.get("result", {})
    
    def place_order(self, symbol: str, side: str, order_type: str, qty: float, price: Optional[float] = None,
                   time_in_force: str = "GTC", reduce_only: bool = False, close_on_trigger: bool = False,
                   stop_loss: Optional[float] = None, take_profit: Optional[float] = None) -> Dict[str, Any]:
        """
        Vytvoří nový objednávku.
        
        Args:
            symbol: Symbol
            side: Strana (Buy, Sell)
            order_type: Typ objednávky (Limit, Market)
            qty: Množství
            price: Cena (pouze pro Limit objednávky)
            time_in_force: Časová platnost (GTC, IOC, FOK)
            reduce_only: Pouze snížení pozice
            close_on_trigger: Uzavřít při spuštění
            stop_loss: Stop-loss cena
            take_profit: Take-profit cena
            
        Returns:
            Dict[str, Any]: Informace o vytvořené objednávce
        """
        endpoint = "/v5/order/create"
        params = {
            "category": "spot",
            "symbol": symbol,
            "side": side,
            "orderType": order_type,
            "qty": str(qty),
            "timeInForce": time_in_force,
            "reduceOnly": reduce_only,
            "closeOnTrigger": close_on_trigger
        }
        
        if price is not None:
            params["price"] = str(price)
        
        if stop_loss is not None:
            params["stopLoss"] = str(stop_loss)
        
        if take_profit is not None:
            params["takeProfit"] = str(take_profit)
        
        data = self._make_request("POST", endpoint, params, auth=True)
        
        return data.get("result", {})
    
    def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """
        Zruší objednávku.
        
        Args:
            order_id: ID objednávky
            symbol: Symbol
            
        Returns:
            Dict[str, Any]: Informace o zrušené objednávce
        """
        endpoint = "/v5/order/cancel"
        params = {
            "category": "spot",
            "symbol": symbol,
            "orderId": order_id
        }
        
        data = self._make_request("POST", endpoint, params, auth=True)
        
        return data.get("result", {})
        
    def get_orderbook(self, symbol: str, limit: int = 25) -> Dict[str, Any]:
        """
        Získá knihu objednávek pro daný symbol.
        
        Args:
            symbol: Symbol
            limit: Maximální počet záznamů na každé straně
            
        Returns:
            Dict[str, Any]: Kniha objednávek
        """
        endpoint = "/v5/market/orderbook"
        params = {
            "category": "spot",
            "symbol": symbol,
            "limit": limit
        }
        
        data = self._make_request("GET", endpoint, params)
        
        return data.get("result", {})