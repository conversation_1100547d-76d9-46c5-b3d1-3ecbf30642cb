"""
SQLAlchemy modely pro persistenci dat.
"""
from sqlalchemy import (
    Column, Integer, String, Float, DateTime, 
    Boolean, ForeignKey, Text, JSON, Enum, 
    create_engine, MetaData
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import enum
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Vytvoření báze pro deklarativní modely
Base = declarative_base()


class SignalTypeEnum(enum.Enum):
    """Enum pro typ signálu."""
    BUY = "BUY"
    SELL = "SELL"
    NEUTRAL = "NEUTRAL"


class SignalSourceEnum(enum.Enum):
    """Enum pro zdroj signálu."""
    TRADINGVIEW = "TRADINGVIEW"
    OPENAI = "OPENAI"
    SYSTEM = "SYSTEM"
    USER = "USER"


class OrderTypeEnum(enum.Enum):
    """Enum pro typ objednávky."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderSideEnum(enum.Enum):
    """Enum pro stranu objednávky."""
    BUY = "BUY"
    SELL = "SELL"


class OrderStatusEnum(enum.Enum):
    """Enum pro status objednávky."""
    CREATED = "CREATED"
    SUBMITTED = "SUBMITTED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELED = "CANCELED"
    REJECTED = "REJECTED"


class PositionSideEnum(enum.Enum):
    """Enum pro stranu pozice."""
    LONG = "LONG"
    SHORT = "SHORT"


class PositionStatusEnum(enum.Enum):
    """Enum pro status pozice."""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PENDING = "PENDING"
    CANCELLED = "CANCELLED"


class SignalModel(Base):
    """SQLAlchemy model pro signál."""
    __tablename__ = "signals"
    
    id = Column(String(36), primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    type = Column(Enum(SignalTypeEnum), nullable=False)
    price = Column(Float, nullable=False)
    confidence = Column(Float, default=0.0)
    source = Column(Enum(SignalSourceEnum), nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    correlation_id = Column(String(36), nullable=True)
    stop_loss = Column(Float, nullable=True)
    take_profit = Column(Float, nullable=True)
    indicators = Column(JSON, nullable=True)
    raw_data = Column(JSON, nullable=True)
    
    # Relace
    orders = relationship("OrderModel", back_populates="signal")
    
    def to_domain(self) -> 'Signal':
        """
        Převede SQLAlchemy model na doménový model.
        
        Returns:
            Signal: Doménový model signálu
        """
        from src.domain.models.signal import Signal, SignalType, SignalSource, SignalMetadata
        
        metadata = SignalMetadata(
            source=SignalSource(self.source.value),
            timestamp=self.timestamp,
            correlation_id=self.correlation_id,
            raw_data=self.raw_data or {}
        )
        
        return Signal(
            id=self.id,
            symbol=self.symbol,
            type=SignalType(self.type.value),
            price=self.price,
            metadata=metadata,
            confidence=self.confidence,
            indicators=self.indicators or {},
            stop_loss=self.stop_loss,
            take_profit=self.take_profit
        )
    
    @classmethod
    def from_domain(cls, signal: 'Signal') -> 'SignalModel':
        """
        Vytvoří SQLAlchemy model z doménového modelu.
        
        Args:
            signal: Doménový model signálu
            
        Returns:
            SignalModel: SQLAlchemy model signálu
        """
        return cls(
            id=signal.id,
            symbol=signal.symbol,
            type=SignalTypeEnum(signal.type.value),
            price=signal.price,
            confidence=signal.confidence,
            source=SignalSourceEnum(signal.metadata.source.value),
            timestamp=signal.metadata.timestamp,
            correlation_id=signal.metadata.correlation_id,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            indicators=signal.indicators,
            raw_data=signal.metadata.raw_data
        )


class OrderModel(Base):
    """SQLAlchemy model pro objednávku."""
    __tablename__ = "orders"
    
    id = Column(String(36), primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(Enum(OrderSideEnum), nullable=False)
    type = Column(Enum(OrderTypeEnum), nullable=False)
    quantity = Column(Float, nullable=False)
    price = Column(Float, nullable=True)
    status = Column(Enum(OrderStatusEnum), nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, index=True)
    signal_id = Column(String(36), ForeignKey("signals.id"), nullable=True)
    stop_loss = Column(Float, nullable=True)
    take_profit = Column(Float, nullable=True)
    external_id = Column(String(100), nullable=True)
    filled_at = Column(DateTime, nullable=True)
    filled_price = Column(Float, nullable=True)
    filled_quantity = Column(Float, nullable=True)
    meta_data = Column(JSON, nullable=True)
    
    # Relace
    signal = relationship("SignalModel", back_populates="orders")
    
    def to_domain(self) -> 'Order':
        """
        Převede SQLAlchemy model na doménový model.
        
        Returns:
            Order: Doménový model objednávky
        """
        from src.domain.models.order import Order, OrderSide, OrderType, OrderStatus
        
        return Order(
            id=self.id,
            symbol=self.symbol,
            side=OrderSide(self.side.value),
            type=OrderType(self.type.value),
            quantity=self.quantity,
            price=self.price,
            status=OrderStatus(self.status.value),
            created_at=self.created_at,
            signal_id=self.signal_id,
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            external_id=self.external_id,
            filled_at=self.filled_at,
            filled_price=self.filled_price,
            filled_quantity=self.filled_quantity,
            metadata=self.meta_data or {}
        )
    
    @classmethod
    def from_domain(cls, order: 'Order') -> 'OrderModel':
        """
        Vytvoří SQLAlchemy model z doménového modelu.
        
        Args:
            order: Doménový model objednávky
            
        Returns:
            OrderModel: SQLAlchemy model objednávky
        """
        return cls(
            id=order.id,
            symbol=order.symbol,
            side=OrderSideEnum(order.side.value),
            type=OrderTypeEnum(order.type.value),
            quantity=order.quantity,
            price=order.price,
            status=OrderStatusEnum(order.status.value),
            created_at=order.created_at,
            signal_id=order.signal_id,
            stop_loss=order.stop_loss,
            take_profit=order.take_profit,
            external_id=order.external_id,
            filled_at=order.filled_at,
            filled_price=order.filled_price,
            filled_quantity=order.filled_quantity,
            meta_data=order.metadata
        )


class PositionModel(Base):
    """SQLAlchemy model pro pozici."""
    __tablename__ = "positions"
    
    id = Column(String(36), primary_key=True)
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(Enum(PositionSideEnum), nullable=False)
    entry_price = Column(Float, nullable=False)
    quantity = Column(Float, nullable=False)
    opened_at = Column(DateTime, nullable=False, index=True)
    status = Column(Enum(PositionStatusEnum), nullable=False, index=True)
    current_price = Column(Float, nullable=True)
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    stop_loss = Column(Float, nullable=True)
    take_profit = Column(Float, nullable=True)
    closed_at = Column(DateTime, nullable=True)
    meta_data = Column(JSON, nullable=True)
    
    def to_domain(self) -> 'Position':
        """
        Převede SQLAlchemy model na doménový model.
        
        Returns:
            Position: Doménový model pozice
        """
        from src.domain.models.position import Position, PositionSide, PositionStatus
        
        return Position(
            id=self.id,
            symbol=self.symbol,
            side=PositionSide(self.side.value),
            entry_price=self.entry_price,
            quantity=self.quantity,
            opened_at=self.opened_at,
            status=PositionStatus(self.status.value),
            current_price=self.current_price,
            unrealized_pnl=self.unrealized_pnl,
            realized_pnl=self.realized_pnl,
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            closed_at=self.closed_at,
            metadata=self.meta_data or {}
        )
    
    @classmethod
    def from_domain(cls, position: 'Position') -> 'PositionModel':
        """
        Vytvoří SQLAlchemy model z doménového modelu.
        
        Args:
            position: Doménový model pozice
            
        Returns:
            PositionModel: SQLAlchemy model pozice
        """
        return cls(
            id=position.id,
            symbol=position.symbol,
            side=PositionSideEnum(position.side.value),
            entry_price=position.entry_price,
            quantity=position.quantity,
            opened_at=position.opened_at,
            status=PositionStatusEnum(position.status.value),
            current_price=position.current_price,
            unrealized_pnl=position.unrealized_pnl,
            realized_pnl=position.realized_pnl,
            stop_loss=position.stop_loss,
            take_profit=position.take_profit,
            closed_at=position.closed_at,
            meta_data=position.metadata
        )


class IndicatorModel(Base):
    """SQLAlchemy model pro technické indikátory."""
    __tablename__ = "indicators"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    interval = Column(String(10), nullable=False)
    rsi = Column(Float, nullable=True)
    macd = Column(Float, nullable=True)
    macd_signal = Column(Float, nullable=True)
    macd_histogram = Column(Float, nullable=True)
    bb_upper = Column(Float, nullable=True)
    bb_middle = Column(Float, nullable=True)
    bb_lower = Column(Float, nullable=True)
    sma_20 = Column(Float, nullable=True)
    sma_50 = Column(Float, nullable=True)
    sma_200 = Column(Float, nullable=True)
    ema_12 = Column(Float, nullable=True)
    ema_26 = Column(Float, nullable=True)
    atr = Column(Float, nullable=True)
    price = Column(Float, nullable=True)
    additional_data = Column(JSON, nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Převede model na slovník.
        
        Returns:
            Dict[str, Any]: Slovník s daty indikátorů
        """
        result = {
            "symbol": self.symbol,
            "timestamp": self.timestamp.isoformat(),
            "interval": self.interval,
            "price": self.price
        }
        
        # Přidání indikátorů, pokud nejsou None
        for indicator in ["rsi", "macd", "macd_signal", "macd_histogram", 
                        "bb_upper", "bb_middle", "bb_lower", 
                        "sma_20", "sma_50", "sma_200", 
                        "ema_12", "ema_26", "atr"]:
            value = getattr(self, indicator)
            if value is not None:
                result[indicator] = value
        
        # Přidání dodatečných dat
        if self.additional_data:
            result.update(self.additional_data)
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IndicatorModel':
        """
        Vytvoří model z dat.
        
        Args:
            data: Slovník s daty indikátorů
            
        Returns:
            IndicatorModel: Model indikátorů
        """
        # Extrakce základních údajů
        symbol = data.get("symbol")
        interval = data.get("interval", "1h")
        timestamp = data.get("timestamp")
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
        
        # Vytvoření instance
        instance = cls(
            symbol=symbol,
            timestamp=timestamp,
            interval=interval,
            price=data.get("price")
        )
        
        # Nastavení známých indikátorů
        known_indicators = ["rsi", "macd", "macd_signal", "macd_histogram", 
                           "bb_upper", "bb_middle", "bb_lower", 
                           "sma_20", "sma_50", "sma_200", 
                           "ema_12", "ema_26", "atr"]
        
        # Přidání indikátorů
        for indicator in known_indicators:
            if indicator in data:
                setattr(instance, indicator, data[indicator])
        
        # Přidání ostatních dat jako JSON
        additional_data = {}
        for key, value in data.items():
            if key not in known_indicators and key not in ["symbol", "timestamp", "interval", "price"]:
                additional_data[key] = value
        
        if additional_data:
            instance.additional_data = additional_data
        
        return instance