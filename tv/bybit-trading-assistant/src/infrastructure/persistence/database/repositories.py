"""
Repozit<PERSON><PERSON><PERSON> pro pr<PERSON>ci s databází.
"""
from typing import Dict, Any, List, Optional, Type, TypeVar, Generic
from datetime import datetime
import logging
from sqlalchemy import desc, asc
from sqlalchemy.orm import Session

from src.infrastructure.persistence.database.database import db
from src.infrastructure.persistence.database.models import (
    SignalModel, OrderModel, PositionModel, IndicatorModel
)
from src.domain.models.signal import Signal
from src.domain.models.order import Order, OrderStatus
from src.domain.models.position import Position, PositionStatus

# Type variables pro generické repozitáře
T = TypeVar('T')
M = TypeVar('M')


class BaseRepository(Generic[T, M]):
    """Základní třída pro repozitáře."""
    
    def __init__(self, model_class: Type[M], domain_class: Type[T]):
        """
        Inicializace repozitáře.
        
        Args:
            model_class: <PERSON><PERSON><PERSON>da SQLAlchemy modelu
            domain_class: <PERSON><PERSON><PERSON><PERSON> modelu
        """
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.model_class = model_class
        self.domain_class = domain_class
    
    async def save(self, entity: T) -> str:
        """
        Uloží entitu.
        
        Args:
            entity: Entita k uložení
            
        Returns:
            str: ID entity
        """
        model = self.model_class.from_domain(entity)
        entity_id = model.id
        
        with db.session() as session:
            # Kontrola, zda entita již existuje
            existing = session.query(self.model_class).filter_by(id=entity_id).first()
            
            if existing:
                # Aktualizace existující entity
                for key, value in model.__dict__.items():
                    if key != "_sa_instance_state":  # Přeskočení SQLAlchemy atributu
                        setattr(existing, key, value)
                session.add(existing)
                self.logger.debug(f"Aktualizována entita {self.model_class.__name__} s ID {entity_id}")
            else:
                # Vytvoření nové entity
                session.add(model)
                self.logger.debug(f"Vytvořena nová entita {self.model_class.__name__} s ID {entity_id}")
        
        return entity_id
    
    async def get_by_id(self, entity_id: str) -> Optional[T]:
        """
        Získá entitu podle ID.
        
        Args:
            entity_id: ID entity
            
        Returns:
            Optional[T]: Entita nebo None, pokud nebyla nalezena
        """
        with db.session() as session:
            model = session.query(self.model_class).filter_by(id=entity_id).first()
            
            if model:
                return model.to_domain()
            
            return None
    
    async def get_all(self) -> List[T]:
        """
        Získá všechny entity.
        
        Returns:
            List[T]: Seznam entit
        """
        with db.session() as session:
            models = session.query(self.model_class).all()
            return [model.to_domain() for model in models]
    
    async def delete(self, entity_id: str) -> bool:
        """
        Odstraní entitu.
        
        Args:
            entity_id: ID entity
            
        Returns:
            bool: True, pokud byla entita odstraněna, jinak False
        """
        with db.session() as session:
            model = session.query(self.model_class).filter_by(id=entity_id).first()
            
            if not model:
                return False
            
            session.delete(model)
            self.logger.debug(f"Odstraněna entita {self.model_class.__name__} s ID {entity_id}")
            return True


class SQLSignalRepository(BaseRepository[Signal, SignalModel]):
    """Repozitář pro signály v databázi."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        super().__init__(SignalModel, Signal)
    
    async def get_by_symbol(self, symbol: str, limit: int = 10) -> List[Signal]:
        """
        Získá signály pro daný symbol.
        
        Args:
            symbol: Symbol
            limit: Maximální počet signálů
            
        Returns:
            List[Signal]: Seznam signálů
        """
        with db.session() as session:
            models = (
                session.query(SignalModel)
                .filter(SignalModel.symbol == symbol)
                .order_by(desc(SignalModel.timestamp))
                .limit(limit)
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_by_time_range(self, start_time: datetime, end_time: datetime) -> List[Signal]:
        """
        Získá signály v časovém rozmezí.
        
        Args:
            start_time: Počáteční čas
            end_time: Koncový čas
            
        Returns:
            List[Signal]: Seznam signálů
        """
        with db.session() as session:
            models = (
                session.query(SignalModel)
                .filter(SignalModel.timestamp >= start_time)
                .filter(SignalModel.timestamp <= end_time)
                .order_by(desc(SignalModel.timestamp))
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_by_source(self, source: str, limit: int = 10) -> List[Signal]:
        """
        Získá signály podle zdroje.
        
        Args:
            source: Zdroj signálu
            limit: Maximální počet signálů
            
        Returns:
            List[Signal]: Seznam signálů
        """
        with db.session() as session:
            models = (
                session.query(SignalModel)
                .filter(SignalModel.source == source)
                .order_by(desc(SignalModel.timestamp))
                .limit(limit)
                .all()
            )
            
            return [model.to_domain() for model in models]


class SQLOrderRepository(BaseRepository[Order, OrderModel]):
    """Repozitář pro objednávky v databázi."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        super().__init__(OrderModel, Order)
    
    async def get_by_status(self, status: OrderStatus) -> List[Order]:
        """
        Získá objednávky podle statusu.
        
        Args:
            status: Status objednávky
            
        Returns:
            List[Order]: Seznam objednávek
        """
        with db.session() as session:
            models = (
                session.query(OrderModel)
                .filter(OrderModel.status == status.value)
                .order_by(desc(OrderModel.created_at))
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_by_symbol(self, symbol: str, limit: int = 10) -> List[Order]:
        """
        Získá objednávky pro daný symbol.
        
        Args:
            symbol: Symbol
            limit: Maximální počet objednávek
            
        Returns:
            List[Order]: Seznam objednávek
        """
        with db.session() as session:
            models = (
                session.query(OrderModel)
                .filter(OrderModel.symbol == symbol)
                .order_by(desc(OrderModel.created_at))
                .limit(limit)
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_by_signal_id(self, signal_id: str) -> List[Order]:
        """
        Získá objednávky pro daný signál.
        
        Args:
            signal_id: ID signálu
            
        Returns:
            List[Order]: Seznam objednávek
        """
        with db.session() as session:
            models = (
                session.query(OrderModel)
                .filter(OrderModel.signal_id == signal_id)
                .order_by(desc(OrderModel.created_at))
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_active_orders(self) -> List[Order]:
        """
        Získá aktivní objednávky.
        
        Returns:
            List[Order]: Seznam aktivních objednávek
        """
        with db.session() as session:
            models = (
                session.query(OrderModel)
                .filter(OrderModel.status.in_(["CREATED", "SUBMITTED", "PARTIALLY_FILLED"]))
                .order_by(desc(OrderModel.created_at))
                .all()
            )
            
            return [model.to_domain() for model in models]


class SQLPositionRepository(BaseRepository[Position, PositionModel]):
    """Repozitář pro pozice v databázi."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        super().__init__(PositionModel, Position)
    
    async def get_all_active(self) -> List[Position]:
        """
        Získá všechny aktivní pozice.
        
        Returns:
            List[Position]: Seznam aktivních pozic
        """
        with db.session() as session:
            models = (
                session.query(PositionModel)
                .filter(PositionModel.status == "OPEN")
                .order_by(desc(PositionModel.opened_at))
                .all()
            )
            
            return [model.to_domain() for model in models]
    
    async def get_by_symbol(self, symbol: str, include_closed: bool = False) -> List[Position]:
        """
        Získá pozice pro daný symbol.
        
        Args:
            symbol: Symbol
            include_closed: Zahrnout uzavřené pozice
            
        Returns:
            List[Position]: Seznam pozic
        """
        with db.session() as session:
            query = session.query(PositionModel).filter(PositionModel.symbol == symbol)
            
            if not include_closed:
                query = query.filter(PositionModel.status == "OPEN")
            
            models = query.order_by(desc(PositionModel.opened_at)).all()
            
            return [model.to_domain() for model in models]
    
    async def get_by_status(self, status: PositionStatus) -> List[Position]:
        """
        Získá pozice podle statusu.
        
        Args:
            status: Status pozice
            
        Returns:
            List[Position]: Seznam pozic
        """
        with db.session() as session:
            models = (
                session.query(PositionModel)
                .filter(PositionModel.status == status.value)
                .order_by(desc(PositionModel.opened_at))
                .all()
            )
            
            return [model.to_domain() for model in models]


class SQLIndicatorRepository:
    """Repozitář pro technické indikátory v databázi."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def save(self, indicators: Dict[str, Any]) -> int:
        """
        Uloží indikátory.
        
        Args:
            indicators: Slovník s indikátory
            
        Returns:
            int: ID záznamu
        """
        model = IndicatorModel.from_dict(indicators)
        
        with db.session() as session:
            session.add(model)
            session.flush()  # Pro získání ID
            record_id = model.id
            session.commit()
            
            self.logger.debug(f"Uloženy indikátory pro {model.symbol} ({model.interval})")
            return record_id
    
    async def get_latest(self, symbol: str, interval: str = "1h") -> Optional[Dict[str, Any]]:
        """
        Získá nejnovější indikátory pro daný symbol a interval.
        
        Args:
            symbol: Symbol
            interval: Časový interval
            
        Returns:
            Optional[Dict[str, Any]]: Slovník s indikátory nebo None
        """
        with db.session() as session:
            model = (
                session.query(IndicatorModel)
                .filter(IndicatorModel.symbol == symbol)
                .filter(IndicatorModel.interval == interval)
                .order_by(desc(IndicatorModel.timestamp))
                .first()
            )
            
            if model:
                return model.to_dict()
            
            return None
    
    async def get_history(self, symbol: str, interval: str = "1h", limit: int = 100) -> List[Dict[str, Any]]:
        """
        Získá historii indikátorů pro daný symbol a interval.
        
        Args:
            symbol: Symbol
            interval: Časový interval
            limit: Maximální počet záznamů
            
        Returns:
            List[Dict[str, Any]]: Seznam slovníků s indikátory
        """
        with db.session() as session:
            models = (
                session.query(IndicatorModel)
                .filter(IndicatorModel.symbol == symbol)
                .filter(IndicatorModel.interval == interval)
                .order_by(desc(IndicatorModel.timestamp))
                .limit(limit)
                .all()
            )
            
            return [model.to_dict() for model in models]
    
    async def get_by_time_range(self, symbol: str, start_time: datetime, end_time: datetime,
                              interval: str = "1h") -> List[Dict[str, Any]]:
        """
        Získá indikátory v časovém rozmezí.
        
        Args:
            symbol: Symbol
            start_time: Počáteční čas
            end_time: Koncový čas
            interval: Časový interval
            
        Returns:
            List[Dict[str, Any]]: Seznam slovníků s indikátory
        """
        with db.session() as session:
            models = (
                session.query(IndicatorModel)
                .filter(IndicatorModel.symbol == symbol)
                .filter(IndicatorModel.interval == interval)
                .filter(IndicatorModel.timestamp >= start_time)
                .filter(IndicatorModel.timestamp <= end_time)
                .order_by(asc(IndicatorModel.timestamp))
                .all()
            )
            
            return [model.to_dict() for model in models]