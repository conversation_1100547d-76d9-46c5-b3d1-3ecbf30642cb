"""
Konfigurace a správa databáze.
"""
import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.engine import Engine
from contextlib import contextmanager
from typing import Iterator, Optional, Dict, Any

from src.config.config import config
from src.infrastructure.persistence.database.models import Base


class Database:
    """Správce databáze."""
    
    _instance = None
    
    def __new__(cls):
        """Vytvoří nebo vrátí singleton instanci."""
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Inicializace databáze."""
        if self._initialized:
            return
        
        self.logger = logging.getLogger(__name__)
        self.engine = self._create_engine()
        self.session_factory = sessionmaker(bind=self.engine)
        self.Session = scoped_session(self.session_factory)
        
        self._initialized = True
    
    def _create_engine(self) -> Engine:
        """
        Vytvoří a konfiguruje SQLAlchemy engine.
        
        Returns:
            Engine: SQLAlchemy engine
        """
        db_type = config.database.type
        db_path = config.database.path
        
        if db_type == "sqlite":
            # Ujistíme se, že adresář existuje
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            connection_string = f"sqlite:///{db_path}"
            self.logger.info(f"Připojování k SQLite databázi: {db_path}")
            return create_engine(
                connection_string,
                connect_args={"check_same_thread": False},
                echo=config.environment == "development"
            )
        else:
            raise ValueError(f"Nepodporovaný typ databáze: {db_type}")
    
    def create_all(self):
        """Vytvoří všechny tabulky v databázi."""
        self.logger.info("Vytváření tabulek v databázi")
        Base.metadata.create_all(self.engine)
    
    def drop_all(self):
        """Odstraní všechny tabulky z databáze."""
        self.logger.warning("Odstraňování všech tabulek z databáze")
        Base.metadata.drop_all(self.engine)
    
    @contextmanager
    def session(self) -> Iterator[scoped_session]:
        """
        Vytvoří a spravuje databázovou relaci.
        
        Yields:
            Session: Databázová relace
        """
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            self.logger.error(f"Chyba při práci s databází: {e}")
            session.rollback()
            raise
        finally:
            session.close()


# Singleton instance
db = Database()