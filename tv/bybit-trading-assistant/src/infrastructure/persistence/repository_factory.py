"""
Továrna pro vytváření repozitářů.
"""
import logging
from typing import Optional, Type, TypeVar, Generic, Dict, Any

from src.infrastructure.persistence.memory.memory_repositories import (
    MemorySignalRepository, MemoryOrderRepository, MemoryPositionRepository
)
from src.infrastructure.persistence.database.repositories import (
    SQLSignalRepository, SQLOrderRepository, SQLPositionRepository, SQLIndicatorRepository
)
from src.config.config import config

# Type variables
T = TypeVar('T')


class RepositoryFactory:
    """Továrna pro vytváření repozitářů."""
    
    def __init__(self):
        """Inicializace továrny."""
        self.logger = logging.getLogger(__name__)
        self.repositories = {}
    
    def create_signal_repository(self):
        """
        Vytvoří repozitář pro signály.
        
        Returns:
            Repository: Repozitář pro signály
        """
        if "signal_repository" in self.repositories:
            return self.repositories["signal_repository"]
        
        if config.database.enabled:
            self.logger.info("Vytvářím SQL repozitář pro signály")
            repository = SQLSignalRepository()
        else:
            self.logger.info("Vytvářím paměťový repozitář pro signály")
            repository = MemorySignalRepository()
        
        self.repositories["signal_repository"] = repository
        return repository
    
    def create_order_repository(self):
        """
        Vytvoří repozitář pro objednávky.
        
        Returns:
            Repository: Repozitář pro objednávky
        """
        if "order_repository" in self.repositories:
            return self.repositories["order_repository"]
        
        if config.database.enabled:
            self.logger.info("Vytvářím SQL repozitář pro objednávky")
            repository = SQLOrderRepository()
        else:
            self.logger.info("Vytvářím paměťový repozitář pro objednávky")
            repository = MemoryOrderRepository()
        
        self.repositories["order_repository"] = repository
        return repository
    
    def create_position_repository(self):
        """
        Vytvoří repozitář pro pozice.
        
        Returns:
            Repository: Repozitář pro pozice
        """
        if "position_repository" in self.repositories:
            return self.repositories["position_repository"]
        
        if config.database.enabled:
            self.logger.info("Vytvářím SQL repozitář pro pozice")
            repository = SQLPositionRepository()
        else:
            self.logger.info("Vytvářím paměťový repozitář pro pozice")
            repository = MemoryPositionRepository()
        
        self.repositories["position_repository"] = repository
        return repository
    
    def create_indicator_repository(self):
        """
        Vytvoří repozitář pro indikátory.
        
        Returns:
            Repository: Repozitář pro indikátory
        """
        if "indicator_repository" in self.repositories:
            return self.repositories["indicator_repository"]
        
        if config.database.enabled:
            self.logger.info("Vytvářím SQL repozitář pro indikátory")
            repository = SQLIndicatorRepository()
        else:
            self.logger.info("Vytvářím paměťový repozitář pro indikátory")
            # Pro indikátory nemáme paměťový repozitář, proto použijeme SQL i v případě,
            # že je databáze vypnutá. To zajistí, že data budou uložena.
            repository = SQLIndicatorRepository()
        
        self.repositories["indicator_repository"] = repository
        return repository


# Singleton instance
repository_factory = RepositoryFactory()