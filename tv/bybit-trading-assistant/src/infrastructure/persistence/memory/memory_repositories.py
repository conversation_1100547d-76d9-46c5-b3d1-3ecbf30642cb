"""
Repozitáře pro ukládání dat v paměti.
"""
from typing import Dict, Any, List, Optional
from datetime import datetime


class MemorySignalRepository:
    """Repozitář pro signály v paměti."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        self.signals = {}
    
    async def save(self, signal):
        """
        Uloží signál.
        
        Args:
            signal: Signál
            
        Returns:
            str: ID signálu
        """
        self.signals[signal.id] = signal
        return signal.id
    
    async def get_by_id(self, signal_id: str):
        """
        Získá signál podle ID.
        
        Args:
            signal_id: ID signálu
            
        Returns:
            Signal: Signál
        """
        return self.signals.get(signal_id)
    
    async def get_all(self):
        """
        Získá všechny signály.
        
        Returns:
            List[Signal]: Seznam signálů
        """
        return list(self.signals.values())


class MemoryOrderRepository:
    """Repozitář pro objednávky v paměti."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        self.orders = {}
    
    async def save(self, order):
        """
        Uloží objednávku.
        
        Args:
            order: Objednávka
            
        Returns:
            str: ID objednávky
        """
        self.orders[order.id] = order
        return order.id
    
    async def get_by_id(self, order_id: str):
        """
        Získá objednávku podle ID.
        
        Args:
            order_id: ID objednávky
            
        Returns:
            Order: Objednávka
        """
        return self.orders.get(order_id)
    
    async def get_by_status(self, status: str):
        """
        Získá objednávky podle statusu.
        
        Args:
            status: Status objednávky
            
        Returns:
            List[Order]: Seznam objednávek
        """
        return [order for order in self.orders.values() if order.status.value == status]
    
    async def get_all(self):
        """
        Získá všechny objednávky.
        
        Returns:
            List[Order]: Seznam objednávek
        """
        return list(self.orders.values())


class MemoryPositionRepository:
    """Repozitář pro pozice v paměti."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        self.positions = {}
    
    async def save(self, position):
        """
        Uloží pozici.
        
        Args:
            position: Pozice
            
        Returns:
            str: ID pozice
        """
        self.positions[position.id] = position
        return position.id
    
    async def get_by_id(self, position_id: str):
        """
        Získá pozici podle ID.
        
        Args:
            position_id: ID pozice
            
        Returns:
            Position: Pozice
        """
        return self.positions.get(position_id)
    
    async def get_all_active(self):
        """
        Získá všechny aktivní pozice.
        
        Returns:
            List[Position]: Seznam aktivních pozic
        """
        return [position for position in self.positions.values() if position.status.value == "OPEN"]
    
    async def get_all(self):
        """
        Získá všechny pozice.
        
        Returns:
            List[Position]: Seznam pozic
        """
        return list(self.positions.values())


class MemoryAnalysisRepository:
    """Repozitář pro analýzy v paměti."""
    
    def __init__(self):
        """Inicializace repozitáře."""
        self.analyses = {}
    
    async def save(self, analysis):
        """
        Uloží analýzu.
        
        Args:
            analysis: Analýza
            
        Returns:
            str: ID analýzy
        """
        self.analyses[analysis.id] = analysis
        return analysis.id
    
    async def get_by_id(self, analysis_id: str):
        """
        Získá analýzu podle ID.
        
        Args:
            analysis_id: ID analýzy
            
        Returns:
            Analysis: Analýza
        """
        return self.analyses.get(analysis_id)
    
    async def get_all(self):
        """
        Získá všechny analýzy.
        
        Returns:
            List[Analysis]: Seznam analýz
        """
        return list(self.analyses.values())
