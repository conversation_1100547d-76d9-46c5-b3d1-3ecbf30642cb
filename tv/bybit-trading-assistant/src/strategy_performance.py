"""
Strategy performance analysis module.

This module provides tools for analyzing and visualizing the performance of trading strategies.
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

# Setup logging
logger = logging.getLogger(__name__)


class StrategyPerformanceAnalyzer:
    """Analyzer for trading strategy performance."""
    
    def __init__(self):
        """Initialize the analyzer."""
        self.logger = logging.getLogger(__name__)
    
    def calculate_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate performance metrics for a list of trades.
        
        Args:
            trades: List of trades with at least the following keys:
                - entry_price: Entry price
                - exit_price: Exit price
                - quantity: Trade size
                - side: LONG or SHORT
                - opened_at: Opening timestamp
                - closed_at: Closing timestamp
                
        Returns:
            Dict with performance metrics
        """
        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "average_profit": 0.0,
                "average_loss": 0.0,
                "largest_profit": 0.0,
                "largest_loss": 0.0,
                "total_profit": 0.0,
                "total_loss": 0.0,
                "net_profit": 0.0,
                "annualized_return": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "max_drawdown_percentage": 0.0,
                "average_holding_time": timedelta(0),
            }
        
        # Convert to DataFrame for easier analysis
        try:
            trades_df = pd.DataFrame(trades)
            
            # Calculate PnL for each trade
            trades_df["pnl"] = trades_df.apply(
                lambda row: self._calculate_trade_pnl(
                    row["entry_price"], 
                    row["exit_price"], 
                    row["quantity"], 
                    row["side"]
                ), 
                axis=1
            )
            
            # Calculate holding time
            trades_df["opened_at"] = pd.to_datetime(trades_df["opened_at"])
            trades_df["closed_at"] = pd.to_datetime(trades_df["closed_at"])
            trades_df["holding_time"] = trades_df["closed_at"] - trades_df["opened_at"]
            
            # Basic metrics
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df["pnl"] > 0])
            losing_trades = len(trades_df[trades_df["pnl"] <= 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Profit metrics
            total_profit = trades_df[trades_df["pnl"] > 0]["pnl"].sum() if winning_trades > 0 else 0
            total_loss = abs(trades_df[trades_df["pnl"] <= 0]["pnl"].sum()) if losing_trades > 0 else 0
            
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
            
            average_profit = total_profit / winning_trades if winning_trades > 0 else 0
            average_loss = total_loss / losing_trades if losing_trades > 0 else 0
            
            largest_profit = trades_df["pnl"].max() if not trades_df.empty else 0
            largest_loss = abs(trades_df["pnl"].min()) if not trades_df.empty else 0
            
            net_profit = total_profit - total_loss
            
            # Time-based metrics
            avg_holding_time = trades_df["holding_time"].mean() if not trades_df.empty else timedelta(0)
            
            # Calculate equity curve for drawdown analysis
            trades_df = trades_df.sort_values("closed_at")
            trades_df["cumulative_pnl"] = trades_df["pnl"].cumsum()
            
            # Calculate drawdown
            trades_df["peak"] = trades_df["cumulative_pnl"].cummax()
            trades_df["drawdown"] = trades_df["peak"] - trades_df["cumulative_pnl"]
            
            max_drawdown = trades_df["drawdown"].max() if not trades_df.empty else 0
            max_drawdown_percentage = (max_drawdown / trades_df["peak"].max()) * 100 if trades_df["peak"].max() > 0 else 0
            
            # Calculate returns for Sharpe ratio
            if len(trades_df) > 1:
                daily_returns = self._calculate_daily_returns(trades_df)
                
                # Annualized return
                days = (trades_df["closed_at"].max() - trades_df["opened_at"].min()).days
                years = days / 365.0 if days > 0 else 1
                
                annualized_return = ((1 + (net_profit / 10000)) ** (1 / years) - 1) * 100 if years > 0 else 0
                
                # Sharpe ratio (using 0% as risk-free rate)
                sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
            else:
                annualized_return = 0
                sharpe_ratio = 0
            
            return {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "profit_factor": profit_factor,
                "average_profit": average_profit,
                "average_loss": average_loss,
                "largest_profit": largest_profit,
                "largest_loss": largest_loss,
                "total_profit": total_profit,
                "total_loss": total_loss,
                "net_profit": net_profit,
                "annualized_return": annualized_return,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "max_drawdown_percentage": max_drawdown_percentage,
                "average_holding_time": avg_holding_time,
            }
        except Exception as e:
            self.logger.error(f"Error calculating metrics: {e}")
            return {
                "error": str(e),
                "total_trades": len(trades),
            }
    
    def _calculate_trade_pnl(self, entry_price: float, exit_price: float, quantity: float, side: str) -> float:
        """
        Calculate P&L for a single trade.
        
        Args:
            entry_price: Entry price
            exit_price: Exit price
            quantity: Trade size
            side: LONG or SHORT
            
        Returns:
            P&L amount
        """
        if side == "LONG":
            return (exit_price - entry_price) * quantity
        else:  # SHORT
            return (entry_price - exit_price) * quantity
    
    def _calculate_daily_returns(self, trades_df: pd.DataFrame) -> pd.Series:
        """
        Calculate daily returns from trade data.
        
        Args:
            trades_df: DataFrame with trade data
            
        Returns:
            Series of daily returns
        """
        # Group by date and sum PnL
        trades_df["date"] = trades_df["closed_at"].dt.date
        daily_pnl = trades_df.groupby("date")["pnl"].sum()
        
        # Calculate daily returns (assuming starting capital of 10000 for simplicity)
        capital = 10000
        daily_returns = daily_pnl / capital
        
        return daily_returns
    
    def create_equity_curve(self, trades: List[Dict[str, Any]], include_drawdown: bool = True) -> Dict[str, Any]:
        """
        Create equity curve data for visualization.
        
        Args:
            trades: List of trades
            include_drawdown: Whether to include drawdown data
            
        Returns:
            Dict with equity curve data
        """
        if not trades:
            return {
                "dates": [],
                "equity": [],
                "drawdown": [] if include_drawdown else None
            }
        
        try:
            trades_df = pd.DataFrame(trades)
            trades_df["closed_at"] = pd.to_datetime(trades_df["closed_at"])
            trades_df = trades_df.sort_values("closed_at")
            
            # Calculate PnL for each trade
            trades_df["pnl"] = trades_df.apply(
                lambda row: self._calculate_trade_pnl(
                    row["entry_price"], 
                    row["exit_price"], 
                    row["quantity"], 
                    row["side"]
                ), 
                axis=1
            )
            
            # Create equity curve (starting with 10000)
            initial_capital = 10000
            trades_df["cumulative_pnl"] = trades_df["pnl"].cumsum()
            trades_df["equity"] = initial_capital + trades_df["cumulative_pnl"]
            
            # Calculate drawdown if required
            if include_drawdown:
                trades_df["peak"] = trades_df["equity"].cummax()
                trades_df["drawdown"] = (trades_df["peak"] - trades_df["equity"]) / trades_df["peak"] * 100
            
            # Return data for plotting
            result = {
                "dates": trades_df["closed_at"].tolist(),
                "equity": trades_df["equity"].tolist()
            }
            
            if include_drawdown:
                result["drawdown"] = trades_df["drawdown"].tolist()
            
            return result
        except Exception as e:
            self.logger.error(f"Error creating equity curve: {e}")
            return {
                "dates": [],
                "equity": [],
                "drawdown": [] if include_drawdown else None,
                "error": str(e)
            }
    
    def compare_strategies(self, strategies_trades: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict[str, Any]]:
        """
        Compare multiple strategies based on their trades.
        
        Args:
            strategies_trades: Dict mapping strategy names to their trade lists
            
        Returns:
            Dict mapping strategy names to their performance metrics
        """
        result = {}
        
        for strategy_name, trades in strategies_trades.items():
            metrics = self.calculate_metrics(trades)
            result[strategy_name] = metrics
        
        return result
    
    def get_optimal_parameters(
        self, 
        strategy_generator_func, 
        parameter_ranges: Dict[str, List[Any]], 
        optimization_metric: str = "net_profit"
    ) -> Dict[str, Any]:
        """
        Find optimal parameters for a strategy using grid search.
        
        Args:
            strategy_generator_func: Function that generates trades given parameters
            parameter_ranges: Dict mapping parameter names to lists of values to try
            optimization_metric: Metric to optimize for
            
        Returns:
            Dict with optimal parameters and performance
        """
        # TODO: Implement parameter optimization
        return {"error": "Not implemented yet"}


def plot_equity_curve(equity_data: Dict[str, Any], title: str = "Equity Curve") -> plt.Figure:
    """
    Plot equity curve from equity data.
    
    Args:
        equity_data: Dict with equity curve data
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    fig, ax1 = plt.subplots(figsize=(12, 6))
    
    # Plot equity curve
    dates = equity_data.get("dates", [])
    equity = equity_data.get("equity", [])
    
    if not dates or not equity:
        ax1.text(0.5, 0.5, "No data to display", horizontalalignment="center", verticalalignment="center")
        return fig
    
    ax1.plot(dates, equity, 'b-', linewidth=2)
    ax1.set_xlabel("Date")
    ax1.set_ylabel("Equity", color="b")
    ax1.tick_params(axis="y", labelcolor="b")
    ax1.grid(True, alpha=0.3)
    
    # Plot drawdown if available
    drawdown = equity_data.get("drawdown")
    if drawdown:
        ax2 = ax1.twinx()
        ax2.plot(dates, drawdown, 'r--', alpha=0.7)
        ax2.set_ylabel("Drawdown %", color="r")
        ax2.tick_params(axis="y", labelcolor="r")
        ax2.invert_yaxis()  # Invert so drawdowns go down
        
        # Highlight max drawdown
        max_dd_idx = np.argmax(drawdown)
        ax2.scatter(dates[max_dd_idx], drawdown[max_dd_idx], color="r", s=100, zorder=5)
        ax2.annotate(f"Max DD: {drawdown[max_dd_idx]:.2f}%", 
                    (dates[max_dd_idx], drawdown[max_dd_idx]),
                    xytext=(15, -15), textcoords="offset points",
                    arrowprops=dict(arrowstyle="->", color="red"))
    
    plt.title(title)
    plt.tight_layout()
    return fig


def plot_win_loss_distribution(trades: List[Dict[str, Any]], title: str = "Win/Loss Distribution") -> plt.Figure:
    """
    Plot distribution of wins and losses.
    
    Args:
        trades: List of trades
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    if not trades:
        fig, ax = plt.subplots()
        ax.text(0.5, 0.5, "No data to display", horizontalalignment="center", verticalalignment="center")
        return fig
    
    # Convert to DataFrame for easier analysis
    trades_df = pd.DataFrame(trades)
    
    # Calculate PnL for each trade
    analyzer = StrategyPerformanceAnalyzer()
    trades_df["pnl"] = trades_df.apply(
        lambda row: analyzer._calculate_trade_pnl(
            row["entry_price"], 
            row["exit_price"], 
            row["quantity"], 
            row["side"]
        ), 
        axis=1
    )
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot histogram of PnL
    ax.hist(trades_df["pnl"], bins=20, alpha=0.7, color="skyblue", edgecolor="black")
    ax.axvline(x=0, color="r", linestyle="--", alpha=0.7)
    
    # Add statistics
    winning_trades = len(trades_df[trades_df["pnl"] > 0])
    losing_trades = len(trades_df[trades_df["pnl"] <= 0])
    win_rate = winning_trades / len(trades_df) if len(trades_df) > 0 else 0
    
    stats_text = (
        f"Win Rate: {win_rate:.2%}\n"
        f"Winning Trades: {winning_trades}\n"
        f"Losing Trades: {losing_trades}\n"
        f"Average Win: ${trades_df[trades_df['pnl'] > 0]['pnl'].mean():.2f}\n"
        f"Average Loss: ${trades_df[trades_df['pnl'] <= 0]['pnl'].mean():.2f}"
    )
    
    # Place text box in top left in axes coords
    props = dict(boxstyle="round", facecolor="wheat", alpha=0.5)
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=12,
            verticalalignment="top", bbox=props)
    
    ax.set_xlabel("Profit/Loss ($)")
    ax.set_ylabel("Number of Trades")
    ax.set_title(title)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def plot_strategy_comparison(strategy_metrics: Dict[str, Dict[str, Any]], metrics_to_plot: List[str]) -> plt.Figure:
    """
    Plot comparison of multiple strategies on selected metrics.
    
    Args:
        strategy_metrics: Dict mapping strategy names to their metrics
        metrics_to_plot: List of metric names to include in comparison
        
    Returns:
        Matplotlib figure
    """
    if not strategy_metrics or not metrics_to_plot:
        fig, ax = plt.subplots()
        ax.text(0.5, 0.5, "No data to display", horizontalalignment="center", verticalalignment="center")
        return fig
    
    # Create figure with subplots for each metric
    n_metrics = len(metrics_to_plot)
    fig, axes = plt.subplots(n_metrics, 1, figsize=(10, 3 * n_metrics))
    
    # Handle case of single metric
    if n_metrics == 1:
        axes = [axes]
    
    for i, metric in enumerate(metrics_to_plot):
        ax = axes[i]
        
        # Extract metric values for each strategy
        strategies = list(strategy_metrics.keys())
        metric_values = [strategy_metrics[s].get(metric, 0) for s in strategies]
        
        # Plot as bar chart
        bars = ax.bar(strategies, metric_values, alpha=0.7)
        
        # Add values on top of bars
        for bar in bars:
            height = bar.get_height()
            if isinstance(height, timedelta):
                # Format timedelta as hours
                hours = height.total_seconds() / 3600
                value_text = f"{hours:.1f}h"
            elif metric.endswith("percentage") or metric == "win_rate":
                value_text = f"{height:.1f}%"
            else:
                value_text = f"{height:.2f}"
                
            ax.annotate(value_text,
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3),  # 3 points vertical offset
                       textcoords="offset points",
                       ha='center', va='bottom')
        
        # Set title and labels
        ax.set_title(f"{metric.replace('_', ' ').title()}")
        ax.set_ylabel(metric.replace("_", " ").title())
        ax.set_xticklabels(strategies, rotation=45, ha="right")
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig