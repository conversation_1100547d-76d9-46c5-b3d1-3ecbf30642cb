"""
Služba pro správu a vyhodnocování obchodních strategií.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.strategies.strategy_manager import StrategyManager
from src.models.market_data import Candle, Ticker
from src.domain.models.signal import Signal, SignalType, SignalMetadata, SignalSource
from src.domain.models.position import Position
from src.config.config import config


class StrategyService:
    """Služba pro správu a vyhodnocování obchodních strategií."""
    
    def __init__(self):
        """Inicializace služby."""
        self.logger = logging.getLogger(__name__)
        self.strategy_manager = StrategyManager()
    
    async def evaluate_strategies(self, symbol: str, candles: List[Candle], 
                                 ticker: Optional[Ticker] = None,
                                 positions: Optional[List[Position]] = None) -> Optional[Signal]:
        """
        Vyhodnotí všechny strategie a vrátí kombinovaný signál.
        
        Args:
            symbol: Symbol
            candles: Seznam svíček
            ticker: Aktuální ticker
            positions: Seznam aktuálních pozic
            
        Returns:
            Optional[Signal]: Kombinovaný signál nebo None, pokud není generován žádný signál
        """
        if not candles:
            self.logger.warning(f"Nedostatek dat pro vyhodnocení strategií pro {symbol}")
            return None
        
        try:
            # Vyhodnocení všech strategií
            strategy_results = self.strategy_manager.evaluate_all_strategies(
                candles=candles,
                ticker=ticker,
                positions=positions or []
            )
            
            # Kombinování signálů
            combined_signal = self.strategy_manager.combine_signals(strategy_results)
            
            if not combined_signal:
                self.logger.info(f"Žádný signál nebyl vygenerován pro {symbol}")
                return None
            
            # Převod na doménový model signálu
            signal_type = SignalType.NEUTRAL
            if combined_signal["type"] == "BUY":
                signal_type = SignalType.BUY
            elif combined_signal["type"] == "SELL":
                signal_type = SignalType.SELL
            
            # Vytvoření metadat
            metadata = SignalMetadata(
                source=SignalSource.SYSTEM,
                timestamp=datetime.now(),
                correlation_id=None,
                raw_data={
                    "strategy_results": strategy_results,
                    "combined_signal": combined_signal
                }
            )
            
            # Vytvoření signálu
            signal = Signal(
                id=f"strategy-{symbol}-{datetime.now().isoformat()}",
                symbol=symbol,
                type=signal_type,
                price=combined_signal["price"],
                metadata=metadata,
                confidence=combined_signal["confidence"],
                indicators={},  # Budou naplněny níže
                stop_loss=combined_signal["stop_loss"],
                take_profit=combined_signal["take_profit"]
            )
            
            # Naplnění indikátorů z analýzy strategií
            indicators = {}
            for strategy_name, result in strategy_results.items():
                if "analysis" in result and "error" not in result["analysis"]:
                    analysis = result["analysis"]
                    # Extrakce relevantních indikátorů podle strategie
                    if strategy_name == "trend_following":
                        indicators["fast_ema"] = analysis.get("fast_ema")
                        indicators["slow_ema"] = analysis.get("slow_ema")
                        indicators["trend_strength"] = analysis.get("trend_strength")
                    elif strategy_name == "rsi_macd":
                        indicators["rsi"] = analysis.get("rsi")
                        indicators["macd"] = analysis.get("macd")
                        indicators["macd_signal"] = analysis.get("macd_signal")
                        indicators["macd_histogram"] = analysis.get("macd_histogram")
                    elif strategy_name == "breakout":
                        indicators["breakout_level"] = analysis.get("breakout_level")
                        indicators["breakout_strength"] = analysis.get("breakout_strength")
                        indicators["volume_confirmation"] = analysis.get("volume_confirmation")
                    elif strategy_name == "volume":
                        indicators["volume_ratio"] = analysis.get("volume_ratio")
                        indicators["price_change"] = analysis.get("price_change")
                        indicators["volatility_ratio"] = analysis.get("volatility_ratio")
            
            signal.indicators = indicators
            
            self.logger.info(f"Vygenerován signál {signal_type.value} pro {symbol} s důvěryhodností {combined_signal['confidence']:.2f}")
            return signal
            
        except Exception as e:
            self.logger.error(f"Chyba při vyhodnocování strategií pro {symbol}: {e}")
            return None
    
    def get_available_strategies(self) -> Dict[str, Dict[str, Any]]:
        """
        Vrátí seznam dostupných strategií a jejich konfiguraci.
        
        Returns:
            Dict[str, Dict[str, Any]]: Slovník strategií
        """
        strategies = {}
        for name, strategy in self.strategy_manager.get_strategies().items():
            strategies[name] = {
                "name": name,
                "description": strategy.__doc__ or "",
                "enabled": getattr(config.strategies, name).enabled,
                "weight": getattr(config.strategies, name).weight
            }
        return strategies