"""
Služba pro obchodování.
"""
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from src.domain.models.signal import Signal, SignalType
from src.domain.models.position import Position, PositionSide, PositionStatus
from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.application.services.signal_service import SignalService
from src.application.services.strategy_service import StrategyService
from src.application.services.market_data_service import MarketDataService
from src.application.services.notification_service import NotificationService, NotificationType, NotificationPriority
from src.config.config import config
from src.models.market_data import Candle, Ticker


# Nastavení loggeru
logger = logging.getLogger(__name__)


class TradingService:
    """Služba pro obchodování."""
    
    def __init__(self, bybit_client: BybitClient = None, 
                 signal_service: SignalService = None,
                 strategy_service: StrategyService = None,
                 market_data_service: MarketDataService = None,
                 notification_service: NotificationService = None):
        """
        Inicializace služby.
        
        Args:
            bybit_client: Klient pro Bybit API
            signal_service: Služba pro zpracování signálů
            strategy_service: Služba pro obchodní strategie
            market_data_service: Služba pro tržní data
            notification_service: Služba pro notifikace
        """
        self.bybit_client = bybit_client or BybitClient()
        self.signal_service = signal_service or SignalService()
        self.strategy_service = strategy_service or StrategyService()
        self.market_data_service = market_data_service or MarketDataService(bybit_client)
        self.notification_service = notification_service or NotificationService()
        self.positions = []
        self.logger = logging.getLogger(__name__)
    
    async def analyze_market(self, symbol: str) -> Dict[str, Any]:
        """
        Analyzuje trh a generuje signály.
        
        Args:
            symbol: Symbol
            
        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        try:
            # Získání tržních dat
            market_data = await self.market_data_service.refresh_market_data(symbol)
            
            # Získání svíček pro analýzu (používáme 1h jako výchozí)
            candles = market_data.get("candles", {}).get("1h", [])
            if not candles:
                self.logger.warning(f"Nedostatek dat pro analýzu {symbol}")
                return {"error": "Nedostatek dat pro analýzu"}
            
            # Získání tickeru
            ticker = market_data.get("ticker")
            
            # Získání otevřených pozic
            positions = self.get_positions(status=PositionStatus.OPEN, symbol=symbol)
            
            # Vyhodnocení strategií
            signal = await self.strategy_service.evaluate_strategies(
                symbol=symbol,
                candles=candles,
                ticker=ticker,
                positions=positions
            )
            
            # Uložení signálu, pokud byl vygenerován
            if signal:
                signal_id = await self.signal_service.save_signal(signal)
                self.logger.info(f"Uložen signál {signal_id} pro {symbol}")
                
                # Notifikace o novém signálu
                self.notification_service.create_notification(
                    title=f"Nový obchodní signál pro {symbol}",
                    message=f"Byl vygenerován nový {signal.type.value} signál s důvěryhodností {signal.confidence:.2f}",
                    type=NotificationType.SIGNAL,
                    priority=NotificationPriority.MEDIUM,
                    data={"signal_id": signal.id, "symbol": symbol, "type": signal.type.value}
                )
                
                # Zpracování signálu do obchodního příkazu
                position = await self.process_signal(signal)
                if position:
                    self.logger.info(f"Vytvořena pozice {position.id} pro {symbol}")
            
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "signal": signal.to_dict() if signal else None,
                "positions": [p.to_dict() for p in positions] if positions else []
            }
            
        except Exception as e:
            error_msg = f"Chyba při analýze trhu pro {symbol}: {e}"
            self.logger.error(error_msg)
            
            # Notifikace o chybě
            self.notification_service.create_notification(
                title=f"Chyba při analýze trhu",
                message=error_msg,
                type=NotificationType.ERROR,
                priority=NotificationPriority.HIGH,
                data={"symbol": symbol, "error": str(e)}
            )
            
            return {"error": str(e)}
    
    async def process_signal(self, signal: Signal) -> Optional[Position]:
        """
        Zpracuje signál a vytvoří pozici.
        
        Args:
            signal: Signál
            
        Returns:
            Optional[Position]: Vytvořená pozice nebo None, pokud není pozice vytvořena
        """
        # Kontrola, zda je signál dostatečně silný
        if signal.confidence < 0.5:
            self.logger.info(f"Signál {signal.id} pro {signal.symbol} má nízkou důvěryhodnost ({signal.confidence}), ignoruji.")
            return None
        
        # Kontrola, zda již nemáme otevřenou pozici pro daný symbol
        open_positions = [p for p in self.positions if p.symbol == signal.symbol and p.status == PositionStatus.OPEN]
        if open_positions:
            self.logger.info(f"Již existuje otevřená pozice pro {signal.symbol}, ignoruji signál.")
            return None
        
        # Kontrola, zda nepřekročíme maximální počet pozic
        open_positions_count = len([p for p in self.positions if p.status == PositionStatus.OPEN])
        if open_positions_count >= config.trading.max_positions:
            self.logger.info(f"Dosažen maximální počet pozic ({config.trading.max_positions}), ignoruji signál.")
            return None
        
        # Výpočet množství
        quantity = self._calculate_quantity(signal.symbol, signal.price)
        
        # Vytvoření pozice
        position_id = str(uuid.uuid4())
        position_side = PositionSide.LONG if signal.type == SignalType.BUY else PositionSide.SHORT
        
        position = Position(
            id=position_id,
            symbol=signal.symbol,
            side=position_side,
            entry_price=signal.price,
            quantity=quantity,
            opened_at=datetime.now(),
            status=PositionStatus.OPEN,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            metadata={"signal_id": signal.id}
        )
        
        # Uložení pozice
        self.positions.append(position)
        
        # Vytvoření objednávky na Bybit
        try:
            order_side = "Buy" if position_side == PositionSide.LONG else "Sell"
            order = await self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                stop_loss=position.stop_loss,
                take_profit=position.take_profit
            )
            
            # Aktualizace metadat pozice
            position.metadata["order_id"] = order.get("orderId", "")
            
            # Notifikace o nové pozici
            self.notification_service.create_notification(
                title=f"Nová pozice otevřena",
                message=f"Otevřena nová {position.side.value} pozice pro {position.symbol} za cenu {position.entry_price}",
                type=NotificationType.POSITION,
                priority=NotificationPriority.HIGH,
                data={
                    "position_id": position.id, 
                    "symbol": position.symbol, 
                    "side": position.side.value,
                    "entry_price": position.entry_price,
                    "quantity": position.quantity
                }
            )
            
            # Notifikace o vytvoření objednávky
            self.notification_service.create_notification(
                title=f"Objednávka vytvořena",
                message=f"Vytvořena {order_side} objednávka pro {position.symbol} za cenu {position.entry_price}",
                type=NotificationType.ORDER,
                priority=NotificationPriority.MEDIUM,
                data={
                    "order_id": order.get("orderId", ""),
                    "symbol": position.symbol,
                    "side": order_side,
                    "price": position.entry_price,
                    "quantity": position.quantity
                }
            )
            
            self.logger.info(f"Vytvořena nová pozice {position.id} pro {position.symbol} ({position.side.value}).")
            return position
        except Exception as e:
            error_msg = f"Chyba při vytváření objednávky: {e}"
            self.logger.error(error_msg)
            position.status = PositionStatus.CANCELLED
            
            # Notifikace o chybě při vytváření objednávky
            self.notification_service.create_notification(
                title=f"Chyba při vytváření objednávky",
                message=error_msg,
                type=NotificationType.ERROR,
                priority=NotificationPriority.HIGH,
                data={
                    "position_id": position.id,
                    "symbol": position.symbol,
                    "side": position.side.value,
                    "error": str(e)
                }
            )
            
            return None
    
    async def update_positions(self) -> List[Position]:
        """
        Aktualizuje stav otevřených pozic.
        
        Returns:
            List[Position]: Seznam aktualizovaných pozic
        """
        # Získání otevřených pozic
        open_positions = [p for p in self.positions if p.status == PositionStatus.OPEN]
        
        # Aktualizace každé pozice
        for position in open_positions:
            try:
                # Získání aktuální ceny
                ticker = await self.market_data_service.get_ticker(position.symbol)
                if not ticker:
                    continue
                
                current_price = ticker.last_price
                
                # Aktualizace pozice
                position.current_price = current_price
                
                # Výpočet nerealizovaného zisku/ztráty
                if position.side == PositionSide.LONG:
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
                
                # Kontrola stop-loss a take-profit
                if position.stop_loss is not None and position.take_profit is not None:
                    if position.side == PositionSide.LONG:
                        if current_price <= position.stop_loss:
                            result = await self._close_position(position, "stop_loss")
                            if result:
                                self.notification_service.create_notification(
                                    title=f"Pozice uzavřena - Stop Loss",
                                    message=f"{position.side.value} pozice pro {position.symbol} uzavřena na stop loss za cenu {current_price}",
                                    type=NotificationType.POSITION,
                                    priority=NotificationPriority.HIGH,
                                    data={
                                        "position_id": position.id,
                                        "symbol": position.symbol,
                                        "side": position.side.value,
                                        "entry_price": position.entry_price,
                                        "exit_price": current_price,
                                        "realized_pnl": position.realized_pnl,
                                        "reason": "stop_loss"
                                    }
                                )
                        elif current_price >= position.take_profit:
                            result = await self._close_position(position, "take_profit")
                            if result:
                                self.notification_service.create_notification(
                                    title=f"Pozice uzavřena - Take Profit",
                                    message=f"{position.side.value} pozice pro {position.symbol} uzavřena na take profit za cenu {current_price}",
                                    type=NotificationType.POSITION,
                                    priority=NotificationPriority.HIGH,
                                    data={
                                        "position_id": position.id,
                                        "symbol": position.symbol,
                                        "side": position.side.value,
                                        "entry_price": position.entry_price,
                                        "exit_price": current_price,
                                        "realized_pnl": position.realized_pnl,
                                        "reason": "take_profit"
                                    }
                                )
                    else:
                        if current_price >= position.stop_loss:
                            result = await self._close_position(position, "stop_loss")
                            if result:
                                self.notification_service.create_notification(
                                    title=f"Pozice uzavřena - Stop Loss",
                                    message=f"{position.side.value} pozice pro {position.symbol} uzavřena na stop loss za cenu {current_price}",
                                    type=NotificationType.POSITION,
                                    priority=NotificationPriority.HIGH,
                                    data={
                                        "position_id": position.id,
                                        "symbol": position.symbol,
                                        "side": position.side.value,
                                        "entry_price": position.entry_price,
                                        "exit_price": current_price,
                                        "realized_pnl": position.realized_pnl,
                                        "reason": "stop_loss"
                                    }
                                )
                        elif current_price <= position.take_profit:
                            result = await self._close_position(position, "take_profit")
                            if result:
                                self.notification_service.create_notification(
                                    title=f"Pozice uzavřena - Take Profit",
                                    message=f"{position.side.value} pozice pro {position.symbol} uzavřena na take profit za cenu {current_price}",
                                    type=NotificationType.POSITION,
                                    priority=NotificationPriority.HIGH,
                                    data={
                                        "position_id": position.id,
                                        "symbol": position.symbol,
                                        "side": position.side.value,
                                        "entry_price": position.entry_price,
                                        "exit_price": current_price,
                                        "realized_pnl": position.realized_pnl,
                                        "reason": "take_profit"
                                    }
                                )
            except Exception as e:
                error_msg = f"Chyba při aktualizaci pozice {position.id}: {e}"
                self.logger.error(error_msg)
                
                # Notifikace o chybě při aktualizaci pozice
                self.notification_service.create_notification(
                    title=f"Chyba při aktualizaci pozice",
                    message=error_msg,
                    type=NotificationType.ERROR,
                    priority=NotificationPriority.HIGH,
                    data={
                        "position_id": position.id,
                        "symbol": position.symbol,
                        "error": str(e)
                    }
                )
        
        return open_positions
    
    async def _close_position(self, position: Position, reason: str) -> bool:
        """
        Uzavře pozici.
        
        Args:
            position: Pozice
            reason: Důvod uzavření
            
        Returns:
            bool: True, pokud byla pozice úspěšně uzavřena
        """
        try:
            # Vytvoření objednávky na Bybit
            order_side = "Sell" if position.side == PositionSide.LONG else "Buy"
            order = await self.bybit_client.place_order(
                symbol=position.symbol,
                side=order_side,
                order_type="Market",
                qty=position.quantity,
                reduce_only=True
            )
            
            # Aktualizace pozice
            position.status = PositionStatus.CLOSED
            position.closed_at = datetime.now()
            position.realized_pnl = position.unrealized_pnl
            position.metadata["close_reason"] = reason
            position.metadata["close_order_id"] = order.get("orderId", "")
            
            # Notifikace o vytvoření uzavírací objednávky
            self.notification_service.create_notification(
                title=f"Uzavírací objednávka vytvořena",
                message=f"Vytvořena {order_side} objednávka pro uzavření pozice {position.symbol}",
                type=NotificationType.ORDER,
                priority=NotificationPriority.MEDIUM,
                data={
                    "order_id": order.get("orderId", ""),
                    "symbol": position.symbol,
                    "side": order_side,
                    "quantity": position.quantity,
                    "position_id": position.id,
                    "reason": reason
                }
            )
            
            self.logger.info(f"Uzavřena pozice {position.id} pro {position.symbol} ({position.side.value}), důvod: {reason}.")
            return True
        except Exception as e:
            error_msg = f"Chyba při uzavírání pozice {position.id}: {e}"
            self.logger.error(error_msg)
            
            # Notifikace o chybě při uzavírání pozice
            self.notification_service.create_notification(
                title=f"Chyba při uzavírání pozice",
                message=error_msg,
                type=NotificationType.ERROR,
                priority=NotificationPriority.HIGH,
                data={
                    "position_id": position.id,
                    "symbol": position.symbol,
                    "side": position.side.value,
                    "error": str(e),
                    "reason": reason
                }
            )
            
            return False
    
    def _calculate_quantity(self, symbol: str, price: float) -> float:
        """
        Vypočítá množství pro objednávku.
        
        Args:
            symbol: Symbol
            price: Aktuální cena
            
        Returns:
            float: Množství
        """
        # Získání velikosti pozice z konfigurace
        position_size = config.trading.position_size
        
        # Výpočet množství
        quantity = position_size / price
        
        # Zaokrouhlení množství podle pravidel burzy
        if symbol == "BTCUSDT":
            quantity = round(quantity, 6)  # BTC má 6 desetinných míst
        elif symbol == "ETHUSDT":
            quantity = round(quantity, 5)  # ETH má 5 desetinných míst
        else:
            quantity = round(quantity, 4)  # Ostatní mají 4 desetinná místa
        
        return quantity
    
    def get_positions(self, status: Optional[PositionStatus] = None, symbol: Optional[str] = None) -> List[Position]:
        """
        Získá seznam pozic.
        
        Args:
            status: Stav pozice pro filtrování
            symbol: Symbol pro filtrování
            
        Returns:
            List[Position]: Seznam pozic
        """
        # Filtrování podle stavu
        filtered_positions = self.positions
        if status:
            filtered_positions = [p for p in filtered_positions if p.status == status]
        
        # Filtrování podle symbolu
        if symbol:
            filtered_positions = [p for p in filtered_positions if p.symbol == symbol]
        
        # Seřazení podle času (nejnovější první)
        return sorted(filtered_positions, key=lambda p: p.opened_at, reverse=True)
    
    def get_open_positions(self) -> List[Position]:
        """
        Získá seznam otevřených pozic.
        
        Returns:
            List[Position]: Seznam otevřených pozic
        """
        return self.get_positions(status=PositionStatus.OPEN)
    
    def get_position_by_id(self, position_id: str) -> Optional[Position]:
        """
        Získá pozici podle ID.
        
        Args:
            position_id: ID pozice
            
        Returns:
            Optional[Position]: Pozice nebo None, pokud není nalezena
        """
        for position in self.positions:
            if position.id == position_id:
                return position
        return None
    
    async def get_market_performance(self, symbol: str, timeframe: str = "1d") -> Dict[str, Any]:
        """
        Získá výkonnost trhu pro daný symbol.
        
        Args:
            symbol: Symbol
            timeframe: Časový rámec pro analýzu
            
        Returns:
            Dict[str, Any]: Výkonnost trhu
        """
        try:
            # Získání svíček
            candles = await self.market_data_service.get_candles(symbol, timeframe, limit=30)
            if not candles:
                return {"error": "Nedostatek dat pro analýzu"}
            
            # Výpočet výkonnosti
            first_candle = candles[0]
            last_candle = candles[-1]
            
            price_change = (last_candle.close - first_candle.open) / first_candle.open * 100
            volatility = sum([abs(c.high - c.low) / c.low * 100 for c in candles]) / len(candles)
            
            # Výpočet maximálního drawdownu
            highest_high = max([c.high for c in candles])
            lowest_low_after_high = min([c.low for c in candles if c.timestamp >= 
                                       candles[[c.high for c in candles].index(highest_high)].timestamp])
            max_drawdown = (lowest_low_after_high - highest_high) / highest_high * 100
            
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "start_date": first_candle.timestamp.isoformat(),
                "end_date": last_candle.timestamp.isoformat(),
                "price_change_percent": price_change,
                "avg_volatility_percent": volatility,
                "max_drawdown_percent": max_drawdown
            }
            
        except Exception as e:
            error_msg = f"Chyba při získávání výkonnosti trhu pro {symbol}: {e}"
            self.logger.error(error_msg)
            
            # Notifikace o chybě při získávání výkonnosti trhu
            self.notification_service.create_notification(
                title=f"Chyba při získávání výkonnosti trhu",
                message=error_msg,
                type=NotificationType.ERROR,
                priority=NotificationPriority.MEDIUM,
                data={
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "error": str(e)
                }
            )
            
            return {"error": str(e)}