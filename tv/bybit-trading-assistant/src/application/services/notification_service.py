"""
Služba pro zasílání notifikací o obchodování.
"""
import logging
from enum import Enum
from typing import Dict, Any, List, Optional
from datetime import datetime


class NotificationType(str, Enum):
    """Typ notifikace."""
    SIGNAL = "SIGNAL"
    ORDER = "ORDER"
    POSITION = "POSITION"
    ERROR = "ERROR"
    INFO = "INFO"
    WARNING = "WARNING"


class NotificationPriority(str, Enum):
    """Priorita notifikace."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class Notification:
    """Model pro notifikaci."""
    
    def __init__(
        self,
        title: str,
        message: str,
        type: NotificationType,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        data: Optional[Dict[str, Any]] = None,
        timestamp: Optional[datetime] = None
    ):
        """
        Inicializace notifikace.
        
        Args:
            title: Titulek notifikace
            message: Zpráva notifikace
            type: Typ notifikace
            priority: Priorita notifikace
            data: Dodatečná data
            timestamp: <PERSON><PERSON>ová značka
        """
        self.title = title
        self.message = message
        self.type = type
        self.priority = priority
        self.data = data or {}
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Převede notifikaci na slovník.
        
        Returns:
            Dict[str, Any]: Slovník s daty notifikace
        """
        return {
            "title": self.title,
            "message": self.message,
            "type": self.type.value,
            "priority": self.priority.value,
            "data": self.data,
            "timestamp": self.timestamp.isoformat()
        }


class NotificationService:
    """Služba pro zasílání notifikací."""
    
    def __init__(self, max_notifications: int = 100):
        """
        Inicializace služby.
        
        Args:
            max_notifications: Maximální počet notifikací v historii
        """
        self._notifications: List[Notification] = []
        self._max_notifications = max_notifications
        self.logger = logging.getLogger(__name__)
    
    def add_notification(self, notification: Notification) -> None:
        """
        Přidá notifikaci.
        
        Args:
            notification: Notifikace
        """
        self._notifications.append(notification)
        
        # Omezení počtu notifikací
        if len(self._notifications) > self._max_notifications:
            self._notifications = self._notifications[-self._max_notifications:]
        
        # Logování
        log_level = logging.INFO
        if notification.priority == NotificationPriority.HIGH:
            log_level = logging.WARNING
        elif notification.priority == NotificationPriority.CRITICAL:
            log_level = logging.ERROR
        
        self.logger.log(log_level, f"[{notification.type}] {notification.title}: {notification.message}")
    
    def create_notification(
        self,
        title: str,
        message: str,
        type: NotificationType,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        data: Optional[Dict[str, Any]] = None
    ) -> Notification:
        """
        Vytvoří a přidá notifikaci.
        
        Args:
            title: Titulek notifikace
            message: Zpráva notifikace
            type: Typ notifikace
            priority: Priorita notifikace
            data: Dodatečná data
            
        Returns:
            Notification: Vytvořená notifikace
        """
        notification = Notification(title, message, type, priority, data)
        self.add_notification(notification)
        return notification
    
    def get_notifications(
        self,
        limit: Optional[int] = None,
        type_filter: Optional[NotificationType] = None,
        min_priority: Optional[NotificationPriority] = None
    ) -> List[Notification]:
        """
        Získá notifikace s možností filtrování.
        
        Args:
            limit: Maximální počet notifikací
            type_filter: Filtr podle typu
            min_priority: Minimální priorita
            
        Returns:
            List[Notification]: Seznam notifikací
        """
        filtered_notifications = self._notifications
        
        # Filtrování podle typu
        if type_filter:
            filtered_notifications = [n for n in filtered_notifications if n.type == type_filter]
        
        # Filtrování podle priority
        if min_priority:
            priority_levels = list(NotificationPriority)
            min_priority_index = priority_levels.index(min_priority)
            filtered_notifications = [
                n for n in filtered_notifications 
                if priority_levels.index(n.priority) >= min_priority_index
            ]
        
        # Seřazení podle času (nejnovější první)
        filtered_notifications = sorted(
            filtered_notifications,
            key=lambda n: n.timestamp,
            reverse=True
        )
        
        # Omezení počtu notifikací
        if limit:
            filtered_notifications = filtered_notifications[:limit]
        
        return filtered_notifications
    
    def get_latest_notification(
        self,
        type_filter: Optional[NotificationType] = None
    ) -> Optional[Notification]:
        """
        Získá nejnovější notifikaci.
        
        Args:
            type_filter: Filtr podle typu
            
        Returns:
            Optional[Notification]: Nejnovější notifikace nebo None
        """
        notifications = self.get_notifications(limit=1, type_filter=type_filter)
        return notifications[0] if notifications else None
    
    def clear_notifications(self) -> None:
        """Vyčistí všechny notifikace."""
        self._notifications = []