"""
Služba pro získávání a zpracování tržních dat.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.infrastructure.external.bybit.bybit_client import BybitClient
from src.models.market_data import Ticker, Candle, OrderBook
from src.config.config import config


class MarketDataService:
    """Služba pro získávání a zpracování tržních dat."""
    
    def __init__(self, bybit_client: BybitClient = None):
        """
        Inicializace služby.
        
        Args:
            bybit_client: Klient pro Bybit API
        """
        self.bybit_client = bybit_client or BybitClient()
        self.logger = logging.getLogger(__name__)
        self.candle_cache = {}  # Mezipaměť pro svíčky
    
    async def get_ticker(self, symbol: str) -> Optional[Ticker]:
        """
        Získá aktuální ticker pro daný symbol.
        
        Args:
            symbol: Symbol
            
        Returns:
            Optional[Ticker]: Ticker nebo None, pokud nastala chyba
        """
        try:
            response = self.bybit_client.get_market_data(symbol)
            return Ticker.from_api_response(response)
        except Exception as e:
            self.logger.error(f"Chyba při získávání tickeru pro {symbol}: {e}")
            return None
    
    async def get_candles(self, symbol: str, interval: str = "1h", limit: int = 100, use_cache: bool = True) -> List[Candle]:
        """
        Získá svíčky pro daný symbol.
        
        Args:
            symbol: Symbol
            interval: Interval svíček (1m, 5m, 15m, 30m, 1h, 4h, 1d)
            limit: Maximální počet svíček
            use_cache: Zda použít mezipaměť
            
        Returns:
            List[Candle]: Seznam svíček
        """
        # Kontrola mezipaměti
        cache_key = f"{symbol}_{interval}"
        if use_cache and cache_key in self.candle_cache:
            cache_data = self.candle_cache[cache_key]
            cache_time = cache_data["timestamp"]
            # Pokud jsou data v mezipaměti dostatečně aktuální, vrátíme je
            if datetime.now() - cache_time < timedelta(minutes=5):
                self.logger.debug(f"Použití mezipaměti pro svíčky {symbol} ({interval})")
                return cache_data["candles"]
        
        try:
            # Převod intervalu na formát Bybit API
            interval_map = {
                "1m": "1",
                "3m": "3",
                "5m": "5",
                "15m": "15",
                "30m": "30",
                "1h": "60",
                "2h": "120",
                "4h": "240",
                "6h": "360",
                "12h": "720",
                "1d": "D",
                "1w": "W",
                "1M": "M"
            }
            
            api_interval = interval_map.get(interval, "60")  # Defaultní 1h
            
            response = self.bybit_client.get_klines(symbol, api_interval, limit)
            candles = [Candle.from_api_response(item) for item in response]
            
            # Uložení do mezipaměti
            self.candle_cache[cache_key] = {
                "timestamp": datetime.now(),
                "candles": candles
            }
            
            return candles
        except Exception as e:
            self.logger.error(f"Chyba při získávání svíček pro {symbol} ({interval}): {e}")
            return []
    
    async def get_orderbook(self, symbol: str, limit: int = 25) -> Optional[OrderBook]:
        """
        Získá knihu objednávek pro daný symbol.
        
        Args:
            symbol: Symbol
            limit: Maximální počet záznamů na každé straně
            
        Returns:
            Optional[OrderBook]: Kniha objednávek nebo None, pokud nastala chyba
        """
        try:
            response = self.bybit_client.get_orderbook(symbol, limit)
            return OrderBook.from_api_response(response)
        except Exception as e:
            self.logger.error(f"Chyba při získávání knihy objednávek pro {symbol}: {e}")
            return None
    
    async def refresh_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        Aktualizuje tržní data pro daný symbol.
        
        Args:
            symbol: Symbol
            
        Returns:
            Dict[str, Any]: Aktualizovaná tržní data
        """
        data = {}
        
        # Získání tickeru
        ticker = self.get_ticker(symbol)
        if ticker:
            data["ticker"] = ticker
        
        # Získání svíček pro různé časové rámce
        intervals = ["5m", "15m", "1h", "4h", "1d"]
        candles = {}
        for interval in intervals:
            candles[interval] = self.get_candles(symbol, interval, limit=100, use_cache=False)
        data["candles"] = candles
        
        # Získání knihy objednávek
        try:
            orderbook = self.get_orderbook(symbol)
            if orderbook:
                data["orderbook"] = orderbook
        except Exception as e:
            self.logger.error(f"Chyba při získávání knihy objednávek pro {symbol}: {e}")
            # Prázdný orderbook jako fallback
            data["orderbook"] = None
        
        return data