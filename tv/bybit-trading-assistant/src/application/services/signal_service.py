"""
Služba pro zpracování signálů.
"""
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.domain.models.signal import Signal, SignalType, SignalMetadata, SignalSource
from src.config.config import config
from src.infrastructure.persistence.repository_factory import repository_factory


class SignalService:
    """Služba pro zpracování signálů."""
    
    def __init__(self):
        """Inicializace služby."""
        self.logger = logging.getLogger(__name__)
        self.signal_repository = repository_factory.create_signal_repository()
        self.indicator_repository = repository_factory.create_indicator_repository()
    
    async def create_signal_from_indicators(self, symbol: str, price: float, indicators: Dict[str, float]) -> Optional[Signal]:
        """
        Vytvoří signál na základě technických indikátorů.
        
        Args:
            symbol: Symbol
            price: Aktuální cena
            indicators: Technické indikátory
            
        Returns:
            Optional[Signal]: Vytvoř<PERSON><PERSON> signál nebo None, pokud není signál generov<PERSON>
        """
        # Získání hodnot indikátorů
        rsi = indicators.get("rsi", 50.0)
        macd = indicators.get("macd", 0.0)
        macd_signal = indicators.get("macd_signal", 0.0)
        
        # Výchozí hodnoty
        signal_type = SignalType.NEUTRAL
        confidence = 0.0
        
        # Pravidla pro generování signálů
        
        # RSI pravidla
        if rsi < 30:
            # Přeprodaný trh - BUY signál
            signal_type = SignalType.BUY
            confidence += 0.3
        elif rsi > 70:
            # Překoupený trh - SELL signál
            signal_type = SignalType.SELL
            confidence += 0.3
        
        # MACD pravidla
        if macd > macd_signal:
            # MACD nad signální linií - BUY signál
            if signal_type == SignalType.BUY:
                confidence += 0.3
            elif signal_type == SignalType.NEUTRAL:
                signal_type = SignalType.BUY
                confidence += 0.2
        elif macd < macd_signal:
            # MACD pod signální linií - SELL signál
            if signal_type == SignalType.SELL:
                confidence += 0.3
            elif signal_type == SignalType.NEUTRAL:
                signal_type = SignalType.SELL
                confidence += 0.2
        
        # Pokud je signál neutrální nebo má nízkou důvěryhodnost, nevrátíme žádný signál
        if signal_type == SignalType.NEUTRAL or confidence < 0.3:
            return None
        
        # Výpočet stop-loss a take-profit
        stop_loss = None
        take_profit = None
        
        if signal_type == SignalType.BUY:
            stop_loss = price * (1 - config.trading.stop_loss_percentage / 100)
            take_profit = price * (1 + config.trading.take_profit_percentage / 100)
        elif signal_type == SignalType.SELL:
            stop_loss = price * (1 + config.trading.stop_loss_percentage / 100)
            take_profit = price * (1 - config.trading.take_profit_percentage / 100)
        
        # Vytvoření signálu
        signal_id = str(uuid.uuid4())
        metadata = SignalMetadata(
            source=SignalSource.SYSTEM,
            timestamp=datetime.now(),
            correlation_id=None,
            raw_data={"indicators": indicators}
        )
        
        signal = Signal(
            id=signal_id,
            symbol=symbol,
            type=signal_type,
            price=price,
            metadata=metadata,
            confidence=confidence,
            indicators=indicators,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Uložení signálu do databáze
        try:
            await self.signal_repository.save(signal)
            self.logger.info(f"Signál {signal_id} byl uložen do databáze")
        except Exception as e:
            self.logger.error(f"Chyba při ukládání signálu do databáze: {e}")
        
        # Uložení indikátorů do databáze
        try:
            indicators_data = {
                "symbol": symbol,
                "timestamp": datetime.now(),
                "interval": "1h",  # Výchozí hodnota
                "price": price,
                **indicators
            }
            await self.indicator_repository.save(indicators_data)
            self.logger.debug(f"Indikátory pro {symbol} byly uloženy do databáze")
        except Exception as e:
            self.logger.error(f"Chyba při ukládání indikátorů do databáze: {e}")
        
        return signal
    
    async def get_signals(self, symbol: Optional[str] = None, limit: int = 10) -> List[Signal]:
        """
        Získá seznam signálů.
        
        Args:
            symbol: Symbol pro filtrování
            limit: Maximální počet signálů
            
        Returns:
            List[Signal]: Seznam signálů
        """
        try:
            if symbol:
                signals = await self.signal_repository.get_by_symbol(symbol, limit)
            else:
                signals = await self.signal_repository.get_all()
                # Omezení počtu signálů
                signals = sorted(signals, key=lambda s: s.metadata.timestamp, reverse=True)[:limit]
            
            return signals
        except Exception as e:
            self.logger.error(f"Chyba při získávání signálů z databáze: {e}")
            return []
    
    async def get_latest_signal(self, symbol: str) -> Optional[Signal]:
        """
        Získá nejnovější signál pro daný symbol.
        
        Args:
            symbol: Symbol
            
        Returns:
            Optional[Signal]: Nejnovější signál nebo None, pokud není žádný signál
        """
        signals = await self.get_signals(symbol, limit=1)
        return signals[0] if signals else None
    
    async def save_signal(self, signal: Signal) -> str:
        """
        Uloží signál do databáze.
        
        Args:
            signal: Signál k uložení
            
        Returns:
            str: ID signálu
        """
        try:
            signal_id = await self.signal_repository.save(signal)
            self.logger.info(f"Signál {signal_id} byl uložen do databáze")
            return signal_id
        except Exception as e:
            self.logger.error(f"Chyba při ukládání signálu do databáze: {e}")
            return signal.id