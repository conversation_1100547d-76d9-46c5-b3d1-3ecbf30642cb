"""
Služba pro získávání signálů z TradingView.
"""
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

from src.domain.models.signal import Signal, SignalType, SignalSource, SignalMetadata
from src.infrastructure.external.tradingview.tradingview_api_adapter import TradingViewApiAdapter
from src.infrastructure.external.tradingview.tradingview_node_adapter import TradingViewNodeAdapter
from src.infrastructure.persistence.repository_factory import repository_factory
from src.config.config import config


class TradingViewService:
    """Služba pro získávání signálů z TradingView."""
    
    def __init__(self):
        """Inicializace služby."""
        self.logger = logging.getLogger(__name__)
        
        # Inicializace adaptérů
        self.api_adapter = TradingViewApiAdapter()
        self.node_adapter = TradingViewNodeAdapter()
        
        # Inicializace repozitářů
        self.signal_repository = repository_factory.create_signal_repository()
        self.indicator_repository = repository_factory.create_indicator_repository()
        
        # Nastavení z konfigurace
        self.symbols = config.trading.symbols
        self.interval = "1h"  # Výchozí interval
        self.check_interval = config.trading.check_interval  # Interval kontroly v sekundách
        
        self.logger.info(f"TradingView služba inicializována pro symboly: {self.symbols}")
    
    async def get_signals(self) -> List[Signal]:
        """
        Získá signály z TradingView.
        
        Returns:
            List[Signal]: Seznam signálů
        """
        try:
            signals = []
            
            # Nejprve zkusíme použít Python adaptér
            try:
                api_signals = await self.api_adapter.get_signals(self.symbols, self.interval)
                if api_signals:
                    self.logger.info(f"Získáno {len(api_signals)} signálů z TradingView API adaptéru")
                    # Uložení signálů do databáze
                    for signal in api_signals:
                        try:
                            await self.signal_repository.save(signal)
                            self.logger.debug(f"Signál {signal.id} byl uložen do databáze")
                        except Exception as e:
                            self.logger.error(f"Chyba při ukládání signálu do databáze: {e}")
                    
                    signals.extend(api_signals)
            except Exception as e:
                self.logger.warning(f"Chyba při získávání signálů z TradingView API adaptéru: {e}")
            
            # Pokud Python adaptér selhal nebo nevrátil žádné signály, použijeme Node.js adaptér
            if not signals:
                try:
                    node_signals = await self.node_adapter.get_signals(self.symbols, self.interval)
                    if node_signals:
                        self.logger.info(f"Získáno {len(node_signals)} signálů z TradingView Node.js adaptéru")
                        # Uložení signálů do databáze
                        for signal in node_signals:
                            try:
                                await self.signal_repository.save(signal)
                                self.logger.debug(f"Signál {signal.id} byl uložen do databáze")
                            except Exception as e:
                                self.logger.error(f"Chyba při ukládání signálu do databáze: {e}")
                        
                        signals.extend(node_signals)
                except Exception as e:
                    self.logger.warning(f"Chyba při získávání signálů z TradingView Node.js adaptéru: {e}")
            
            # Pokud oba adaptéry selhaly, vytvoříme simulovaný signál (pouze pro testování)
            if not signals and config.environment == "development":
                self.logger.warning("Nepodařilo se získat žádné signály z TradingView, vytvářím simulovaný signál")
                
                signal = Signal(
                    id=str(uuid.uuid4()),
                    symbol="BTCUSDT",
                    type=SignalType.BUY,
                    price=50000.0,
                    metadata=SignalMetadata(
                        source=SignalSource.SYSTEM,
                        timestamp=datetime.now(),
                        correlation_id=str(uuid.uuid4())
                    ),
                    confidence=0.8,
                    indicators={
                        "rsi": 45.0,
                        "macd": 100.0,
                        "macd_signal": 90.0
                    },
                    stop_loss=47500.0,
                    take_profit=55000.0
                )
                
                # Uložení simulovaného signálu do databáze
                try:
                    await self.signal_repository.save(signal)
                    self.logger.debug(f"Simulovaný signál {signal.id} byl uložen do databáze")
                except Exception as e:
                    self.logger.error(f"Chyba při ukládání simulovaného signálu do databáze: {e}")
                
                signals.append(signal)
            
            return signals
        except Exception as e:
            self.logger.error(f"Neočekávaná chyba při získávání signálů z TradingView: {e}")
            return []
    
    async def get_technical_indicators(self, symbol: str) -> Dict[str, Any]:
        """
        Získá technické indikátory z TradingView.
        
        Args:
            symbol: Symbol
            
        Returns:
            Dict[str, Any]: Technické indikátory
        """
        try:
            indicators = None
            
            # Nejprve zkusíme použít Node.js adaptér
            try:
                indicators = await self.node_adapter.get_technical_indicators(symbol, self.interval)
                if indicators:
                    self.logger.info(f"Získány indikátory pro {symbol} z Node.js adaptéru")
                    
                    # Uložení indikátorů do databáze
                    try:
                        indicators_data = {
                            "symbol": symbol,
                            "timestamp": datetime.now(),
                            "interval": self.interval,
                            "price": indicators.get("price", 0.0),
                            **indicators
                        }
                        await self.indicator_repository.save(indicators_data)
                        self.logger.debug(f"Indikátory pro {symbol} byly uloženy do databáze")
                    except Exception as e:
                        self.logger.error(f"Chyba při ukládání indikátorů do databáze: {e}")
                    
                    return indicators
            except Exception as e:
                self.logger.warning(f"Chyba při získávání indikátorů z TradingView Node.js adaptéru: {e}")
            
            # Pokud Node.js adaptér selhal, použijeme Python adaptér
            if not indicators:
                try:
                    analysis = await self.api_adapter.get_analysis(symbol, self.interval)
                    if analysis and "indicators" in analysis:
                        self.logger.info(f"Získány indikátory pro {symbol} z Python adaptéru")
                        indicators = analysis["indicators"]
                        
                        # Uložení indikátorů do databáze
                        try:
                            indicators_data = {
                                "symbol": symbol,
                                "timestamp": datetime.now(),
                                "interval": self.interval,
                                "price": indicators.get("close", 0.0),
                                **indicators
                            }
                            await self.indicator_repository.save(indicators_data)
                            self.logger.debug(f"Indikátory pro {symbol} byly uloženy do databáze")
                        except Exception as e:
                            self.logger.error(f"Chyba při ukládání indikátorů do databáze: {e}")
                        
                        return indicators
                except Exception as e:
                    self.logger.warning(f"Chyba při získávání indikátorů z TradingView Python adaptéru: {e}")
            
            # Pokud oba adaptéry selhaly, vrátíme simulované indikátory
            self.logger.warning(f"Nepodařilo se získat žádné indikátory z TradingView pro {symbol}")
            
            # Získáme poslední uložené indikátory z databáze
            try:
                stored_indicators = await self.indicator_repository.get_latest(symbol, self.interval)
                if stored_indicators:
                    self.logger.info(f"Získány poslední uložené indikátory pro {symbol} z databáze")
                    return stored_indicators
            except Exception as e:
                self.logger.error(f"Chyba při získávání indikátorů z databáze: {e}")
            
            # Pokud nemáme ani uložené indikátory, vrátíme simulované hodnoty
            self.logger.warning(f"Používám simulované indikátory pro {symbol}")
            return {
                "rsi": 45.0,
                "macd": 100.0,
                "macd_signal": 90.0,
                "bb_upper": 52000.0,
                "bb_middle": 50000.0,
                "bb_lower": 48000.0
            }
        except Exception as e:
            self.logger.error(f"Neočekávaná chyba při získávání indikátorů z TradingView pro {symbol}: {e}")
            return {}
    
    async def get_historical_indicators(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Získá historii technických indikátorů z databáze.
        
        Args:
            symbol: Symbol
            limit: Maximální počet záznamů
            
        Returns:
            List[Dict[str, Any]]: Seznam technických indikátorů
        """
        try:
            indicators = await self.indicator_repository.get_history(symbol, self.interval, limit)
            self.logger.info(f"Získáno {len(indicators)} historických indikátorů pro {symbol}")
            return indicators
        except Exception as e:
            self.logger.error(f"Chyba při získávání historických indikátorů z databáze: {e}")
            return []
    
    async def start_signal_monitoring(self, callback):
        """
        Spustí monitorování signálů z TradingView.
        
        Args:
            callback: Funkce, která bude volána při získání signálů
        """
        self.logger.info("Spuštěno monitorování signálů z TradingView")
        
        while True:
            try:
                # Získání signálů
                signals = await self.get_signals()
                
                # Volání callback funkce
                if signals:
                    await callback(signals)
                
                # Čekání na další kontrolu
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Chyba při monitorování signálů z TradingView: {e}")
                await asyncio.sleep(10)  # Kratší interval při chybě