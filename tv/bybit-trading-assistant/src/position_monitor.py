"""
Modul pro monitorování pozic na Bybit.
"""
import os
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from tabulate import tabulate
from colorama import Fore, Style, init

from api.wrappers.bybit_api_wrapper import BybitApiWrapper

# Inicializace colorama
init(autoreset=True)

# Načtení proměnných prostředí
load_dotenv()

# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/position_monitor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PositionMonitor:
    """
    Třída pro monitorování pozic na Bybit.
    """

    def __init__(self, config_path: str = "config/config.json", testnet: bool = False):
        """
        Inicializace monitoru pozic.

        Args:
            config_path: Cesta ke konfiguračnímu souboru
            testnet: Zda používat testnet (vývojové prostředí)
        """
        self.config_path = config_path
        self.testnet = testnet
        self.config = self._load_config()
        
        # Inicializace API klienta
        api_key = os.getenv("BYBIT_API_KEY") or self.config.get("api", {}).get("bybit", {}).get("api_key", "")
        api_secret = os.getenv("BYBIT_API_SECRET") or self.config.get("api", {}).get("bybit", {}).get("api_secret", "")
        
        if not api_key or not api_secret:
            logger.warning("API klíče nejsou nastaveny. Některé funkce nebudou dostupné.")
            
        self.client = BybitApiWrapper(api_key, api_secret, testnet)
        
        # Nastavení symbolů
        self.symbols = self.config.get("trading", {}).get("default_symbols", ["BTCUSDT", "ETHUSDT", "SOLUSDT"])
        
        # Nastavení intervalu obnovení
        self.refresh_interval = self.config.get("trading", {}).get("refresh_interval", 10)

    def _load_config(self) -> Dict[str, Any]:
        """
        Načte konfigurační soubor.

        Returns:
            Dict[str, Any]: Konfigurace
        """
        try:
            with open(self.config_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {self.config_path} nebyl nalezen.")
            return {}
        except json.JSONDecodeError:
            logger.error(f"Konfigurační soubor {self.config_path} není platný JSON.")
            return {}

    def get_account_info(self) -> Dict[str, Any]:
        """
        Získá informace o účtu.

        Returns:
            Dict[str, Any]: Informace o účtu
        """
        return self.client.get_account_info()

    def get_positions(self, category: str = "linear") -> List[Dict[str, Any]]:
        """
        Získá seznam otevřených pozic.

        Args:
            category: Kategorie (spot, linear, inverse)

        Returns:
            List[Dict[str, Any]]: Seznam otevřených pozic
        """
        positions = []
        
        for symbol in self.symbols:
            result = self.client.get_positions(category, symbol)
            
            if "result" in result and "list" in result["result"]:
                positions.extend(result["result"]["list"])
                
        return positions

    def get_market_data(self, category: str = "linear") -> Dict[str, Dict[str, Any]]:
        """
        Získá tržní data pro sledované symboly.

        Args:
            category: Kategorie (spot, linear, inverse)

        Returns:
            Dict[str, Dict[str, Any]]: Tržní data
        """
        market_data = {}
        
        for symbol in self.symbols:
            result = self.client.get_market_data(category, symbol)
            
            if "result" in result and "list" in result["result"]:
                for item in result["result"]["list"]:
                    if item["symbol"] == symbol:
                        market_data[symbol] = item
                        break
                        
        return market_data

    def display_positions(self, positions: List[Dict[str, Any]], market_data: Dict[str, Dict[str, Any]]):
        """
        Zobrazí otevřené pozice.

        Args:
            positions: Seznam otevřených pozic
            market_data: Tržní data
        """
        if not positions:
            print(f"{Fore.YELLOW}Žádné otevřené pozice.")
            return
            
        # Příprava dat pro tabulku
        table_data = []
        
        for position in positions:
            symbol = position.get("symbol", "")
            side = position.get("side", "")
            size = float(position.get("size", 0))
            
            if size == 0:
                continue
                
            entry_price = float(position.get("entryPrice", 0))
            leverage = float(position.get("leverage", 1))
            
            # Získání aktuální ceny z tržních dat
            current_price = 0.0
            if symbol in market_data:
                current_price = float(market_data[symbol].get("lastPrice", 0))
                
            # Výpočet zisku/ztráty
            pnl = float(position.get("unrealisedPnl", 0))
            pnl_percentage = 0.0
            
            if entry_price > 0 and current_price > 0:
                if side == "Buy":
                    pnl_percentage = ((current_price - entry_price) / entry_price) * 100
                else:
                    pnl_percentage = ((entry_price - current_price) / entry_price) * 100
                    
            # Určení barvy pro zisk/ztrátu
            if pnl > 0:
                pnl_color = Fore.GREEN
            elif pnl < 0:
                pnl_color = Fore.RED
            else:
                pnl_color = Fore.WHITE
                
            # Přidání řádku do tabulky
            table_data.append([
                symbol,
                f"{Fore.BLUE if side == 'Buy' else Fore.MAGENTA}{side}{Style.RESET_ALL}",
                f"{size:.6f}",
                f"{entry_price:.2f}",
                f"{current_price:.2f}",
                f"{leverage}x",
                f"{pnl_color}{pnl:.2f} USD{Style.RESET_ALL}",
                f"{pnl_color}{pnl_percentage:.2f}%{Style.RESET_ALL}"
            ])
            
        # Zobrazení tabulky
        headers = ["Symbol", "Strana", "Velikost", "Vstupní cena", "Aktuální cena", "Páka", "PnL", "PnL %"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))

    def display_account_info(self, account_info: Dict[str, Any]):
        """
        Zobrazí informace o účtu.

        Args:
            account_info: Informace o účtu
        """
        if "result" not in account_info or "list" not in account_info["result"]:
            print(f"{Fore.YELLOW}Nelze získat informace o účtu.")
            return
            
        account_list = account_info["result"]["list"]
        
        if not account_list:
            print(f"{Fore.YELLOW}Žádné informace o účtu.")
            return
            
        # Příprava dat pro tabulku
        table_data = []
        
        for account in account_list:
            account_type = account.get("accountType", "")
            
            if "coin" not in account:
                continue
                
            for coin in account["coin"]:
                coin_name = coin.get("coin", "")
                available_balance = float(coin.get("availableToWithdraw", 0))
                wallet_balance = float(coin.get("walletBalance", 0))
                
                # Přidání řádku do tabulky
                table_data.append([
                    account_type,
                    coin_name,
                    f"{available_balance:.8f}",
                    f"{wallet_balance:.8f}"
                ])
                
        # Zobrazení tabulky
        headers = ["Typ účtu", "Měna", "Dostupný zůstatek", "Celkový zůstatek"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))

    def run(self):
        """
        Spustí monitorování pozic.
        """
        print(f"{Fore.CYAN}=== Bybit Trading Assistant - Monitor pozic ==={Style.RESET_ALL}")
        print(f"Testnet: {self.testnet}")
        print(f"Sledované symboly: {', '.join(self.symbols)}")
        print(f"Interval obnovení: {self.refresh_interval} sekund")
        print()
        
        try:
            while True:
                # Získání dat
                account_info = self.get_account_info()
                market_data = self.get_market_data("linear")
                positions = self.get_positions("linear")
                
                # Vyčištění obrazovky
                os.system('cls' if os.name == 'nt' else 'clear')
                
                # Zobrazení času
                print(f"{Fore.CYAN}=== Bybit Trading Assistant - Monitor pozic ==={Style.RESET_ALL}")
                print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Testnet: {self.testnet}")
                print()
                
                # Zobrazení informací o účtu
                print(f"{Fore.CYAN}=== Informace o účtu ==={Style.RESET_ALL}")
                self.display_account_info(account_info)
                print()
                
                # Zobrazení pozic
                print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
                self.display_positions(positions, market_data)
                print()
                
                # Čekání na další obnovení
                print(f"Další obnovení za {self.refresh_interval} sekund. Stiskněte Ctrl+C pro ukončení.")
                time.sleep(self.refresh_interval)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Monitorování pozic ukončeno.{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"Chyba při monitorování pozic: {e}")
            print(f"\n{Fore.RED}Chyba při monitorování pozic: {e}{Style.RESET_ALL}")


def main():
    """
    Hlavní funkce.
    """
    parser = argparse.ArgumentParser(description="Bybit Trading Assistant - Monitor pozic")
    parser.add_argument("--config", type=str, default="config/config.json", help="Cesta ke konfiguračnímu souboru")
    parser.add_argument("--testnet", action="store_true", help="Použít testnet (vývojové prostředí)")
    args = parser.parse_args()
    
    monitor = PositionMonitor(args.config, args.testnet)
    monitor.run()


if __name__ == "__main__":
    main()
