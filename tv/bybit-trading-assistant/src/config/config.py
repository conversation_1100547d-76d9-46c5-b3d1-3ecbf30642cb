"""
Konfigurační soubor pro aplikaci.
"""
import os
import json
from pathlib import Path
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional

# Import API kl<PERSON><PERSON>
try:
    from config.api_keys import BYBIT_API_KEY, BYBIT_API_SECRET, TRADINGVIEW_USERNAME, TRADINGVIEW_PASSWORD, OPENAI_API_KEY
except ImportError:
    BYBIT_API_KEY = os.environ.get("BYBIT_API_KEY", "")
    BYBIT_API_SECRET = os.environ.get("BYBIT_API_SECRET", "")
    TRADINGVIEW_USERNAME = os.environ.get("TRADINGVIEW_USERNAME", "")
    TRADINGVIEW_PASSWORD = os.environ.get("TRADINGVIEW_PASSWORD", "")
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")


@dataclass
class BybitConfig:
    """Konfigurace pro Bybit API."""
    api_key: str = BYBIT_API_KEY
    api_secret: str = BYBIT_API_SECRET
    testnet: bool = False
    base_url: str = "https://api.bybit.com"


@dataclass
class TradingViewConfig:
    """Konfigurace pro TradingView API."""
    username: str = TRADINGVIEW_USERNAME
    password: str = TRADINGVIEW_PASSWORD
    webhook_path: str = "/webhook"
    webhook_secret: str = ""


@dataclass
class OpenAIConfig:
    """Konfigurace pro OpenAI API."""
    api_key: str = OPENAI_API_KEY
    model: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: int = 1000


@dataclass
class TradingConfig:
    """Konfigurace pro obchodování."""
    symbols: List[str] = field(default_factory=lambda: ["BTCUSDT", "ETHUSDT", "SOLUSDT"])
    position_size: float = 25.0  # Velikost pozice v USD
    max_positions: int = 2  # Maximální počet současných pozic
    stop_loss_percentage: float = 2.0  # Procento pro stop-loss
    take_profit_percentage: float = 5.0  # Procento pro take-profit
    check_interval: int = 60  # Interval kontroly v sekundách


@dataclass
class StrategyConfig:
    """Konfigurace pro obchodní strategie."""
    enabled: bool = True
    weight: float = 1.0
    params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategiesConfig:
    """Konfigurace pro všechny obchodní strategie."""
    trend_following: StrategyConfig = field(default_factory=StrategyConfig)
    rsi_macd: StrategyConfig = field(default_factory=StrategyConfig)
    breakout: StrategyConfig = field(default_factory=StrategyConfig)
    volume: StrategyConfig = field(default_factory=StrategyConfig)


@dataclass
class LoggingConfig:
    """Konfigurace pro logování."""
    level: str = "INFO"
    file: str = "logs/trading_assistant.log"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class DatabaseConfig:
    """Konfigurace pro databázi."""
    enabled: bool = True
    type: str = "sqlite"  # sqlite, mysql, postgresql, ...
    path: str = "data/trading.db"
    auto_migrate: bool = True


@dataclass
class ServerConfig:
    """Konfigurace pro webový server."""
    host: str = "0.0.0.0"
    port: int = 8000
    base_url: str = "http://localhost:8000"


@dataclass
class Config:
    """Hlavní konfigurace aplikace."""
    bybit: BybitConfig = field(default_factory=BybitConfig)
    tradingview: TradingViewConfig = field(default_factory=TradingViewConfig)
    openai: OpenAIConfig = field(default_factory=OpenAIConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    strategies: StrategiesConfig = field(default_factory=StrategiesConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    server: ServerConfig = field(default_factory=ServerConfig)
    environment: str = field(default="development")

    @classmethod
    def load_from_file(cls, file_path: str) -> 'Config':
        """
        Načte konfiguraci ze souboru.
        
        Args:
            file_path: Cesta k konfiguračnímu souboru
            
        Returns:
            Config: Instance konfigurace
        """
        path = Path(file_path)
        if not path.exists():
            return cls()
        
        with open(path, "r") as f:
            config_data = json.load(f)
        
        # Vytvoření instance konfigurace
        config = cls()
        
        # Naplnění údajů z konfiguračního souboru
        if "api" in config_data:
            api_data = config_data["api"]
            
            if "bybit" in api_data:
                bybit_data = api_data["bybit"]
                config.bybit.api_key = bybit_data.get("api_key", config.bybit.api_key)
                config.bybit.api_secret = bybit_data.get("api_secret", config.bybit.api_secret)
                config.bybit.testnet = bybit_data.get("testnet", config.bybit.testnet)
                config.bybit.base_url = bybit_data.get("base_url", config.bybit.base_url)
            
            if "tradingview" in api_data:
                tv_data = api_data["tradingview"]
                config.tradingview.username = tv_data.get("username", config.tradingview.username)
                config.tradingview.password = tv_data.get("password", config.tradingview.password)
            
            if "openai" in api_data:
                openai_data = api_data["openai"]
                config.openai.api_key = openai_data.get("api_key", config.openai.api_key)
                config.openai.model = openai_data.get("model", config.openai.model)
                config.openai.temperature = openai_data.get("temperature", config.openai.temperature)
                config.openai.max_tokens = openai_data.get("max_tokens", config.openai.max_tokens)
        
        if "trading" in config_data:
            trading_data = config_data["trading"]
            config.trading.symbols = trading_data.get("default_symbols", config.trading.symbols)
            config.trading.position_size = trading_data.get("position_size", config.trading.position_size)
            config.trading.max_positions = trading_data.get("max_positions", config.trading.max_positions)
            config.trading.check_interval = trading_data.get("refresh_interval", config.trading.check_interval)
            
            if "risk_management" in trading_data:
                risk_data = trading_data["risk_management"]
                config.trading.stop_loss_percentage = risk_data.get("stop_loss_percentage", config.trading.stop_loss_percentage)
                config.trading.take_profit_percentage = risk_data.get("take_profit_percentage", config.trading.take_profit_percentage)
        
        # Načtení konfigurace strategií
        if "strategies" in config_data:
            strategies_data = config_data["strategies"]
            
            # Konfigurace trend following strategie
            if "trend_following" in strategies_data:
                trend_data = strategies_data["trend_following"]
                config.strategies.trend_following.enabled = trend_data.get("enabled", True)
                config.strategies.trend_following.weight = trend_data.get("weight", 1.0)
                # Kopírování všech parametrů
                for key, value in trend_data.items():
                    if key not in ["enabled", "weight"]:
                        config.strategies.trend_following.params[key] = value
            
            # Konfigurace RSI+MACD strategie
            if "rsi_macd" in strategies_data:
                rsi_macd_data = strategies_data["rsi_macd"]
                config.strategies.rsi_macd.enabled = rsi_macd_data.get("enabled", True)
                config.strategies.rsi_macd.weight = rsi_macd_data.get("weight", 1.0)
                # Kopírování všech parametrů
                for key, value in rsi_macd_data.items():
                    if key not in ["enabled", "weight"]:
                        config.strategies.rsi_macd.params[key] = value
            
            # Konfigurace breakout strategie
            if "breakout" in strategies_data:
                breakout_data = strategies_data["breakout"]
                config.strategies.breakout.enabled = breakout_data.get("enabled", True)
                config.strategies.breakout.weight = breakout_data.get("weight", 1.0)
                # Kopírování všech parametrů
                for key, value in breakout_data.items():
                    if key not in ["enabled", "weight"]:
                        config.strategies.breakout.params[key] = value
            
            # Konfigurace volume strategie
            if "volume" in strategies_data:
                volume_data = strategies_data["volume"]
                config.strategies.volume.enabled = volume_data.get("enabled", True)
                config.strategies.volume.weight = volume_data.get("weight", 1.0)
                # Kopírování všech parametrů
                for key, value in volume_data.items():
                    if key not in ["enabled", "weight"]:
                        config.strategies.volume.params[key] = value
        
        if "logging" in config_data:
            logging_data = config_data["logging"]
            config.logging.level = logging_data.get("level", config.logging.level)
            config.logging.file = logging_data.get("file", config.logging.file)
            
        if "database" in config_data:
            db_data = config_data["database"]
            config.database.enabled = db_data.get("enabled", config.database.enabled)
            config.database.type = db_data.get("type", config.database.type)
            config.database.path = db_data.get("path", config.database.path)
            config.database.auto_migrate = db_data.get("auto_migrate", config.database.auto_migrate)
        
        if "server" in config_data:
            server_data = config_data["server"]
            config.server.host = server_data.get("host", config.server.host)
            config.server.port = server_data.get("port", config.server.port)
            config.server.base_url = server_data.get("base_url", config.server.base_url)
        
        # Nastavení proměnné prostředí
        if "environment" in config_data:
            config.environment = config_data["environment"]
        else:
            config.environment = os.environ.get("ENVIRONMENT", "development")
        
        return config


# Načtení konfigurace
config_path = os.environ.get("CONFIG_PATH", "config/config.json")
config = Config.load_from_file(config_path)