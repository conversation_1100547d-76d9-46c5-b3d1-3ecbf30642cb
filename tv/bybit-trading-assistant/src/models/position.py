"""
Model pro pozice.
"""
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any


class PositionSide(str, Enum):
    """Strana pozice."""
    LONG = "Buy"
    SHORT = "Sell"


class PositionStatus(str, Enum):
    """Stav pozice."""
    OPEN = "open"
    CLOSED = "closed"
    LIQUIDATED = "liquidated"


@dataclass
class Position:
    """Model pro pozici."""
    symbol: str
    side: PositionSide
    size: float
    entry_price: float
    leverage: float
    created_at: datetime
    status: PositionStatus = PositionStatus.OPEN
    take_profit: Optional[float] = None
    stop_loss: Optional[float] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    mark_price: Optional[float] = None
    liquidation_price: Optional[float] = None
    closed_at: Optional[datetime] = None
    position_id: Optional[str] = None
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'Position':
        """
        Vytvoří instanci pozice z odpovědi API.
        
        Args:
            data: Data z API
            
        Returns:
            Position: Instance pozice
        """
        return cls(
            symbol=data.get("symbol", ""),
            side=PositionSide(data.get("side", "Buy")),
            size=float(data.get("size", 0)),
            entry_price=float(data.get("avgPrice", 0)),
            leverage=float(data.get("leverage", 1)),
            created_at=datetime.fromtimestamp(int(data.get("createdTime", 0)) / 1000),
            status=PositionStatus.OPEN,
            take_profit=float(data.get("takeProfit", 0)) or None,
            stop_loss=float(data.get("stopLoss", 0)) or None,
            unrealized_pnl=float(data.get("unrealisedPnl", 0)),
            realized_pnl=float(data.get("realisedPnl", 0)),
            mark_price=float(data.get("markPrice", 0)),
            liquidation_price=float(data.get("liqPrice", 0)) or None,
            position_id=data.get("positionId", None)
        )
    
    def calculate_profit_percentage(self) -> float:
        """
        Vypočítá procentuální zisk/ztrátu.
        
        Returns:
            float: Procentuální zisk/ztráta
        """
        if not self.entry_price or not self.mark_price:
            return 0.0
            
        if self.side == PositionSide.LONG:
            return ((self.mark_price - self.entry_price) / self.entry_price) * 100
        else:
            return ((self.entry_price - self.mark_price) / self.entry_price) * 100
