"""
Model pro objednávky.
"""
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any


class OrderSide(str, Enum):
    """Strana objednávky."""
    BUY = "Buy"
    SELL = "Sell"


class OrderType(str, Enum):
    """Typ objednávky."""
    LIMIT = "Limit"
    MARKET = "Market"


class OrderStatus(str, Enum):
    """Stav objednávky."""
    CREATED = "Created"
    NEW = "New"
    PARTIALLY_FILLED = "PartiallyFilled"
    FILLED = "Filled"
    CANCELLED = "Cancelled"
    REJECTED = "Rejected"


@dataclass
class Order:
    """Model pro objednávku."""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float]
    created_at: datetime
    status: OrderStatus
    order_id: Optional[str] = None
    filled_quantity: float = 0.0
    average_price: Optional[float] = None
    time_in_force: str = "GTC"
    reduce_only: bool = False
    close_on_trigger: bool = False
    updated_at: Optional[datetime] = None
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'Order':
        """
        Vytvoří instanci objednávky z odpovědi API.
        
        Args:
            data: Data z API
            
        Returns:
            Order: Instance objednávky
        """
        return cls(
            symbol=data.get("symbol", ""),
            side=OrderSide(data.get("side", "Buy")),
            order_type=OrderType(data.get("orderType", "Limit")),
            quantity=float(data.get("qty", 0)),
            price=float(data.get("price", 0)) if data.get("price") else None,
            created_at=datetime.fromtimestamp(int(data.get("createdTime", 0)) / 1000),
            status=OrderStatus(data.get("orderStatus", "Created")),
            order_id=data.get("orderId", None),
            filled_quantity=float(data.get("cumExecQty", 0)),
            average_price=float(data.get("avgPrice", 0)) if data.get("avgPrice") else None,
            time_in_force=data.get("timeInForce", "GTC"),
            reduce_only=data.get("reduceOnly", False),
            close_on_trigger=data.get("closeOnTrigger", False),
            updated_at=datetime.fromtimestamp(int(data.get("updatedTime", 0)) / 1000) if data.get("updatedTime") else None
        )
