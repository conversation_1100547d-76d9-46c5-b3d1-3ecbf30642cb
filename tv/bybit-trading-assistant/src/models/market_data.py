"""
Model pro tržní data.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any, Optional


@dataclass
class Ticker:
    """Model pro ticker."""
    symbol: str
    last_price: float
    bid_price: float
    ask_price: float
    volume_24h: float
    price_change_24h: float
    high_price_24h: float
    low_price_24h: float
    timestamp: datetime
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'Ticker':
        """
        Vytvoří instanci tickeru z odpovědi API.
        
        Args:
            data: Data z API
            
        Returns:
            Ticker: Instance tickeru
        """
        return cls(
            symbol=data.get("symbol", ""),
            last_price=float(data.get("lastPrice", 0)),
            bid_price=float(data.get("bid1Price", 0)),
            ask_price=float(data.get("ask1Price", 0)),
            volume_24h=float(data.get("volume24h", 0)),
            price_change_24h=float(data.get("price24hPcnt", 0)) * 100,
            high_price_24h=float(data.get("highPrice24h", 0)),
            low_price_24h=float(data.get("lowPrice24h", 0)),
            timestamp=datetime.now()
        )


@dataclass
class Candle:
    """Model pro svíčku."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    @classmethod
    def from_api_response(cls, data: List[Any]) -> 'Candle':
        """
        Vytvoří instanci svíčky z odpovědi API.
        
        Args:
            data: Data z API [timestamp, open, high, low, close, volume]
            
        Returns:
            Candle: Instance svíčky
        """
        return cls(
            timestamp=datetime.fromtimestamp(int(data[0]) / 1000),
            open=float(data[1]),
            high=float(data[2]),
            low=float(data[3]),
            close=float(data[4]),
            volume=float(data[5])
        )


@dataclass
class OrderBook:
    """Model pro knihu objednávek."""
    symbol: str
    timestamp: datetime
    bids: List[List[float]]  # [cena, množství]
    asks: List[List[float]]  # [cena, množství]
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'OrderBook':
        """
        Vytvoří instanci knihy objednávek z odpovědi API.
        
        Args:
            data: Data z API
            
        Returns:
            OrderBook: Instance knihy objednávek
        """
        # Formát Bybit API V5
        if "symbol" in data:
            return cls(
                symbol=data.get("symbol", ""),
                timestamp=datetime.fromtimestamp(int(data.get("updateId", 0)) / 1000) 
                    if data.get("updateId") else datetime.now(),
                bids=[[float(item[0]), float(item[1])] for item in data.get("bids", [])],
                asks=[[float(item[0]), float(item[1])] for item in data.get("asks", [])]
            )
        # Starší formát nebo alternativní formát
        else:
            return cls(
                symbol=data.get("s", ""),
                timestamp=datetime.fromtimestamp(int(data.get("ts", 0)) / 1000) 
                    if data.get("ts") else datetime.now(),
                bids=[[float(item[0]), float(item[1])] for item in data.get("b", [])],
                asks=[[float(item[0]), float(item[1])] for item in data.get("a", [])]
            )
