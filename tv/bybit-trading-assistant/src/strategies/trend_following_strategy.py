"""
Strategie založená na sledování trendu pomocí klouzavých průměrů.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.strategies.base_strategy import BaseStrategy
from src.models.market_data import Candle, Ticker
from src.models.position import Position, PositionSide
from src.models.order import Order, OrderSide, OrderType

logger = logging.getLogger(__name__)


class TrendFollowingStrategy(BaseStrategy):
    """
    Strategie založená na sledování trendu pomocí klouzavých průměrů.
    
    Používá dvě klouzavé průměry (EMA) - rychlý a pomalý. 
    - Nákupní signál vzniká, když rychlý EMA překříží pomalý EMA zdola nahoru
    - Prodejní signál vzniká, když rychlý EMA překříží pomalý EMA shora dolů
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Inicializace strategie.

        Args:
            config: Konfigurace strategie
        """
        super().__init__(config)
        self.fast_period = config.get("fast_period", 9)
        self.slow_period = config.get("slow_period", 21)
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Inicializace TrendFollowingStrategy s rychlým EMA ({self.fast_period}) a pomalým EMA ({self.slow_period})")

    def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
        """
        Analyzuje trh pomocí klouzavých průměrů.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker

        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        if len(candles) < self.slow_period + 1:
            return {"error": f"Nedostatek dat pro analýzu. Potřeba alespoň {self.slow_period + 1} svíček."}

        # Výpočet klouzavých průměrů
        close_prices = [candle.close for candle in candles]
        fast_ema = self._calculate_ema(close_prices, self.fast_period)
        slow_ema = self._calculate_ema(close_prices, self.slow_period)

        # Zjištění signálu
        current_fast_ema = fast_ema[-1]
        previous_fast_ema = fast_ema[-2]
        current_slow_ema = slow_ema[-1]
        previous_slow_ema = slow_ema[-2]

        # Zjištění signálu na základě křížení
        signal = None
        if previous_fast_ema < previous_slow_ema and current_fast_ema > current_slow_ema:
            signal = "BUY"  # Rychlý EMA překřížil pomalý EMA zdola nahoru
        elif previous_fast_ema > previous_slow_ema and current_fast_ema < current_slow_ema:
            signal = "SELL"  # Rychlý EMA překřížil pomalý EMA shora dolů

        # Výpočet síly trendu
        trend_strength = abs(current_fast_ema - current_slow_ema) / current_slow_ema * 100

        # Příprava výsledků
        return {
            "signal": signal,
            "current_price": candles[-1].close,
            "fast_ema": current_fast_ema,
            "slow_ema": current_slow_ema,
            "trend_strength": trend_strength,
            "confidence": self._calculate_confidence(trend_strength, signal)
        }

    def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
        """
        Generuje obchodní signál na základě analýzy trendu.

        Args:
            analysis: Výsledky analýzy
            positions: Seznam aktuálních pozic

        Returns:
            Optional[Dict[str, Any]]: Obchodní signál nebo None
        """
        if "error" in analysis:
            self.logger.warning(f"Chyba v analýze: {analysis['error']}")
            return None

        # Kontrola, zda máme signál
        signal = analysis.get("signal")
        if not signal:
            return None

        # Kontrola, zda již nemáme otevřenou pozici stejného typu
        for position in positions:
            if position.side == PositionSide.LONG and signal == "BUY":
                return None
            if position.side == PositionSide.SHORT and signal == "SELL":
                return None

        # Příprava signálu
        current_price = analysis["current_price"]
        confidence = analysis["confidence"]
        
        # Výpočet stop-loss a take-profit
        if signal == "BUY":
            side = PositionSide.LONG
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)
        else:  # SELL
            side = PositionSide.SHORT
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)

        return {
            "type": signal,
            "price": current_price,
            "confidence": confidence,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "fast_ema": analysis["fast_ema"],
            "slow_ema": analysis["slow_ema"],
            "trend_strength": analysis["trend_strength"]
        }

    def _calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """
        Vypočítá exponenciální klouzavý průměr.

        Args:
            prices: Seznam cen
            period: Perioda EMA

        Returns:
            List[float]: Seznam hodnot EMA
        """
        if len(prices) < period:
            raise ValueError(f"Nedostatek dat pro výpočet EMA s periodou {period}")

        # Výpočet multiplikátoru
        multiplier = 2 / (period + 1)
        
        # Inicializace EMA - první hodnota je SMA
        ema = [sum(prices[:period]) / period]
        
        # Výpočet EMA pro zbývající ceny
        for i in range(period, len(prices)):
            ema.append((prices[i] - ema[-1]) * multiplier + ema[-1])
        
        # Doplnění hodnot na začátek, aby délka odpovídala vstupnímu seznamu
        return [None] * (period - 1) + ema

    def _calculate_confidence(self, trend_strength: float, signal: Optional[str]) -> float:
        """
        Vypočítá úroveň jistoty signálu na základě síly trendu.

        Args:
            trend_strength: Síla trendu
            signal: Typ signálu

        Returns:
            float: Úroveň jistoty (0.0 - 1.0)
        """
        if not signal:
            return 0.0
        
        # Základní jistota na základě síly trendu
        # Silnější trend znamená vyšší jistotu
        base_confidence = min(trend_strength / 5, 0.8)
        
        # Přidání dodatečné jistoty pro signály v silném trendu
        if trend_strength > 3.0:
            base_confidence += 0.1
        
        # Omezení hodnoty na rozsah 0.0 - 1.0
        return min(max(base_confidence, 0.0), 1.0)