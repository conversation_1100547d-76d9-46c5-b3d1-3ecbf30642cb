"""
Strategie založená na kombinaci RSI a MACD indikátorů.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.strategies.base_strategy import BaseStrategy
from src.models.market_data import Candle, Ticker
from src.models.position import Position, PositionSide
from src.models.order import Order, OrderSide, OrderType

logger = logging.getLogger(__name__)


class RsiMacdStrategy(BaseStrategy):
    """
    Strategie založená na kombinaci RSI a MACD indikátorů.
    
    Generuje signály na základě kombinace těchto indikátorů:
    - BUY: RSI < 30 (přeprodáno) a MACD překříží signální linii zdola nahoru
    - SELL: RSI > 70 (překoupeno) a MACD překříží signální linii shora dolů
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Inicializace strategie.

        Args:
            config: Konfigurace strategie
        """
        super().__init__(config)
        # RSI parametry
        self.rsi_period = config.get("rsi_period", 14)
        self.rsi_overbought = config.get("rsi_overbought", 70)
        self.rsi_oversold = config.get("rsi_oversold", 30)
        
        # MACD parametry
        self.macd_fast = config.get("macd_fast", 12)
        self.macd_slow = config.get("macd_slow", 26)
        self.macd_signal = config.get("macd_signal", 9)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Inicializace RsiMacdStrategy (RSI: {self.rsi_period}, MACD: {self.macd_fast}/{self.macd_slow}/{self.macd_signal})")

    def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
        """
        Analyzuje trh pomocí RSI a MACD.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker

        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        min_period = max(self.rsi_period, self.macd_slow + self.macd_signal)
        if len(candles) < min_period + 10:
            return {"error": f"Nedostatek dat pro analýzu. Potřeba alespoň {min_period + 10} svíček."}

        # Extrakce cen
        close_prices = [candle.close for candle in candles]
        
        # Výpočet RSI
        rsi = self._calculate_rsi(close_prices, self.rsi_period)
        current_rsi = rsi[-1]
        previous_rsi = rsi[-2]
        
        # Výpočet MACD
        macd_line, signal_line, histogram = self._calculate_macd(
            close_prices, self.macd_fast, self.macd_slow, self.macd_signal
        )
        current_macd = macd_line[-1]
        previous_macd = macd_line[-2]
        current_signal = signal_line[-1]
        previous_signal = signal_line[-2]
        current_histogram = histogram[-1]
        previous_histogram = histogram[-2]
        
        # Analýza signálů
        rsi_signal = None
        if current_rsi < self.rsi_oversold:
            rsi_signal = "BUY"
        elif current_rsi > self.rsi_overbought:
            rsi_signal = "SELL"
        
        macd_signal = None
        if previous_macd < previous_signal and current_macd > current_signal:
            macd_signal = "BUY"  # MACD překřížil signální linii zdola nahoru
        elif previous_macd > previous_signal and current_macd < current_signal:
            macd_signal = "SELL"  # MACD překřížil signální linii shora dolů
        
        # Kombinovaný signál
        combined_signal = None
        if rsi_signal == "BUY" and macd_signal == "BUY":
            combined_signal = "BUY"
        elif rsi_signal == "SELL" and macd_signal == "SELL":
            combined_signal = "SELL"
        
        # Výpočet síly signálu
        signal_strength = 0.0
        if combined_signal:
            # RSI síla (jak daleko od neutrální hodnoty 50)
            rsi_strength = abs(current_rsi - 50) / 50
            
            # MACD síla (velikost histogramu)
            macd_strength = abs(current_histogram) / abs(current_macd) if current_macd != 0 else 0
            
            # Kombinovaná síla
            signal_strength = (rsi_strength + macd_strength) / 2
        
        return {
            "signal": combined_signal,
            "current_price": candles[-1].close,
            "rsi": current_rsi,
            "macd": current_macd,
            "macd_signal": current_signal,
            "macd_histogram": current_histogram,
            "signal_strength": signal_strength,
            "confidence": self._calculate_confidence(current_rsi, current_macd, current_signal, combined_signal)
        }

    def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
        """
        Generuje obchodní signál na základě analýzy RSI a MACD.

        Args:
            analysis: Výsledky analýzy
            positions: Seznam aktuálních pozic

        Returns:
            Optional[Dict[str, Any]]: Obchodní signál nebo None
        """
        if "error" in analysis:
            self.logger.warning(f"Chyba v analýze: {analysis['error']}")
            return None

        # Kontrola, zda máme signál
        signal = analysis.get("signal")
        if not signal:
            return None

        # Kontrola, zda je signál dostatečně silný
        confidence = analysis["confidence"]
        if confidence < 0.5:
            self.logger.info(f"Signál {signal} má nízkou důvěryhodnost ({confidence:.2f}), ignoruji.")
            return None

        # Kontrola, zda již nemáme otevřenou pozici stejného typu
        for position in positions:
            if position.side == PositionSide.LONG and signal == "BUY":
                return None
            if position.side == PositionSide.SHORT and signal == "SELL":
                return None

        # Příprava signálu
        current_price = analysis["current_price"]
        
        # Výpočet stop-loss a take-profit
        if signal == "BUY":
            side = PositionSide.LONG
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)
        else:  # SELL
            side = PositionSide.SHORT
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)

        return {
            "type": signal,
            "price": current_price,
            "confidence": confidence,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "indicators": {
                "rsi": analysis["rsi"],
                "macd": analysis["macd"],
                "macd_signal": analysis["macd_signal"],
                "macd_histogram": analysis["macd_histogram"]
            }
        }

    def _calculate_rsi(self, prices: List[float], period: int) -> List[float]:
        """
        Vypočítá RSI (Relative Strength Index).

        Args:
            prices: Seznam cen
            period: Perioda RSI

        Returns:
            List[float]: Seznam hodnot RSI
        """
        if len(prices) < period + 1:
            raise ValueError(f"Nedostatek dat pro výpočet RSI s periodou {period}")
        
        # Výpočet změn cen
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        
        # Oddělení pozitivních a negativních změn
        gains = [max(0, d) for d in deltas]
        losses = [max(0, -d) for d in deltas]
        
        # Výpočet průměrných zisků a ztrát
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        # První hodnota RSI
        rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
        rsi = [100 - (100 / (1 + rs))]
        
        # Výpočet zbývajících hodnot RSI
        for i in range(period, len(deltas)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
            rsi.append(100 - (100 / (1 + rs)))
        
        # Doplnění hodnot na začátek, aby délka odpovídala vstupnímu seznamu
        return [None] * period + rsi

    def _calculate_macd(self, prices: List[float], fast_period: int, slow_period: int, signal_period: int) -> tuple:
        """
        Vypočítá MACD (Moving Average Convergence Divergence).

        Args:
            prices: Seznam cen
            fast_period: Perioda rychlého EMA
            slow_period: Perioda pomalého EMA
            signal_period: Perioda signální linie

        Returns:
            tuple: (macd_line, signal_line, histogram)
        """
        # Výpočet EMA
        fast_ema = self._calculate_ema(prices, fast_period)
        slow_ema = self._calculate_ema(prices, slow_period)
        
        # Výpočet MACD linie
        macd_line = []
        for i in range(len(prices)):
            if fast_ema[i] is not None and slow_ema[i] is not None:
                macd_line.append(fast_ema[i] - slow_ema[i])
            else:
                macd_line.append(None)
        
        # Výpočet signální linie
        valid_macd = [m for m in macd_line if m is not None]
        signal_start_idx = len(macd_line) - len(valid_macd)
        signal_ema = self._calculate_ema(valid_macd, signal_period)
        
        signal_line = [None] * (signal_start_idx + signal_period - 1)
        signal_line.extend(signal_ema)
        
        # Výpočet histogramu
        histogram = []
        for i in range(len(macd_line)):
            if macd_line[i] is not None and signal_line[i] is not None:
                histogram.append(macd_line[i] - signal_line[i])
            else:
                histogram.append(None)
        
        return macd_line, signal_line, histogram

    def _calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """
        Vypočítá exponenciální klouzavý průměr.

        Args:
            prices: Seznam cen
            period: Perioda EMA

        Returns:
            List[float]: Seznam hodnot EMA
        """
        if len(prices) < period:
            raise ValueError(f"Nedostatek dat pro výpočet EMA s periodou {period}")

        # Výpočet multiplikátoru
        multiplier = 2 / (period + 1)
        
        # Inicializace EMA - první hodnota je SMA
        ema = [sum(prices[:period]) / period]
        
        # Výpočet EMA pro zbývající ceny
        for i in range(period, len(prices)):
            ema.append((prices[i] - ema[-1]) * multiplier + ema[-1])
        
        # Doplnění hodnot na začátek, aby délka odpovídala vstupnímu seznamu
        return [None] * (period - 1) + ema

    def _calculate_confidence(self, rsi: float, macd: float, signal: float, combined_signal: Optional[str]) -> float:
        """
        Vypočítá úroveň jistoty signálu.

        Args:
            rsi: Hodnota RSI
            macd: Hodnota MACD
            signal: Hodnota signální linie
            combined_signal: Typ signálu

        Returns:
            float: Úroveň jistoty (0.0 - 1.0)
        """
        if not combined_signal:
            return 0.0
        
        confidence = 0.0
        
        # RSI confidence
        if combined_signal == "BUY":
            # Čím nižší RSI, tím vyšší důvěryhodnost pro BUY
            rsi_confidence = max(0, (self.rsi_oversold - rsi) / 10 + 0.5)
        else:  # SELL
            # Čím vyšší RSI, tím vyšší důvěryhodnost pro SELL
            rsi_confidence = max(0, (rsi - self.rsi_overbought) / 10 + 0.5)
        
        # MACD confidence
        macd_diff = abs(macd - signal)
        macd_confidence = min(macd_diff / 0.5, 0.7)  # Normalizace rozdílu
        
        # Celková důvěryhodnost je průměr RSI a MACD
        confidence = (rsi_confidence + macd_confidence) / 2
        
        # Omezení hodnoty na rozsah 0.0 - 1.0
        return min(max(confidence, 0.0), 1.0)