"""
Modul pro testování obchodních strategií.
"""
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.strategies.strategy_manager import StrategyManager
from src.application.services.market_data_service import MarketDataService
from src.domain.models.position import Position, PositionSide, PositionStatus
from src.models.market_data import Candle, Ticker
from src.config.config import config


class StrategyTester:
    """
    Testování obchodních strategií na historických datech.
    """
    
    def __init__(self, strategy_manager: StrategyManager = None, market_data_service: MarketDataService = None):
        """
        Inicializace testeru.

        Args:
            strategy_manager: Správce strategií
            market_data_service: Služba pro tržní data
        """
        self.strategy_manager = strategy_manager or StrategyManager()
        self.market_data_service = market_data_service or MarketDataService()
        self.logger = logging.getLogger(__name__)
        self.logger.info("Inicializace testeru strategií")
    
    def test_strategy(self, strategy_name: str, symbol: str, timeframe: str = "1h",
                     start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
                     initial_balance: float = 10000.0) -> Dict[str, Any]:
        """
        Testuje strategii na historických datech.

        Args:
            strategy_name: Název strategie
            symbol: Symbol
            timeframe: Časový rámec
            start_date: Počáteční datum
            end_date: Koncové datum
            initial_balance: Počáteční zůstatek

        Returns:
            Dict[str, Any]: Výsledky testu
        """
        # Získání strategie
        strategy = self.strategy_manager.get_strategy(strategy_name)
        if not strategy:
            return {"error": f"Strategie {strategy_name} není registrována"}
        
        # Nastavení výchozích dat, pokud nejsou zadána
        if not end_date:
            end_date = datetime.now()
        if not start_date:
            start_date = end_date - timedelta(days=30)  # Výchozí 30 dní
        
        self.logger.info(f"Testování strategie {strategy_name} pro {symbol} od {start_date} do {end_date}")
        
        try:
            # Získání historických dat
            candles = self.market_data_service.get_candles(symbol, timeframe, limit=1000)
            if not candles:
                return {"error": f"Nedostatek dat pro testování strategie pro {symbol}"}
            
            # Filtrování svíček podle datumu
            filtered_candles = [c for c in candles if start_date <= c.timestamp <= end_date]
            if not filtered_candles:
                return {"error": f"Žádná data pro zadané období {start_date} - {end_date}"}
            
            self.logger.info(f"Získáno {len(filtered_candles)} svíček pro testování")
            
            # Inicializace proměnných pro backtest
            balance = initial_balance
            positions: List[Position] = []
            trades = []
            equity_curve = []
            
            # Simulace obchodování
            for i in range(len(filtered_candles) - 1):
                # Vytvoření "plovoucího okna" historických dat
                historical_candles = filtered_candles[:i+1]
                current_candle = filtered_candles[i]
                next_candle = filtered_candles[i+1]
                
                # Simulace aktuální ceny
                current_price = current_candle.close
                
                # Vytvoření ticker objektu
                ticker = Ticker(
                    symbol=symbol,
                    last_price=current_price,
                    bid_price=current_price * 0.9999,  # Simulace spread
                    ask_price=current_price * 1.0001,  # Simulace spread
                    volume_24h=current_candle.volume,
                    price_change_24h=0.0,
                    high_price_24h=current_candle.high,
                    low_price_24h=current_candle.low,
                    timestamp=current_candle.timestamp
                )
                
                # Aktualizace otevřených pozic
                open_positions = [p for p in positions if p.status == PositionStatus.OPEN]
                for position in open_positions:
                    # Aktualizace ceny
                    position.current_price = current_price
                    
                    # Výpočet nerealizovaného zisku/ztráty
                    if position.side == PositionSide.LONG:
                        position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                    else:
                        position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
                    
                    # Kontrola stop-loss a take-profit v následující svíčce
                    if position.stop_loss is not None and position.take_profit is not None:
                        if position.side == PositionSide.LONG:
                            # Kontrola, zda došlo k aktivaci stop-loss nebo take-profit
                            if next_candle.low <= position.stop_loss:
                                # Stop-loss byl aktivován
                                self._close_position(position, next_candle.open, "stop_loss")
                                # Aktualizace zůstatku
                                balance += position.realized_pnl
                                trades.append({
                                    "symbol": position.symbol,
                                    "side": position.side.value,
                                    "entry_price": position.entry_price,
                                    "exit_price": position.exit_price,
                                    "quantity": position.quantity,
                                    "profit_loss": position.realized_pnl,
                                    "opened_at": position.opened_at.isoformat(),
                                    "closed_at": position.closed_at.isoformat(),
                                    "reason": "stop_loss"
                                })
                            elif next_candle.high >= position.take_profit:
                                # Take-profit byl aktivován
                                self._close_position(position, position.take_profit, "take_profit")
                                # Aktualizace zůstatku
                                balance += position.realized_pnl
                                trades.append({
                                    "symbol": position.symbol,
                                    "side": position.side.value,
                                    "entry_price": position.entry_price,
                                    "exit_price": position.exit_price,
                                    "quantity": position.quantity,
                                    "profit_loss": position.realized_pnl,
                                    "opened_at": position.opened_at.isoformat(),
                                    "closed_at": position.closed_at.isoformat(),
                                    "reason": "take_profit"
                                })
                        else:  # SHORT
                            # Kontrola, zda došlo k aktivaci stop-loss nebo take-profit
                            if next_candle.high >= position.stop_loss:
                                # Stop-loss byl aktivován
                                self._close_position(position, next_candle.open, "stop_loss")
                                # Aktualizace zůstatku
                                balance += position.realized_pnl
                                trades.append({
                                    "symbol": position.symbol,
                                    "side": position.side.value,
                                    "entry_price": position.entry_price,
                                    "exit_price": position.exit_price,
                                    "quantity": position.quantity,
                                    "profit_loss": position.realized_pnl,
                                    "opened_at": position.opened_at.isoformat(),
                                    "closed_at": position.closed_at.isoformat(),
                                    "reason": "stop_loss"
                                })
                            elif next_candle.low <= position.take_profit:
                                # Take-profit byl aktivován
                                self._close_position(position, position.take_profit, "take_profit")
                                # Aktualizace zůstatku
                                balance += position.realized_pnl
                                trades.append({
                                    "symbol": position.symbol,
                                    "side": position.side.value,
                                    "entry_price": position.entry_price,
                                    "exit_price": position.exit_price,
                                    "quantity": position.quantity,
                                    "profit_loss": position.realized_pnl,
                                    "opened_at": position.opened_at.isoformat(),
                                    "closed_at": position.closed_at.isoformat(),
                                    "reason": "take_profit"
                                })
                
                # Aktualizace equity křivky
                equity = balance + sum([p.unrealized_pnl for p in open_positions])
                equity_curve.append({
                    "timestamp": current_candle.timestamp.isoformat(),
                    "equity": equity,
                    "balance": balance,
                    "open_positions": len(open_positions)
                })
                
                # Analýza trhu a generování signálu
                analysis = strategy.analyze(historical_candles, ticker)
                signal = strategy.generate_signal(analysis, open_positions)
                
                # Pokud je generován signál, vytvoříme novou pozici
                if signal:
                    # Výpočet velikosti pozice
                    position_size = balance * 0.02  # 2% z aktuálního zůstatku
                    quantity = position_size / current_price
                    
                    # Vytvoření pozice
                    position_id = f"test-{symbol}-{current_candle.timestamp.isoformat()}"
                    position_side = PositionSide.LONG if signal["type"] == "BUY" else PositionSide.SHORT
                    
                    position = Position(
                        id=position_id,
                        symbol=symbol,
                        side=position_side,
                        entry_price=current_price,
                        quantity=quantity,
                        opened_at=current_candle.timestamp,
                        status=PositionStatus.OPEN,
                        stop_loss=signal["stop_loss"],
                        take_profit=signal["take_profit"],
                        metadata={"signal": signal}
                    )
                    
                    positions.append(position)
            
            # Uzavření všech otevřených pozic na konci testu
            last_candle = filtered_candles[-1]
            last_price = last_candle.close
            
            for position in [p for p in positions if p.status == PositionStatus.OPEN]:
                self._close_position(position, last_price, "test_end")
                # Aktualizace zůstatku
                balance += position.realized_pnl
                trades.append({
                    "symbol": position.symbol,
                    "side": position.side.value,
                    "entry_price": position.entry_price,
                    "exit_price": position.exit_price,
                    "quantity": position.quantity,
                    "profit_loss": position.realized_pnl,
                    "opened_at": position.opened_at.isoformat(),
                    "closed_at": position.closed_at.isoformat(),
                    "reason": "test_end"
                })
            
            # Výpočet statistik
            total_trades = len(trades)
            if total_trades == 0:
                return {
                    "strategy": strategy_name,
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "initial_balance": initial_balance,
                    "final_balance": balance,
                    "profit_loss": balance - initial_balance,
                    "profit_loss_percent": (balance - initial_balance) / initial_balance * 100,
                    "total_trades": 0,
                    "win_rate": 0,
                    "equity_curve": equity_curve,
                    "trades": []
                }
            
            winning_trades = [t for t in trades if t["profit_loss"] > 0]
            losing_trades = [t for t in trades if t["profit_loss"] <= 0]
            
            win_rate = len(winning_trades) / total_trades * 100
            average_win = sum([t["profit_loss"] for t in winning_trades]) / len(winning_trades) if winning_trades else 0
            average_loss = sum([t["profit_loss"] for t in losing_trades]) / len(losing_trades) if losing_trades else 0
            
            profit_factor = abs(sum([t["profit_loss"] for t in winning_trades])) / abs(sum([t["profit_loss"] for t in losing_trades])) if losing_trades and sum([t["profit_loss"] for t in losing_trades]) != 0 else float('inf')
            
            # Výpočet maximálního drawdownu
            peak = initial_balance
            drawdown = 0
            max_drawdown = 0
            
            for point in equity_curve:
                equity = point["equity"]
                if equity > peak:
                    peak = equity
                
                drawdown = (peak - equity) / peak * 100
                max_drawdown = max(max_drawdown, drawdown)
            
            return {
                "strategy": strategy_name,
                "symbol": symbol,
                "timeframe": timeframe,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "initial_balance": initial_balance,
                "final_balance": balance,
                "profit_loss": balance - initial_balance,
                "profit_loss_percent": (balance - initial_balance) / initial_balance * 100,
                "total_trades": total_trades,
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": win_rate,
                "average_win": average_win,
                "average_loss": average_loss,
                "profit_factor": profit_factor,
                "max_drawdown": max_drawdown,
                "equity_curve": equity_curve,
                "trades": trades
            }
            
        except Exception as e:
            self.logger.error(f"Chyba při testování strategie {strategy_name} pro {symbol}: {e}")
            return {"error": str(e)}
    
    def test_all_strategies(self, symbol: str, timeframe: str = "1h",
                           start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
                           initial_balance: float = 10000.0) -> Dict[str, Dict[str, Any]]:
        """
        Testuje všechny strategie na historických datech.

        Args:
            symbol: Symbol
            timeframe: Časový rámec
            start_date: Počáteční datum
            end_date: Koncové datum
            initial_balance: Počáteční zůstatek

        Returns:
            Dict[str, Dict[str, Any]]: Výsledky testů pro všechny strategie
        """
        results = {}
        
        for name in self.strategy_manager.get_strategies():
            result = self.test_strategy(
                strategy_name=name,
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                initial_balance=initial_balance
            )
            results[name] = result
        
        return results
    
    def _close_position(self, position: Position, exit_price: float, reason: str) -> None:
        """
        Uzavře pozici.

        Args:
            position: Pozice
            exit_price: Výstupní cena
            reason: Důvod uzavření
        """
        # Aktualizace pozice
        position.status = PositionStatus.CLOSED
        position.closed_at = datetime.now()
        position.exit_price = exit_price
        
        # Výpočet realizovaného zisku/ztráty
        if position.side == PositionSide.LONG:
            position.realized_pnl = (exit_price - position.entry_price) * position.quantity
        else:
            position.realized_pnl = (position.entry_price - exit_price) * position.quantity
        
        position.metadata["close_reason"] = reason