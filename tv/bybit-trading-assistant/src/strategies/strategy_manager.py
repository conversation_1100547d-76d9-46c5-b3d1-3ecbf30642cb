"""
Spr<PERSON><PERSON>ce obchodních strategií.
"""
import logging
from typing import Dict, Any, List, Optional, Type
from datetime import datetime

from src.strategies.base_strategy import BaseStrategy
from src.strategies.trend_following_strategy import TrendFollowingStrategy
from src.strategies.rsi_macd_strategy import RsiMacdStrategy
from src.strategies.breakout_strategy import BreakoutStrategy
from src.strategies.volume_strategy import VolumeStrategy
from src.models.market_data import Candle, Ticker
from src.models.position import Position
from src.config.config import config

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Správce obchodních strategií.
    
    Umožňuje registrovat, vyhodnocovat a kombinovat různé obchodní strategie.
    """

    def __init__(self):
        """Inicializace správce strategií."""
        self.strategies = {}
        self.strategy_weights = {}
        self.logger = logging.getLogger(__name__)
        self.logger.info("Inicializace správce strategií")
        
        # Registrace výchozích strategií
        self._register_default_strategies()

    def register_strategy(self, name: str, strategy: BaseStrategy, weight: float = 1.0):
        """
        Registruje novou strategii.

        Args:
            name: Název strategie
            strategy: Instance strategie
            weight: Váha strategie při kombinování signálů (výchozí 1.0)
        """
        self.strategies[name] = strategy
        self.strategy_weights[name] = weight
        self.logger.info(f"Registrována strategie: {name} s váhou {weight}")

    def get_strategies(self) -> Dict[str, BaseStrategy]:
        """
        Vrátí slovník všech registrovaných strategií.

        Returns:
            Dict[str, BaseStrategy]: Slovník strategií
        """
        return self.strategies

    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """
        Vrátí strategii podle názvu.

        Args:
            name: Název strategie

        Returns:
            Optional[BaseStrategy]: Instance strategie nebo None, pokud strategie neexistuje
        """
        return self.strategies.get(name)

    def evaluate_strategy(self, name: str, candles: List[Candle], 
                          ticker: Optional[Ticker] = None,
                          positions: Optional[List[Position]] = None) -> Dict[str, Any]:
        """
        Vyhodnotí jednu strategii.

        Args:
            name: Název strategie
            candles: Seznam svíček
            ticker: Aktuální ticker
            positions: Seznam aktuálních pozic

        Returns:
            Dict[str, Any]: Výsledky vyhodnocení strategie
        """
        if name not in self.strategies:
            return {"error": f"Strategie {name} není registrována"}
        
        strategy = self.strategies[name]
        positions = positions or []
        
        try:
            # Analýza trhu
            analysis = strategy.analyze(candles, ticker)
            
            # Generování signálu
            signal = strategy.generate_signal(analysis, positions)
            
            return {
                "strategy": name,
                "analysis": analysis,
                "signal": signal,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Chyba při vyhodnocení strategie {name}: {e}")
            return {
                "strategy": name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def evaluate_all_strategies(self, candles: List[Candle], 
                               ticker: Optional[Ticker] = None,
                               positions: Optional[List[Position]] = None) -> Dict[str, Dict[str, Any]]:
        """
        Vyhodnotí všechny registrované strategie.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker
            positions: Seznam aktuálních pozic

        Returns:
            Dict[str, Dict[str, Any]]: Výsledky vyhodnocení všech strategií
        """
        results = {}
        for name in self.strategies:
            results[name] = self.evaluate_strategy(name, candles, ticker, positions)
        return results

    def combine_signals(self, strategy_results: Dict[str, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Kombinuje signály z různých strategií podle jejich vah.

        Args:
            strategy_results: Výsledky vyhodnocení strategií

        Returns:
            Optional[Dict[str, Any]]: Kombinovaný signál nebo None, pokud není žádný signál
        """
        # Počítání vah pro každý typ signálu
        signal_weights = {"BUY": 0.0, "SELL": 0.0, "NEUTRAL": 0.0}
        signal_confidences = {"BUY": [], "SELL": [], "NEUTRAL": []}
        signal_details = {"BUY": [], "SELL": [], "NEUTRAL": []}
        total_weight = 0.0
        
        # Procházení výsledků
        for name, result in strategy_results.items():
            # Kontrola chyby
            if "error" in result:
                self.logger.warning(f"Strategie {name} vrátila chybu: {result['error']}")
                continue
            
            # Získání signálu
            signal = result.get("signal")
            if not signal:
                continue
            
            signal_type = signal.get("type")
            if not signal_type:
                continue
            
            weight = self.strategy_weights.get(name, 1.0)
            confidence = signal.get("confidence", 0.5)
            
            # Akumulace vah a důvěryhodnosti
            signal_weights[signal_type] += weight * confidence
            signal_confidences[signal_type].append(confidence)
            signal_details[signal_type].append({
                "strategy": name,
                "confidence": confidence,
                "price": signal.get("price"),
                "stop_loss": signal.get("stop_loss"),
                "take_profit": signal.get("take_profit")
            })
            total_weight += weight
        
        if total_weight == 0:
            return None
        
        # Normalizace vah
        for signal_type in signal_weights:
            signal_weights[signal_type] /= total_weight
        
        # Výběr signálu s nejvyšší váhou
        best_signal = max(signal_weights.items(), key=lambda x: x[1])
        
        # Pokud je váha nejvyššího signálu nízká, nevrátíme žádný signál
        if best_signal[1] < 0.2:
            return None
        
        # Výpočet průměrné důvěryhodnosti
        avg_confidence = sum(signal_confidences[best_signal[0]]) / len(signal_confidences[best_signal[0]]) if signal_confidences[best_signal[0]] else 0.0
        
        # Příprava kombinovaného signálu
        # Použijeme průměrné hodnoty z jednotlivých strategií
        price_values = [detail["price"] for detail in signal_details[best_signal[0]] if detail.get("price")]
        stop_loss_values = [detail["stop_loss"] for detail in signal_details[best_signal[0]] if detail.get("stop_loss")]
        take_profit_values = [detail["take_profit"] for detail in signal_details[best_signal[0]] if detail.get("take_profit")]
        
        avg_price = sum(price_values) / len(price_values) if price_values else None
        avg_stop_loss = sum(stop_loss_values) / len(stop_loss_values) if stop_loss_values else None
        avg_take_profit = sum(take_profit_values) / len(take_profit_values) if take_profit_values else None
        
        return {
            "type": best_signal[0],
            "price": avg_price,
            "confidence": avg_confidence,
            "weight": best_signal[1],
            "stop_loss": avg_stop_loss,
            "take_profit": avg_take_profit,
            "supporting_strategies": [detail["strategy"] for detail in signal_details[best_signal[0]]],
            "timestamp": datetime.now().isoformat()
        }

    def _register_default_strategies(self):
        """Registruje výchozí strategie ze souboru konfigurace."""
        try:
            # Registrace strategie sledování trendu
            trend_config = config.strategies.trend_following.params
            if config.strategies.trend_following.enabled:
                trend_strategy = TrendFollowingStrategy(trend_config)
                self.register_strategy("trend_following", trend_strategy, 
                                      config.strategies.trend_following.weight)
            
            # Registrace RSI+MACD strategie
            rsi_macd_config = config.strategies.rsi_macd.params
            if config.strategies.rsi_macd.enabled:
                rsi_macd_strategy = RsiMacdStrategy(rsi_macd_config)
                self.register_strategy("rsi_macd", rsi_macd_strategy, 
                                      config.strategies.rsi_macd.weight)
            
            # Registrace breakout strategie
            breakout_config = config.strategies.breakout.params
            if config.strategies.breakout.enabled:
                breakout_strategy = BreakoutStrategy(breakout_config)
                self.register_strategy("breakout", breakout_strategy, 
                                      config.strategies.breakout.weight)
            
            # Registrace volume strategie
            volume_config = config.strategies.volume.params
            if config.strategies.volume.enabled:
                volume_strategy = VolumeStrategy(volume_config)
                self.register_strategy("volume", volume_strategy, 
                                      config.strategies.volume.weight)
            
            self.logger.info(f"Registrováno {len(self.strategies)} výchozích strategií")
            
        except Exception as e:
            self.logger.error(f"Chyba při registraci výchozích strategií: {e}")
            
            # Registrujeme alespoň jednu strategii s výchozí konfigurací
            self.logger.info("Registruji základní strategii s výchozí konfigurací")
            default_config = {
                "fast_period": 9,
                "slow_period": 21,
                "risk_management": {
                    "stop_loss_percentage": 2.0,
                    "take_profit_percentage": 4.0
                }
            }
            trend_strategy = TrendFollowingStrategy(default_config)
            self.register_strategy("trend_following", trend_strategy, 1.0)