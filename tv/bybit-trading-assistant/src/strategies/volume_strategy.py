"""
Strategie založená na objemu obchodů.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.strategies.base_strategy import BaseStrategy
from src.models.market_data import Candle, Ticker
from src.models.position import Position, PositionSide
from src.models.order import Order, OrderSide, OrderType

logger = logging.getLogger(__name__)


class VolumeStrategy(BaseStrategy):
    """
    Strategie založená na objemu obchodů.
    
    Sleduje neobvyklé změny v objemu obchodů a jejich vztah k pohybům ceny:
    - BUY: Významný nárůst objemu spojený s růstem ceny (akumulace)
    - SELL: Významný nárůst objemu spojený s poklesem ceny (distribuce)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Inicializace strategie.

        Args:
            config: Konfigurace strategie
        """
        super().__init__(config)
        # Parametry pro analýzu objemu
        self.volume_period = config.get("volume_period", 20)
        self.volume_threshold = config.get("volume_threshold", 2.0)  # Násobek průměrného objemu
        self.price_change_threshold = config.get("price_change_threshold", 0.01)  # 1% změna ceny
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Inicializace VolumeStrategy (období: {self.volume_period}, práh objemu: {self.volume_threshold})")

    def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
        """
        Analyzuje trh na základě objemu obchodů.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker

        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        if len(candles) < self.volume_period + 1:
            return {"error": f"Nedostatek dat pro analýzu. Potřeba alespoň {self.volume_period + 1} svíček."}

        # Získání historických dat pro analýzu
        historical_candles = candles[-(self.volume_period+1):-1]
        current_candle = candles[-1]
        
        # Výpočet průměrného objemu
        avg_volume = sum([candle.volume for candle in historical_candles]) / len(historical_candles)
        
        # Výpočet relativního objemu
        volume_ratio = current_candle.volume / avg_volume if avg_volume > 0 else 0
        
        # Výpočet procentuální změny ceny
        price_change = (current_candle.close - current_candle.open) / current_candle.open
        
        # Výpočet průměrné volatility (high-low)
        avg_volatility = sum([(c.high - c.low) / c.low for c in historical_candles]) / len(historical_candles)
        
        # Výpočet aktuální volatility
        current_volatility = (current_candle.high - current_candle.low) / current_candle.low
        
        # Výpočet relativní volatility
        volatility_ratio = current_volatility / avg_volatility if avg_volatility > 0 else 0
        
        # Analýza objemu vzhledem k ceně
        signal = None
        if volume_ratio > self.volume_threshold:
            if price_change > self.price_change_threshold:
                # Významný nárůst objemu spojený s růstem ceny - BUY signál
                signal = "BUY"
            elif price_change < -self.price_change_threshold:
                # Významný nárůst objemu spojený s poklesem ceny - SELL signál
                signal = "SELL"
        
        # Výpočet trendu objemu (stoupající nebo klesající?)
        volume_trend = 0
        if len(historical_candles) >= 5:
            recent_volumes = [c.volume for c in historical_candles[-5:]]
            early_avg = sum(recent_volumes[:2]) / 2
            late_avg = sum(recent_volumes[3:]) / 2
            volume_trend = (late_avg - early_avg) / early_avg if early_avg > 0 else 0
        
        # Síla signálu závisí na objemu a změně ceny
        signal_strength = volume_ratio * abs(price_change) / self.price_change_threshold
        
        return {
            "signal": signal,
            "current_price": current_candle.close,
            "volume_ratio": volume_ratio,
            "price_change": price_change,
            "volatility_ratio": volatility_ratio,
            "volume_trend": volume_trend,
            "signal_strength": signal_strength,
            "confidence": self._calculate_confidence(volume_ratio, abs(price_change), volatility_ratio)
        }

    def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
        """
        Generuje obchodní signál na základě analýzy objemu.

        Args:
            analysis: Výsledky analýzy
            positions: Seznam aktuálních pozic

        Returns:
            Optional[Dict[str, Any]]: Obchodní signál nebo None
        """
        if "error" in analysis:
            self.logger.warning(f"Chyba v analýze: {analysis['error']}")
            return None

        # Kontrola, zda máme signál
        signal = analysis.get("signal")
        if not signal:
            return None

        # Kontrola, zda je signál dostatečně silný
        confidence = analysis["confidence"]
        if confidence < 0.55:
            self.logger.info(f"Signál {signal} má nízkou důvěryhodnost ({confidence:.2f}), ignoruji.")
            return None

        # Kontrola, zda již nemáme otevřenou pozici stejného typu
        for position in positions:
            if position.side == PositionSide.LONG and signal == "BUY":
                return None
            if position.side == PositionSide.SHORT and signal == "SELL":
                return None

        # Příprava signálu
        current_price = analysis["current_price"]
        
        # Výpočet stop-loss a take-profit
        if signal == "BUY":
            side = PositionSide.LONG
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)
        else:  # SELL
            side = PositionSide.SHORT
            stop_loss = self.calculate_stop_loss(current_price, side)
            take_profit = self.calculate_take_profit(current_price, side)

        return {
            "type": signal,
            "price": current_price,
            "confidence": confidence,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "volume_ratio": analysis["volume_ratio"],
            "price_change": analysis["price_change"],
            "volatility_ratio": analysis["volatility_ratio"]
        }

    def _calculate_confidence(self, volume_ratio: float, price_change: float, volatility_ratio: float) -> float:
        """
        Vypočítá úroveň jistoty signálu.

        Args:
            volume_ratio: Poměr aktuálního objemu k průměrnému objemu
            price_change: Absolutní procentuální změna ceny
            volatility_ratio: Poměr aktuální volatility k průměrné volatilitě

        Returns:
            float: Úroveň jistoty (0.0 - 1.0)
        """
        # Základní důvěryhodnost na základě poměru objemu
        volume_confidence = min((volume_ratio - 1) / 5, 0.6)
        
        # Důvěryhodnost na základě změny ceny
        price_confidence = min(price_change / 0.03, 0.3)  # Maximálně 0.3 při 3% změně
        
        # Důvěryhodnost na základě volatility
        volatility_confidence = min(volatility_ratio / 3, 0.1)  # Maximálně 0.1
        
        # Celková důvěryhodnost
        confidence = volume_confidence + price_confidence + volatility_confidence
        
        # Omezení hodnoty na rozsah 0.0 - 1.0
        return min(max(confidence, 0.0), 1.0)