"""
Základní třída pro obchodní strategie.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.models.market_data import Candle, Ticker
from src.models.position import Position, PositionSide
from src.models.order import Order, OrderSide, OrderType

# Nastavení loggeru
logger = logging.getLogger(__name__)


class BaseStrategy(ABC):
    """
    Základní třída pro obchodní strategie.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Inicializace strategie.

        Args:
            config: Konfigurace strategie
        """
        self.config = config
        self.name = self.__class__.__name__
        logger.info(f"Inicializace strategie: {self.name}")

    @abstractmethod
    def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
        """
        Analyzuje trh a vrací signály.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker

        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        pass

    @abstractmethod
    def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
        """
        Generuje obchodní signál na základě analýzy.

        Args:
            analysis: Výsledky analýzy
            positions: Seznam aktuálních pozic

        Returns:
            Optional[Dict[str, Any]]: Obchodní signál nebo None
        """
        pass

    def calculate_position_size(self, price: float, account_balance: float) -> float:
        """
        Vypočítá velikost pozice na základě řízení rizika.

        Args:
            price: Aktuální cena
            account_balance: Zůstatek účtu

        Returns:
            float: Velikost pozice
        """
        # Získání parametrů řízení rizika z konfigurace
        risk_params = self.config.get("risk_management", {})
        max_position_size_usd = risk_params.get("max_position_size_usd", 1000)
        risk_per_trade_percent = risk_params.get("risk_per_trade_percent", 1.0)
        
        # Výpočet velikosti pozice na základě rizika
        risk_amount = account_balance * (risk_per_trade_percent / 100)
        position_size_usd = min(risk_amount, max_position_size_usd)
        
        # Převod na množství
        quantity = position_size_usd / price
        
        return quantity

    def calculate_stop_loss(self, entry_price: float, side: PositionSide) -> float:
        """
        Vypočítá úroveň stop-loss.

        Args:
            entry_price: Vstupní cena
            side: Strana pozice

        Returns:
            float: Úroveň stop-loss
        """
        risk_params = self.config.get("risk_management", {})
        stop_loss_percentage = risk_params.get("stop_loss_percentage", 2.0)
        
        if side == PositionSide.LONG:
            return entry_price * (1 - stop_loss_percentage / 100)
        else:
            return entry_price * (1 + stop_loss_percentage / 100)

    def calculate_take_profit(self, entry_price: float, side: PositionSide) -> float:
        """
        Vypočítá úroveň take-profit.

        Args:
            entry_price: Vstupní cena
            side: Strana pozice

        Returns:
            float: Úroveň take-profit
        """
        risk_params = self.config.get("risk_management", {})
        take_profit_percentage = risk_params.get("take_profit_percentage", 5.0)
        
        if side == PositionSide.LONG:
            return entry_price * (1 + take_profit_percentage / 100)
        else:
            return entry_price * (1 - take_profit_percentage / 100)