"""
Strategie založená na průlomech cenových úrovní.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.strategies.base_strategy import BaseStrategy
from src.models.market_data import Candle, Ticker
from src.models.position import Position, PositionSide
from src.models.order import Order, OrderSide, OrderType

logger = logging.getLogger(__name__)


class BreakoutStrategy(BaseStrategy):
    """
    Strategie založená na průlomech cenových úrovní.
    
    Sleduje formování cenových úrovní (supporty a resistance) a generuje signály
    v případě průlomu těchto úrovní:
    - BUY: Průlom nad resistance
    - SELL: Průlom pod support
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Inicializace strategie.

        Args:
            config: Konfigurace strategie
        """
        super().__init__(config)
        # Parametry pro hledání úrovní
        self.lookback_period = config.get("lookback_period", 20)
        self.threshold = config.get("threshold", 0.003)  # 0.3% jako v<PERSON><PERSON><PERSON>í hodnota
        self.min_touchpoints = config.get("min_touchpoints", 3)
        self.confirmation_candles = config.get("confirmation_candles", 2)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Inicializace BreakoutStrategy (lookback: {self.lookback_period}, threshold: {self.threshold})")

    def analyze(self, candles: List[Candle], ticker: Optional[Ticker] = None) -> Dict[str, Any]:
        """
        Analyzuje trh a hledá průlomy cenových úrovní.

        Args:
            candles: Seznam svíček
            ticker: Aktuální ticker

        Returns:
            Dict[str, Any]: Výsledky analýzy
        """
        if len(candles) < self.lookback_period + self.confirmation_candles:
            return {"error": f"Nedostatek dat pro analýzu. Potřeba alespoň {self.lookback_period + self.confirmation_candles} svíček."}

        # Získání historických dat pro analýzu
        lookback_candles = candles[-self.lookback_period-self.confirmation_candles:-self.confirmation_candles]
        current_candles = candles[-self.confirmation_candles:]
        
        # Nalezení úrovní podpory a odporu
        support_levels = self._find_support_levels(lookback_candles)
        resistance_levels = self._find_resistance_levels(lookback_candles)
        
        # Nalezení nejbližších úrovní k současné ceně
        current_price = candles[-1].close
        closest_support = self._find_closest_level(support_levels, current_price, below=True)
        closest_resistance = self._find_closest_level(resistance_levels, current_price, below=False)
        
        # Kontrola průlomů
        breakout_type = None
        breakout_level = None
        breakout_strength = 0.0
        
        # Kontrola průlomu odporu
        if closest_resistance and self._check_resistance_breakout(current_candles, closest_resistance):
            breakout_type = "BUY"
            breakout_level = closest_resistance
            # Síla průlomu závisí na vzdálenosti ceny od úrovně
            breakout_strength = (current_price - closest_resistance) / closest_resistance
        
        # Kontrola průlomu podpory
        elif closest_support and self._check_support_breakout(current_candles, closest_support):
            breakout_type = "SELL"
            breakout_level = closest_support
            # Síla průlomu závisí na vzdálenosti ceny od úrovně
            breakout_strength = (closest_support - current_price) / closest_support
        
        # Potvrzení průlomu objemem
        volume_confirmation = False
        if breakout_type and len(current_candles) > 0:
            avg_volume = sum([c.volume for c in lookback_candles]) / len(lookback_candles)
            current_volume = current_candles[-1].volume
            volume_confirmation = current_volume > 1.5 * avg_volume
        
        return {
            "signal": breakout_type,
            "current_price": current_price,
            "breakout_level": breakout_level,
            "breakout_strength": breakout_strength,
            "volume_confirmation": volume_confirmation,
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "confidence": self._calculate_confidence(breakout_strength, volume_confirmation)
        }

    def generate_signal(self, analysis: Dict[str, Any], positions: List[Position]) -> Optional[Dict[str, Any]]:
        """
        Generuje obchodní signál na základě analýzy průlomů.

        Args:
            analysis: Výsledky analýzy
            positions: Seznam aktuálních pozic

        Returns:
            Optional[Dict[str, Any]]: Obchodní signál nebo None
        """
        if "error" in analysis:
            self.logger.warning(f"Chyba v analýze: {analysis['error']}")
            return None

        # Kontrola, zda máme signál
        signal = analysis.get("signal")
        if not signal:
            return None

        # Kontrola, zda je signál dostatečně silný
        confidence = analysis["confidence"]
        if confidence < 0.6:  # Vyšší práh pro strategii průlomů
            self.logger.info(f"Signál {signal} má nízkou důvěryhodnost ({confidence:.2f}), ignoruji.")
            return None

        # Kontrola, zda již nemáme otevřenou pozici stejného typu
        for position in positions:
            if position.side == PositionSide.LONG and signal == "BUY":
                return None
            if position.side == PositionSide.SHORT and signal == "SELL":
                return None

        # Příprava signálu
        current_price = analysis["current_price"]
        breakout_level = analysis["breakout_level"]
        
        # Výpočet stop-loss a take-profit
        if signal == "BUY":
            side = PositionSide.LONG
            # Pro nákup je stop-loss pod úrovní průlomu
            stop_loss = breakout_level * 0.995  # 0.5% pod úrovní průlomu
            take_profit = current_price + (current_price - breakout_level) * 2  # 2:1 risk-reward
        else:  # SELL
            side = PositionSide.SHORT
            # Pro prodej je stop-loss nad úrovní průlomu
            stop_loss = breakout_level * 1.005  # 0.5% nad úrovní průlomu
            take_profit = current_price - (breakout_level - current_price) * 2  # 2:1 risk-reward

        return {
            "type": signal,
            "price": current_price,
            "confidence": confidence,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "breakout_level": breakout_level,
            "breakout_strength": analysis["breakout_strength"],
            "volume_confirmation": analysis["volume_confirmation"]
        }

    def _find_support_levels(self, candles: List[Candle]) -> List[float]:
        """
        Najde úrovně podpory v daném časovém úseku.

        Args:
            candles: Seznam svíček

        Returns:
            List[float]: Seznam úrovní podpory
        """
        # Výběr lokálních minim
        potential_supports = []
        for i in range(1, len(candles) - 1):
            if candles[i].low < candles[i - 1].low and candles[i].low < candles[i + 1].low:
                potential_supports.append(candles[i].low)
        
        # Shlukování blízkých úrovní
        supports = []
        for level in potential_supports:
            if not self._is_near_existing_level(supports, level):
                # Počítání "dotyků" této úrovně
                touches = self._count_touches(candles, level, is_support=True)
                if touches >= self.min_touchpoints:
                    supports.append(level)
        
        return supports

    def _find_resistance_levels(self, candles: List[Candle]) -> List[float]:
        """
        Najde úrovně odporu v daném časovém úseku.

        Args:
            candles: Seznam svíček

        Returns:
            List[float]: Seznam úrovní odporu
        """
        # Výběr lokálních maxim
        potential_resistances = []
        for i in range(1, len(candles) - 1):
            if candles[i].high > candles[i - 1].high and candles[i].high > candles[i + 1].high:
                potential_resistances.append(candles[i].high)
        
        # Shlukování blízkých úrovní
        resistances = []
        for level in potential_resistances:
            if not self._is_near_existing_level(resistances, level):
                # Počítání "dotyků" této úrovně
                touches = self._count_touches(candles, level, is_support=False)
                if touches >= self.min_touchpoints:
                    resistances.append(level)
        
        return resistances

    def _is_near_existing_level(self, levels: List[float], level: float) -> bool:
        """
        Zkontroluje, zda je úroveň blízko existujících úrovní.

        Args:
            levels: Seznam existujících úrovní
            level: Kontrolovaná úroveň

        Returns:
            bool: True, pokud je úroveň blízko existujících úrovní
        """
        for existing_level in levels:
            if abs(existing_level - level) / level < self.threshold:
                return True
        return False

    def _count_touches(self, candles: List[Candle], level: float, is_support: bool) -> int:
        """
        Spočítá, kolikrát se cena dotkla dané úrovně.

        Args:
            candles: Seznam svíček
            level: Úroveň
            is_support: True pro úroveň podpory, False pro úroveň odporu

        Returns:
            int: Počet "dotyků"
        """
        touches = 0
        for candle in candles:
            if is_support:
                # Pokud je svíčka v blízkosti úrovně podpory (low je blízko)
                if abs(candle.low - level) / level < self.threshold:
                    touches += 1
            else:
                # Pokud je svíčka v blízkosti úrovně odporu (high je blízko)
                if abs(candle.high - level) / level < self.threshold:
                    touches += 1
        return touches

    def _find_closest_level(self, levels: List[float], price: float, below: bool) -> Optional[float]:
        """
        Najde nejbližší úroveň k dané ceně.

        Args:
            levels: Seznam úrovní
            price: Cena
            below: True pro hledání pod cenou, False pro hledání nad cenou

        Returns:
            Optional[float]: Nejbližší úroveň nebo None
        """
        if not levels:
            return None
        
        if below:
            # Najít nejvyšší úroveň pod cenou
            below_levels = [level for level in levels if level < price]
            return max(below_levels) if below_levels else None
        else:
            # Najít nejnižší úroveň nad cenou
            above_levels = [level for level in levels if level > price]
            return min(above_levels) if above_levels else None

    def _check_resistance_breakout(self, candles: List[Candle], resistance: float) -> bool:
        """
        Zkontroluje, zda došlo k průlomu úrovně odporu.

        Args:
            candles: Seznam svíček
            resistance: Úroveň odporu

        Returns:
            bool: True, pokud došlo k průlomu
        """
        # Kontrola, zda všechny svíčky mají close nad úrovní odporu
        return all(candle.close > resistance for candle in candles)

    def _check_support_breakout(self, candles: List[Candle], support: float) -> bool:
        """
        Zkontroluje, zda došlo k průlomu úrovně podpory.

        Args:
            candles: Seznam svíček
            support: Úroveň podpory

        Returns:
            bool: True, pokud došlo k průlomu
        """
        # Kontrola, zda všechny svíčky mají close pod úrovní podpory
        return all(candle.close < support for candle in candles)

    def _calculate_confidence(self, breakout_strength: float, volume_confirmation: bool) -> float:
        """
        Vypočítá úroveň jistoty signálu.

        Args:
            breakout_strength: Síla průlomu
            volume_confirmation: Potvrzení objemem

        Returns:
            float: Úroveň jistoty (0.0 - 1.0)
        """
        # Základní důvěryhodnost na základě síly průlomu
        base_confidence = min(breakout_strength * 10, 0.7)
        
        # Přidání důvěryhodnosti pro potvrzení objemem
        if volume_confirmation:
            base_confidence += 0.2
        
        # Omezení hodnoty na rozsah 0.0 - 1.0
        return min(max(base_confidence, 0.0), 1.0)