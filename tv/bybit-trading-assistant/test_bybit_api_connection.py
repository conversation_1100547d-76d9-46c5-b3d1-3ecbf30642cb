#!/usr/bin/env python3
"""
Skript pro testování připojení k Bybit API a získání platných API klíčů.
"""
import json
import requests
import time
import hmac
import hashlib
import sys
import argparse
from typing import Dict, Any

# URL pro Bybit API
TESTNET_URL = "https://api-testnet.bybit.com"
MAIN_URL = "https://api.bybit.com"

def test_api_connection(api_key: str, api_secret: str, use_testnet: bool = False) -> Dict[str, Any]:
    """
    Otestuje připojení k Bybit API s dan<PERSON><PERSON> kl<PERSON>.
    
    Args:
        api_key: API klíč
        api_secret: API tajný klíč
        
    Returns:
        Dict[str, Any]: Výsledek testu
    """
    # Nastavení endpointu pro testování
    endpoint = "/v5/account/wallet-balance"
    
    # Nastavení parametrů požadavku
    timestamp = int(time.time() * 1000)
    recv_window = 5000
    
    # Parametry pro podpis
    params = {
        "accountType": "UNIFIED"
    }
    
    # Použijeme správnou URL podle parametru use_testnet
    base_url = TESTNET_URL if use_testnet else MAIN_URL
    print(f"Používám API URL: {base_url}")
    
    # Parametry pro podpis
    param_str = "accountType=UNIFIED"
    
    # Vytvoření podpisu
    signature_payload = str(timestamp) + api_key + str(recv_window) + param_str
    print(f"Payload pro podpis: {signature_payload}")
    
    signature = hmac.new(
        bytes(api_secret, "utf-8"),
        bytes(signature_payload, "utf-8"),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    # Přidání parametru do URL
    url = f"{base_url}{endpoint}?accountType=UNIFIED"
    
    # Nastavení hlaviček
    headers = {
        "X-BAPI-API-KEY": api_key,
        "X-BAPI-SIGN": signature,
        "X-BAPI-SIGN-TYPE": "2",
        "X-BAPI-TIMESTAMP": str(timestamp),
        "X-BAPI-RECV-WINDOW": str(recv_window),
        "Content-Type": "application/json"
    }
    
    print(f"Podepisovací algoritmus: HMAC-SHA256")
    print(f"Timestamp: {timestamp}")
    print(f"Request URL: {url}")
    
    # Odeslání požadavku
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        if result.get("retCode") == 0:
            return {
                "success": True,
                "message": "Připojení k Bybit API je úspěšné!",
                "data": result.get("result")
            }
        else:
            return {
                "success": False,
                "message": f"Chyba API: {result.get('retMsg')}",
                "code": result.get("retCode"),
                "data": None
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Chyba při připojení k Bybit API: {str(e)}",
            "data": None
        }

def test_public_api(use_testnet: bool = False) -> Dict[str, Any]:
    """
    Otestuje připojení k veřejnému API (nepotřebuje klíče).
    
    Args:
        use_testnet: Zda použít testovací síť
    
    Returns:
        Dict[str, Any]: Výsledek testu
    """
    base_url = TESTNET_URL if use_testnet else MAIN_URL
    url = f"{base_url}/v5/market/tickers?category=spot&symbol=BTCUSDT"
    print(f"Používám API URL: {base_url}")
    
    try:
        response = requests.get(url)
        result = response.json()
        
        if result.get("retCode") == 0:
            return {
                "success": True,
                "message": "Připojení k veřejnému API je úspěšné!",
                "data": result.get("result")
            }
        else:
            return {
                "success": False,
                "message": f"Chyba API: {result.get('retMsg')}",
                "code": result.get("retCode"),
                "data": None
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Chyba při připojení k Bybit API: {str(e)}",
            "data": None
        }

def main():
    parser = argparse.ArgumentParser(description="Test připojení k Bybit API")
    parser.add_argument("--key", help="API klíč")
    parser.add_argument("--secret", help="API tajný klíč")
    parser.add_argument("--public", action="store_true", help="Testovat pouze veřejné API")
    parser.add_argument("--config", help="Cesta ke konfiguračnímu souboru")
    parser.add_argument("--testnet", action="store_true", help="Použít testovací síť")
    args = parser.parse_args()
    
    # Načtení konfigurace
    testnet = args.testnet
    if not args.key or not args.secret:
        if args.config:
            try:
                with open(args.config, "r") as f:
                    config = json.load(f)
                testnet = config.get("api", {}).get("bybit", {}).get("testnet", False)
            except Exception:
                pass
        else:
            try:
                with open("config/config.json", "r") as f:
                    config = json.load(f)
                testnet = config.get("api", {}).get("bybit", {}).get("testnet", False)
            except Exception:
                pass
    
    if args.public:
        print("Testování připojení k veřejnému API...")
        result = test_public_api(testnet)
        print(f"Výsledek: {result['message']}")
        if result['success']:
            print("Ticker data pro BTCUSDT:")
            btc_data = result['data']['list'][0]
            print(f"  Poslední cena: {btc_data['lastPrice']}")
            print(f"  24h změna: {btc_data['price24hPcnt']}%")
            print(f"  24h objem: {btc_data['volume24h']}")
        return
    
    # Získání API klíčů
    api_key = args.key
    api_secret = args.secret
    
    # Pokud byly zadány parametry v příkazové řádce, použijeme je
    if not api_key or not api_secret:
        # Pokud byl zadán konfigurační soubor, načteme klíče z něj
        if args.config:
            try:
                with open(args.config, "r") as f:
                    config = json.load(f)
                api_key = config.get("api", {}).get("bybit", {}).get("api_key")
                api_secret = config.get("api", {}).get("bybit", {}).get("api_secret")
            except Exception as e:
                print(f"Chyba při načítání konfiguračního souboru: {e}")
                return
        # Jinak použijeme výchozí konfiguraci
        else:
            try:
                with open("config/config.json", "r") as f:
                    config = json.load(f)
                api_key = config.get("api", {}).get("bybit", {}).get("api_key")
                api_secret = config.get("api", {}).get("bybit", {}).get("api_secret")
            except Exception as e:
                print(f"Chyba při načítání konfiguračního souboru: {e}")
                return
    
    # Kontrola, zda byly klíče nalezeny
    if not api_key or not api_secret:
        print("Chyba: API klíče nebyly nalezeny. Použijte parametry --key a --secret nebo správně nakonfigurujte config.json.")
        return
    
    # Testování připojení k API
    print("Testování připojení k Bybit API pomocí zadaných klíčů...")
    print(f"API klíč: {api_key[:5]}...")
    print(f"Testnet: {testnet}")
    result = test_api_connection(api_key, api_secret, testnet)
    print(f"Výsledek: {result['message']}")
    
    # Pokud bylo připojení úspěšné, zobrazíme informace o účtu
    if result['success']:
        account_info = result['data']
        print("\nInformace o účtu:")
        for account in account_info.get("list", []):
            total_equity = account.get("totalEquity")
            total_wallet_balance = account.get("totalWalletBalance")
            account_type = account.get("accountType")
            print(f"Typ účtu: {account_type}")
            print(f"Celková hodnota: {total_equity} USDT")
            print(f"Zůstatek peněženky: {total_wallet_balance} USDT")
            print("\nAktiva:")
            for coin in account.get("coin", []):
                name = coin.get("coin")
                balance = coin.get("walletBalance")
                available = coin.get("free")
                if float(balance) > 0:
                    print(f"  {name}: {balance} (Dostupné: {available})")

if __name__ == "__main__":
    main()