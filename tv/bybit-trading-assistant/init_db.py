#!/usr/bin/env python
"""
Inicializace databáze a vytvoření tabulek.
"""
import os
import sys
import logging
import argparse
from pathlib import Path

# Přidání projektu do Python cesty
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.infrastructure.persistence.database.database import db
from src.config.config import config


def setup_logging():
    """Nastavení logování."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )


def init_database(drop_tables=False):
    """
    Inicializace databáze a vytvoření tabulek.
    
    Args:
        drop_tables: <PERSON>r<PERSON><PERSON><PERSON>, zda se mají nejprve odstranit existující tabulky
    """
    logger = logging.getLogger(__name__)
    
    # Ujistíme se, že adres<PERSON>ř pro databázi existuje
    db_path = Path(config.database.path)
    db_dir = db_path.parent
    db_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Inicializace databáze: {config.database.path}")
    
    if drop_tables:
        logger.warning("Odstraňování existujících tabulek")
        db.drop_all()
    
    logger.info("Vytváření tabulek")
    db.create_all()
    
    logger.info("Databáze byla úspěšně inicializována")


def run_migrations():
    """Spustí migrace databáze pomocí Alembic."""
    logger = logging.getLogger(__name__)
    
    try:
        import alembic.config
        alembic_args = [
            '--raiseerr',
            'upgrade', 'head',
        ]
        alembic.config.main(argv=alembic_args)
        logger.info("Migrace byly úspěšně provedeny")
    except Exception as e:
        logger.error(f"Chyba při provádění migrací: {e}")
        raise


def main():
    """Hlavní funkce."""
    parser = argparse.ArgumentParser(description="Inicializace databáze")
    parser.add_argument("--drop", action="store_true", help="Odstranit existující tabulky")
    parser.add_argument("--migrate", action="store_true", help="Spustit migrace")
    
    args = parser.parse_args()
    
    setup_logging()
    
    if args.migrate:
        run_migrations()
    else:
        init_database(args.drop)


if __name__ == "__main__":
    main()