"""
Monitorovací skript pro sledování aktuálních otevř<PERSON> poz<PERSON> (demo verze).
"""
import os
import time
import json
import argparse
import random
from datetime import datetime
from typing import Dict, Any, List, Optional
from tabulate import tabulate
import colorama
from colorama import Fore, Style


# Inicializace colorama
colorama.init()


def clear_screen():
    """Vyčistí obrazovku terminálu."""
    os.system('cls' if os.name == 'nt' else 'clear')


def format_currency(value: float) -> str:
    """Formátuje měnovou hodnotu."""
    return f"${value:.2f}"


def format_percentage(value: float) -> str:
    """Formátuje procentuální hodnotu."""
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:.2f}%{Style.RESET_ALL}"


def format_time(timestamp: str) -> str:
    """Formátuje časové razítko."""
    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def generate_positions() -> List[Dict[str, Any]]:
    """
    Generuje seznam otevřených pozic pro demo účely.
    
    Returns:
        List[Dict[str, Any]]: Seznam otevřených pozic
    """
    positions = []
    
    # BTC pozice
    btc_price = 50000.0 + random.uniform(-500, 500)
    btc_entry_price = 49800.0
    btc_quantity = 0.001
    btc_unrealized_pnl = (btc_price - btc_entry_price) * btc_quantity
    btc_profit_percentage = ((btc_price - btc_entry_price) / btc_entry_price) * 100
    
    positions.append({
        "id": "pos-btc-001",
        "symbol": "BTCUSDT",
        "side": "LONG",
        "entry_price": btc_entry_price,
        "quantity": btc_quantity,
        "current_price": btc_price,
        "unrealized_pnl": btc_unrealized_pnl,
        "realized_pnl": 0.0,
        "status": "OPEN",
        "stop_loss": 48000.0,
        "take_profit": 52000.0,
        "opened_at": datetime.now().isoformat(),
        "value": btc_price * btc_quantity,
        "profit_percentage": btc_profit_percentage
    })
    
    # ETH pozice
    eth_price = 2800.0 + random.uniform(-50, 50)
    eth_entry_price = 2850.0
    eth_quantity = 0.01
    eth_unrealized_pnl = (eth_entry_price - eth_price) * eth_quantity
    eth_profit_percentage = ((eth_entry_price - eth_price) / eth_entry_price) * 100
    
    positions.append({
        "id": "pos-eth-001",
        "symbol": "ETHUSDT",
        "side": "SHORT",
        "entry_price": eth_entry_price,
        "quantity": eth_quantity,
        "current_price": eth_price,
        "unrealized_pnl": eth_unrealized_pnl,
        "realized_pnl": 0.0,
        "status": "OPEN",
        "stop_loss": 2950.0,
        "take_profit": 2700.0,
        "opened_at": datetime.now().isoformat(),
        "value": eth_price * eth_quantity,
        "profit_percentage": eth_profit_percentage
    })
    
    return positions


def generate_account_info() -> Dict[str, Any]:
    """
    Generuje informace o účtu pro demo účely.
    
    Returns:
        Dict[str, Any]: Informace o účtu
    """
    positions = generate_positions()
    
    total_position_value = sum(position["value"] for position in positions)
    total_unrealized_pnl = sum(position["unrealized_pnl"] for position in positions)
    total_realized_pnl = 25.0  # Simulovaný realizovaný zisk
    total_pnl = total_unrealized_pnl + total_realized_pnl
    
    return {
        "total_position_value": total_position_value,
        "total_unrealized_pnl": total_unrealized_pnl,
        "total_realized_pnl": total_realized_pnl,
        "total_pnl": total_pnl
    }


def generate_market_data(symbol: str) -> Dict[str, Any]:
    """
    Generuje tržní data pro demo účely.
    
    Args:
        symbol: Symbol
        
    Returns:
        Dict[str, Any]: Tržní data
    """
    if symbol == "BTCUSDT":
        price = 50000.0 + random.uniform(-500, 500)
        volume = 1000000000.0 + random.uniform(-100000000, 100000000)
        price_change = random.uniform(-2, 2)
    elif symbol == "ETHUSDT":
        price = 2800.0 + random.uniform(-50, 50)
        volume = 500000000.0 + random.uniform(-50000000, 50000000)
        price_change = random.uniform(-3, 3)
    else:
        price = 100.0 + random.uniform(-5, 5)
        volume = 10000000.0 + random.uniform(-1000000, 1000000)
        price_change = random.uniform(-5, 5)
    
    return {
        "ticker": {
            "symbol": symbol,
            "last_price": price,
            "price_change_percent_24h": price_change / 100,
            "volume_24h": volume
        },
        "indicators": {
            "rsi": 50.0 + random.uniform(-20, 20),
            "macd": 10.0 + random.uniform(-5, 5),
            "macd_signal": 8.0 + random.uniform(-4, 4),
            "bb_upper": price * 1.05,
            "bb_middle": price,
            "bb_lower": price * 0.95
        }
    }


def display_positions(positions: List[Dict[str, Any]]):
    """
    Zobrazí seznam otevřených pozic.
    
    Args:
        positions: Seznam otevřených pozic
    """
    if not positions:
        print(f"{Fore.YELLOW}Žádné otevřené pozice{Style.RESET_ALL}")
        return
    
    # Příprava dat pro tabulku
    table_data = []
    for position in positions:
        symbol = position.get("symbol", "")
        side = position.get("side", "")
        side_color = Fore.GREEN if side == "LONG" else Fore.RED
        quantity = position.get("quantity", 0)
        entry_price = position.get("entry_price", 0)
        current_price = position.get("current_price", 0)
        unrealized_pnl = position.get("unrealized_pnl", 0)
        profit_percentage = position.get("profit_percentage", 0)
        stop_loss = position.get("stop_loss", None)
        take_profit = position.get("take_profit", None)
        
        # Formátování hodnot
        side_formatted = f"{side_color}{side}{Style.RESET_ALL}"
        unrealized_pnl_formatted = format_currency(unrealized_pnl)
        profit_percentage_formatted = format_percentage(profit_percentage)
        stop_loss_formatted = format_currency(stop_loss) if stop_loss else "N/A"
        take_profit_formatted = format_currency(take_profit) if take_profit else "N/A"
        
        # Přidání řádku do tabulky
        table_data.append([
            symbol,
            side_formatted,
            f"{quantity:.6f}",
            format_currency(entry_price),
            format_currency(current_price),
            unrealized_pnl_formatted,
            profit_percentage_formatted,
            stop_loss_formatted,
            take_profit_formatted
        ])
    
    # Zobrazení tabulky
    headers = ["Symbol", "Strana", "Množství", "Vstupní cena", "Aktuální cena", "PnL", "PnL %", "Stop-Loss", "Take-Profit"]
    print(tabulate(table_data, headers=headers, tablefmt="pretty"))


def display_account_info(account_info: Dict[str, Any]):
    """
    Zobrazí informace o účtu.
    
    Args:
        account_info: Informace o účtu
    """
    if "error" in account_info:
        print(f"{Fore.RED}Chyba: {account_info['error']}{Style.RESET_ALL}")
        return
    
    # Získání hodnot
    total_balance = account_info.get("total_position_value", 0)
    unrealized_pnl = account_info.get("total_unrealized_pnl", 0)
    realized_pnl = account_info.get("total_realized_pnl", 0)
    total_pnl = account_info.get("total_pnl", 0)
    
    # Formátování hodnot
    total_balance_formatted = format_currency(total_balance)
    unrealized_pnl_formatted = format_currency(unrealized_pnl)
    realized_pnl_formatted = format_currency(realized_pnl)
    total_pnl_formatted = format_currency(total_pnl)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== Informace o účtu ==={Style.RESET_ALL}")
    print(f"Celková hodnota: {total_balance_formatted}")
    print(f"Nerealizovaný zisk: {unrealized_pnl_formatted}")
    print(f"Realizovaný zisk: {realized_pnl_formatted}")
    print(f"Celkový zisk: {total_pnl_formatted}")
    print()


def display_market_data(market_data: Dict[str, Any], symbol: str):
    """
    Zobrazí tržní data pro daný symbol.
    
    Args:
        market_data: Tržní data
        symbol: Symbol
    """
    if not market_data:
        return
    
    ticker = market_data.get("ticker", {})
    indicators = market_data.get("indicators", {})
    
    # Získání hodnot
    last_price = ticker.get("last_price", 0)
    price_change = ticker.get("price_change_percent_24h", 0) * 100
    volume = ticker.get("volume_24h", 0)
    
    # Formátování hodnot
    last_price_formatted = format_currency(last_price)
    price_change_formatted = format_percentage(price_change)
    
    # Zobrazení informací
    print(f"{Fore.CYAN}=== {symbol} ==={Style.RESET_ALL}")
    print(f"Cena: {last_price_formatted} ({price_change_formatted})")
    
    # Zobrazení indikátorů
    if indicators:
        print(f"{Fore.CYAN}Indikátory:{Style.RESET_ALL}")
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            rsi_color = Fore.GREEN if rsi < 70 else Fore.RED
            print(f"RSI: {rsi_color}{rsi:.2f}{Style.RESET_ALL}")
        
        if "macd" in indicators and "macd_signal" in indicators:
            macd = indicators["macd"]
            macd_signal = indicators["macd_signal"]
            macd_color = Fore.GREEN if macd > macd_signal else Fore.RED
            print(f"MACD: {macd_color}{macd:.2f}{Style.RESET_ALL}, Signal: {macd_signal:.2f}")
    
    print()


def monitor(refresh_interval: int):
    """
    Monitoruje stav obchodního asistenta.
    
    Args:
        refresh_interval: Interval obnovení v sekundách
    """
    try:
        while True:
            clear_screen()
            
            print(f"{Fore.YELLOW}=== Bybit Trading Assistant - Monitor pozic (Demo) ==={Style.RESET_ALL}")
            print(f"Čas: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # Získání dat
            positions = generate_positions()
            account_info = generate_account_info()
            
            # Zobrazení dat
            display_account_info(account_info)
            
            print(f"{Fore.CYAN}=== Otevřené pozice ==={Style.RESET_ALL}")
            display_positions(positions)
            print()
            
            # Zobrazení tržních dat pro každý symbol s otevřenou pozicí
            position_symbols = [p.get("symbol") for p in positions]
            for symbol in position_symbols:
                market_data = generate_market_data(symbol)
                display_market_data(market_data, symbol)
            
            print(f"Obnovení za {refresh_interval} sekund... (Ctrl+C pro ukončení)")
            time.sleep(refresh_interval)
    except KeyboardInterrupt:
        print("\nMonitorování ukončeno.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Monitor pozic pro Bybit Trading Assistant (Demo)")
    parser.add_argument("--interval", type=int, default=5, help="Interval obnovení v sekundách")
    
    args = parser.parse_args()
    
    monitor(args.interval)
