# Nastavení Bybit API klíčů

Tento dokument vás provede procesem získání a nastavení platných API klíčů pro Bybit Trading Assistant.

## 1. Vytvoření testovac<PERSON>ho <PERSON> (Testnet)

Pro bezpečné testování bez použití reálných peněz je vhodné použít testovací síť Bybit:

1. Přejděte na https://testnet.bybit.com/
2. Zaregistrujte se pro nový testovací účet (pokud ho ještě nemáte)
3. Přihlaste se do svého testovacího účtu

## 2. <PERSON>ísk<PERSON><PERSON> testovacích mincí

Pro testování potřebujete testovací mince:

1. Klikněte na **Assets** → **Assets Overview** → **Request Test Coins**
2. V zobrazeném okně klikněte na **Request**
3. Obdržíte 10,000 USDT a 1 BTC na váš testovací účet
4. Můžete požádat o další testovací mince po 24 hodinách

## 3. Vytvoření API klíčů pro testovací síť

Pro přístup k testovací síti potřebujete API klíče:

1. Přejděte na https://testnet.bybit.com/app/user/api-management
2. Klikněte na **Create New Key**
3. Nastavte oprávnění:
   - **Read** - povoleno
   - **Trade** - povoleno
   - **Withdraw** - zakázáno (není třeba pro trading bota)
4. Pro větší bezpečnost můžete nastavit omezení IP adres
5. Zadejte svůj 2FA kód (Google Authenticator)
6. Klikněte na **Submit**

## 4. Uložení API klíčů

Po vytvoření klíčů je důležité:

1. **ZKOPÍROVAT** a bezpečně uložit oba klíče:
   - **API Key** - veřejný klíč
   - **API Secret** - tajný klíč
2. **POZOR**: Tajný klíč (Secret) bude zobrazen pouze jednou! Pokud ho nezaznamenáte, budete muset vygenerovat nový klíč

## 5. Konfigurace Bybit Trading Assistant

Nyní nakonfigurujte aplikaci s novými API klíči:

1. Otevřete soubor `config/config.json`
2. Upravte sekci `api.bybit`:
```json
"api": {
  "bybit": {
    "api_key": "VÁŠ_NOVÝ_API_KLÍČ",
    "api_secret": "VÁŠ_NOVÝ_API_SECRET",
    "testnet": true
  }
}
```
3. Uložte soubor

## 6. Testování připojení

Po nastavení API klíčů je vhodné otestovat připojení:

```bash
./test_api_connection.py
```

Pokud je vše správně nastaveno, měli byste vidět informace o vašem účtu.

## 7. Přechod na produkční prostředí

Až budete připraveni obchodovat s reálnými penězi, vytvořte API klíče na hlavní síti:

1. Přejděte na https://www.bybit.com/app/user/api-management
2. Postupujte stejně jako u testovací sítě
3. V `config.json` nastavte `"testnet": false`

## Důležité poznámky

- **Bezpečnost**: Nikdy nesdílejte své API klíče s nikým
- **Omezení přístupu**: Pro zvýšení bezpečnosti omezte IP adresy, které mohou používat API
- **Pravidelná rotace**: Je dobrou praxí pravidelně měnit API klíče
- **Testovací vs. produkční**: Vždy důkladně otestujte své strategie na testovací síti, než přejdete na produkční prostředí