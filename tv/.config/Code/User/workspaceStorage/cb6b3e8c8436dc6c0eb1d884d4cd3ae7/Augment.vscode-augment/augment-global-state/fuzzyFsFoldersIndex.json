{"/home/<USER>/": {"rootPath": "/home", "relPath": "tv/"}, "/home/<USER>/.vscode/": {"rootPath": "/home", "relPath": "tv/.vscode/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/permanent/chrome/idb/3870112724rsegmnoittet-es.files/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/permanent/chrome/idb/3870112724rsegmnoittet-es.files/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/"}, "/home/<USER>/.config/Code/logs/20250503T151732/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/"}, "/home/<USER>/.config/Code/logs/20250503T145648/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/"}, "/home/<USER>/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/weave/toFetch/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/weave/toFetch/"}, "/home/<USER>/.config/cinnamon/spices/<EMAIL>/": {"rootPath": "/home", "relPath": "tv/.config/cinnamon/spices/<EMAIL>/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/l10n/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/l10n/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/idb/"}, "/home/<USER>/.config/Code/User/workspaceStorage/5849e520db98d97431e3c30548274014/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/5849e520db98d97431e3c30548274014/Augment.vscode-augment/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/cache/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/cache/"}, "/home/<USER>/.config/caja/": {"rootPath": "/home", "relPath": "tv/.config/caja/"}, "/home/<USER>/.config/Code/WebStorage/4/IndexedDB/indexeddb.leveldb/": {"rootPath": "/home", "relPath": "tv/.config/Code/WebStorage/4/IndexedDB/indexeddb.leveldb/"}, "/home/<USER>/.config/": {"rootPath": "/home", "relPath": "tv/.config/"}, "/home/<USER>/.config/Code/logs/20250503T150453/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/"}, "/home/<USER>/.dotnet/corefx/cryptography/crls/": {"rootPath": "/home", "relPath": "tv/.dotnet/corefx/cryptography/crls/"}, "/home/<USER>/.config/Code/Local Storage/leveldb/": {"rootPath": "/home", "relPath": "tv/.config/Code/Local Storage/leveldb/"}, "/home/<USER>/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/dist/terminator/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/dist/terminator/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/weave/failed/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/weave/failed/"}, "/home/<USER>/.config/Code/": {"rootPath": "/home", "relPath": "tv/.config/Code/"}, "/home/<USER>/.config/Code/User/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/permanent/chrome/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/permanent/chrome/idb/"}, "/home/<USER>/.config/Code/User/History/298619d9/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/History/298619d9/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part26/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part26/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/vscode.git/"}, "/home/<USER>/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/Augment.vscode-augment/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part41/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part41/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++bc27f079-a449-4bbf-9d17-26154b45cdbf^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++bc27f079-a449-4bbf-9d17-26154b45cdbf^userContextId=**********/idb/"}, "/home/<USER>/.config/Code/User/workspaceStorage/5849e520db98d97431e3c30548274014/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/5849e520db98d97431e3c30548274014/"}, "/home/<USER>/.pki/nssdb/": {"rootPath": "/home", "relPath": "tv/.pki/nssdb/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/Augment.vscode-augment/"}, "/home/<USER>/.config/Code/Service Worker/Database/": {"rootPath": "/home", "relPath": "tv/.config/Code/Service Worker/Database/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/assets/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/dist/"}, "/home/<USER>/.local/share/evolution/tasks/system/": {"rootPath": "/home", "relPath": "tv/.local/share/evolution/tasks/system/"}, "/home/<USER>/.mozilla/firefox/Crash Reports/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/Crash Reports/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part49/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part49/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/exthost/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part38/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part38/"}, "/home/<USER>/.linuxmint/mintupdate/": {"rootPath": "/home", "relPath": "tv/.linuxmint/mintupdate/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/vscode.github/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/"}, "/home/<USER>/.config/Code/shared_proto_db/metadata/": {"rootPath": "/home", "relPath": "tv/.config/Code/shared_proto_db/metadata/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/gmp-gmpopenh264/2.6.0/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/gmp-gmpopenh264/2.6.0/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/dist/browser/extension/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/dist/browser/extension/"}, "/home/<USER>/.config/Code/User/globalStorage/rooveterinaryinc.roo-cline/settings/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/rooveterinaryinc.roo-cline/settings/"}, "/home/<USER>/.cache/hypnotix/favorites/": {"rootPath": "/home", "relPath": "tv/.cache/hypnotix/favorites/"}, "/home/<USER>/.cache/Microsoft/DeveloperTools/": {"rootPath": "/home", "relPath": "tv/.cache/Microsoft/DeveloperTools/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/Augment.vscode-augment/"}, "/home/<USER>/.cache/fontconfig/": {"rootPath": "/home", "relPath": "tv/.cache/fontconfig/"}, "/home/<USER>/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/firefox-devtools.vscode-firefox-debug-2.15.0/dist/"}, "/home/<USER>/.config/Code/Session Storage/": {"rootPath": "/home", "relPath": "tv/.config/Code/Session Storage/"}, "/home/<USER>/.config/Code/VideoDecodeStats/": {"rootPath": "/home", "relPath": "tv/.config/Code/VideoDecodeStats/"}, "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfc20317-d6cd-48df-867a-21e57737b8e2^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfc20317-d6cd-48df-867a-21e57737b8e2^userContextId=**********/idb/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/entries/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/entries/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/datareporting/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/datareporting/"}, "/home/<USER>/.config/Code/shared_proto_db/": {"rootPath": "/home", "relPath": "tv/.config/Code/shared_proto_db/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/vscode.github/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/output_logging_20250503T151736/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/output_logging_20250503T151736/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/datareporting/glean/events/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/datareporting/glean/events/"}, "/home/<USER>/.config/qt5ct/": {"rootPath": "/home", "relPath": "tv/.config/qt5ct/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/GitHub.copilot/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/GitHub.copilot/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/GitHub.vscode-pull-request-github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/GitHub.vscode-pull-request-github/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T180351/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T180351/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/GitHub.copilot-chat/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/GitHub.copilot-chat/"}, "/home/<USER>/.config/Code/Crashpad/": {"rootPath": "/home", "relPath": "tv/.config/Code/Crashpad/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T181412/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T181412/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6^userContextId=**********/idb/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com/ls/"}, "/home/<USER>/.local/share/ice/firefox/OnlineChat4519/": {"rootPath": "/home", "relPath": "tv/.local/share/ice/firefox/OnlineChat4519/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/"}, "/home/<USER>/.cache/cs_themes/": {"rootPath": "/home", "relPath": "tv/.cache/cs_themes/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfc20317-d6cd-48df-867a-21e57737b8e2/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfc20317-d6cd-48df-867a-21e57737b8e2/ls/"}, "/home/<USER>/.config/Code/logs/20250503T180347/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/"}, "/home/<USER>/.local/share/applications/": {"rootPath": "/home", "relPath": "tv/.local/share/applications/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++84daf55a-73bd-480d-a7f9-72ba55522a92^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++84daf55a-73bd-480d-a7f9-72ba55522a92^userContextId=**********/idb/"}, "/home/<USER>/.local/share/ice/firefox/OnlineChat4519/chrome/": {"rootPath": "/home", "relPath": "tv/.local/share/ice/firefox/OnlineChat4519/chrome/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/settings/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/settings/"}, "/home/<USER>/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/chatSessions/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/chatSessions/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++530911e9-553e-4cb5-ac3b-b4e66f5aa604^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++530911e9-553e-4cb5-ac3b-b4e66f5aa604^userContextId=**********/idb/"}, "/home/<USER>/.config/Code/clp/fc411d9682d131fef5a449febd1a93f7.cs/": {"rootPath": "/home", "relPath": "tv/.config/Code/clp/fc411d9682d131fef5a449febd1a93f7.cs/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/weave/changes/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/weave/changes/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part36/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part36/"}, "/home/<USER>/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/"}, "/home/<USER>/.config/Code/User/History/-4737d418/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/History/-4737d418/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/output_20250503T151736/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/output_20250503T151736/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.googletagmanager.com^partitionKey=%28https%2Caugmentcode.com%29/cache/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.googletagmanager.com^partitionKey=%28https%2Caugmentcode.com%29/cache/"}, "/home/<USER>/.cache/hypnotix/providers/": {"rootPath": "/home", "relPath": "tv/.cache/hypnotix/providers/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/output_20250503T145653/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/output_20250503T145653/"}, "/home/<USER>/.config/Code/User/History/-50c953ef/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/History/-50c953ef/"}, "/home/<USER>/.mozilla/firefox/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/"}, "/home/<USER>/.config/Code/User/globalStorage/saoudrizwan.claude-dev/settings/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/saoudrizwan.claude-dev/settings/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++af739c0f-756e-4f88-b868-83df4d264dc6^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++af739c0f-756e-4f88-b868-83df4d264dc6^userContextId=**********/idb/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/vscode.github/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/"}, "/home/<USER>/.local/share/flatpak/repo/": {"rootPath": "/home", "relPath": "tv/.local/share/flatpak/repo/"}, "/home/<USER>/.local/state/wireplumber/": {"rootPath": "/home", "relPath": "tv/.local/state/wireplumber/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/vscode.json-language-features/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/vscode.json-language-features/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part13/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part13/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/output_20250503T181351/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/output_20250503T181351/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/output_20250503T180351/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/output_20250503T180351/"}, "/home/<USER>/.config/nemo/": {"rootPath": "/home", "relPath": "tv/.config/nemo/"}, "/home/<USER>/.config/evolution/sources/": {"rootPath": "/home", "relPath": "tv/.config/evolution/sources/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/icons/dark/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/icons/dark/"}, "/home/<USER>/.local/share/": {"rootPath": "/home", "relPath": "tv/.local/share/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/GitHub.copilot-chat/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/GitHub.copilot-chat/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part24/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part24/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/dist/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/settings/main/ms-language-packs/browser/newtab/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/settings/main/ms-language-packs/browser/newtab/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/Augment.vscode-augment/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/vscode.git/"}, "/home/<USER>/.config/Code/User/globalStorage/saoudrizwan.claude-dev/cache/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/saoudrizwan.claude-dev/cache/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/GitHub.vscode-pull-request-github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/GitHub.vscode-pull-request-github/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfb6007d-ab06-4d36-b484-bc26c68017d2/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfb6007d-ab06-4d36-b484-bc26c68017d2/idb/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/GitHub.copilot/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/GitHub.copilot/"}, "/home/<USER>/.config/Code/Shared Dictionary/": {"rootPath": "/home", "relPath": "tv/.config/Code/Shared Dictionary/"}, "/home/<USER>/.cache/mesa_shader_cache_db/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/"}, "/home/<USER>/.local/share/evolution/calendar/system/": {"rootPath": "/home", "relPath": "tv/.local/share/evolution/calendar/system/"}, "/home/<USER>/.local/share/flatpak/": {"rootPath": "/home", "relPath": "tv/.local/share/flatpak/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++accounts.youtube.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++accounts.youtube.com/ls/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/output_20250503T151338/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/output_20250503T151338/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/GitHub.copilot/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/GitHub.copilot/"}, "/home/<USER>/.config/gtk-3.0/": {"rootPath": "/home", "relPath": "tv/.config/gtk-3.0/"}, "/home/<USER>/.mozilla/firefox/9i8xiamx.default/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/9i8xiamx.default/"}, "/home/<USER>/.config/Code/logs/20250503T151732/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T151732/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfb6007d-ab06-4d36-b484-bc26c68017d2^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++cfb6007d-ab06-4d36-b484-bc26c68017d2^userContextId=**********/idb/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part27/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part27/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/vscode.git/"}, "/home/<USER>/Desktop/": {"rootPath": "/home", "relPath": "tv/Desktop/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com^partitionKey=%28https%2Caugmentcode.com%29/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com^partitionKey=%28https%2Caugmentcode.com%29/ls/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/exthost/vscode.github/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++00bd2ddf-b75c-4022-8063-4cc88e35dc1a^userContextId=**********/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++00bd2ddf-b75c-4022-8063-4cc88e35dc1a^userContextId=**********/idb/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/output_20250503T150458/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/output_20250503T150458/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.108.0/resources/icons/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/node_modules/@vscode/copilot-typescript-server-plugin/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/node_modules/@vscode/copilot-typescript-server-plugin/dist/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/output_logging_20250503T151338/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/output_logging_20250503T151338/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/output_20250503T181411/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/output_20250503T181411/"}, "/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/node_modules/@vscode/copilot-typescript-server-plugin/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-chat-0.26.7/node_modules/@vscode/copilot-typescript-server-plugin/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T181351/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/output_logging_20250503T181351/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/exthost/vscode.git/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/GitHub.vscode-pull-request-github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/GitHub.vscode-pull-request-github/"}, "/home/<USER>/.config/Code/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/weave/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/weave/"}, "/home/<USER>/.config/Code/User/globalStorage/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/"}, "/home/<USER>/.config/Code/logs/20250503T180347/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T180347/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/chatEditingSessions/e9097f6d-878a-4a2a-812b-29780868040a/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/d0e05aafa21b1f31db267d7d21444db6/chatEditingSessions/e9097f6d-878a-4a2a-812b-29780868040a/"}, "/home/<USER>/.config/Code/logs/20250503T150453/window1/exthost/GitHub.copilot-chat/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T150453/window1/exthost/GitHub.copilot-chat/"}, "/home/<USER>/.cache/mesa_shader_cache_db/part46/": {"rootPath": "/home", "relPath": "tv/.cache/mesa_shader_cache_db/part46/"}, "/home/<USER>/.linuxmint/mintwelcome/": {"rootPath": "/home", "relPath": "tv/.linuxmint/mintwelcome/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6/idb/"}, "/home/<USER>/.config/Code/logs/20250503T145648/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250503T145648/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/build/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/build/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/build/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/build/assets/"}, "/home/<USER>/.vscode/extensions/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/node_modules/katex/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/node_modules/katex/dist/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/build/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/build/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/build/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/webview-ui/build/assets/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/standalone/runtime-files/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/standalone/runtime-files/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/standalone/runtime-files/vscode/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/standalone/runtime-files/vscode/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/src/integrations/theme/default-themes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/src/integrations/theme/default-themes/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/scripts/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/scripts/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/proto/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/proto/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/tools/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/tools/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/custom instructions library/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/custom instructions library/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/custom instructions library/raw-instructions/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/prompting/custom instructions library/raw-instructions/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/mcp/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/mcp/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/getting-started-new-coders/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/getting-started-new-coders/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/cline-customization/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/cline-customization/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/architecture/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/old_docs/architecture/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/node_modules/@vscode/codicons/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/node_modules/@vscode/codicons/dist/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/zh-tw/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/zh-tw/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/zh-cn/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/zh-cn/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/pt-BR/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/pt-BR/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ko/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ko/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ja/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ja/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/es/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/es/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/de/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/de/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ar-sa/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/locales/ar-sa/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/evals/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/evals/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/evals/cli/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/evals/cli/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/assets/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/assets/icons/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/assets/docs/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/assets/docs/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.husky/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.husky/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/workflows/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/workflows/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/tests/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/tests/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/coverage_check/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/scripts/coverage_check/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/ISSUE_TEMPLATE/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.github/ISSUE_TEMPLATE/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.clinerules/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.clinerules/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.changeset/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.16.1/.changeset/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/vscode-material-icons/generated/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/vscode-material-icons/generated/icons/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/src/integrations/theme/default-themes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/src/integrations/theme/default-themes/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/.storybook/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/webview-ui/.storybook/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/vscode-material-icons/generated/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/vscode-material-icons/generated/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/@vscode/codicons/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/node_modules/@vscode/codicons/dist/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/zh-TW/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/zh-TW/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/zh-CN/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/zh-CN/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/vi/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/vi/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/tr/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/tr/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ru/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ru/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/pt-BR/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/pt-BR/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/pl/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/pl/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/nl/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/nl/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ko/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ko/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ja/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ja/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/it/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/it/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/hi/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/hi/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/fr/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/fr/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/es/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/es/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/en/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/en/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/de/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/de/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ca/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/dist/i18n/locales/ca/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/assets/images/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/assets/images/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/assets/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.17.2/assets/icons/"}, "/home/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-cs-1.100.2025051409/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/ms-ceintl.vscode-language-pack-cs-1.100.2025051409/"}, "/home/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-cs-1.100.2025051409/translations/extensions/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/ms-ceintl.vscode-language-pack-cs-1.100.2025051409/translations/extensions/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/dark/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/dark/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/dist/"}, "/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0/dist/browser/extension/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.vscode-pull-request-github-0.110.0/dist/browser/extension/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.322.0/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.322.0/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.322.0/syntaxes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.322.0/syntaxes/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/next-edit/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/media/next-edit/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/common-webviews/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/common-webviews/assets/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/dark/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/dark/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/light/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/light/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/common-webviews/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/common-webviews/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.322.0/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.322.0/dist/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/media/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.322.0/dist/node_modules/@vscode/codicons/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.322.0/dist/node_modules/@vscode/codicons/dist/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.322.0/assets/status/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.322.0/assets/status/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.441.1/media/keyboard/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/weave/logs/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/weave/logs/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6^userContextId=**********/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6^userContextId=**********/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++00bd2ddf-b75c-4022-8063-4cc88e35dc1a^userContextId=**********/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++00bd2ddf-b75c-4022-8063-4cc88e35dc1a^userContextId=**********/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/saved-telemetry-pings/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/saved-telemetry-pings/"}, "/home/<USER>/.config/Code/logs/20250518T124728/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/output_20250518T124732/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/output_20250518T124732/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/vscode.github/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/vscode.git/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/output_logging_20250518T124732/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/output_logging_20250518T124732/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/GitHub.vscode-pull-request-github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/GitHub.vscode-pull-request-github/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/GitHub.copilot-chat/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/GitHub.copilot-chat/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/GitHub.copilot/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/GitHub.copilot/"}, "/home/<USER>/.config/Code/clp/c22e3cea639bb83d07b694b2b61040e9.cs/": {"rootPath": "/home", "relPath": "tv/.config/Code/clp/c22e3cea639bb83d07b694b2b61040e9.cs/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/thumbnails/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/thumbnails/"}, "/home/<USER>/.config/Code/WebStorage/": {"rootPath": "/home", "relPath": "tv/.config/Code/WebStorage/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/Augment.vscode-augment/"}, "/home/<USER>/.config/Code/logs/20250518T124728/window1/exthost/vscode.json-language-features/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250518T124728/window1/exthost/vscode.json-language-features/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++accounts.google.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++accounts.google.com/ls/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++login.tailscale.com/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++login.tailscale.com/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++login.tailscale.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++login.tailscale.com/ls/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++github.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++github.com/ls/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++github.com/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++github.com/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/doomed/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/doomed/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++84daf55a-73bd-480d-a7f9-72ba55522a92^userContextId=**********/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++84daf55a-73bd-480d-a7f9-72ba55522a92^userContextId=**********/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++530911e9-553e-4cb5-ac3b-b4e66f5aa604^userContextId=**********/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++530911e9-553e-4cb5-ac3b-b4e66f5aa604^userContextId=**********/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.google.com/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/cache/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/cache/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/idb/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/idb/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com^partitionKey=%28https%2Cgoogle.com%29/ls/"}, "/home/<USER>/.ssh/": {"rootPath": "/home", "relPath": "tv/.ssh/"}, "/home/<USER>/.config/tailscale/": {"rootPath": "/home", "relPath": "tv/.config/tailscale/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/datareporting/glean/pending_pings/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/datareporting/glean/pending_pings/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++af739c0f-756e-4f88-b868-83df4d264dc6^userContextId=**********/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++af739c0f-756e-4f88-b868-83df4d264dc6^userContextId=**********/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/moz-extension+++56a66393-22b7-4567-a584-da91fc8f2cd6/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/ls/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++www.youtube.com/ls/"}, "/home/<USER>/.cache/": {"rootPath": "/home", "relPath": "tv/.cache/"}, "/home/<USER>/bybit-trading-assistant/venvn/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/venvn/"}, "/home/<USER>/bybit-trading-assistant/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/"}, "/home/<USER>/bybit-trading-assistant/venv/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/venv/"}, "/home/<USER>/bybit-trading-assistant/venv/bin/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/venv/bin/"}, "/home/<USER>/bybit-trading-assistant/venv/include/site/python3.12/greenlet/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/venv/include/site/python3.12/greenlet/"}, "/home/<USER>/bybit-trading-assistant/src/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/"}, "/home/<USER>/bybit-trading-assistant/src/application/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/application/"}, "/home/<USER>/bybit-trading-assistant/src/domain/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/domain/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/external/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/external/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/persistence/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/persistence/"}, "/home/<USER>/bybit-trading-assistant/tests/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/tests/"}, "/home/<USER>/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/": {"rootPath": "/home", "relPath": "tv/.cache/mozilla/firefox/gdmi3eu8.default-release/cache2/"}, "/home/<USER>/bybit-trading-assistant/examples/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/examples/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/external/tradingview/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/external/tradingview/"}, "/home/<USER>/bybit-trading-assistant/config/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/config/"}, "/home/<USER>/lakylukperun2.0/": {"rootPath": "/home", "relPath": "tv/lakylukperun2.0/"}, "/home/<USER>/bybit-trading-assistant/src/strategies/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/strategies/"}, "/home/<USER>/bybit-trading-assistant/src/models/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/models/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/persistence/memory/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/persistence/memory/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/persistence/database/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/persistence/database/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/external/tradingview/node_scripts/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/external/tradingview/node_scripts/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/external/openai/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/external/openai/"}, "/home/<USER>/bybit-trading-assistant/src/infrastructure/external/bybit/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/infrastructure/external/bybit/"}, "/home/<USER>/bybit-trading-assistant/src/domain/services/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/domain/services/"}, "/home/<USER>/bybit-trading-assistant/src/domain/models/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/domain/models/"}, "/home/<USER>/bybit-trading-assistant/src/config/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/config/"}, "/home/<USER>/bybit-trading-assistant/src/application/services/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/application/services/"}, "/home/<USER>/bybit-trading-assistant/src/api/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/api/"}, "/home/<USER>/bybit-trading-assistant/src/api/wrappers/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/api/wrappers/"}, "/home/<USER>/bybit-trading-assistant/src/api/wrappers/node_scripts/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/src/api/wrappers/node_scripts/"}, "/home/<USER>/bybit-trading-assistant/node_modules/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/"}, "/home/<USER>/bybit-trading-assistant/node_modules/ws/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/ws/"}, "/home/<USER>/bybit-trading-assistant/node_modules/util-deprecate/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/util-deprecate/"}, "/home/<USER>/bybit-trading-assistant/node_modules/string_decoder/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/string_decoder/"}, "/home/<USER>/bybit-trading-assistant/node_modules/setimmediate/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/setimmediate/"}, "/home/<USER>/bybit-trading-assistant/node_modules/safe-buffer/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/safe-buffer/"}, "/home/<USER>/bybit-trading-assistant/node_modules/hasown/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/hasown/"}, "/home/<USER>/bybit-trading-assistant/node_modules/hasown/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/hasown/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-tostringtag/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-tostringtag/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-tostringtag/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-tostringtag/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-tostringtag/test/shams/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-tostringtag/test/shams/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-tostringtag/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-tostringtag/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-symbols/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-symbols/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-symbols/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-symbols/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-symbols/test/shams/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-symbols/test/shams/"}, "/home/<USER>/bybit-trading-assistant/node_modules/has-symbols/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/has-symbols/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/gopd/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/gopd/"}, "/home/<USER>/bybit-trading-assistant/node_modules/gopd/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/gopd/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/gopd/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/gopd/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-proto/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-proto/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-proto/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-proto/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-proto/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-proto/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-intrinsic/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-intrinsic/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-intrinsic/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-intrinsic/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/get-intrinsic/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/get-intrinsic/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/function-bind/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/function-bind/"}, "/home/<USER>/bybit-trading-assistant/node_modules/function-bind/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/function-bind/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/function-bind/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/function-bind/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/form-data/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/form-data/"}, "/home/<USER>/bybit-trading-assistant/node_modules/follow-redirects/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/follow-redirects/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-set-tostringtag/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-set-tostringtag/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-set-tostringtag/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-set-tostringtag/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-object-atoms/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-object-atoms/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-object-atoms/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-object-atoms/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-object-atoms/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-object-atoms/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-errors/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-errors/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-errors/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-errors/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-errors/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-errors/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-define-property/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-define-property/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-define-property/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-define-property/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/es-define-property/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/es-define-property/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/dunder-proto/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/dunder-proto/"}, "/home/<USER>/bybit-trading-assistant/node_modules/dunder-proto/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/dunder-proto/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/dunder-proto/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/dunder-proto/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/delayed-stream/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/delayed-stream/"}, "/home/<USER>/bybit-trading-assistant/node_modules/core-util-is/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/core-util-is/"}, "/home/<USER>/bybit-trading-assistant/node_modules/combined-stream/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/combined-stream/"}, "/home/<USER>/bybit-trading-assistant/node_modules/call-bind-apply-helpers/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/call-bind-apply-helpers/"}, "/home/<USER>/bybit-trading-assistant/node_modules/call-bind-apply-helpers/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/call-bind-apply-helpers/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/call-bind-apply-helpers/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/call-bind-apply-helpers/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/axios/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/axios/"}, "/home/<USER>/bybit-trading-assistant/node_modules/asynckit/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/asynckit/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/quote/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/quote/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/classes/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/classes/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/chart/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/src/chart/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/examples/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/examples/"}, "/home/<USER>/bybit-trading-assistant/node_modules/@mathieuc/tradingview/docs/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/@mathieuc/tradingview/docs/"}, "/home/<USER>/bybit-trading-assistant/migrations/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/migrations/"}, "/home/<USER>/bybit-trading-assistant/node_modules/readable-stream/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/readable-stream/"}, "/home/<USER>/bybit-trading-assistant/node_modules/readable-stream/doc/wg-meetings/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/readable-stream/doc/wg-meetings/"}, "/home/<USER>/bybit-trading-assistant/node_modules/proxy-from-env/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/proxy-from-env/"}, "/home/<USER>/bybit-trading-assistant/node_modules/process-nextick-args/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/process-nextick-args/"}, "/home/<USER>/bybit-trading-assistant/node_modules/pako/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/pako/"}, "/home/<USER>/bybit-trading-assistant/node_modules/mime-types/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/mime-types/"}, "/home/<USER>/bybit-trading-assistant/node_modules/mime-db/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/mime-db/"}, "/home/<USER>/bybit-trading-assistant/node_modules/math-intrinsics/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/math-intrinsics/"}, "/home/<USER>/bybit-trading-assistant/node_modules/math-intrinsics/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/math-intrinsics/test/"}, "/home/<USER>/bybit-trading-assistant/node_modules/math-intrinsics/constants/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/math-intrinsics/constants/"}, "/home/<USER>/bybit-trading-assistant/node_modules/math-intrinsics/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/math-intrinsics/.github/"}, "/home/<USER>/bybit-trading-assistant/node_modules/lie/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/lie/"}, "/home/<USER>/bybit-trading-assistant/node_modules/jszip/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/jszip/"}, "/home/<USER>/bybit-trading-assistant/node_modules/jszip/vendor/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/jszip/vendor/"}, "/home/<USER>/bybit-trading-assistant/node_modules/jszip/.github/workflows/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/jszip/.github/workflows/"}, "/home/<USER>/bybit-trading-assistant/node_modules/isarray/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/isarray/"}, "/home/<USER>/bybit-trading-assistant/node_modules/inherits/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/inherits/"}, "/home/<USER>/bybit-trading-assistant/node_modules/immediate/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/node_modules/immediate/"}, "/home/<USER>/bybit-trading-assistant/migrations/versions/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/migrations/versions/"}, "/home/<USER>/bybit-trading-assistant/logs/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/logs/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/webpack/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/webpack/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/test/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/test/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/test/v5/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/test/v5/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/util/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/util/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/util/websockets/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/util/websockets/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/types/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/types/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/types/websockets/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/types/websockets/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/types/response/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/types/response/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/types/request/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/types/request/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/src/constants/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/src/constants/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/deprecated/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/deprecated/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/User/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/User/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/media/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/media/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/media/next-edit/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/media/next-edit/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Position/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Position/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Account/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Account/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Market/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Market/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Margin-Trade-(Normal)/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Margin-Trade-(Normal)/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Asset/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Asset/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/light/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/light/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Leverage-Token/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Leverage-Token/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/P2P/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/P2P/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Crypto-Loan/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Crypto-Loan/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Earn/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Earn/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/dark/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/dark/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/media/keyboard/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Preupgrade/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Preupgrade/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/docs/images/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/docs/images/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Broker/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Broker/"}, "/home/<USER>/bybit-trading-assistant/docs/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/docs/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spread-Trading/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spread-Trading/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Margin-Trade-(UTA)/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Spot-Margin-Trade-(UTA)/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Institutional-Loan/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Institutional-Loan/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Trade/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Trade/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/.github/workflows/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/.github/workflows/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/docs/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/docs/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Affiliate/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/examples/apidoc/V5/Affiliate/"}, "/home/<USER>/bybit-trading-assistant/external/bybit-api/.github/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/external/bybit-api/.github/"}, "/home/<USER>/bybit-trading-assistant/data/": {"rootPath": "/home", "relPath": "tv/bybit-trading-assistant/data/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/common-webviews/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/common-webviews/"}, "/home/<USER>/.vscode/extensions/augment.vscode-augment-0.458.1/common-webviews/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/augment.vscode-augment-0.458.1/common-webviews/assets/"}, "/home/<USER>/.npm/_cacache/index-v5/15/02/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/15/02/"}, "/home/<USER>/.npm/_cacache/index-v5/14/f7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/14/f7/"}, "/home/<USER>/.npm/_cacache/index-v5/12/c0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/12/c0/"}, "/home/<USER>/.npm/_cacache/index-v5/12/40/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/12/40/"}, "/home/<USER>/.npm/_cacache/index-v5/10/c4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/10/c4/"}, "/home/<USER>/.npm/_cacache/index-v5/10/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/10/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/10/6a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/10/6a/"}, "/home/<USER>/.npm/_cacache/index-v5/10/01/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/10/01/"}, "/home/<USER>/.npm/_cacache/index-v5/0f/75/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0f/75/"}, "/home/<USER>/.npm/_cacache/index-v5/0f/22/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0f/22/"}, "/home/<USER>/.npm/_cacache/index-v5/0f/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0f/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/0f/09/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0f/09/"}, "/home/<USER>/.npm/_cacache/index-v5/08/3b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/08/3b/"}, "/home/<USER>/.npm/_cacache/index-v5/06/d1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/06/d1/"}, "/home/<USER>/.npm/_cacache/index-v5/06/4b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/06/4b/"}, "/home/<USER>/.npm/_cacache/index-v5/06/24/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/06/24/"}, "/home/<USER>/.npm/_cacache/index-v5/06/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/06/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/05/5c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/05/5c/"}, "/home/<USER>/.npm/_cacache/index-v5/04/bb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/04/bb/"}, "/home/<USER>/.npm/_cacache/index-v5/04/b4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/04/b4/"}, "/home/<USER>/.npm/_cacache/index-v5/04/4c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/04/4c/"}, "/home/<USER>/.npm/_cacache/index-v5/03/af/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/03/af/"}, "/home/<USER>/.npm/_cacache/index-v5/03/5d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/03/5d/"}, "/home/<USER>/.npm/_cacache/index-v5/03/0e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/03/0e/"}, "/home/<USER>/.npm/_cacache/index-v5/03/0c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/03/0c/"}, "/home/<USER>/.npm/_cacache/index-v5/01/f7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/01/f7/"}, "/home/<USER>/.npm/_cacache/index-v5/00/c2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/00/c2/"}, "/home/<USER>/.npm/_cacache/index-v5/00/bc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/00/bc/"}, "/home/<USER>/.npm/_cacache/index-v5/00/8f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/00/8f/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/df/12/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/df/12/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/de/bc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/de/bc/"}, "/home/<USER>/.npm/_cacache/index-v5/0b/90/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0b/90/"}, "/home/<USER>/.npm/_cacache/index-v5/09/de/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/09/de/"}, "/home/<USER>/.npm/_cacache/index-v5/08/6a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/08/6a/"}, "/home/<USER>/.npm/_cacache/index-v5/00/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/00/60/"}, "/home/<USER>/.npm/_cacache/index-v5/00/43/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/00/43/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/f8/4c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/f8/4c/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/f1/68/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/f1/68/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/ee/5f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/ee/5f/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/ed/67/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/ed/67/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/ea/ba/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/ea/ba/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/e8/52/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/e8/52/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/bc/41/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/bc/41/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/b6/1a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/b6/1a/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/ae/ca/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/ae/ca/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/94/61/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/94/61/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/8a/0c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/8a/0c/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/85/d4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/85/d4/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/81/1e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/81/1e/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/80/58/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/80/58/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/7f/93/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/7f/93/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/73/35/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/73/35/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/71/73/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/71/73/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/6a/a2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/6a/a2/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/65/42/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/65/42/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/60/22/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/60/22/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/60/10/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/60/10/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/4d/62/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/4d/62/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/45/a0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/45/a0/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/3c/e9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/3c/e9/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/3b/a7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/3b/a7/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/2e/7d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/2e/7d/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/2a/85/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/2a/85/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/27/b6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/27/b6/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/20/0d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/20/0d/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/20/04/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/20/04/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/1f/9b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/1f/9b/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/15/c8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/15/c8/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/12/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/12/8d/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/09/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/09/8d/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/08/f0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/08/f0/"}, "/home/<USER>/.streamlit/": {"rootPath": "/home", "relPath": "tv/.streamlit/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/scripts/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/scripts/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-linux-x64/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-linux-x64/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/lib/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/lib/"}, "/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/lib/glib-2.0/include/": {"rootPath": "/home", "relPath": "tv/.npm-global/lib/node_modules/@anthropic-ai/claude-code/node_modules/@img/sharp-libvips-linux-x64/lib/glib-2.0/include/"}, "/home/<USER>/.npm/": {"rootPath": "/home", "relPath": "tv/.npm/"}, "/home/<USER>/.npm/_logs/": {"rootPath": "/home", "relPath": "tv/.npm/_logs/"}, "/home/<USER>/.npm/_cacache/index-v5/ff/d3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ff/d3/"}, "/home/<USER>/.npm/_cacache/index-v5/ff/5b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ff/5b/"}, "/home/<USER>/.npm/_cacache/index-v5/ff/40/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ff/40/"}, "/home/<USER>/.npm/_cacache/index-v5/fe/b0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fe/b0/"}, "/home/<USER>/.npm/_cacache/index-v5/fe/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fe/26/"}, "/home/<USER>/.npm/_cacache/index-v5/fd/fb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fd/fb/"}, "/home/<USER>/.npm/_cacache/index-v5/fd/53/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fd/53/"}, "/home/<USER>/.npm/_cacache/index-v5/fb/e2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fb/e2/"}, "/home/<USER>/.npm/_cacache/index-v5/fb/c3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fb/c3/"}, "/home/<USER>/.npm/_cacache/index-v5/fa/d2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fa/d2/"}, "/home/<USER>/.npm/_cacache/index-v5/fa/c2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fa/c2/"}, "/home/<USER>/.npm/_cacache/index-v5/fa/aa/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fa/aa/"}, "/home/<USER>/.npm/_cacache/index-v5/fa/88/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/fa/88/"}, "/home/<USER>/.npm/_cacache/index-v5/f9/e8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f9/e8/"}, "/home/<USER>/.npm/_cacache/index-v5/f9/8b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f9/8b/"}, "/home/<USER>/.npm/_cacache/index-v5/f7/7c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f7/7c/"}, "/home/<USER>/.npm/_cacache/index-v5/f6/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f6/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/f6/79/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f6/79/"}, "/home/<USER>/.npm/_cacache/index-v5/f6/46/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f6/46/"}, "/home/<USER>/.npm/_cacache/index-v5/f5/e7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f5/e7/"}, "/home/<USER>/.npm/_cacache/index-v5/f5/91/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f5/91/"}, "/home/<USER>/.npm/_cacache/index-v5/f5/7b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f5/7b/"}, "/home/<USER>/.npm/_cacache/index-v5/f4/82/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f4/82/"}, "/home/<USER>/.npm/_cacache/index-v5/f4/10/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f4/10/"}, "/home/<USER>/.npm/_cacache/index-v5/f3/9c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f3/9c/"}, "/home/<USER>/.npm/_cacache/index-v5/f3/53/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f3/53/"}, "/home/<USER>/.npm/_cacache/index-v5/f3/2c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f3/2c/"}, "/home/<USER>/.npm/_cacache/index-v5/f2/f0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f2/f0/"}, "/home/<USER>/.npm/_cacache/index-v5/f2/b4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f2/b4/"}, "/home/<USER>/.npm/_cacache/index-v5/f2/a8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f2/a8/"}, "/home/<USER>/.npm/_cacache/index-v5/f2/1f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f2/1f/"}, "/home/<USER>/.npm/_cacache/index-v5/f1/ad/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f1/ad/"}, "/home/<USER>/.npm/_cacache/index-v5/f1/17/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f1/17/"}, "/home/<USER>/.npm/_cacache/index-v5/f0/f1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f0/f1/"}, "/home/<USER>/.npm/_cacache/index-v5/f0/c1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f0/c1/"}, "/home/<USER>/.npm/_cacache/index-v5/f0/85/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/f0/85/"}, "/home/<USER>/.npm/_cacache/index-v5/ef/a0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ef/a0/"}, "/home/<USER>/.npm/_cacache/index-v5/ef/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ef/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/ef/56/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ef/56/"}, "/home/<USER>/.npm/_cacache/index-v5/ee/b7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ee/b7/"}, "/home/<USER>/.npm/_cacache/index-v5/ee/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ee/60/"}, "/home/<USER>/.npm/_cacache/index-v5/ee/3d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ee/3d/"}, "/home/<USER>/.npm/_cacache/index-v5/ee/1c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ee/1c/"}, "/home/<USER>/.npm/_cacache/index-v5/ed/ae/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ed/ae/"}, "/home/<USER>/.npm/_cacache/index-v5/ec/d9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ec/d9/"}, "/home/<USER>/.npm/_cacache/index-v5/eb/b8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/eb/b8/"}, "/home/<USER>/.npm/_cacache/index-v5/ea/bd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ea/bd/"}, "/home/<USER>/.npm/_cacache/index-v5/ea/5f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ea/5f/"}, "/home/<USER>/.npm/_cacache/index-v5/ea/58/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ea/58/"}, "/home/<USER>/.npm/_cacache/index-v5/e9/e3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e9/e3/"}, "/home/<USER>/.npm/_cacache/index-v5/e9/a9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e9/a9/"}, "/home/<USER>/.npm/_cacache/index-v5/e6/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e6/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/e5/e5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e5/e5/"}, "/home/<USER>/.npm/_cacache/index-v5/e3/e6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e3/e6/"}, "/home/<USER>/.npm/_cacache/index-v5/e3/6a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e3/6a/"}, "/home/<USER>/.npm/_cacache/index-v5/e3/55/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e3/55/"}, "/home/<USER>/.npm/_cacache/index-v5/e3/09/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e3/09/"}, "/home/<USER>/.npm/_cacache/index-v5/e2/53/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e2/53/"}, "/home/<USER>/.npm/_cacache/index-v5/e2/52/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e2/52/"}, "/home/<USER>/.npm/_cacache/index-v5/e1/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e1/da/"}, "/home/<USER>/.npm/_cacache/index-v5/e1/9e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e1/9e/"}, "/home/<USER>/.npm/_cacache/index-v5/e0/c9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/e0/c9/"}, "/home/<USER>/.npm/_cacache/index-v5/df/e3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/df/e3/"}, "/home/<USER>/.npm/_cacache/index-v5/df/9e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/df/9e/"}, "/home/<USER>/.npm/_cacache/index-v5/df/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/df/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/df/2a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/df/2a/"}, "/home/<USER>/.npm/_cacache/index-v5/de/3a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/de/3a/"}, "/home/<USER>/.npm/_cacache/index-v5/dd/f2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/dd/f2/"}, "/home/<USER>/.npm/_cacache/index-v5/dd/a3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/dd/a3/"}, "/home/<USER>/.npm/_cacache/index-v5/dd/51/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/dd/51/"}, "/home/<USER>/.npm/_cacache/index-v5/dd/3b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/dd/3b/"}, "/home/<USER>/.npm/_cacache/index-v5/dc/51/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/dc/51/"}, "/home/<USER>/.npm/_cacache/index-v5/db/79/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/db/79/"}, "/home/<USER>/.npm/_cacache/index-v5/db/1e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/db/1e/"}, "/home/<USER>/.npm/_cacache/index-v5/d9/46/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d9/46/"}, "/home/<USER>/.npm/_cacache/index-v5/d8/c0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d8/c0/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/f5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/f5/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/d8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/d8/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/9c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/9c/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/88/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/88/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/77/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/77/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/67/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/67/"}, "/home/<USER>/.npm/_cacache/index-v5/d7/62/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d7/62/"}, "/home/<USER>/.npm/_cacache/index-v5/d6/d6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d6/d6/"}, "/home/<USER>/.npm/_cacache/index-v5/d6/47/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d6/47/"}, "/home/<USER>/.npm/_cacache/index-v5/d6/3c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d6/3c/"}, "/home/<USER>/.npm/_cacache/index-v5/d5/f4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d5/f4/"}, "/home/<USER>/.npm/_cacache/index-v5/d5/c4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d5/c4/"}, "/home/<USER>/.npm/_cacache/index-v5/d5/94/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d5/94/"}, "/home/<USER>/.npm/_cacache/index-v5/d5/1b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d5/1b/"}, "/home/<USER>/.npm/_cacache/index-v5/d4/40/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d4/40/"}, "/home/<USER>/.npm/_cacache/index-v5/d3/82/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d3/82/"}, "/home/<USER>/.npm/_cacache/index-v5/d2/dd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d2/dd/"}, "/home/<USER>/.npm/_cacache/index-v5/d1/47/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d1/47/"}, "/home/<USER>/.npm/_cacache/index-v5/d1/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d1/26/"}, "/home/<USER>/.npm/_cacache/index-v5/d0/f3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d0/f3/"}, "/home/<USER>/.npm/_cacache/index-v5/d0/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d0/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/d0/d2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/d0/d2/"}, "/home/<USER>/.npm/_cacache/index-v5/cf/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cf/26/"}, "/home/<USER>/.npm/_cacache/index-v5/ce/72/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ce/72/"}, "/home/<USER>/.npm/_cacache/index-v5/ce/0c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ce/0c/"}, "/home/<USER>/.npm/_cacache/index-v5/cd/d3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cd/d3/"}, "/home/<USER>/.npm/_cacache/index-v5/cd/c6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cd/c6/"}, "/home/<USER>/.npm/_cacache/index-v5/cd/b0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cd/b0/"}, "/home/<USER>/.npm/_cacache/index-v5/cc/96/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cc/96/"}, "/home/<USER>/.npm/_cacache/index-v5/cc/09/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cc/09/"}, "/home/<USER>/.npm/_cacache/index-v5/cc/02/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cc/02/"}, "/home/<USER>/.npm/_cacache/index-v5/cb/f4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cb/f4/"}, "/home/<USER>/.npm/_cacache/index-v5/cb/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cb/da/"}, "/home/<USER>/.npm/_cacache/index-v5/cb/20/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/cb/20/"}, "/home/<USER>/.npm/_cacache/index-v5/ca/de/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ca/de/"}, "/home/<USER>/.npm/_cacache/index-v5/ca/c5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ca/c5/"}, "/home/<USER>/.npm/_cacache/index-v5/ca/72/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ca/72/"}, "/home/<USER>/.npm/_cacache/index-v5/c9/f5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c9/f5/"}, "/home/<USER>/.npm/_cacache/index-v5/c9/5b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c9/5b/"}, "/home/<USER>/.npm/_cacache/index-v5/c9/10/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c9/10/"}, "/home/<USER>/.npm/_cacache/index-v5/c8/fc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c8/fc/"}, "/home/<USER>/.npm/_cacache/index-v5/c6/45/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c6/45/"}, "/home/<USER>/.npm/_cacache/index-v5/c5/39/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c5/39/"}, "/home/<USER>/.npm/_cacache/index-v5/c5/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c5/26/"}, "/home/<USER>/.npm/_cacache/index-v5/c4/be/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c4/be/"}, "/home/<USER>/.npm/_cacache/index-v5/c4/bd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c4/bd/"}, "/home/<USER>/.npm/_cacache/index-v5/c4/b3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c4/b3/"}, "/home/<USER>/.npm/_cacache/index-v5/c4/ac/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c4/ac/"}, "/home/<USER>/.npm/_cacache/index-v5/c4/2f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c4/2f/"}, "/home/<USER>/.npm/_cacache/index-v5/c3/ff/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c3/ff/"}, "/home/<USER>/.npm/_cacache/index-v5/c3/ea/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c3/ea/"}, "/home/<USER>/.npm/_cacache/index-v5/c2/a4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c2/a4/"}, "/home/<USER>/.npm/_cacache/index-v5/c1/d3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c1/d3/"}, "/home/<USER>/.npm/_cacache/index-v5/c1/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c1/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/c1/62/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c1/62/"}, "/home/<USER>/.npm/_cacache/index-v5/c1/02/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c1/02/"}, "/home/<USER>/.npm/_cacache/index-v5/c0/89/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c0/89/"}, "/home/<USER>/.npm/_cacache/index-v5/c0/66/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c0/66/"}, "/home/<USER>/.npm/_cacache/index-v5/c0/31/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c0/31/"}, "/home/<USER>/.npm/_cacache/index-v5/c0/06/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/c0/06/"}, "/home/<USER>/.npm/_cacache/index-v5/bf/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bf/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/bf/2c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bf/2c/"}, "/home/<USER>/.npm/_cacache/index-v5/be/a3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/be/a3/"}, "/home/<USER>/.npm/_cacache/index-v5/be/9f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/be/9f/"}, "/home/<USER>/.npm/_cacache/index-v5/be/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/be/8d/"}, "/home/<USER>/.npm/_cacache/index-v5/bd/12/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bd/12/"}, "/home/<USER>/.npm/_cacache/index-v5/bc/96/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bc/96/"}, "/home/<USER>/.npm/_cacache/index-v5/bc/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bc/60/"}, "/home/<USER>/.npm/_cacache/index-v5/bc/47/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bc/47/"}, "/home/<USER>/.npm/_cacache/index-v5/bb/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bb/da/"}, "/home/<USER>/.npm/_cacache/index-v5/bb/68/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/bb/68/"}, "/home/<USER>/.npm/_cacache/index-v5/ba/d5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ba/d5/"}, "/home/<USER>/.npm/_cacache/index-v5/ba/31/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ba/31/"}, "/home/<USER>/.npm/_cacache/index-v5/ba/23/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ba/23/"}, "/home/<USER>/.npm/_cacache/index-v5/b9/bf/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b9/bf/"}, "/home/<USER>/.npm/_cacache/index-v5/b9/73/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b9/73/"}, "/home/<USER>/.npm/_cacache/index-v5/b9/22/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b9/22/"}, "/home/<USER>/.npm/_cacache/index-v5/b8/b6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b8/b6/"}, "/home/<USER>/.npm/_cacache/index-v5/b8/3c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b8/3c/"}, "/home/<USER>/.npm/_cacache/index-v5/b8/04/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b8/04/"}, "/home/<USER>/.npm/_cacache/index-v5/b7/62/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b7/62/"}, "/home/<USER>/.npm/_cacache/index-v5/b7/38/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b7/38/"}, "/home/<USER>/.npm/_cacache/index-v5/b7/28/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b7/28/"}, "/home/<USER>/.npm/_cacache/index-v5/b5/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b5/26/"}, "/home/<USER>/.npm/_cacache/index-v5/b5/05/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b5/05/"}, "/home/<USER>/.npm/_cacache/index-v5/b4/e1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b4/e1/"}, "/home/<USER>/.npm/_cacache/index-v5/b4/9d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b4/9d/"}, "/home/<USER>/.npm/_cacache/index-v5/b4/1a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b4/1a/"}, "/home/<USER>/.npm/_cacache/index-v5/b4/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b4/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/b3/b3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b3/b3/"}, "/home/<USER>/.npm/_cacache/index-v5/b3/66/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b3/66/"}, "/home/<USER>/.npm/_cacache/index-v5/b3/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b3/60/"}, "/home/<USER>/.npm/_cacache/index-v5/b3/0d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b3/0d/"}, "/home/<USER>/.npm/_cacache/index-v5/b1/96/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b1/96/"}, "/home/<USER>/.npm/_cacache/index-v5/b0/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b0/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/b0/cc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b0/cc/"}, "/home/<USER>/.npm/_cacache/index-v5/b0/4a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b0/4a/"}, "/home/<USER>/.npm/_cacache/index-v5/b0/2c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/b0/2c/"}, "/home/<USER>/.npm/_cacache/index-v5/af/bc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/af/bc/"}, "/home/<USER>/.npm/_cacache/index-v5/af/5d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/af/5d/"}, "/home/<USER>/.npm/_cacache/index-v5/ac/87/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ac/87/"}, "/home/<USER>/.npm/_cacache/index-v5/ac/86/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ac/86/"}, "/home/<USER>/.npm/_cacache/index-v5/ac/72/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ac/72/"}, "/home/<USER>/.npm/_cacache/index-v5/ac/53/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ac/53/"}, "/home/<USER>/.npm/_cacache/index-v5/ac/45/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ac/45/"}, "/home/<USER>/.npm/_cacache/index-v5/ab/bd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ab/bd/"}, "/home/<USER>/.npm/_cacache/index-v5/ab/a3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/ab/a3/"}, "/home/<USER>/.npm/_cacache/index-v5/aa/c4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/aa/c4/"}, "/home/<USER>/.npm/_cacache/index-v5/aa/9b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/aa/9b/"}, "/home/<USER>/.npm/_cacache/index-v5/a9/c7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a9/c7/"}, "/home/<USER>/.npm/_cacache/index-v5/a9/c4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a9/c4/"}, "/home/<USER>/.npm/_cacache/index-v5/a9/23/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a9/23/"}, "/home/<USER>/.npm/_cacache/index-v5/a6/d7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a6/d7/"}, "/home/<USER>/.npm/_cacache/index-v5/a6/90/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a6/90/"}, "/home/<USER>/.npm/_cacache/index-v5/a5/24/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a5/24/"}, "/home/<USER>/.npm/_cacache/index-v5/a5/14/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a5/14/"}, "/home/<USER>/.npm/_cacache/index-v5/a4/ff/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a4/ff/"}, "/home/<USER>/.npm/_cacache/index-v5/a4/f3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a4/f3/"}, "/home/<USER>/.npm/_cacache/index-v5/a4/7a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a4/7a/"}, "/home/<USER>/.npm/_cacache/index-v5/a3/07/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a3/07/"}, "/home/<USER>/.npm/_cacache/index-v5/a2/fb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a2/fb/"}, "/home/<USER>/.npm/_cacache/index-v5/a2/9b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a2/9b/"}, "/home/<USER>/.npm/_cacache/index-v5/a2/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a2/60/"}, "/home/<USER>/.npm/_cacache/index-v5/a2/4a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a2/4a/"}, "/home/<USER>/.npm/_cacache/index-v5/a2/1a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a2/1a/"}, "/home/<USER>/.npm/_cacache/index-v5/a1/ff/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a1/ff/"}, "/home/<USER>/.npm/_cacache/index-v5/a1/56/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a1/56/"}, "/home/<USER>/.npm/_cacache/index-v5/a1/1a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a1/1a/"}, "/home/<USER>/.npm/_cacache/index-v5/a0/33/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/a0/33/"}, "/home/<USER>/.npm/_cacache/index-v5/9f/75/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9f/75/"}, "/home/<USER>/.npm/_cacache/index-v5/9f/43/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9f/43/"}, "/home/<USER>/.npm/_cacache/index-v5/9f/27/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9f/27/"}, "/home/<USER>/.npm/_cacache/index-v5/9e/eb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9e/eb/"}, "/home/<USER>/.npm/_cacache/index-v5/9d/f1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9d/f1/"}, "/home/<USER>/.npm/_cacache/index-v5/9d/6e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9d/6e/"}, "/home/<USER>/.npm/_cacache/index-v5/9d/6b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9d/6b/"}, "/home/<USER>/.npm/_cacache/index-v5/9c/a9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9c/a9/"}, "/home/<USER>/.npm/_cacache/index-v5/9c/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9c/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/9b/e7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9b/e7/"}, "/home/<USER>/.npm/_cacache/index-v5/9b/b8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9b/b8/"}, "/home/<USER>/.npm/_cacache/index-v5/9b/5d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9b/5d/"}, "/home/<USER>/.npm/_cacache/index-v5/9b/5b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9b/5b/"}, "/home/<USER>/.npm/_cacache/index-v5/9b/3f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9b/3f/"}, "/home/<USER>/.npm/_cacache/index-v5/9a/46/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9a/46/"}, "/home/<USER>/.npm/_cacache/index-v5/9a/3d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/9a/3d/"}, "/home/<USER>/.npm/_cacache/index-v5/99/a6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/99/a6/"}, "/home/<USER>/.npm/_cacache/index-v5/98/6a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/98/6a/"}, "/home/<USER>/.npm/_cacache/index-v5/98/63/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/98/63/"}, "/home/<USER>/.npm/_cacache/index-v5/98/39/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/98/39/"}, "/home/<USER>/.npm/_cacache/index-v5/98/31/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/98/31/"}, "/home/<USER>/.npm/_cacache/index-v5/97/55/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/97/55/"}, "/home/<USER>/.npm/_cacache/index-v5/96/3a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/96/3a/"}, "/home/<USER>/.npm/_cacache/index-v5/95/b5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/95/b5/"}, "/home/<USER>/.npm/_cacache/index-v5/95/af/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/95/af/"}, "/home/<USER>/.npm/_cacache/index-v5/95/20/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/95/20/"}, "/home/<USER>/.npm/_cacache/index-v5/95/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/95/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/94/16/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/94/16/"}, "/home/<USER>/.npm/_cacache/index-v5/93/c9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/93/c9/"}, "/home/<USER>/.npm/_cacache/index-v5/93/a9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/93/a9/"}, "/home/<USER>/.npm/_cacache/index-v5/92/af/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/92/af/"}, "/home/<USER>/.npm/_cacache/index-v5/92/9f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/92/9f/"}, "/home/<USER>/.npm/_cacache/index-v5/92/93/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/92/93/"}, "/home/<USER>/.npm/_cacache/index-v5/92/38/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/92/38/"}, "/home/<USER>/.npm/_cacache/index-v5/91/73/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/91/73/"}, "/home/<USER>/.npm/_cacache/index-v5/91/2f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/91/2f/"}, "/home/<USER>/.npm/_cacache/index-v5/91/07/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/91/07/"}, "/home/<USER>/.npm/_cacache/index-v5/90/d2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/90/d2/"}, "/home/<USER>/.npm/_cacache/index-v5/90/93/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/90/93/"}, "/home/<USER>/.npm/_cacache/index-v5/8f/e7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8f/e7/"}, "/home/<USER>/.npm/_cacache/index-v5/8e/4d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8e/4d/"}, "/home/<USER>/.npm/_cacache/index-v5/8e/0f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8e/0f/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/f8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/f8/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/a5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/a5/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/a1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/a1/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/41/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/41/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/0e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/0e/"}, "/home/<USER>/.npm/_cacache/index-v5/8d/09/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8d/09/"}, "/home/<USER>/.npm/_cacache/index-v5/8c/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8c/8d/"}, "/home/<USER>/.npm/_cacache/index-v5/8c/3f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8c/3f/"}, "/home/<USER>/.npm/_cacache/index-v5/8c/03/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8c/03/"}, "/home/<USER>/.npm/_cacache/index-v5/8b/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8b/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/8a/d6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8a/d6/"}, "/home/<USER>/.npm/_cacache/index-v5/8a/c9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8a/c9/"}, "/home/<USER>/.npm/_cacache/index-v5/8a/ae/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/8a/ae/"}, "/home/<USER>/.npm/_cacache/index-v5/89/85/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/89/85/"}, "/home/<USER>/.npm/_cacache/index-v5/89/0b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/89/0b/"}, "/home/<USER>/.npm/_cacache/index-v5/88/ed/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/88/ed/"}, "/home/<USER>/.npm/_cacache/index-v5/88/cb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/88/cb/"}, "/home/<USER>/.npm/_cacache/index-v5/87/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/87/da/"}, "/home/<USER>/.npm/_cacache/index-v5/87/55/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/87/55/"}, "/home/<USER>/.npm/_cacache/index-v5/86/e1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/86/e1/"}, "/home/<USER>/.npm/_cacache/index-v5/86/d4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/86/d4/"}, "/home/<USER>/.npm/_cacache/index-v5/85/e1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/85/e1/"}, "/home/<USER>/.npm/_cacache/index-v5/85/4f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/85/4f/"}, "/home/<USER>/.npm/_cacache/index-v5/84/d7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/84/d7/"}, "/home/<USER>/.npm/_cacache/index-v5/84/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/84/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/84/87/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/84/87/"}, "/home/<USER>/.npm/_cacache/index-v5/84/3c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/84/3c/"}, "/home/<USER>/.npm/_cacache/index-v5/84/24/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/84/24/"}, "/home/<USER>/.npm/_cacache/index-v5/83/f3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/83/f3/"}, "/home/<USER>/.npm/_cacache/index-v5/83/4d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/83/4d/"}, "/home/<USER>/.npm/_cacache/index-v5/83/3f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/83/3f/"}, "/home/<USER>/.npm/_cacache/index-v5/82/c6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/82/c6/"}, "/home/<USER>/.npm/_cacache/index-v5/82/9b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/82/9b/"}, "/home/<USER>/.npm/_cacache/index-v5/82/6b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/82/6b/"}, "/home/<USER>/.npm/_cacache/index-v5/82/22/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/82/22/"}, "/home/<USER>/.npm/_cacache/index-v5/81/87/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/81/87/"}, "/home/<USER>/.npm/_cacache/index-v5/81/5b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/81/5b/"}, "/home/<USER>/.npm/_cacache/index-v5/81/46/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/81/46/"}, "/home/<USER>/.npm/_cacache/index-v5/81/18/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/81/18/"}, "/home/<USER>/.npm/_cacache/index-v5/7f/58/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7f/58/"}, "/home/<USER>/.npm/_cacache/index-v5/7f/13/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7f/13/"}, "/home/<USER>/.npm/_cacache/index-v5/7e/db/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7e/db/"}, "/home/<USER>/.npm/_cacache/index-v5/7e/af/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7e/af/"}, "/home/<USER>/.npm/_cacache/index-v5/7e/1e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7e/1e/"}, "/home/<USER>/.npm/_cacache/index-v5/7d/e8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7d/e8/"}, "/home/<USER>/.npm/_cacache/index-v5/7d/8f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7d/8f/"}, "/home/<USER>/.npm/_cacache/index-v5/7d/65/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7d/65/"}, "/home/<USER>/.npm/_cacache/index-v5/7d/35/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7d/35/"}, "/home/<USER>/.npm/_cacache/index-v5/7c/76/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7c/76/"}, "/home/<USER>/.npm/_cacache/index-v5/7b/89/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7b/89/"}, "/home/<USER>/.npm/_cacache/index-v5/7b/65/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7b/65/"}, "/home/<USER>/.npm/_cacache/index-v5/7b/32/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7b/32/"}, "/home/<USER>/.npm/_cacache/index-v5/7a/e8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7a/e8/"}, "/home/<USER>/.npm/_cacache/index-v5/7a/1e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/7a/1e/"}, "/home/<USER>/.npm/_cacache/index-v5/79/72/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/79/72/"}, "/home/<USER>/.npm/_cacache/index-v5/79/51/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/79/51/"}, "/home/<USER>/.npm/_cacache/index-v5/79/23/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/79/23/"}, "/home/<USER>/.npm/_cacache/index-v5/78/23/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/78/23/"}, "/home/<USER>/.npm/_cacache/index-v5/76/a5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/76/a5/"}, "/home/<USER>/.npm/_cacache/index-v5/76/27/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/76/27/"}, "/home/<USER>/.npm/_cacache/index-v5/75/e3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/75/e3/"}, "/home/<USER>/.npm/_cacache/index-v5/75/d3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/75/d3/"}, "/home/<USER>/.npm/_cacache/index-v5/75/17/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/75/17/"}, "/home/<USER>/.npm/_cacache/index-v5/74/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/74/8d/"}, "/home/<USER>/.npm/_cacache/index-v5/72/e0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/72/e0/"}, "/home/<USER>/.npm/_cacache/index-v5/72/6f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/72/6f/"}, "/home/<USER>/.npm/_cacache/index-v5/71/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/71/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/6f/ae/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6f/ae/"}, "/home/<USER>/.npm/_cacache/index-v5/6f/30/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6f/30/"}, "/home/<USER>/.npm/_cacache/index-v5/6e/69/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6e/69/"}, "/home/<USER>/.npm/_cacache/index-v5/6d/eb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6d/eb/"}, "/home/<USER>/.npm/_cacache/index-v5/6d/9d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6d/9d/"}, "/home/<USER>/.npm/_cacache/index-v5/6d/7f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6d/7f/"}, "/home/<USER>/.npm/_cacache/index-v5/6d/61/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6d/61/"}, "/home/<USER>/.npm/_cacache/index-v5/6d/32/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6d/32/"}, "/home/<USER>/.npm/_cacache/index-v5/6c/db/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6c/db/"}, "/home/<USER>/.npm/_cacache/index-v5/6c/c4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6c/c4/"}, "/home/<USER>/.npm/_cacache/index-v5/6c/6f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6c/6f/"}, "/home/<USER>/.npm/_cacache/index-v5/6b/ac/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6b/ac/"}, "/home/<USER>/.npm/_cacache/index-v5/6b/96/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6b/96/"}, "/home/<USER>/.npm/_cacache/index-v5/34/01/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/34/01/"}, "/home/<USER>/.npm/_cacache/index-v5/35/7f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/35/7f/"}, "/home/<USER>/.npm/_cacache/index-v5/1c/e6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1c/e6/"}, "/home/<USER>/.npm/_cacache/index-v5/41/27/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/41/27/"}, "/home/<USER>/.npm/_cacache/index-v5/22/f3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/22/f3/"}, "/home/<USER>/.npm/_cacache/index-v5/24/e3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/24/e3/"}, "/home/<USER>/.npm/_cacache/index-v5/2c/24/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2c/24/"}, "/home/<USER>/.npm/_cacache/index-v5/1e/c2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1e/c2/"}, "/home/<USER>/.npm/_cacache/index-v5/61/89/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/61/89/"}, "/home/<USER>/.npm/_cacache/index-v5/5b/51/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5b/51/"}, "/home/<USER>/.npm/_cacache/index-v5/2a/b7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2a/b7/"}, "/home/<USER>/.npm/_cacache/index-v5/62/44/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/62/44/"}, "/home/<USER>/.npm/_cacache/index-v5/5b/5b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5b/5b/"}, "/home/<USER>/.npm/_cacache/index-v5/1b/15/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1b/15/"}, "/home/<USER>/.npm/_cacache/index-v5/4a/db/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4a/db/"}, "/home/<USER>/.npm/_cacache/index-v5/15/20/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/15/20/"}, "/home/<USER>/.npm/_cacache/index-v5/3e/77/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3e/77/"}, "/home/<USER>/.npm/_cacache/index-v5/20/e0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/20/e0/"}, "/home/<USER>/.npm/_cacache/index-v5/35/61/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/35/61/"}, "/home/<USER>/.npm/_cacache/index-v5/4a/f9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4a/f9/"}, "/home/<USER>/.npm/_cacache/index-v5/3c/d3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3c/d3/"}, "/home/<USER>/.npm/_cacache/index-v5/4c/04/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4c/04/"}, "/home/<USER>/.npm/_cacache/index-v5/53/aa/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/53/aa/"}, "/home/<USER>/.npm/_cacache/index-v5/37/fa/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/37/fa/"}, "/home/<USER>/.npm/_cacache/index-v5/2f/cf/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2f/cf/"}, "/home/<USER>/.npm/_cacache/index-v5/24/d7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/24/d7/"}, "/home/<USER>/.npm/_cacache/index-v5/31/ca/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/31/ca/"}, "/home/<USER>/.npm/_cacache/index-v5/62/82/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/62/82/"}, "/home/<USER>/.npm/_cacache/index-v5/23/30/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/23/30/"}, "/home/<USER>/.npm/_cacache/index-v5/4f/21/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4f/21/"}, "/home/<USER>/.npm/_cacache/index-v5/3d/dd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3d/dd/"}, "/home/<USER>/.npm/_cacache/index-v5/1a/fd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1a/fd/"}, "/home/<USER>/.npm/_cacache/index-v5/64/6f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/64/6f/"}, "/home/<USER>/.npm/_cacache/index-v5/28/1d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/28/1d/"}, "/home/<USER>/.npm/_cacache/index-v5/18/55/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/18/55/"}, "/home/<USER>/.npm/_cacache/index-v5/66/84/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/66/84/"}, "/home/<USER>/.npm/_cacache/index-v5/1f/8b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1f/8b/"}, "/home/<USER>/.npm/_cacache/index-v5/41/b3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/41/b3/"}, "/home/<USER>/.npm/_cacache/index-v5/54/cc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/54/cc/"}, "/home/<USER>/.npm/_cacache/index-v5/3b/cc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3b/cc/"}, "/home/<USER>/.npm/_cacache/index-v5/44/1f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/44/1f/"}, "/home/<USER>/.npm/_cacache/index-v5/3a/f8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3a/f8/"}, "/home/<USER>/.npm/_cacache/index-v5/4f/bb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4f/bb/"}, "/home/<USER>/.npm/_cacache/index-v5/42/f1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/42/f1/"}, "/home/<USER>/.npm/_cacache/index-v5/51/3c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/51/3c/"}, "/home/<USER>/.npm/_cacache/index-v5/4c/74/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4c/74/"}, "/home/<USER>/.npm/_cacache/index-v5/55/2c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/55/2c/"}, "/home/<USER>/.npm/_cacache/index-v5/42/70/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/42/70/"}, "/home/<USER>/.npm/_cacache/index-v5/40/bd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/40/bd/"}, "/home/<USER>/.npm/_cacache/index-v5/3b/71/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3b/71/"}, "/home/<USER>/.npm/_cacache/index-v5/5a/48/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5a/48/"}, "/home/<USER>/.npm/_cacache/index-v5/1d/d4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1d/d4/"}, "/home/<USER>/.npm/_cacache/index-v5/5b/8d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5b/8d/"}, "/home/<USER>/.npm/_cacache/index-v5/1d/66/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1d/66/"}, "/home/<USER>/.npm/_cacache/index-v5/46/b3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/46/b3/"}, "/home/<USER>/.npm/_cacache/index-v5/5e/43/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5e/43/"}, "/home/<USER>/.npm/_cacache/index-v5/57/c6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/57/c6/"}, "/home/<USER>/.npm/_cacache/index-v5/5e/2f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5e/2f/"}, "/home/<USER>/.npm/_cacache/index-v5/40/2e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/40/2e/"}, "/home/<USER>/.npm/_cacache/index-v5/56/a4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/56/a4/"}, "/home/<USER>/.npm/_cacache/index-v5/29/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/29/da/"}, "/home/<USER>/.npm/_cacache/index-v5/41/36/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/41/36/"}, "/home/<USER>/.npm/_cacache/index-v5/63/ff/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/63/ff/"}, "/home/<USER>/.npm/_cacache/index-v5/1b/36/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1b/36/"}, "/home/<USER>/.npm/_cacache/index-v5/64/9b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/64/9b/"}, "/home/<USER>/.npm/_cacache/index-v5/2e/c8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2e/c8/"}, "/home/<USER>/.npm/_cacache/index-v5/18/ce/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/18/ce/"}, "/home/<USER>/.npm/_cacache/index-v5/34/b8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/34/b8/"}, "/home/<USER>/.npm/_cacache/index-v5/6a/de/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6a/de/"}, "/home/<USER>/.npm/_cacache/index-v5/4c/a8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4c/a8/"}, "/home/<USER>/.npm/_cacache/index-v5/2d/80/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2d/80/"}, "/home/<USER>/.npm/_cacache/index-v5/6b/33/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/6b/33/"}, "/home/<USER>/.npm/_cacache/index-v5/53/39/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/53/39/"}, "/home/<USER>/.npm/_cacache/index-v5/3b/bf/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3b/bf/"}, "/home/<USER>/.npm/_cacache/index-v5/1f/a2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1f/a2/"}, "/home/<USER>/.npm/_cacache/index-v5/39/c3/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/39/c3/"}, "/home/<USER>/.npm/_cacache/index-v5/35/a9/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/35/a9/"}, "/home/<USER>/.npm/_cacache/index-v5/33/ed/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/33/ed/"}, "/home/<USER>/.npm/_cacache/index-v5/3a/ec/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3a/ec/"}, "/home/<USER>/.npm/_cacache/index-v5/22/a4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/22/a4/"}, "/home/<USER>/.npm/_cacache/index-v5/45/a2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/45/a2/"}, "/home/<USER>/.npm/_cacache/index-v5/25/2d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/25/2d/"}, "/home/<USER>/.npm/_cacache/index-v5/31/ac/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/31/ac/"}, "/home/<USER>/.npm/_cacache/index-v5/34/2b/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/34/2b/"}, "/home/<USER>/.npm/_cacache/index-v5/31/a8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/31/a8/"}, "/home/<USER>/.npm/_cacache/index-v5/52/fa/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/52/fa/"}, "/home/<USER>/.npm/_cacache/index-v5/1a/da/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1a/da/"}, "/home/<USER>/.npm/_cacache/index-v5/34/f7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/34/f7/"}, "/home/<USER>/.npm/_cacache/index-v5/24/7e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/24/7e/"}, "/home/<USER>/.npm/_cacache/index-v5/61/d1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/61/d1/"}, "/home/<USER>/.npm/_cacache/index-v5/54/f2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/54/f2/"}, "/home/<USER>/.npm/_cacache/index-v5/57/f7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/57/f7/"}, "/home/<USER>/.npm/_cacache/index-v5/53/37/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/53/37/"}, "/home/<USER>/.npm/_cacache/index-v5/30/54/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/30/54/"}, "/home/<USER>/.npm/_cacache/index-v5/43/d6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/43/d6/"}, "/home/<USER>/.npm/_cacache/index-v5/60/59/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/60/59/"}, "/home/<USER>/.npm/_cacache/index-v5/4a/1d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4a/1d/"}, "/home/<USER>/.npm/_cacache/index-v5/44/b0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/44/b0/"}, "/home/<USER>/.npm/_cacache/index-v5/61/86/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/61/86/"}, "/home/<USER>/.npm/_cacache/index-v5/2d/0f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2d/0f/"}, "/home/<USER>/.npm/_cacache/index-v5/33/be/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/33/be/"}, "/home/<USER>/.npm/_cacache/index-v5/5a/6c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5a/6c/"}, "/home/<USER>/.npm/_cacache/index-v5/23/3e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/23/3e/"}, "/home/<USER>/.npm/_cacache/index-v5/3c/f6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3c/f6/"}, "/home/<USER>/.npm/_cacache/index-v5/4a/c0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4a/c0/"}, "/home/<USER>/.npm/_cacache/index-v5/5f/b2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5f/b2/"}, "/home/<USER>/.npm/_cacache/index-v5/37/41/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/37/41/"}, "/home/<USER>/.npm/_cacache/index-v5/21/5c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/21/5c/"}, "/home/<USER>/.npm/_cacache/index-v5/2c/a1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2c/a1/"}, "/home/<USER>/.npm/_cacache/index-v5/29/eb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/29/eb/"}, "/home/<USER>/.npm/_cacache/index-v5/63/c0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/63/c0/"}, "/home/<USER>/.npm/_cacache/index-v5/24/ed/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/24/ed/"}, "/home/<USER>/.npm/_cacache/index-v5/69/54/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/69/54/"}, "/home/<USER>/.npm/_cacache/index-v5/2f/b5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2f/b5/"}, "/home/<USER>/.npm/_cacache/index-v5/48/b0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/48/b0/"}, "/home/<USER>/.npm/_cacache/index-v5/46/e4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/46/e4/"}, "/home/<USER>/.npm/_cacache/index-v5/47/95/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/47/95/"}, "/home/<USER>/.npm/_cacache/index-v5/18/69/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/18/69/"}, "/home/<USER>/.npm/_cacache/index-v5/68/26/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/68/26/"}, "/home/<USER>/.npm/_cacache/index-v5/1d/a1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1d/a1/"}, "/home/<USER>/.npm/_cacache/index-v5/55/06/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/55/06/"}, "/home/<USER>/.npm/_cacache/index-v5/2a/52/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2a/52/"}, "/home/<USER>/.npm/_cacache/index-v5/4b/3f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4b/3f/"}, "/home/<USER>/.npm/_cacache/index-v5/43/a0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/43/a0/"}, "/home/<USER>/.npm/_cacache/index-v5/25/6e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/25/6e/"}, "/home/<USER>/.npm/_cacache/index-v5/3b/1c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/3b/1c/"}, "/home/<USER>/.npm/_cacache/index-v5/31/f4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/31/f4/"}, "/home/<USER>/.npm/_cacache/index-v5/1d/1f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1d/1f/"}, "/home/<USER>/.npm/_cacache/index-v5/28/af/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/28/af/"}, "/home/<USER>/.npm/_cacache/index-v5/67/7e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/67/7e/"}, "/home/<USER>/.npm/_cacache/index-v5/38/44/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/38/44/"}, "/home/<USER>/.npm/_cacache/index-v5/5d/88/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5d/88/"}, "/home/<USER>/.npm/_cacache/index-v5/5f/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5f/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/15/65/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/15/65/"}, "/home/<USER>/.npm/_cacache/index-v5/64/28/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/64/28/"}, "/home/<USER>/.npm/_cacache/index-v5/16/a0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/16/a0/"}, "/home/<USER>/.npm/_cacache/index-v5/23/76/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/23/76/"}, "/home/<USER>/.npm/_cacache/index-v5/33/83/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/33/83/"}, "/home/<USER>/.npm/_cacache/index-v5/61/34/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/61/34/"}, "/home/<USER>/.npm/_cacache/index-v5/60/1a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/60/1a/"}, "/home/<USER>/.npm/_cacache/index-v5/1b/09/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1b/09/"}, "/home/<USER>/.npm/_cacache/index-v5/40/cb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/40/cb/"}, "/home/<USER>/.npm/_cacache/index-v5/51/ea/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/51/ea/"}, "/home/<USER>/.npm/_cacache/index-v5/51/ff/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/51/ff/"}, "/home/<USER>/.npm/_cacache/index-v5/5a/8c/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5a/8c/"}, "/home/<USER>/.npm/_cacache/index-v5/37/ed/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/37/ed/"}, "/home/<USER>/.npm/_cacache/index-v5/29/c7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/29/c7/"}, "/home/<USER>/.npm/_cacache/index-v5/59/0e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/59/0e/"}, "/home/<USER>/.npm/_cacache/index-v5/46/94/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/46/94/"}, "/home/<USER>/.npm/_cacache/index-v5/41/c5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/41/c5/"}, "/home/<USER>/.npm/_cacache/index-v5/24/ae/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/24/ae/"}, "/home/<USER>/.npm/_cacache/index-v5/1d/0d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1d/0d/"}, "/home/<USER>/.npm/_cacache/index-v5/27/b7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/27/b7/"}, "/home/<USER>/.npm/_cacache/index-v5/5e/e7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5e/e7/"}, "/home/<USER>/.npm/_cacache/index-v5/26/53/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/26/53/"}, "/home/<USER>/.npm/_cacache/index-v5/54/ad/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/54/ad/"}, "/home/<USER>/.npm/_cacache/index-v5/4f/eb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4f/eb/"}, "/home/<USER>/.npm/_cacache/index-v5/25/7f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/25/7f/"}, "/home/<USER>/.npm/_cacache/index-v5/36/d6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/36/d6/"}, "/home/<USER>/.npm/_cacache/index-v5/4e/cb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4e/cb/"}, "/home/<USER>/.npm/_cacache/index-v5/59/08/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/59/08/"}, "/home/<USER>/.npm/_cacache/index-v5/4b/4d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/4b/4d/"}, "/home/<USER>/.npm/_cacache/index-v5/56/02/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/56/02/"}, "/home/<USER>/.npm/_cacache/index-v5/52/1d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/52/1d/"}, "/home/<USER>/.npm/_cacache/index-v5/60/29/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/60/29/"}, "/home/<USER>/.npm/_cacache/index-v5/5f/85/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/5f/85/"}, "/home/<USER>/.npm/_cacache/index-v5/27/cc/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/27/cc/"}, "/home/<USER>/.npm/_cacache/index-v5/32/a8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/32/a8/"}, "/home/<USER>/.npm/_cacache/index-v5/35/41/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/35/41/"}, "/home/<USER>/.npm/_cacache/index-v5/49/fb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/49/fb/"}, "/home/<USER>/.npm/_cacache/index-v5/2d/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/2d/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/46/33/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/46/33/"}, "/home/<USER>/.npm/_cacache/index-v5/45/ee/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/45/ee/"}, "/home/<USER>/.npm/_cacache/index-v5/40/62/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/40/62/"}, "/home/<USER>/.npm/_cacache/index-v5/1c/a4/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/1c/a4/"}, "/home/<USER>/.npm/_cacache/index-v5/67/f8/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/67/f8/"}, "/home/<USER>/.npm/_cacache/index-v5/42/01/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/42/01/"}, "/home/<USER>/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++metamask.github.io/cache/": {"rootPath": "/home", "relPath": "tv/.mozilla/firefox/gdmi3eu8.default-release/storage/default/https+++metamask.github.io/cache/"}, "/home/<USER>/.config/Code/logs/20250525T115816/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/output_20250525T115819/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/output_20250525T115819/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/vscode.github-authentication/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/vscode.github-authentication/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/vscode.github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/vscode.github/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/vscode.git/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/vscode.git/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/output_logging_20250525T115820/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/output_logging_20250525T115820/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/GitHub.vscode-pull-request-github/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/GitHub.vscode-pull-request-github/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/GitHub.copilot-chat/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/GitHub.copilot-chat/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/GitHub.copilot/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/GitHub.copilot/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/Augment.vscode-augment/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/Augment.vscode-augment/"}, "/home/<USER>/.claude/": {"rootPath": "/home", "relPath": "tv/.claude/"}, "/home/<USER>/.claude/todos/": {"rootPath": "/home", "relPath": "tv/.claude/todos/"}, "/home/<USER>/.claude/statsig/": {"rootPath": "/home", "relPath": "tv/.claude/statsig/"}, "/home/<USER>/.cache/pip/wheels/65/9b/08/7d9c48abf1bc958f26770dff6bcf7361408b414460849bb7ac/": {"rootPath": "/home", "relPath": "tv/.cache/pip/wheels/65/9b/08/7d9c48abf1bc958f26770dff6bcf7361408b414460849bb7ac/"}, "/home/<USER>/.cache/pip/wheels/5c/a1/5f/c6b85a7d9452057be4ce68a8e45d77ba34234a6d46581777c6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/wheels/5c/a1/5f/c6b85a7d9452057be4ce68a8e45d77ba34234a6d46581777c6/"}, "/home/<USER>/.cache/pip/http-v2/f/c/c/2/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/c/c/2/8/"}, "/home/<USER>/.cache/pip/http-v2/f/c/1/9/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/c/1/9/f/"}, "/home/<USER>/.cache/pip/http-v2/f/a/a/b/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/a/a/b/4/"}, "/home/<USER>/.cache/pip/http-v2/f/9/9/b/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/9/9/b/3/"}, "/home/<USER>/.cache/pip/http-v2/f/5/5/8/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/5/5/8/d/"}, "/home/<USER>/.cache/pip/http-v2/f/4/c/1/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/4/c/1/0/"}, "/home/<USER>/.cache/pip/http-v2/f/2/c/9/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/2/c/9/d/"}, "/home/<USER>/.cache/pip/http-v2/f/0/4/b/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/0/4/b/2/"}, "/home/<USER>/.cache/pip/http-v2/f/0/4/6/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/0/4/6/a/"}, "/home/<USER>/.cache/pip/http-v2/f/0/0/1/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/f/0/0/1/6/"}, "/home/<USER>/.cache/pip/http-v2/e/f/a/1/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/f/a/1/c/"}, "/home/<USER>/.cache/pip/http-v2/e/a/c/a/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/a/c/a/f/"}, "/home/<USER>/.cache/pip/http-v2/e/9/4/a/7/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/9/4/a/7/"}, "/home/<USER>/.cache/pip/http-v2/e/7/1/9/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/7/1/9/2/"}, "/home/<USER>/.cache/pip/http-v2/e/6/0/d/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/6/0/d/6/"}, "/home/<USER>/.cache/pip/http-v2/e/5/7/1/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/5/7/1/d/"}, "/home/<USER>/.cache/pip/http-v2/e/4/4/8/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/4/4/8/3/"}, "/home/<USER>/.cache/pip/http-v2/e/1/8/7/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/1/8/7/2/"}, "/home/<USER>/.cache/pip/http-v2/e/1/8/0/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/1/8/0/5/"}, "/home/<USER>/.cache/pip/http-v2/e/0/f/a/9/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/e/0/f/a/9/"}, "/home/<USER>/.cache/pip/http-v2/d/f/f/3/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/f/f/3/d/"}, "/home/<USER>/.cache/pip/http-v2/d/e/f/a/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/e/f/a/5/"}, "/home/<USER>/.cache/pip/http-v2/d/d/5/b/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/d/5/b/2/"}, "/home/<USER>/.cache/pip/http-v2/d/a/9/3/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/a/9/3/5/"}, "/home/<USER>/.cache/pip/http-v2/d/a/0/d/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/a/0/d/1/"}, "/home/<USER>/.cache/pip/http-v2/d/7/5/b/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/7/5/b/a/"}, "/home/<USER>/.cache/pip/http-v2/d/4/c/e/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/4/c/e/6/"}, "/home/<USER>/.cache/pip/http-v2/d/0/6/1/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/d/0/6/1/4/"}, "/home/<USER>/.cache/pip/http-v2/c/f/6/d/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/c/f/6/d/0/"}, "/home/<USER>/.cache/pip/http-v2/c/7/9/4/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/c/7/9/4/1/"}, "/home/<USER>/.cache/pip/http-v2/c/0/2/1/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/c/0/2/1/d/"}, "/home/<USER>/.cache/pip/http-v2/b/e/4/0/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/e/4/0/8/"}, "/home/<USER>/.cache/pip/http-v2/b/b/0/3/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/b/0/3/c/"}, "/home/<USER>/.cache/pip/http-v2/b/a/8/d/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/a/8/d/1/"}, "/home/<USER>/.cache/pip/http-v2/b/9/9/4/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/9/9/4/a/"}, "/home/<USER>/.cache/pip/http-v2/b/2/f/3/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/2/f/3/e/"}, "/home/<USER>/.cache/pip/http-v2/b/1/9/9/9/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/b/1/9/9/9/"}, "/home/<USER>/.cache/pip/http-v2/a/d/c/2/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/d/c/2/6/"}, "/home/<USER>/.cache/pip/http-v2/a/c/6/c/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/c/6/c/e/"}, "/home/<USER>/.cache/pip/http-v2/a/9/0/2/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/9/0/2/2/"}, "/home/<USER>/.cache/pip/http-v2/a/7/7/6/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/7/7/6/e/"}, "/home/<USER>/.cache/pip/http-v2/a/7/0/7/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/7/0/7/b/"}, "/home/<USER>/.cache/pip/http-v2/a/6/5/9/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/6/5/9/1/"}, "/home/<USER>/.cache/pip/http-v2/a/6/2/a/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/6/2/a/4/"}, "/home/<USER>/.cache/pip/http-v2/a/0/3/e/7/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/0/3/e/7/"}, "/home/<USER>/.cache/pip/http-v2/9/f/1/d/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/f/1/d/b/"}, "/home/<USER>/.cache/pip/http-v2/9/e/4/b/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/e/4/b/1/"}, "/home/<USER>/.cache/pip/http-v2/9/b/e/f/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/b/e/f/3/"}, "/home/<USER>/.cache/pip/http-v2/8/e/c/f/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/e/c/f/f/"}, "/home/<USER>/.cache/pip/http-v2/8/d/b/c/7/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/d/b/c/7/"}, "/home/<USER>/.cache/pip/http-v2/8/b/b/9/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/b/b/9/0/"}, "/home/<USER>/.cache/pip/http-v2/8/b/9/f/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/b/9/f/0/"}, "/home/<USER>/.cache/pip/http-v2/8/a/3/c/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/a/3/c/e/"}, "/home/<USER>/.cache/pip/http-v2/8/8/b/8/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/8/b/8/b/"}, "/home/<USER>/.cache/pip/http-v2/8/7/6/0/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/7/6/0/8/"}, "/home/<USER>/.cache/pip/http-v2/8/4/4/b/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/4/4/b/2/"}, "/home/<USER>/.cache/pip/http-v2/7/d/7/1/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/d/7/1/d/"}, "/home/<USER>/.cache/pip/http-v2/7/a/a/4/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/a/a/4/3/"}, "/home/<USER>/.cache/pip/http-v2/7/a/9/a/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/a/9/a/3/"}, "/home/<USER>/.cache/pip/http-v2/7/9/4/b/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/9/4/b/3/"}, "/home/<USER>/.cache/pip/http-v2/7/5/f/0/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/5/f/0/0/"}, "/home/<USER>/.cache/pip/http-v2/7/5/6/9/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/5/6/9/f/"}, "/home/<USER>/.cache/pip/http-v2/7/4/7/8/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/4/7/8/d/"}, "/home/<USER>/.cache/pip/http-v2/7/3/4/5/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/3/4/5/f/"}, "/home/<USER>/.cache/pip/http-v2/7/0/e/1/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/7/0/e/1/0/"}, "/home/<USER>/.cache/pip/http-v2/6/b/0/e/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/b/0/e/d/"}, "/home/<USER>/.cache/pip/http-v2/6/5/f/c/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/5/f/c/4/"}, "/home/<USER>/.cache/pip/http-v2/6/5/a/f/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/5/a/f/8/"}, "/home/<USER>/.cache/pip/http-v2/6/5/1/e/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/5/1/e/5/"}, "/home/<USER>/.cache/pip/http-v2/6/4/6/e/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/4/6/e/4/"}, "/home/<USER>/.cache/pip/http-v2/6/0/7/f/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/6/0/7/f/c/"}, "/home/<USER>/.cache/pip/http-v2/5/c/f/a/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/5/c/f/a/1/"}, "/home/<USER>/.cache/pip/http-v2/5/b/9/a/9/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/5/b/9/a/9/"}, "/home/<USER>/.cache/pip/http-v2/5/b/7/2/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/5/b/7/2/6/"}, "/home/<USER>/.cache/pip/http-v2/5/b/5/4/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/5/b/5/4/a/"}, "/home/<USER>/.cache/pip/http-v2/5/a/3/9/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/5/a/3/9/3/"}, "/home/<USER>/.cache/pip/http-v2/4/a/f/5/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/a/f/5/a/"}, "/home/<USER>/.cache/pip/http-v2/4/a/b/6/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/a/b/6/5/"}, "/home/<USER>/.cache/pip/http-v2/4/8/f/b/4/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/8/f/b/4/"}, "/home/<USER>/.cache/pip/http-v2/4/8/b/0/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/8/b/0/e/"}, "/home/<USER>/.cache/pip/http-v2/4/7/9/c/6/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/7/9/c/6/"}, "/home/<USER>/.cache/pip/http-v2/4/2/e/0/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/4/2/e/0/8/"}, "/home/<USER>/.cache/pip/http-v2/3/f/d/c/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/f/d/c/e/"}, "/home/<USER>/.cache/pip/http-v2/3/e/a/c/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/e/a/c/b/"}, "/home/<USER>/.cache/pip/http-v2/3/9/d/b/e/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/9/d/b/e/"}, "/home/<USER>/.cache/pip/http-v2/3/7/8/2/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/7/8/2/f/"}, "/home/<USER>/.cache/pip/http-v2/3/6/9/0/5/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/6/9/0/5/"}, "/home/<USER>/.cache/pip/http-v2/3/2/f/1/8/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/2/f/1/8/"}, "/home/<USER>/.cache/pip/http-v2/3/1/e/0/0/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/1/e/0/0/"}, "/home/<USER>/.cache/pip/http-v2/3/1/5/b/d/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/3/1/5/b/d/"}, "/home/<USER>/.cache/pip/http-v2/2/2/7/7/9/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/2/2/7/7/9/"}, "/home/<USER>/.cache/pip/http-v2/2/0/e/6/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/2/0/e/6/3/"}, "/home/<USER>/.cache/pip/http-v2/2/0/9/a/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/2/0/9/a/2/"}, "/home/<USER>/.cache/pip/http-v2/1/f/a/d/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/f/a/d/b/"}, "/home/<USER>/.cache/pip/http-v2/1/f/3/4/9/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/f/3/4/9/"}, "/home/<USER>/.cache/pip/http-v2/1/7/3/7/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/7/3/7/a/"}, "/home/<USER>/.cache/pip/http-v2/1/5/d/4/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/5/d/4/c/"}, "/home/<USER>/.cache/pip/http-v2/1/5/3/5/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/5/3/5/1/"}, "/home/<USER>/.cache/pip/http-v2/1/0/e/9/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/1/0/e/9/2/"}, "/home/<USER>/.cache/pip/http-v2/0/f/d/4/3/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/f/d/4/3/"}, "/home/<USER>/.cache/pip/http-v2/0/d/d/c/1/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/d/d/c/1/"}, "/home/<USER>/.cache/pip/http-v2/0/b/1/d/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/b/1/d/c/"}, "/home/<USER>/.cache/pip/http-v2/0/6/4/a/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/6/4/a/2/"}, "/home/<USER>/.cache/pip/http-v2/0/5/d/2/b/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/5/d/2/b/"}, "/home/<USER>/.cache/pip/http-v2/0/3/1/2/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/3/1/2/2/"}, "/home/<USER>/.cache/pip/http-v2/0/1/1/4/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/0/1/1/4/2/"}, "/home/<USER>/.cache/pip/http-v2/a/5/c/a/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/a/5/c/a/a/"}, "/home/<USER>/.cache/pip/http-v2/9/5/8/4/c/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/5/8/4/c/"}, "/home/<USER>/.cache/pip/http-v2/9/3/6/e/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/3/6/e/f/"}, "/home/<USER>/.cache/pip/http-v2/9/2/e/f/7/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/2/e/f/7/"}, "/home/<USER>/.cache/pip/http-v2/9/0/b/e/f/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/0/b/e/f/"}, "/home/<USER>/.cache/pip/http-v2/9/0/7/9/2/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/9/0/7/9/2/"}, "/home/<USER>/.cache/pip/http-v2/8/f/7/8/a/": {"rootPath": "/home", "relPath": "tv/.cache/pip/http-v2/8/f/7/8/a/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/dd/e7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/dd/e7/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/d1/19/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/d1/19/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/c8/24/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/c8/24/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/c1/0d/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/c1/0d/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/bd/85/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/bd/85/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/b8/3a/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/b8/3a/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/ab/03/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/ab/03/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/a8/b2/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/a8/b2/"}, "/home/<USER>/.npm/_cacache/index-v5/0d/8f/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0d/8f/"}, "/home/<USER>/.npm/_cacache/index-v5/0d/8e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0d/8e/"}, "/home/<USER>/.npm/_cacache/index-v5/0c/80/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0c/80/"}, "/home/<USER>/.npm/_cacache/index-v5/0c/41/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0c/41/"}, "/home/<USER>/.npm/_cacache/index-v5/0c/11/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0c/11/"}, "/home/<USER>/.npm/_cacache/index-v5/0b/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0b/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/0b/a1/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0b/a1/"}, "/home/<USER>/.npm/_cacache/index-v5/0b/47/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0b/47/"}, "/home/<USER>/.npm/_cacache/index-v5/0a/d0/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/0a/d0/"}, "/home/<USER>/.npm/_cacache/index-v5/09/fb/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/09/fb/"}, "/home/<USER>/.npm/_cacache/index-v5/09/59/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/09/59/"}, "/home/<USER>/.npm/_cacache/index-v5/08/b7/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/08/b7/"}, "/home/<USER>/.npm/_cacache/index-v5/08/3e/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/index-v5/08/3e/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/e7/cd/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/e7/cd/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/e1/60/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/e1/60/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/a8/49/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/a8/49/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/9b/a5/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/9b/a5/"}, "/home/<USER>/.npm/_cacache/content-v2/sha512/96/e6/": {"rootPath": "/home", "relPath": "tv/.npm/_cacache/content-v2/sha512/96/e6/"}, "/home/<USER>/.cache/matplotlib/": {"rootPath": "/home", "relPath": "tv/.cache/matplotlib/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/node_modules/katex/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/node_modules/katex/dist/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/build/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/build/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/build/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/webview-ui/build/assets/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/standalone/runtime-files/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/standalone/runtime-files/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/standalone/runtime-files/vscode/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/standalone/runtime-files/vscode/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/src/integrations/theme/default-themes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/src/integrations/theme/default-themes/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/scripts/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/scripts/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/proto/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/proto/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/tools/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/tools/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/custom instructions library/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/custom instructions library/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/custom instructions library/raw-instructions/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/prompting/custom instructions library/raw-instructions/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/mcp/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/mcp/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/getting-started-new-coders/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/getting-started-new-coders/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/cline-customization/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/cline-customization/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/architecture/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/old_docs/architecture/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/node_modules/@vscode/codicons/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/node_modules/@vscode/codicons/dist/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/zh-tw/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/zh-tw/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/zh-cn/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/zh-cn/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/pt-BR/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/pt-BR/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ko/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ko/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ja/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ja/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/es/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/es/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/de/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/de/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ar-sa/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/locales/ar-sa/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/evals/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/evals/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/webview-ui/build/assets/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/webview-ui/build/assets/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/vscode-material-icons/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/vscode-material-icons/icons/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/hi/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/hi/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/zh-CN/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/zh-CN/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.323.0/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.323.0/dist/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/de/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/de/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.changeset/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.changeset/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/evals/cli/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/evals/cli/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/coverage_check/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/coverage_check/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/workflows/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/workflows/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/en/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/en/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.323.0/dist/node_modules/@vscode/codicons/dist/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.323.0/dist/node_modules/@vscode/codicons/dist/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/ISSUE_TEMPLATE/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/ISSUE_TEMPLATE/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.clinerules/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.clinerules/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/fr/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/fr/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/assets/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/assets/icons/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/integrations/theme/default-themes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/integrations/theme/default-themes/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.husky/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.husky/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/tests/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/tests/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/pl/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/pl/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/es/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/es/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/vi/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/vi/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.323.0/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.323.0/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/it/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/it/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ko/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ko/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/pt-BR/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/pt-BR/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/zh-TW/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/zh-TW/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.github/scripts/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.323.0/assets/status/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.323.0/assets/status/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ru/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ru/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/assets/docs/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/assets/docs/"}, "/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.clinerules/workflows/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5/.clinerules/workflows/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ja/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ja/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/nl/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/nl/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/tr/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/tr/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ca/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/dist/i18n/locales/ca/"}, "/home/<USER>/.vscode/extensions/github.copilot-1.323.0/syntaxes/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/github.copilot-1.323.0/syntaxes/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/vscode-material-icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/vscode-material-icons/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/images/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/images/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/icons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/icons/"}, "/home/<USER>/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/codicons/": {"rootPath": "/home", "relPath": "tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3/assets/codicons/"}, "/home/<USER>/.config/Code/logs/20250525T115816/window1/exthost/vscode.json-language-features/": {"rootPath": "/home", "relPath": "tv/.config/Code/logs/20250525T115816/window1/exthost/vscode.json-language-features/"}, "/home/<USER>/.config/Signal/Crashpad/": {"rootPath": "/home", "relPath": "tv/.config/Signal/Crashpad/"}, "/home/<USER>/.config/Signal/Local Storage/leveldb/": {"rootPath": "/home", "relPath": "tv/.config/Signal/Local Storage/leveldb/"}, "/home/<USER>/.config/Signal/Shared Dictionary/": {"rootPath": "/home", "relPath": "tv/.config/Signal/Shared Dictionary/"}, "/home/<USER>/.config/Signal/": {"rootPath": "/home", "relPath": "tv/.config/Signal/"}, "/home/<USER>/.config/Signal/logs/": {"rootPath": "/home", "relPath": "tv/.config/Signal/logs/"}, "/home/<USER>/.config/Signal/IndexedDB/file__0.indexeddb.leveldb/": {"rootPath": "/home", "relPath": "tv/.config/Signal/IndexedDB/file__0.indexeddb.leveldb/"}, "/home/<USER>/.config/Signal/optionalResources/": {"rootPath": "/home", "relPath": "tv/.config/Signal/optionalResources/"}, "/home/<USER>/.config/Signal/WebStorage/": {"rootPath": "/home", "relPath": "tv/.config/Signal/WebStorage/"}, "/home/<USER>/.config/Signal/badges.noindex/66/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/66/"}, "/home/<USER>/.config/Signal/badges.noindex/86/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/86/"}, "/home/<USER>/.config/Signal/badges.noindex/b1/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/b1/"}, "/home/<USER>/.config/Signal/badges.noindex/b8/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/b8/"}, "/home/<USER>/.config/Signal/badges.noindex/38/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/38/"}, "/home/<USER>/.config/Signal/badges.noindex/c5/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/c5/"}, "/home/<USER>/.config/Signal/badges.noindex/34/": {"rootPath": "/home", "relPath": "tv/.config/Signal/badges.noindex/34/"}, "/home/<USER>/.config/Signal/downloads.noindex/00/": {"rootPath": "/home", "relPath": "tv/.config/Signal/downloads.noindex/00/"}, "/home/<USER>/Downloads/": {"rootPath": "/home", "relPath": "tv/Downloads/"}, "/home/<USER>/.config/Signal/Session Storage/": {"rootPath": "/home", "relPath": "tv/.config/Signal/Session Storage/"}, "/home/<USER>/.config/enchant/": {"rootPath": "/home", "relPath": "tv/.config/enchant/"}, "/home/<USER>/.config/xed/": {"rootPath": "/home", "relPath": "tv/.config/xed/"}, "/home/<USER>/.config/Code/User/globalStorage/github.vscode-pull-request-github/assignableUsers/Sjeror11/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/globalStorage/github.vscode-pull-request-github/assignableUsers/Sjeror11/"}, "/home/<USER>/.config/Code/User/History/2d7dc8c1/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/History/2d7dc8c1/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/checkpoint-documents/1100547d-76d9-46c5-b3d1-3ecbf30642cb/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/augment-user-assets/checkpoint-documents/1100547d-76d9-46c5-b3d1-3ecbf30642cb/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/chatEditingSessions/511bfbe4-0e0e-42e0-8859-3e82966d2844/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/chatEditingSessions/511bfbe4-0e0e-42e0-8859-3e82966d2844/"}, "/home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/chatSessions/": {"rootPath": "/home", "relPath": "tv/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/chatSessions/"}}