2025-05-25 11:58:17.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'terminalShellType' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:17.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-python-envs' wants API proposal 'terminalShellType' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:17.461 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatReadonlyPromptReference' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:18.321 [info] Started local extension host with pid 110726.
2025-05-25 11:58:19.768 [error] [/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0]: Rozšíření není kompatibilní s Code 1.99.3. Rozšíření vyžaduje: ^1.100.0.
2025-05-25 11:58:22.276 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-05-25 11:58:24.274 [info] [perf] Render performance baseline is 79ms
2025-05-25 11:58:24.838 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, rooveterinaryinc.roo-cline, saoudrizwan.claude-dev
2025-05-25 11:58:25.136 [info] Extension host (LocalProcess pid: 110726) is unresponsive.
2025-05-25 11:58:25.839 [info] Auto updating extension augment.vscode-augment
2025-05-25 11:58:25.840 [info] Auto updating extension github.copilot
2025-05-25 11:58:25.842 [info] Auto updating extension rooveterinaryinc.roo-cline
2025-05-25 11:58:25.842 [info] Auto updating extension saoudrizwan.claude-dev
2025-05-25 11:58:26.278 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-05-25 11:58:26.879 [info] Extension host (LocalProcess pid: 110726) is responsive.
2025-05-25 11:58:26.880 [info] UNRESPONSIVE extension host: received responsive event and cancelling profiling session
2025-05-25 11:58:27.664 [warning] UNRESPONSIVE extension host: 'saoudrizwan.claude-dev' took 87.11791144209207% of 557.072ms, saved PROFILE here: 'file:///tmp/exthost-86c280.cpuprofile'
2025-05-25 11:58:37.161 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":80,"rows":30}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-05-25 11:58:39.154 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":80,"rows":30}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}},"exitCode":0}
2025-05-25 11:58:46.596 [info] [Extensions]: Checking for updates. Reason: Product update
2025-05-25 11:58:47.797 [info] Auto updating outdated extensions. github.vscode-pull-request-github
2025-05-25 11:59:00.877 [info] Auto updating extension github.vscode-pull-request-github
2025-05-25 11:59:02.062 [error] Nelze přečíst rozšíření z /home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0.: ScanningExtension: Nelze přečíst rozšíření z /home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0.
    at wh.scanLocalExtension (file:///usr/share/code/resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:72727)
2025-05-25 12:08:03.388 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cat /etc/os-release","confidence":2,"isTrusted":true}}}
2025-05-25 12:08:03.391 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cat /etc/os-release","confidence":2,"isTrusted":true}},"exitCode":0}
2025-05-25 12:08:08.931 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:08.936 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:09.147 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"wget -O- https://updates.signal.org/desktop/apt/keys.asc | gpg --dearmor > signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:09.335 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"wget -O- https://updates.signal.org/desktop/apt/keys.asc | gpg --dearmor > signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":1}
2025-05-25 12:08:14.240 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:14.245 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:14.366 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home && wget -O- https://updates.signal.org/desktop/apt/keys.asc | sudo gpg --dearmor -o /usr/share/keyrings/signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:09.019 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home && wget -O- https://updates.signal.org/desktop/apt/keys.asc | sudo gpg --dearmor -o /usr/share/keyrings/signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:14.235 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.236 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.363 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'deb [arch=amd64 signed-by=/usr/share/keyrings/signal-desktop-keyring.gpg] https://updates.signal.org/desktop/apt xenial main' | sudo tee /etc/apt/sources.list.d/signal-xenial.list","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.389 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'deb [arch=amd64 signed-by=/usr/share/keyrings/signal-desktop-keyring.gpg] https://updates.signal.org/desktop/apt xenial main' | sudo tee /etc/apt/sources.list.d/signal-xenial.list","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:18.734 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:18.735 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:18.831 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt update","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:24.996 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt update","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:29.393 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:29.396 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:29.511 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt install signal-desktop","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:10:47.292 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt install signal-desktop","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:17:47.755 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.756 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.918 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && pwd && ls -la","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.933 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && pwd && ls -la","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:17:53.349 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.352 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.446 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.470 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 12:17:56.954 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:56.956 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:57.054 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name && git config --global user.email","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:57.055 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name && git config --global user.email","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":1}
