2025-05-25 11:58:17.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'terminalShellType' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:17.460 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-python-envs' wants API proposal 'terminalShellType' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:17.461 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatReadonlyPromptReference' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-25 11:58:18.321 [info] Started local extension host with pid 110726.
2025-05-25 11:58:19.768 [error] [/home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0]: Rozšíření není kompatibilní s Code 1.99.3. Rozšíření vyžaduje: ^1.100.0.
2025-05-25 11:58:22.276 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-05-25 11:58:24.274 [info] [perf] Render performance baseline is 79ms
2025-05-25 11:58:24.838 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, rooveterinaryinc.roo-cline, saoudrizwan.claude-dev
2025-05-25 11:58:25.136 [info] Extension host (LocalProcess pid: 110726) is unresponsive.
2025-05-25 11:58:25.839 [info] Auto updating extension augment.vscode-augment
2025-05-25 11:58:25.840 [info] Auto updating extension github.copilot
2025-05-25 11:58:25.842 [info] Auto updating extension rooveterinaryinc.roo-cline
2025-05-25 11:58:25.842 [info] Auto updating extension saoudrizwan.claude-dev
2025-05-25 11:58:26.278 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-05-25 11:58:26.879 [info] Extension host (LocalProcess pid: 110726) is responsive.
2025-05-25 11:58:26.880 [info] UNRESPONSIVE extension host: received responsive event and cancelling profiling session
2025-05-25 11:58:27.664 [warning] UNRESPONSIVE extension host: 'saoudrizwan.claude-dev' took 87.11791144209207% of 557.072ms, saved PROFILE here: 'file:///tmp/exthost-86c280.cpuprofile'
2025-05-25 11:58:37.161 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":80,"rows":30}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}}}
2025-05-25 11:58:39.154 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment Terminal Capability Check","processId":{},"creationOptions":{"name":"Augment Terminal Capability Check","hideFromUser":true,"shellPath":"bash","isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":80,"rows":30}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'Terminal capability test'","confidence":2,"isTrusted":true}},"exitCode":0}
2025-05-25 11:58:46.596 [info] [Extensions]: Checking for updates. Reason: Product update
2025-05-25 11:58:47.797 [info] Auto updating outdated extensions. github.vscode-pull-request-github
2025-05-25 11:59:00.877 [info] Auto updating extension github.vscode-pull-request-github
2025-05-25 11:59:02.062 [error] Nelze přečíst rozšíření z /home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0.: ScanningExtension: Nelze přečíst rozšíření z /home/<USER>/.vscode/extensions/github.vscode-pull-request-github-0.110.0.
    at wh.scanLocalExtension (file:///usr/share/code/resources/app/out/vs/code/electron-utility/sharedProcess/sharedProcessMain.js:74:72727)
2025-05-25 12:08:03.388 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cat /etc/os-release","confidence":2,"isTrusted":true}}}
2025-05-25 12:08:03.391 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cat /etc/os-release","confidence":2,"isTrusted":true}},"exitCode":0}
2025-05-25 12:08:08.931 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:08.936 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:09.147 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"wget -O- https://updates.signal.org/desktop/apt/keys.asc | gpg --dearmor > signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:09.335 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"wget -O- https://updates.signal.org/desktop/apt/keys.asc | gpg --dearmor > signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":1}
2025-05-25 12:08:14.240 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:14.245 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:08:14.366 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home && wget -O- https://updates.signal.org/desktop/apt/keys.asc | sudo gpg --dearmor -o /usr/share/keyrings/signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:09.019 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home && wget -O- https://updates.signal.org/desktop/apt/keys.asc | sudo gpg --dearmor -o /usr/share/keyrings/signal-desktop-keyring.gpg","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:14.235 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.236 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.363 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'deb [arch=amd64 signed-by=/usr/share/keyrings/signal-desktop-keyring.gpg] https://updates.signal.org/desktop/apt xenial main' | sudo tee /etc/apt/sources.list.d/signal-xenial.list","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:14.389 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo 'deb [arch=amd64 signed-by=/usr/share/keyrings/signal-desktop-keyring.gpg] https://updates.signal.org/desktop/apt xenial main' | sudo tee /etc/apt/sources.list.d/signal-xenial.list","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:18.734 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:18.735 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:18.831 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt update","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:24.996 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt update","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:09:29.393 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:29.396 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:09:29.511 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt install signal-desktop","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:10:47.292 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sudo apt install signal-desktop","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:17:47.755 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.756 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.918 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && pwd && ls -la","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}}}
2025-05-25 12:17:47.933 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && pwd && ls -la","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home","path":"/home","scheme":"file"}},"exitCode":0}
2025-05-25 12:17:53.349 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.352 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.446 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:53.470 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 12:17:56.954 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:56.956 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:57.054 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name && git config --global user.email","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:17:57.055 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name && git config --global user.email","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":1}
2025-05-25 12:21:35.861 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:35.863 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:35.958 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name \"sjeror11\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:35.959 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.name \"sjeror11\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:21:41.058 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:41.065 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:41.172 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.email \"<EMAIL>\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:41.174 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git config --global user.email \"<EMAIL>\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:21:45.502 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:45.509 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:45.628 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git init","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:45.667 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git init","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:21:50.168 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:50.170 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:50.280 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git branch -m main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:50.285 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git branch -m main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:21:59.467 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:59.472 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:59.574 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git add .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:21:59.829 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git add .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:22:04.299 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:04.301 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:04.493 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git rm --cached external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:04.496 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git rm --cached external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":1}
2025-05-25 12:22:08.070 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:08.071 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:08.197 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git rm --cached -f external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:08.199 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":177,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git rm --cached -f external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:22:12.222 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:12.224 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:12.324 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"rm -rf external/bybit-api/.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:12.329 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"rm -rf external/bybit-api/.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:22:16.201 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:16.202 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:16.325 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitadd external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:16.467 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitadd external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:22:22.303 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git add external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:22.501 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git add external/bybit-api","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:22:22.504 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Initial commit: Bybit Trading Assistant with complete documentation\n\n- Complete trading bot with multiple strategies\n- Comprehensive documentation (GUIDE.md, ROADMAP.md, STRATEGIES.md)\n- Development continuation plan (POKRACOVANI_VYVOJE.md)\n- Dashboard for monitoring trades\n- API integration setup\n- Testing scripts and examples\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:04.578 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Initial commit: Bybit Trading Assistant with complete documentation\n\n- Complete trading bot with multiple strategies\n- Comprehensive documentation (GUIDE.md, ROADMAP.md, STRATEGIES.md)\n- Development continuation plan (POKRACOVANI_VYVOJE.md)\n- Dashboard for monitoring trades\n- API integration setup\n- Testing scripts and examples\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:04.579 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Initial commit: Bybit Trading Assistant with complete documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:22.997 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Initial commit: Bybit Trading Assistant with complete documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:23.038 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitstatus","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:28.071 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitstatus","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:28.073 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitlog --oneline","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:34.215 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitlog --oneline","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:34.220 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gh repo create bybit-trading-assistant --public --description \"Automated trading assistant for Bybit cryptocurrency exchange with multiple strategies, dashboard, and comprehensive documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:23:34.427 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gh repo create bybit-trading-assistant --public --description \"Automated trading assistant for Bybit cryptocurrency exchange with multiple strategies, dashboard, and comprehensive documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":127}
2025-05-25 12:25:21.096 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:25:21.098 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:25:21.202 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote add origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:25:21.206 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote add origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:25:24.886 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git commit -m \"Initial commit: Bybit Trading Assistant with complete documentation\n\n- Complete trading bot with multiple strategies\n- Comprehensive documentation (GUIDE.md, ROADMAP.md, STRATEGIES.md)\n- Development continuation plan (POKRACOVANI_VYVOJE.md)\n- Dashboard for monitoring trades\n- API integration setup\n- Testing scripts and examples\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:25:25.038 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git commit -m \"Initial commit: Bybit Trading Assistant with complete documentation\n\n- Complete trading bot with multiple strategies\n- Comprehensive documentation (GUIDE.md, ROADMAP.md, STRATEGIES.md)\n- Development continuation plan (POKRACOVANI_VYVOJE.md)\n- Dashboard for monitoring trades\n- API integration setup\n- Testing scripts and examples\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:25:25.040 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:26:41.549 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:26:41.552 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:23.946 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:23.950 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"ls -la ~/.ssh/","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:23.951 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"ls -la ~/.ssh/","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:27:28.660 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:28.671 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:28.802 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-<NAME_EMAIL>:sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:28.806 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-<NAME_EMAIL>:sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:27:32.710 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git commit -m \"Initial commit: Bybit Trading Assistant with complete documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:32.831 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git commit -m \"Initial commit: Bybit Trading Assistant with complete documentation\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:32.833 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"ssh-add ~/.ssh/id_rsa_tv","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:32.885 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"ssh-add ~/.ssh/id_rsa_tv","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:27:45.303 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:45.305 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:27:45.446 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:28:52.433 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 12:28:59.553 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:28:59.720 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:28:59.729 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-url origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:32:28.589 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-url origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:32:28.592 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://sjeror11:<EMAIL>/sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:46.928 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://sjeror11:<EMAIL>/sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:46.933 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitconfig --global credential.helper store","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:52.807 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitconfig --global credential.helper store","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:52.821 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"https://sjeror11:<EMAIL>\" > ~/.git-credentials","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:52.822 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"https://sjeror11:<EMAIL>\" > ~/.git-credentials","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:33:57.419 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:57.420 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:57.496 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:33:58.412 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 12:34:03.382 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git log --oneline","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:03.500 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git log --oneline","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:03.513 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-url origin https://github.com/Sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:08.603 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote set-url origin https://github.com/Sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:08.608 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"https://Sjeror11:<EMAIL>\" > ~/.git-credentials","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:08.609 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"https://Sjeror11:<EMAIL>\" > ~/.git-credentials","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:34:13.017 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:13.018 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:13.179 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:34:13.678 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 12:43:14.981 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:43:14.982 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:43:15.064 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"curl -I https://github.com/Sjeror11/bybit-trading-assistant","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:43:15.457 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"curl -I https://github.com/Sjeror11/bybit-trading-assistant","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:43:25.037 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git remote add origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:43:25.165 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git remote add origin https://github.com/sjeror11/bybit-trading-assistant.git","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:43:25.172 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:44:45.007 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:44:45.009 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"du -sh .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:44:46.281 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"du -sh .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:45:14.676 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:14.678 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:14.761 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitrm -r --cached node_modules/","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:14.859 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitrm -r --cached node_modules/","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 12:45:20.659 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:20.752 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:20.754 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitadd .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:45.446 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitadd .","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:45:45.451 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Remove node_modules and update .gitignore\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:49:36.799 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Remove node_modules and update .gitignore\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 12:49:36.806 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitstatus --porcelain","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:00:42.224 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitstatus --porcelain","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:00:42.227 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Remove node_modules and update .gitignore\"^C","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:00:42.390 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitcommit -m \"Remove node_modules and update .gitignore\"^C","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:00:42.393 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:11:45.338 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:11:45.341 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"^C","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:11:45.500 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"^C","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:11:45.503 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitlog --oneline -n 3","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:12:01.611 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitlog --oneline -n 3","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:12:01.617 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":19,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/ -x \"bybit-trading-assistant/node_modules/*\" \"bybit-trading-assistant/venv/*\" \"bybit-trading-assistant/.git/*\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 13:12:19.479 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/ -x \"bybit-trading-assistant/node_modules/*\" \"bybit-trading-assistant/venv/*\" \"bybit-trading-assistant/.git/*\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":0}
2025-05-25 14:58:01.314 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:58:01.317 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:58:01.416 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}}}
2025-05-25 14:58:01.439 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistant && git status","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>","external":"file:///home/<USER>","path":"/home/<USER>","scheme":"file"}},"exitCode":0}
2025-05-25 14:58:12.305 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git push origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:58:12.413 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"git push origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:58:12.415 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:59:34.840 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush https://<EMAIL>/Sjeror11/bybit-trading-assistant.git main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 14:59:34.844 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sshsjeror@************** \"cd /path/to/project && git push origin main\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:01:10.699 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"sshsjeror@************** \"cd /path/to/project && git push origin main\"","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:01:10.700 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote -v","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:01:19.162 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote -v","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:01:19.164 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"Augment","processId":{},"creationOptions":{"name":"Augment","shellPath":"bash","cwd":"/home","env":{"PAGER":"cat","LESS":"-FX","GIT_PAGER":"cat"},"iconPath":{"light":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-light.svg","scheme":"file"},"dark":{"$mid":1,"path":"/home/<USER>/.vscode/extensions/augment.vscode-augment-0.441.1/media/panel-icon-dark.svg","scheme":"file"}},"isTransient":true},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"fsPath":"/home/<USER>/bybit-trading-assistant","external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitpush origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:03:07.084 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"bash","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"^M","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home","scheme":"file"}}}
2025-05-25 15:03:07.203 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"bash","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"^M","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"exitCode":127}
2025-05-25 15:03:12.973 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"bash","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistantgit statusgit push origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home","scheme":"file"}}}
2025-05-25 15:03:13.575 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"git","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"cd /home/<USER>/bybit-trading-assistantgit statusgit push origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home","scheme":"file"}},"exitCode":128}
2025-05-25 15:03:38.070 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"bash","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"# bybit-trading-assistant\" >> README.mdgit initgit add README.mdgit commit -m \"first commit\"git branch -M maingit remote add origin https://github.com/Sjeror11/bybit-trading-assistant.gitgit push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:03:39.240 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"git","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"echo \"# bybit-trading-assistant\" >> README.mdgit initgit add README.mdgit commit -m \"first commit\"git branch -M maingit remote add origin https://github.com/Sjeror11/bybit-trading-assistant.gitgit push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
2025-05-25 15:03:53.316 [error] [Extension Host] [onDidStartTerminalShellExecution] Shell execution started, but not from a Roo-registered terminal: {"terminal":{"name":"bash","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote add origin https://github.com/Sjeror11/bybit-trading-assistant.gitgit branch -M maingit push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}}}
2025-05-25 15:03:54.276 [error] [Extension Host] [onDidEndTerminalShellExecution] Shell execution ended, but not from a Roo-registered terminal: {"terminal":{"name":"git","processId":{},"creationOptions":{},"state":{"isInteractedWith":true,"shell":"bash"},"shellIntegration":{"cwd":{"$mid":1,"external":"file:///home/<USER>/bybit-trading-assistant","path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"dimensions":{"columns":57,"rows":35}},"shellIntegration":"[Circular]","execution":{"commandLine":{"value":"gitremote add origin https://github.com/Sjeror11/bybit-trading-assistant.gitgit branch -M maingit push -u origin main","confidence":2,"isTrusted":true},"cwd":{"$mid":1,"path":"/home/<USER>/bybit-trading-assistant","scheme":"file"}},"exitCode":128}
