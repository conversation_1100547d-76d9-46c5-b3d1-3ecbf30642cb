2025-05-25 11:58:35.221 [info] Using the Electron fetcher.
2025-05-25 11:58:35.221 [info] Initializing Git extension service.
2025-05-25 11:58:35.221 [info] Successfully activated the vscode.git extension.
2025-05-25 11:58:35.221 [info] Enablement state of the vscode.git extension: true.
2025-05-25 11:58:35.221 [info] Successfully registered Git commit message provider.
2025-05-25 11:58:44.723 [warning] GitHub login failed
2025-05-25 11:58:44.729 [info] copilot token chat_enabled: undefined, sku: 
2025-05-25 11:58:44.729 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at yB.waitForChatEnabled (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:441:17603)
    at yB.run (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:441:17487)
    at ik.askToUpgradeAuthPermissions (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:441:16858): Failed to get copilot token
2025-05-25 11:58:44.729 [error] You are not signed in to GitHub. Please sign in to use Copilot.
2025-05-25 11:58:44.729 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-05-25 11:58:44.729 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at qk._getAuthSession (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3082)
    at Object.o [as task] (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:738)
    at kp._processQueue (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:474:3192971)
2025-05-25 11:58:44.729 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-05-25 11:58:44.729 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at qk._getAuthSession (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3082)
    at t (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:2545)
    at qk._registerEmbeddings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3044)
    at async Promise.all (index 1)
    at async Promise.allSettled (index 0)
    at $b.waitForActivationBlockers (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:2190)
    at /home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:2340:5536
    at aoo (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:2340:5349)
    at cw.n (file:///usr/share/code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:13359)
    at cw.m (file:///usr/share/code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:13322)
    at cw.l (file:///usr/share/code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:111:12778)
2025-05-25 11:58:44.729 [info] activationBlocker from 'languageModelAccess' took for 5918ms
2025-05-25 11:58:48.284 [warning] GitHub login failed
2025-05-25 11:58:48.288 [error] You are not signed in to GitHub. Please sign in to use Copilot.
2025-05-25 11:58:48.288 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-05-25 11:58:48.288 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at qk._getAuthSession (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3082)
    at t (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:2545)
2025-05-25 11:58:48.288 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-05-25 11:58:48.289 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at qk._getAuthSession (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3082)
    at Object.o [as task] (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:738)
    at kp._processQueue (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:474:3192971)
2025-05-25 12:13:48.300 [warning] GitHub login failed
2025-05-25 12:13:48.301 [warning] [LanguageModelAccess] LanguageModel/Embeddings are not available without auth token
2025-05-25 12:13:48.301 [error] Error: GitHubLoginFailed
    at uT._authShowWarnings (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:55501)
    at uT.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:480:54029)
    at V5.getCopilotToken (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:423:3589)
    at qk._getAuthSession (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:3082)
    at Object.o [as task] (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:479:738)
    at kp._processQueue (/home/<USER>/.vscode/extensions/github.copilot-chat-0.26.7/dist/extension.js:474:3192971)
2025-05-25 12:26:25.939 [info] Logged in as Sjeror11
2025-05-25 12:26:26.671 [info] Got Copilot token for Sjeror11
2025-05-25 12:26:26.857 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-25 12:26:26.940 [info] Registering default platform agent...
2025-05-25 12:26:26.953 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-05-25 12:26:26.953 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-05-25 12:26:26.954 [info] Successfully registered GitHub PR title and description provider.
2025-05-25 12:26:26.954 [info] Successfully registered GitHub PR reviewer comments provider.
2025-05-25 12:26:27.451 [info] Fetched model metadata in 366ms a7c7d62b-55bb-4310-afb4-86c6881a3b80
2025-05-25 12:45:16.495 [info] message 0 returned. finish reason: [stop]
2025-05-25 12:45:16.504 [info] request done: requestId: [36d64282-97fa-4dbb-8256-c24c5992cde0] model deployment ID: []
2025-05-25 12:56:28.946 [info] Logged in as Sjeror11
2025-05-25 12:56:29.604 [info] Got Copilot token for Sjeror11
2025-05-25 12:56:29.624 [info] copilot token chat_enabled: true, sku: free_limited_copilot
