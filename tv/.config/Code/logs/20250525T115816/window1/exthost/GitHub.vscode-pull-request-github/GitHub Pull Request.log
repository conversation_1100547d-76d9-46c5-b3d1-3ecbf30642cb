2025-05-25 11:58:35.313 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-05-25 11:58:35.313 [info] [Activation] Extension version: 0.108.0
2025-05-25 11:58:43.732 [info] [Activation] Looking for git repository
2025-05-25 11:58:43.732 [info] [Activation] Found 0 repositories during activation
2025-05-25 11:58:43.732 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-05-25 11:58:43.736 [info] [GitAPI] Registering git provider
2025-05-25 12:21:46.481 [info] [GitAPI] Repository file:///home/<USER>/bybit-trading-assistant has been opened
2025-05-25 12:21:46.482 [info] [Review+0] Validate state in progress
2025-05-25 12:21:46.482 [info] [Review+0] Validating state...
2025-05-25 12:21:46.486 [warning] No remotes found. The following remotes are missing: origin, upstream
2025-05-25 12:21:46.486 [info] [FolderRepositoryManager+0] No GitHub remotes found for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:21:46.489 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-05-25 12:21:46.587 [info] [FolderRepositoryManager+0] GlobalState does not exist for assignableUsers.
2025-05-25 12:21:46.795 [info] [Review+0] Validate state in progress
2025-05-25 12:21:46.795 [info] [Review+0] Validating state...
2025-05-25 12:21:46.796 [info] [Activation] Repo state for file:///home/<USER>/bybit-trading-assistant changed.
2025-05-25 12:21:46.796 [info] [Activation] Repo file:///home/<USER>/bybit-trading-assistant has already been setup.
2025-05-25 12:21:46.860 [warning] No remotes found. The following remotes are missing: origin, upstream
2025-05-25 12:21:46.861 [info] [FolderRepositoryManager+0] No GitHub remotes found for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:21:46.863 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-05-25 12:21:47.271 [info] [Review+0] No matching pull request metadata found locally for current branch master
2025-05-25 12:21:47.340 [info] [Review+0] No matching pull request metadata found on GitHub for current branch master
2025-05-25 12:21:50.520 [info] [Review+0] Validate state in progress
2025-05-25 12:21:50.520 [info] [Review+0] Validating state...
2025-05-25 12:21:50.567 [warning] No remotes found. The following remotes are missing: origin, upstream
2025-05-25 12:21:50.567 [info] [FolderRepositoryManager+0] No GitHub remotes found for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:21:50.568 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-05-25 12:21:50.749 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-05-25 12:21:50.828 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-05-25 12:22:23.853 [info] [Review+0] Validate state in progress
2025-05-25 12:22:23.853 [info] [Review+0] Validating state...
2025-05-25 12:22:23.855 [warning] No remotes found. The following remotes are missing: origin, upstream
2025-05-25 12:22:23.855 [info] [FolderRepositoryManager+0] No GitHub remotes found for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:22:23.856 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-05-25 12:22:24.038 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-05-25 12:22:24.096 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-05-25 12:25:22.586 [info] [Review+0] Validate state in progress
2025-05-25 12:25:22.586 [info] [Review+0] Validating state...
2025-05-25 12:25:22.600 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:25:22.601 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-05-25 12:25:22.725 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-05-25 12:25:22.786 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-05-25 12:26:24.901 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/bybit-trading-assistant
2025-05-25 12:26:26.028 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-05-25 12:26:26.323 [info] [FolderRepositoryManager+0] GlobalState does not exist for assignableUsers.
