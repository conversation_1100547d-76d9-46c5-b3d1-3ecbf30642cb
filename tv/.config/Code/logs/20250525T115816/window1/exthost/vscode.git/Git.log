2025-05-25 11:58:34.535 [info] [hlavn<PERSON>] Úroveň protokolu: Info
2025-05-25 11:58:34.535 [info] [hlavn<PERSON>] O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nalezeného <PERSON>ště Git v: git
2025-05-25 11:58:34.535 [info] [hlavní] Použ<PERSON>vá se úložiště Git 2.43.0 z git
2025-05-25 11:58:34.535 [info] [Model][doInitialScan] Initial repository scan started
2025-05-25 11:58:34.535 [info] > git rev-parse --show-toplevel [1890ms]
2025-05-25 11:58:34.535 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 11:58:34.535 [info] > git rev-parse --show-toplevel [2438ms]
2025-05-25 11:58:34.535 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 11:58:34.535 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-25 12:21:36.656 [info] > git rev-parse --show-toplevel [10ms]
2025-05-25 12:21:36.663 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:21:41.821 [info] > git rev-parse --show-toplevel [2ms]
2025-05-25 12:21:41.821 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:21:46.371 [info] > git rev-parse --show-toplevel [2ms]
2025-05-25 12:21:46.441 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-05-25 12:21:46.482 [info] [Model][openRepository] Opened repository: /home/<USER>/bybit-trading-assistant
2025-05-25 12:21:46.578 [info] > git config --get commit.template [5ms]
2025-05-25 12:21:46.650 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:21:46.658 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [9ms]
2025-05-25 12:21:46.658 [warning] [Git][getBranch] No such branch: master
2025-05-25 12:21:46.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-25 12:21:46.772 [info] > git status -z -uall [62ms]
2025-05-25 12:21:47.165 [info] > git config --get --local branch.master.github-pr-owner-number [244ms]
2025-05-25 12:21:47.165 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:47.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [416ms]
2025-05-25 12:21:47.271 [warning] [Git][getBranch] No such branch: master
2025-05-25 12:21:47.273 [error] [GitHistoryProvider][resolveHEADMergeBase] Failed to resolve merge base for master: Error: No such branch: master.
2025-05-25 12:21:47.278 [info] > git config --get --local branch.master.remote [7ms]
2025-05-25 12:21:47.278 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:47.335 [info] > git config --get commit.template [172ms]
2025-05-25 12:21:47.339 [info] > git config --get --local branch.master.merge [5ms]
2025-05-25 12:21:47.339 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:47.393 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:21:47.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/master refs/remotes/master [3ms]
2025-05-25 12:21:47.395 [warning] [Git][getBranch] No such branch: master
2025-05-25 12:21:47.510 [info] > git status -z -uall [60ms]
2025-05-25 12:21:47.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-25 12:21:50.386 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:21:50.387 [info] > git config --get commit.template [60ms]
2025-05-25 12:21:50.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-25 12:21:50.399 [warning] [Git][getBranch] No such branch: main
2025-05-25 12:21:50.508 [info] > git status -z -uall [56ms]
2025-05-25 12:21:50.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-25 12:21:50.703 [info] > git config --get --local branch.main.github-pr-owner-number [86ms]
2025-05-25 12:21:50.703 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:50.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [182ms]
2025-05-25 12:21:50.749 [warning] [Git][getBranch] No such branch: main
2025-05-25 12:21:50.750 [error] [GitHistoryProvider][resolveHEADMergeBase] Failed to resolve merge base for main: Error: No such branch: main.
2025-05-25 12:21:50.762 [info] > git config --get --local branch.main.remote [14ms]
2025-05-25 12:21:50.762 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:50.828 [info] > git config --get --local branch.main.merge [10ms]
2025-05-25 12:21:50.828 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:21:59.865 [info] > git config --get commit.template [2ms]
2025-05-25 12:21:59.913 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:21:59.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-25 12:21:59.926 [warning] [Git][getBranch] No such branch: main
2025-05-25 12:22:00.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-25 12:22:00.103 [info] > git status -z -uall [129ms]
2025-05-25 12:22:08.246 [info] > git config --get commit.template [3ms]
2025-05-25 12:22:08.340 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:22:08.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-05-25 12:22:08.352 [warning] [Git][getBranch] No such branch: main
2025-05-25 12:22:08.476 [info] > git status -z -uall [61ms]
2025-05-25 12:22:08.485 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-25 12:22:17.716 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:22:17.717 [info] > git config --get commit.template [53ms]
2025-05-25 12:22:17.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-25 12:22:17.718 [warning] [Git][getBranch] No such branch: main
2025-05-25 12:22:17.844 [info] > git status -z -uall [68ms]
2025-05-25 12:22:17.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-25 12:22:23.704 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:22:23.705 [info] > git config --get commit.template [56ms]
2025-05-25 12:22:23.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-05-25 12:22:23.844 [info] > git status -z -uall [55ms]
2025-05-25 12:22:23.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-25 12:22:23.953 [info] > git config --get --local branch.main.github-pr-owner-number [12ms]
2025-05-25 12:22:23.953 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:22:24.043 [info] > git config --get --local branch.main.remote [6ms]
2025-05-25 12:22:24.043 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:22:24.095 [info] > git config --get --local branch.main.merge [3ms]
2025-05-25 12:22:24.095 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:23:05.775 [info] > git config --get commit.template [3ms]
2025-05-25 12:23:05.828 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-25 12:23:05.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-25 12:23:05.940 [info] > git status -z -uall [58ms]
2025-05-25 12:23:05.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-25 12:25:22.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-25 12:25:22.470 [info] > git config --get commit.template [57ms]
2025-05-25 12:25:22.580 [info] > git status -z -uall [56ms]
2025-05-25 12:25:22.582 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-25 12:25:22.668 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-05-25 12:25:22.668 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:25:22.727 [info] > git config --get --local branch.main.remote [2ms]
2025-05-25 12:25:22.727 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:25:22.785 [info] > git config --get --local branch.main.merge [4ms]
2025-05-25 12:25:22.785 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:26:26.363 [info] > git rev-parse --show-toplevel [46ms]
2025-05-25 12:26:26.363 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:27:59.104 [info] > git config --get commit.template [60ms]
2025-05-25 12:27:59.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-25 12:27:59.272 [info] > git status -z -uall [94ms]
2025-05-25 12:27:59.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-25 12:29:00.908 [info] > git config --get commit.template [51ms]
2025-05-25 12:29:00.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-25 12:29:01.013 [info] > git status -z -uall [54ms]
2025-05-25 12:29:01.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-25 12:33:47.564 [info] > git rev-parse --show-toplevel [2ms]
2025-05-25 12:33:47.564 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:33:53.890 [info] > git rev-parse --show-toplevel [6ms]
2025-05-25 12:33:53.890 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:34:04.919 [info] > git config --get commit.template [63ms]
2025-05-25 12:34:04.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-25 12:34:05.067 [info] > git status -z -uall [72ms]
2025-05-25 12:34:05.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-25 12:34:09.280 [info] > git rev-parse --show-toplevel [1ms]
2025-05-25 12:34:09.280 [info] fatal: not a git repository (or any of the parent directories): .git
2025-05-25 12:45:07.030 [info] > git config --get commit.template [25ms]
2025-05-25 12:45:07.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-25 12:45:07.172 [info] > git status -z -uall [48ms]
2025-05-25 12:45:07.173 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-25 12:45:16.103 [info] > git config --get commit.template [3ms]
2025-05-25 12:45:16.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-25 12:45:16.258 [info] > git status -z -uall [49ms]
2025-05-25 12:45:16.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-25 12:45:21.976 [info] > git config --get commit.template [40ms]
2025-05-25 12:45:22.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-25 12:45:22.134 [info] > git status -z -uall [52ms]
2025-05-25 12:45:22.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-25 12:45:46.678 [info] > git config --get commit.template [15ms]
2025-05-25 12:45:46.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-25 12:45:46.852 [info] > git status -z -uall [59ms]
2025-05-25 12:45:46.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-25 12:45:46.994 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-05-25 12:45:46.994 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:45:47.059 [info] > git config --get --local branch.main.remote [8ms]
2025-05-25 12:45:47.060 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 12:45:47.164 [info] > git config --get --local branch.main.merge [8ms]
2025-05-25 12:45:47.164 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 15:03:39.266 [info] > git config --get commit.template [3ms]
2025-05-25 15:03:39.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-25 15:03:39.422 [info] > git status -z -uall [52ms]
2025-05-25 15:03:39.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [32ms]
2025-05-25 15:03:39.571 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-05-25 15:03:39.571 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 15:03:39.628 [info] > git config --get --local branch.main.remote [7ms]
2025-05-25 15:03:39.628 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 15:03:39.694 [info] > git config --get --local branch.main.merge [12ms]
2025-05-25 15:03:39.695 [warning] [Git][config] git config failed: Failed to execute git
2025-05-25 15:03:54.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-05-25 15:03:54.551 [info] > git config --get commit.template [92ms]
2025-05-25 15:03:54.657 [info] > git status -z -uall [54ms]
2025-05-25 15:03:54.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
