2025-05-25 11:58:28.107 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-25 11:58:28.108 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{"v2Enabled":false},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-25 11:58:28.108 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"modelRegistry":{}}
2025-05-25 11:58:28.108 [info] 'AugmentExtension' Retrieving model config
2025-05-25 11:58:33.638 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2341 msec late.
2025-05-25 11:58:34.565 [info] 'AugmentExtension' Retrieved model config
2025-05-25 11:58:34.565 [info] 'AugmentExtension' Returning model config
2025-05-25 11:58:34.649 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
2025-05-25 11:58:34.649 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 3. 5. 2025 18:14:32
2025-05-25 11:58:34.649 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-25 11:58:34.649 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 3. 5. 2025 18:14:32; type = explicit
2025-05-25 11:58:34.649 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-05-25 11:58:34.649 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 3. 5. 2025 18:14:32
2025-05-25 11:58:34.686 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-25 11:58:34.686 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-25 11:58:34.686 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-25 11:58:34.688 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-25 11:58:34.717 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-25 11:58:34.721 [info] 'ToolsModel' Host: localToolHost (10 tools: 167 enabled, 0 disabled})
 + save-file
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-25 11:58:35.268 [info] 'WorkspaceManager[home]' Start tracking
2025-05-25 11:58:37.092 [info] 'PathMap' Opened source folder /home with id 100
2025-05-25 11:58:37.092 [info] 'OpenFileManager' Opened source folder 100
2025-05-25 11:58:37.097 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-05-25 11:58:43.414 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-25 11:58:43.414 [info] 'ToolsModel' Host: sidecarToolHost (4 tools: 62 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + remember

2025-05-25 11:58:43.415 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 3588 msec late.
2025-05-25 11:58:43.455 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-25 11:58:43.455 [info] 'ToolsModel' Host: localToolHost (10 tools: 167 enabled, 0 disabled})
 + save-file
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-25 11:58:43.455 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-25 11:58:43.455 [info] 'ToolsModel' Host: sidecarToolHost (4 tools: 62 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + remember

2025-05-25 11:58:43.485 [info] 'MtimeCache[home]' read 6283 entries from /home/<USER>/.config/Code/User/workspaceStorage/cb6b3e8c8436dc6c0eb1d884d4cd3ae7/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-05-25 11:58:48.303 [info] 'StallDetector' Recent work: [{"name":"resolve-file-request","durationMs":11398.687331,"timestamp":"2025-05-25T09:58:48.241Z"}]
2025-05-25 11:58:50.178 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/saoudrizwan.claude-dev-3.17.5
2025-05-25 11:58:53.634 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-25 11:58:54.045 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365
2025-05-25 11:58:54.078 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist
2025-05-25 11:58:54.086 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/syntaxes
2025-05-25 11:58:54.818 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225
2025-05-25 11:58:54.846 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist
2025-05-25 11:58:55.831 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/assets
2025-05-25 11:58:55.844 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/assets/status
2025-05-25 11:58:55.844 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled
2025-05-25 11:58:55.848 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled/linux
2025-05-25 11:58:55.848 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled/linux/x64
2025-05-25 11:58:55.848 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/resources
2025-05-25 11:58:56.178 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled/darwin
2025-05-25 11:58:56.181 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled/darwin/arm64
2025-05-25 11:58:56.181 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365/dist/compiled/linux/arm64
2025-05-25 11:58:57.001 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/github.copilot-1.323.0
2025-05-25 11:58:57.001 [info] 'WorkspaceManager[home]' Directory removed: tv/.vscode/extensions/.f087e5f1-7e62-485d-94cd-46a7935c6365
2025-05-25 11:58:57.174 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/workers
2025-05-25 11:58:57.180 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/webview-ui
2025-05-25 11:58:57.180 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/webview-ui/audio
2025-05-25 11:58:57.790 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets
2025-05-25 11:58:58.007 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets/codicons
2025-05-25 11:58:58.008 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets/icons
2025-05-25 11:58:58.008 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets/images
2025-05-25 11:58:58.008 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets/vscode-material-icons
2025-05-25 11:58:58.009 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/webview-ui/build
2025-05-25 11:58:58.031 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/webview-ui/build/assets
2025-05-25 11:58:58.803 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/assets/vscode-material-icons/icons
2025-05-25 11:58:59.129 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/integrations
2025-05-25 11:58:59.911 [info] 'WorkspaceManager[home]' Directory removed: tv/.config/Code/clp/fc411d9682d131fef5a449febd1a93f7.cs
2025-05-25 11:59:00.697 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n
2025-05-25 11:59:00.755 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales
2025-05-25 11:59:00.755 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/nl
2025-05-25 11:59:00.756 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/pl
2025-05-25 11:59:00.756 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/pt-BR
2025-05-25 11:59:00.756 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/ru
2025-05-25 11:59:00.757 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/tr
2025-05-25 11:59:00.757 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/vi
2025-05-25 11:59:00.758 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225/dist/i18n/locales/zh-CN
2025-05-25 11:59:01.160 [info] 'WorkspaceManager[home]' Directory removed: tv/.vscode/extensions/.fc20b566-1bea-49b8-98f0-6e7f05906225
2025-05-25 11:59:01.197 [info] 'WorkspaceManager[home]' Directory created: tv/.vscode/extensions/rooveterinaryinc.roo-cline-3.18.3
2025-05-25 11:59:56.661 [info] 'WorkspaceManager[home]' Tracking enabled
2025-05-25 11:59:56.661 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 3939
  - files emitted: 32562
  - other paths emitted: 7
  - total paths emitted: 36508
  - timing stats:
    - readDir: 203 ms
    - filter: 1264 ms
    - yield: 313 ms
    - total: 3589 ms
2025-05-25 11:59:56.661 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 7874
  - paths not accessible: 1
  - not plain files: 0
  - large files: 341
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 4189
  - mtime cache misses: 3719
  - probe batches: 27
  - blob names probed: 11926
  - files read: 33438
  - blobs uploaded: 3648
  - timing stats:
    - ingestPath: 112 ms
    - probe: 27649 ms
    - stat: 879 ms
    - read: 38555 ms
    - upload: 44338 ms
2025-05-25 11:59:56.661 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 1824 ms
  - read MtimeCache: 6393 ms
  - pre-populate PathMap: 201 ms
  - create PathFilter: 833 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 3592 ms
  - purge stale PathMap entries: 81 ms
  - enumerate: 1 ms
  - await DiskFileManager quiesced: 68441 ms
  - enable persist: 26 ms
  - total: 81392 ms
2025-05-25 11:59:56.662 [info] 'WorkspaceManager' Workspace startup complete in 82054 ms
