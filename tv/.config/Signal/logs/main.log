{"level":30,"time":"2025-05-25T10:11:45.408Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.417Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.418Z","msg":"initializing localeOverride setting null"}
{"level":30,"time":"2025-05-25T10:11:45.418Z","msg":"app.ready: hour cycle preference: UnknownPreference"}
{"level":30,"time":"2025-05-25T10:11:45.418Z","msg":"app.ready: preferred system locales: cs"}
{"level":30,"time":"2025-05-25T10:11:45.418Z","msg":"locale: Supported locales: af-ZA, ar, az-AZ, bg-BG, bn-BD, bs-BA, ca, cs, da, de, el, en, es, et-EE, eu, fa-IR, fi, fr, ga-IE, gl-ES, gu-IN, he, hi-IN, hr-HR, hu, id, it, ja, ka-GE, kk-KZ, km-KH, kn-IN, ko, ky-KG, lt-LT, lv-LV, mk-MK, ml-IN, mr-IN, ms, my, nb, nl, pa-IN, pl, pt-BR, pt-PT, ro-RO, ru, sk-SK, sl-SI, sq-AL, sr, sv, sw, ta-IN, te-IN, th, tl-PH, tr, ug, uk-UA, ur, vi, yue, zh-CN, zh-HK, zh-Hant"}
{"level":30,"time":"2025-05-25T10:11:45.419Z","msg":"locale: Preferred locales: cs"}
{"level":30,"time":"2025-05-25T10:11:45.419Z","msg":"locale: Locale Override: null"}
{"level":30,"time":"2025-05-25T10:11:45.427Z","msg":"locale: Matched locale: cs"}
{"level":40,"time":"2025-05-25T10:11:45.528Z","msg":"intl.onWarn [@formatjs/intl] \"defaultRichTextElements\" was specified but \"message\" was not pre-compiled. \nPlease consider using \"@formatjs/cli\" to pre-compile your messages for performance.\nFor more details see https://formatjs.github.io/docs/getting-started/message-distribution"}
{"level":30,"time":"2025-05-25T10:11:45.533Z","msg":"locale: Text info direction for cs: ltr"}
{"level":40,"time":"2025-05-25T10:11:45.537Z","msg":"getSQLKey: got key from config, but it wasn't a string"}
{"level":30,"time":"2025-05-25T10:11:45.537Z","msg":"key/initialize: Generating new encryption key, since we did not find it on disk"}
{"level":30,"time":"2025-05-25T10:11:45.537Z","msg":"getSQLKey: updating encrypted key in the config"}
{"level":30,"time":"2025-05-25T10:11:45.537Z","msg":"config/set: Saving user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.549Z","msg":"config/set: Saved user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.549Z","msg":"config/set: Saving user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.556Z","msg":"config/set: Saved user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.556Z","msg":"getSQLKey: saving safeStorageBackend: gnome_libsecret"}
{"level":30,"time":"2025-05-25T10:11:45.559Z","msg":"config/set: Saving user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.565Z","msg":"config/set: Saved user config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.567Z","msg":"getSystemTraySetting got no value, returning Uninitialized"}
{"level":30,"time":"2025-05-25T10:11:45.567Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.580Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.580Z","msg":"app.ready: setting system-tray-setting to DoNotUseSystemTray"}
{"level":30,"time":"2025-05-25T10:11:45.581Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.587Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.593Z","msg":"app ready"}
{"level":30,"time":"2025-05-25T10:11:45.593Z","msg":"starting version 7.55.0"}
{"level":30,"time":"2025-05-25T10:11:45.593Z","msg":"media access status [object Undefined] [object Undefined] [object Undefined]"}
{"level":30,"time":"2025-05-25T10:11:45.639Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.646Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.646Z","msg":"saving theme-setting value system"}
{"level":30,"time":"2025-05-25T10:11:45.646Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.651Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:45.651Z","msg":"initializing spellcheck setting true"}
{"level":30,"time":"2025-05-25T10:11:45.652Z","msg":"Initializing BrowserWindow config: {\"show\":false,\"width\":800,\"height\":610,\"minWidth\":300,\"minHeight\":200,\"autoHideMenuBar\":false,\"titleBarStyle\":\"default\",\"backgroundColor\":\"#121212\",\"webPreferences\":{\"devTools\":false,\"spellcheck\":true,\"enablePreferredSizeMode\":true,\"nodeIntegration\":false,\"nodeIntegrationInWorker\":false,\"sandbox\":false,\"contextIsolation\":true,\"preload\":\"[REDACTED]/preload.wrapper.js\",\"backgroundThrottling\":true},\"icon\":\"[REDACTED]/images/signal-logo-desktop-linux.png\"}"}
{"level":30,"time":"2025-05-25T10:11:45.857Z","msg":"spellcheck: user locales: [\"cs\"]"}
{"level":30,"time":"2025-05-25T10:11:45.857Z","msg":"spellcheck: available spellchecker languages: [\"af\",\"bg\",\"ca\",\"cs\",\"cy\",\"da\",\"de\",\"de-DE\",\"el\",\"en\",\"en-AU\",\"en-CA\",\"en-GB\",\"en-GB-oxendict\",\"en-US\",\"es\",\"es-419\",\"es-AR\",\"es-ES\",\"es-MX\",\"es-US\",\"et\",\"fa\",\"fo\",\"fr\",\"fr-FR\",\"he\",\"hi\",\"hr\",\"hu\",\"hy\",\"id\",\"it\",\"it-IT\",\"ko\",\"lt\",\"lv\",\"nb\",\"nl\",\"pl\",\"pt\",\"pt-BR\",\"pt-PT\",\"ro\",\"ru\",\"sh\",\"sk\",\"sl\",\"sq\",\"sr\",\"sv\",\"ta\",\"tg\",\"tr\",\"uk\",\"vi\"]"}
{"level":30,"time":"2025-05-25T10:11:45.857Z","msg":"spellcheck: setting languages to: [\"cs\"]"}
{"level":30,"time":"2025-05-25T10:11:46.118Z","msg":"MainSQL: migrateSchemaVersion: Migrating from schema_version 0 to user_version 0"}
{"level":30,"time":"2025-05-25T10:11:46.124Z","msg":"MainSQL: updateSchema:\n  Current user_version: 0;\n  Most recent db schema: 1350;\n  SQLite version: 3.49.1;\n  SQLCipher version: 4.7.0 community;\n  (deprecated) schema_version: 0;\n"}
{"level":30,"time":"2025-05-25T10:11:46.124Z","msg":"MainSQL: updateToSchemaVersion1: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.129Z","msg":"MainSQL: updateToSchemaVersion1: success!"}
{"level":30,"time":"2025-05-25T10:11:46.129Z","msg":"MainSQL: updateToSchemaVersion2: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.132Z","msg":"MainSQL: updateToSchemaVersion2: success!"}
{"level":30,"time":"2025-05-25T10:11:46.133Z","msg":"MainSQL: updateToSchemaVersion3: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.136Z","msg":"MainSQL: updateToSchemaVersion3: success!"}
{"level":30,"time":"2025-05-25T10:11:46.136Z","msg":"MainSQL: updateToSchemaVersion4: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.138Z","msg":"MainSQL: updateToSchemaVersion4: success!"}
{"level":30,"time":"2025-05-25T10:11:46.139Z","msg":"MainSQL: updateToSchemaVersion6: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.142Z","msg":"MainSQL: updateToSchemaVersion6: success!"}
{"level":30,"time":"2025-05-25T10:11:46.142Z","msg":"MainSQL: updateToSchemaVersion7: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.145Z","msg":"MainSQL: updateToSchemaVersion7: success!"}
{"level":30,"time":"2025-05-25T10:11:46.145Z","msg":"MainSQL: updateToSchemaVersion8: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.149Z","msg":"MainSQL: updateToSchemaVersion8: success!"}
{"level":30,"time":"2025-05-25T10:11:46.149Z","msg":"MainSQL: updateToSchemaVersion9: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.152Z","msg":"MainSQL: updateToSchemaVersion9: success!"}
{"level":30,"time":"2025-05-25T10:11:46.152Z","msg":"MainSQL: updateToSchemaVersion10: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.156Z","msg":"MainSQL: updateToSchemaVersion10: success!"}
{"level":30,"time":"2025-05-25T10:11:46.156Z","msg":"MainSQL: updateToSchemaVersion11: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.158Z","msg":"MainSQL: updateToSchemaVersion11: success!"}
{"level":30,"time":"2025-05-25T10:11:46.158Z","msg":"MainSQL: updateToSchemaVersion12: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.161Z","msg":"MainSQL: updateToSchemaVersion12: success!"}
{"level":30,"time":"2025-05-25T10:11:46.161Z","msg":"MainSQL: updateToSchemaVersion13: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.164Z","msg":"MainSQL: updateToSchemaVersion13: success!"}
{"level":30,"time":"2025-05-25T10:11:46.164Z","msg":"MainSQL: updateToSchemaVersion14: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.166Z","msg":"MainSQL: updateToSchemaVersion14: success!"}
{"level":30,"time":"2025-05-25T10:11:46.166Z","msg":"MainSQL: updateToSchemaVersion15: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.170Z","msg":"MainSQL: updateToSchemaVersion15: success!"}
{"level":30,"time":"2025-05-25T10:11:46.170Z","msg":"MainSQL: updateToSchemaVersion16: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.174Z","msg":"MainSQL: updateToSchemaVersion16: success!"}
{"level":30,"time":"2025-05-25T10:11:46.174Z","msg":"MainSQL: updateToSchemaVersion17: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.177Z","msg":"MainSQL: updateToSchemaVersion17: success!"}
{"level":30,"time":"2025-05-25T10:11:46.177Z","msg":"MainSQL: updateToSchemaVersion18: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.179Z","msg":"MainSQL: updateToSchemaVersion18: success!"}
{"level":30,"time":"2025-05-25T10:11:46.180Z","msg":"MainSQL: updateToSchemaVersion19: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.183Z","msg":"MainSQL: updateToSchemaVersion19: success!"}
{"level":30,"time":"2025-05-25T10:11:46.183Z","msg":"MainSQL: updateToSchemaVersion20: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.189Z","msg":"MainSQL: updateToSchemaVersion20: success!"}
{"level":30,"time":"2025-05-25T10:11:46.192Z","msg":"MainSQL: updateToSchemaVersion21: success!"}
{"level":30,"time":"2025-05-25T10:11:46.195Z","msg":"MainSQL: updateToSchemaVersion22: success!"}
{"level":30,"time":"2025-05-25T10:11:46.198Z","msg":"MainSQL: updateToSchemaVersion23: success!"}
{"level":30,"time":"2025-05-25T10:11:46.201Z","msg":"MainSQL: updateToSchemaVersion24: success!"}
{"level":30,"time":"2025-05-25T10:11:46.209Z","msg":"MainSQL: updateToSchemaVersion25: success!"}
{"level":30,"time":"2025-05-25T10:11:46.211Z","msg":"MainSQL: updateToSchemaVersion26: success!"}
{"level":30,"time":"2025-05-25T10:11:46.214Z","msg":"MainSQL: updateToSchemaVersion27: success!"}
{"level":30,"time":"2025-05-25T10:11:46.217Z","msg":"MainSQL: updateToSchemaVersion28: success!"}
{"level":30,"time":"2025-05-25T10:11:46.220Z","msg":"MainSQL: updateToSchemaVersion29: success!"}
{"level":30,"time":"2025-05-25T10:11:46.223Z","msg":"MainSQL: updateToSchemaVersion30: success!"}
{"level":30,"time":"2025-05-25T10:11:46.223Z","msg":"MainSQL: updateToSchemaVersion31: starting..."}
{"level":30,"time":"2025-05-25T10:11:46.227Z","msg":"MainSQL: updateToSchemaVersion31: success!"}
{"level":30,"time":"2025-05-25T10:11:46.231Z","msg":"MainSQL: updateToSchemaVersion32: success!"}
{"level":30,"time":"2025-05-25T10:11:46.234Z","msg":"MainSQL: updateToSchemaVersion33: success!"}
{"level":30,"time":"2025-05-25T10:11:46.237Z","msg":"MainSQL: updateToSchemaVersion34: success!"}
{"level":30,"time":"2025-05-25T10:11:46.240Z","msg":"MainSQL: updateToSchemaVersion35: success!"}
{"level":30,"time":"2025-05-25T10:11:46.242Z","msg":"MainSQL: updateToSchemaVersion36: success!"}
{"level":30,"time":"2025-05-25T10:11:46.247Z","msg":"MainSQL: updateToSchemaVersion37: success!"}
{"level":30,"time":"2025-05-25T10:11:46.255Z","msg":"MainSQL: updateToSchemaVersion38: success!"}
{"level":30,"time":"2025-05-25T10:11:46.260Z","msg":"MainSQL: updateToSchemaVersion39: success!"}
{"level":30,"time":"2025-05-25T10:11:46.263Z","msg":"MainSQL: updateToSchemaVersion40: success!"}
{"level":30,"time":"2025-05-25T10:11:46.273Z","msg":"MainSQL: updateToSchemaVersion41: success!"}
{"level":30,"time":"2025-05-25T10:11:46.276Z","msg":"MainSQL: updateToSchemaVersion42: success!"}
{"level":30,"time":"2025-05-25T10:11:46.277Z","msg":"MainSQL: updateToSchemaVersion43: About to iterate through 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:46.277Z","msg":"MainSQL: updateToSchemaVersion43: About to iterate through 0 messages"}
{"level":30,"time":"2025-05-25T10:11:46.277Z","msg":"MainSQL: updateToSchemaVersion43: Updated 0 messages"}
{"level":30,"time":"2025-05-25T10:11:46.279Z","msg":"MainSQL: updateToSchemaVersion43: success!"}
{"level":30,"time":"2025-05-25T10:11:46.283Z","msg":"MainSQL: updateToSchemaVersion44: success!"}
{"level":30,"time":"2025-05-25T10:11:46.288Z","msg":"MainSQL: updateToSchemaVersion45: success!"}
{"level":30,"time":"2025-05-25T10:11:46.292Z","msg":"MainSQL: updateToSchemaVersion46: success!"}
{"level":30,"time":"2025-05-25T10:11:46.299Z","msg":"MainSQL: updateToSchemaVersion47: our UUID not found"}
{"level":30,"time":"2025-05-25T10:11:46.302Z","msg":"MainSQL: updateToSchemaVersion47: success!"}
{"level":30,"time":"2025-05-25T10:11:46.304Z","msg":"MainSQL: updateToSchemaVersion48: success!"}
{"level":30,"time":"2025-05-25T10:11:46.307Z","msg":"MainSQL: updateToSchemaVersion49: success!"}
{"level":30,"time":"2025-05-25T10:11:46.310Z","msg":"MainSQL: updateToSchemaVersion50: success!"}
{"level":30,"time":"2025-05-25T10:11:46.312Z","msg":"MainSQL: updateToSchemaVersion51: success!"}
{"level":30,"time":"2025-05-25T10:11:46.315Z","msg":"MainSQL: updateToSchemaVersion52: success!"}
{"level":30,"time":"2025-05-25T10:11:46.315Z","msg":"MainSQL: updateToSchemaVersion53: About to iterate through 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:46.315Z","msg":"MainSQL: updateToSchemaVersion53: Updated 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:46.334Z","msg":"MainSQL: updateToSchemaVersion53: success!"}
{"level":30,"time":"2025-05-25T10:11:46.338Z","msg":"MainSQL: updateToSchemaVersion54: success!"}
{"level":30,"time":"2025-05-25T10:11:46.341Z","msg":"MainSQL: updateToSchemaVersion55: success!"}
{"level":30,"time":"2025-05-25T10:11:46.347Z","msg":"MainSQL: updateToSchemaVersion56: success!"}
{"level":30,"time":"2025-05-25T10:11:46.350Z","msg":"MainSQL: updateToSchemaVersion57: success!"}
{"level":30,"time":"2025-05-25T10:11:46.355Z","msg":"MainSQL: updateToSchemaVersion58: success!"}
{"level":30,"time":"2025-05-25T10:11:46.358Z","msg":"MainSQL: updateToSchemaVersion59: success!"}
{"level":30,"time":"2025-05-25T10:11:46.361Z","msg":"MainSQL: updateToSchemaVersion60: success!"}
{"level":30,"time":"2025-05-25T10:11:46.374Z","msg":"Updating BrowserWindow config: %s {\"maximized\":false,\"autoHideMenuBar\":false,\"fullscreen\":false,\"width\":800,\"height\":610,\"x\":560,\"y\":215}"}
{"level":30,"time":"2025-05-25T10:11:46.375Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:46.379Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:46.390Z","msg":"MainSQL: updateToSchemaVersion61: success!"}
{"level":30,"time":"2025-05-25T10:11:46.393Z","msg":"MainSQL: updateToSchemaVersion62: success!"}
{"level":30,"time":"2025-05-25T10:11:46.395Z","msg":"MainSQL: updateToSchemaVersion63: success!"}
{"level":30,"time":"2025-05-25T10:11:46.400Z","msg":"MainSQL: updateToSchemaVersion64: success!"}
{"level":30,"time":"2025-05-25T10:11:46.406Z","msg":"MainSQL: updateToSchemaVersion65: success!"}
{"level":30,"time":"2025-05-25T10:11:46.410Z","msg":"MainSQL: updateToSchemaVersion66: success!"}
{"level":30,"time":"2025-05-25T10:11:46.414Z","msg":"MainSQL: updateToSchemaVersion67: success!"}
{"level":30,"time":"2025-05-25T10:11:46.422Z","msg":"MainSQL: updateToSchemaVersion68: success!"}
{"level":30,"time":"2025-05-25T10:11:46.425Z","msg":"MainSQL: updateToSchemaVersion69: success!"}
{"level":30,"time":"2025-05-25T10:11:46.428Z","msg":"MainSQL: updateToSchemaVersion70: success!"}
{"level":30,"time":"2025-05-25T10:11:46.447Z","msg":"MainSQL: updateToSchemaVersion71: success!"}
{"level":30,"time":"2025-05-25T10:11:46.452Z","msg":"MainSQL: updateToSchemaVersion72: success!"}
{"level":30,"time":"2025-05-25T10:11:46.470Z","msg":"MainSQL: updateToSchemaVersion73: success!"}
{"level":30,"time":"2025-05-25T10:11:46.476Z","msg":"MainSQL: updateToSchemaVersion74: success!"}
{"level":30,"time":"2025-05-25T10:11:46.478Z","msg":"MainSQL: updateToSchemaVersion75: success!"}
{"level":30,"time":"2025-05-25T10:11:46.489Z","msg":"MainSQL: updateToSchemaVersion76: success!"}
{"level":30,"time":"2025-05-25T10:11:46.492Z","msg":"MainSQL: updateToSchemaVersion77: success!"}
{"level":30,"time":"2025-05-25T10:11:46.495Z","msg":"MainSQL: updateToSchemaVersion78: success!"}
{"level":30,"time":"2025-05-25T10:11:46.498Z","msg":"MainSQL: updateToSchemaVersion79: success!"}
{"level":30,"time":"2025-05-25T10:11:46.506Z","msg":"MainSQL: updateToSchemaVersion80: success!"}
{"level":30,"time":"2025-05-25T10:11:46.523Z","msg":"MainSQL: updateToSchemaVersion81: success!"}
{"level":30,"time":"2025-05-25T10:11:46.529Z","msg":"MainSQL: updateToSchemaVersion82: success!"}
{"level":30,"time":"2025-05-25T10:11:46.533Z","msg":"MainSQL: updateToSchemaVersion83: success!"}
{"level":30,"time":"2025-05-25T10:11:46.536Z","msg":"MainSQL: updateToSchemaVersion84: success!"}
{"level":30,"time":"2025-05-25T10:11:46.539Z","msg":"MainSQL: updateToSchemaVersion85: success!"}
{"level":30,"time":"2025-05-25T10:11:46.542Z","msg":"MainSQL: updateToSchemaVersion86: success!"}
{"level":30,"time":"2025-05-25T10:11:46.542Z","msg":"MainSQL: updateToSchemaVersion87(cleanup)/kyberPreKeys: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.542Z","msg":"MainSQL: updateToSchemaVersion87(cleanup)/preKeys: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.543Z","msg":"MainSQL: updateToSchemaVersion87(cleanup)/signedPreKeys: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.543Z","msg":"MainSQL: updateToSchemaVersion87(cleanup): success!"}
{"level":30,"time":"2025-05-25T10:11:46.599Z","msg":"MainSQL: updateToSchemaVersion88: updating 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:46.599Z","msg":"MainSQL: updateToSchemaVersion88: Our UUID not found"}
{"level":30,"time":"2025-05-25T10:11:46.599Z","msg":"MainSQL: updateToSchemaVersion88: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.599Z","msg":"MainSQL: updateToSchemaVersion88: updating 0 sessions"}
{"level":30,"time":"2025-05-25T10:11:46.600Z","msg":"MainSQL: updateToSchemaVersion88: updating messages"}
{"level":30,"time":"2025-05-25T10:11:46.600Z","msg":"MainSQL: updateToSchemaVersion88: updated 0 messages"}
{"level":30,"time":"2025-05-25T10:11:46.600Z","msg":"MainSQL: updateToSchemaVersion88: updating 0 preKeys"}
{"level":30,"time":"2025-05-25T10:11:46.601Z","msg":"MainSQL: updateToSchemaVersion88: updating 0 signedPreKeys"}
{"level":30,"time":"2025-05-25T10:11:46.601Z","msg":"MainSQL: updateToSchemaVersion88: updating 0 kyberPreKeys"}
{"level":30,"time":"2025-05-25T10:11:46.601Z","msg":"MainSQL: updateToSchemaVersion88: updated 0 jobs"}
{"level":30,"time":"2025-05-25T10:11:46.606Z","msg":"MainSQL: updateToSchemaVersion88: success!"}
{"level":30,"time":"2025-05-25T10:11:46.618Z","msg":"MainSQL: updateToSchemaVersion89: success!"}
{"level":30,"time":"2025-05-25T10:11:46.621Z","msg":"MainSQL: updateToSchemaVersion90: removed screenshotData from 0 message"}
{"level":30,"time":"2025-05-25T10:11:46.621Z","msg":"MainSQL: updateToSchemaVersion90: success!"}
{"level":30,"time":"2025-05-25T10:11:46.636Z","msg":"MainSQL: updateToSchemaVersion91: Found 0 keys"}
{"level":30,"time":"2025-05-25T10:11:46.643Z","msg":"MainSQL: updateToSchemaVersion91: success!"}
{"level":30,"time":"2025-05-25T10:11:46.643Z","msg":"MainSQL: updateToSchemaVersion920/kyberPreKeys: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.644Z","msg":"MainSQL: updateToSchemaVersion920/signedPreKeys: Our PNI not found"}
{"level":30,"time":"2025-05-25T10:11:46.644Z","msg":"MainSQL: updateToSchemaVersion920: Done with deletions"}
{"level":30,"time":"2025-05-25T10:11:46.644Z","msg":"MainSQL: updateToSchemaVersion920: user_version set to 920. Starting vacuum..."}
{"level":30,"time":"2025-05-25T10:11:46.651Z","msg":"spellcheck: dictionary download success: cs"}
{"level":30,"time":"2025-05-25T10:11:46.661Z","msg":"MainSQL: updateToSchemaVersion920: Vacuum complete."}
{"level":30,"time":"2025-05-25T10:11:46.661Z","msg":"MainSQL: updateToSchemaVersion920: success!"}
{"level":30,"time":"2025-05-25T10:11:46.663Z","msg":"MainSQL: updateToSchemaVersion930: success!"}
{"level":30,"time":"2025-05-25T10:11:46.665Z","msg":"MainSQL: updateToSchemaVersion940: success!"}
{"level":30,"time":"2025-05-25T10:11:46.667Z","msg":"MainSQL: updateToSchemaVersion950: success!"}
{"level":30,"time":"2025-05-25T10:11:46.669Z","msg":"MainSQL: updateToSchemaVersion960: Our ACI not found"}
{"level":30,"time":"2025-05-25T10:11:46.669Z","msg":"MainSQL: updateToSchemaVersion960: not running, pni is normalized"}
{"level":30,"time":"2025-05-25T10:11:46.669Z","msg":"MainSQL: updateToSchemaVersion960: success!"}
{"level":30,"time":"2025-05-25T10:11:46.671Z","msg":"spellcheck: dictionary initialized: cs"}
{"level":30,"time":"2025-05-25T10:11:46.673Z","msg":"MainSQL: updateToSchemaVersion970: success! fts optimize took 4ms"}
{"level":30,"time":"2025-05-25T10:11:46.676Z","msg":"MainSQL: updateToSchemaVersion980: success!"}
{"level":30,"time":"2025-05-25T10:11:46.678Z","msg":"MainSQL: updateToSchemaVersion990: success!"}
{"level":30,"time":"2025-05-25T10:11:46.681Z","msg":"MainSQL: updateToSchemaVersion1000: success!"}
{"level":30,"time":"2025-05-25T10:11:46.683Z","msg":"MainSQL: updateToSchemaVersion1010: success!"}
{"level":30,"time":"2025-05-25T10:11:46.683Z","msg":"MainSQL: updateToSchemaVersion1020: not linked"}
{"level":30,"time":"2025-05-25T10:11:46.685Z","msg":"MainSQL: updateToSchemaVersion1020: success!"}
{"level":30,"time":"2025-05-25T10:11:46.696Z","msg":"MainSQL: updateToSchemaVersion1030: success!"}
{"level":30,"time":"2025-05-25T10:11:46.696Z","msg":"MainSQL: updateToSchemaVersion1040: loaded 0 existing jobs"}
{"level":30,"time":"2025-05-25T10:11:46.700Z","msg":"MainSQL: updateToSchemaVersion1040: transferred 0 rows, removed 0"}
{"level":30,"time":"2025-05-25T10:11:46.702Z","msg":"MainSQL: updateToSchemaVersion1040: success!"}
{"level":30,"time":"2025-05-25T10:11:46.705Z","msg":"MainSQL: updateToSchemaVersion1050: success!"}
{"level":30,"time":"2025-05-25T10:11:46.709Z","msg":"MainSQL: updateToSchemaVersion1060: success!"}
{"level":30,"time":"2025-05-25T10:11:46.713Z","msg":"MainSQL: updateToSchemaVersion1070: success!"}
{"level":30,"time":"2025-05-25T10:11:46.715Z","msg":"MainSQL: updateToSchemaVersion1080: success!"}
{"level":30,"time":"2025-05-25T10:11:46.718Z","msg":"MainSQL: updateToSchemaVersion1090: success!"}
{"level":30,"time":"2025-05-25T10:11:46.721Z","msg":"MainSQL: updateToSchemaVersion1100: success!"}
{"level":30,"time":"2025-05-25T10:11:46.726Z","msg":"MainSQL: updateToSchemaVersion1110: success!"}
{"level":30,"time":"2025-05-25T10:11:46.728Z","msg":"MainSQL: updateToSchemaVersion1120: success!"}
{"level":30,"time":"2025-05-25T10:11:46.730Z","msg":"MainSQL: updateToSchemaVersion1130: success!"}
{"level":30,"time":"2025-05-25T10:11:46.734Z","msg":"MainSQL: updateToSchemaVersion1140: success!"}
{"level":30,"time":"2025-05-25T10:11:46.737Z","msg":"MainSQL: updateToSchemaVersion1150: success!"}
{"level":30,"time":"2025-05-25T10:11:46.739Z","msg":"MainSQL: updateToSchemaVersion1160: success!"}
{"level":30,"time":"2025-05-25T10:11:46.742Z","msg":"MainSQL: updateToSchemaVersion1170: success!"}
{"level":30,"time":"2025-05-25T10:11:46.746Z","msg":"MainSQL: updateToSchemaVersion1180: success!"}
{"level":30,"time":"2025-05-25T10:11:46.753Z","msg":"MainSQL: updateToSchemaVersion1190: success!"}
{"level":30,"time":"2025-05-25T10:11:46.756Z","msg":"MainSQL: updateToSchemaVersion1200: success!"}
{"level":30,"time":"2025-05-25T10:11:46.761Z","msg":"MainSQL: updateToSchemaVersion1210: success!"}
{"level":30,"time":"2025-05-25T10:11:46.765Z","msg":"MainSQL: updateToSchemaVersion1220: no identity/registration id"}
{"level":30,"time":"2025-05-25T10:11:46.768Z","msg":"MainSQL: updateToSchemaVersion1220: success!"}
{"level":30,"time":"2025-05-25T10:11:46.770Z","msg":"MainSQL: updateToSchemaVersion1230: success!"}
{"level":30,"time":"2025-05-25T10:11:46.772Z","msg":"MainSQL: updateToSchemaVersion1240: success!"}
{"level":30,"time":"2025-05-25T10:11:46.779Z","msg":"MainSQL: updateToSchemaVersion1250: success!"}
{"level":30,"time":"2025-05-25T10:11:46.781Z","msg":"MainSQL: updateToSchemaVersion1260: success!"}
{"level":30,"time":"2025-05-25T10:11:46.799Z","msg":"MainSQL: updateToSchemaVersion1270: success!"}
{"level":30,"time":"2025-05-25T10:11:46.803Z","msg":"MainSQL: updateToSchemaVersion1280: success!"}
{"level":30,"time":"2025-05-25T10:11:46.816Z","msg":"MainSQL: updateToSchemaVersion1290: success!"}
{"level":30,"time":"2025-05-25T10:11:46.821Z","msg":"MainSQL: updateToSchemaVersion1300: success!"}
{"level":30,"time":"2025-05-25T10:11:46.823Z","msg":"MainSQL: updateToSchemaVersion1310: success!"}
{"level":30,"time":"2025-05-25T10:11:46.826Z","msg":"MainSQL: updateToSchemaVersion1320: success!"}
{"level":30,"time":"2025-05-25T10:11:46.828Z","msg":"MainSQL: updateToSchemaVersion1330: success!"}
{"level":30,"time":"2025-05-25T10:11:46.831Z","msg":"MainSQL: updateToSchemaVersion1340: success!"}
{"level":30,"time":"2025-05-25T10:11:46.834Z","msg":"MainSQL: updateToSchemaVersion1350: success!"}
{"level":30,"time":"2025-05-25T10:11:46.834Z","msg":"MainSQL: enableFTS5SecureDelete: enabling"}
{"level":30,"time":"2025-05-25T10:11:46.838Z","msg":"MainSQL: updateSchema: optimize took 2ms"}
{"level":30,"time":"2025-05-25T10:11:47.393Z","msg":"got fast theme-setting value system"}
{"level":30,"time":"2025-05-25T10:11:48.405Z","msg":"got fast spellcheck setting true"}
{"level":30,"time":"2025-05-25T10:11:48.417Z","msg":"System tray service: created"}
{"level":30,"time":"2025-05-25T10:11:48.417Z","msg":"System tray service: updating main window. Previously, there was not a window, and now there is"}
{"level":30,"time":"2025-05-25T10:11:48.417Z","msg":"System tray service: rendering no tray"}
{"level":30,"time":"2025-05-25T10:11:48.417Z","msg":"Begin ensuring permissions"}
{"level":30,"time":"2025-05-25T10:11:48.420Z","msg":"Ensuring file permissions for 4 files"}
{"level":30,"time":"2025-05-25T10:11:48.421Z","msg":"Finish ensuring permissions in 3ms"}
{"level":30,"time":"2025-05-25T10:11:48.427Z","msg":"main window is ready-to-show"}
{"level":30,"time":"2025-05-25T10:11:48.427Z","msg":"showing main window"}
{"level":30,"time":"2025-05-25T10:11:48.428Z","msg":"System tray service: rendering no tray"}
{"level":30,"time":"2025-05-25T10:11:48.707Z","msg":"deleteAllBadges: deleted 0 files"}
{"level":30,"time":"2025-05-25T10:11:48.709Z","msg":"MainSQL: removeKnownStickers: About to iterate through 0 stickers"}
{"level":30,"time":"2025-05-25T10:11:48.709Z","msg":"MainSQL: removeKnownStickers: Done processing 0 stickers"}
{"level":30,"time":"2025-05-25T10:11:48.709Z","msg":"deleteAllStickers: deleted 0 files"}
{"level":30,"time":"2025-05-25T10:11:48.712Z","msg":"MainSQL: removeKnownDraftAttachments: About to iterate through 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:48.712Z","msg":"MainSQL: removeKnownDraftAttachments: Done processing 0 conversations"}
{"level":30,"time":"2025-05-25T10:11:48.712Z","msg":"deleteAllDraftAttachments: deleted 0 files"}
{"level":30,"time":"2025-05-25T10:11:48.714Z","msg":"cleanupOrphanedAttachments: found 0 attachments on disk"}
{"level":30,"time":"2025-05-25T10:11:48.715Z","msg":"cleanupOrphanedAttachments: found 0 downloads on disk"}
{"level":30,"time":"2025-05-25T10:11:48.716Z","msg":"MainSQL: getKnownConversationAttachments: About to iterate through 0"}
{"level":30,"time":"2025-05-25T10:11:48.717Z","msg":"MainSQL: getKnownConversationAttachments: Done processing"}
{"level":30,"time":"2025-05-25T10:11:48.717Z","msg":"cleanupOrphanedAttachments: found 0 conversation attachments (0 missing), 0 remain"}
{"level":30,"time":"2025-05-25T10:11:48.718Z","msg":"cleanupOrphanedAttachments: found 0 downloads (0 missing), 0 remain"}
{"level":30,"time":"2025-05-25T10:11:48.719Z","msg":"cleanupOrphanedAttachments: took 16ms"}
{"level":30,"time":"2025-05-25T10:11:48.722Z","msg":"MainSQL: pageMessages(aafe50a404b70405): Starting iteration through 0 messages"}
{"level":30,"time":"2025-05-25T10:11:48.774Z","msg":"MainSQL: finishPageMessages(aafe50a404b70405): reached the end after processing 0 messages"}
{"level":30,"time":"2025-05-25T10:11:48.775Z","msg":"cleanupOrphanedAttachments: found 0 message attachments, (0 missing) 0 remain"}
{"level":30,"time":"2025-05-25T10:11:48.775Z","msg":"deleteAll: deleted 0 files"}
{"level":30,"time":"2025-05-25T10:11:48.775Z","msg":"cleanupOrphanedAttachments: found 0 downloads (0 missing) 0 remain"}
{"level":30,"time":"2025-05-25T10:11:48.775Z","msg":"deleteAllDownloads: deleted 0 files"}
{"level":30,"time":"2025-05-25T10:11:48.775Z","msg":"deleteOrphanedAttachments: took 56ms"}
{"level":30,"time":"2025-05-25T10:11:48.819Z","msg":"Prevent display sleep service: allowing display sleep"}
{"level":30,"time":"2025-05-25T10:11:48.819Z","msg":"Background throttling enabled because no call is active"}
{"level":30,"time":"2025-05-25T10:11:48.954Z","msg":"createHTTPSAgent.createConnection(updates2.signal.org): connected to IPv6 addr after 71ms (attempts v4=0 v6=1)"}
{"level":30,"time":"2025-05-25T10:11:49.320Z","msg":"Updating BrowserWindow config: %s {\"maximized\":false,\"autoHideMenuBar\":false,\"fullscreen\":false,\"width\":800,\"height\":610,\"x\":560,\"y\":215}"}
{"level":30,"time":"2025-05-25T10:11:49.320Z","msg":"config/set: Saving ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:11:49.331Z","msg":"config/set: Saved ephemeral config to disk"}
{"level":30,"time":"2025-05-25T10:13:00.883Z","msg":"MainSQL: slow query bulkAddKyberPreKeys duration=62ms"}
{"level":30,"time":"2025-05-25T10:13:08.383Z","msg":"Top 10 queries by cumulative duration (ms) over last 81538ms: createOrUpdateItem: cumulative 154.7 | average: 3.68 | max: 15.67 | count: 42 ||| bulkAddKyberPreKeys: cumulative 87.07 | average: 21.77 | max: 62.28 | count: 4 ||| commitDecryptResult: cumulative 53.4 | average: 4.11 | max: 12.42 | count: 13 ||| insertSentProto: cumulative 26.86 | average: 8.95 | max: 12.04 | count: 3 ||| saveConversation: cumulative 19.74 | average: 9.87 | max: 10.39 | count: 2 ||| badgeImageFileDownloaded: cumulative 17.28 | average: 2.47 | max: 2.77 | count: 7 ||| removeAllConfiguration: cumulative 14.47 | average: 14.47 | max: 14.47 | count: 1 ||| updateConversations: cumulative 13.82 | average: 6.91 | max: 9.71 | count: 2 ||| insertJob: cumulative 11.6 | average: 2.9 | max: 3.9 | count: 4 ||| bulkAddPreKeys: cumulative 10.98 | average: 5.49 | max: 5.62 | count: 2; Total cumulative duration of all SQL queries during this epoch: 476.78ms"}
{"level":30,"time":"2025-05-25T10:13:08.383Z","msg":"Resetting query stats"}
{"level":30,"time":"2025-05-25T10:13:12.219Z","msg":"MainSQL: slow query saveMessages duration=1406ms"}
{"level":30,"time":"2025-05-25T10:13:12.400Z","msg":"MainSQL: slow query saveAttachmentDownloadJobs duration=47ms"}
{"level":30,"time":"2025-05-25T10:13:12.786Z","msg":"MainSQL: slow query saveAttachmentDownloadJobs duration=49ms"}
{"level":30,"time":"2025-05-25T10:13:12.943Z","msg":"MainSQL: slow query saveConversations duration=52ms"}
{"level":30,"time":"2025-05-25T10:13:13.536Z","msg":"MainSQL: slow query enableMessageInsertTriggersAndBackfill duration=134ms"}
{"level":30,"time":"2025-05-25T10:13:13.730Z","msg":"MainSQL: slow query enableFSyncAndCheckpoint duration=193ms"}
{"level":30,"time":"2025-05-25T10:13:13.733Z","msg":"Top 10 queries by cumulative duration (ms) over last 5349ms during 'Backup Import': saveMessages: cumulative 1440.42 | average: 720.21 | max: 1406.12 | count: 2 ||| enableFSyncAndCheckpoint: cumulative 192.66 | average: 192.66 | max: 192.66 | count: 1 ||| saveAttachmentDownloadJobs: cumulative 180.65 | average: 30.11 | max: 48.78 | count: 6 ||| enableMessageInsertTriggersAndBackfill: cumulative 133.8 | average: 133.8 | max: 133.8 | count: 1 ||| saveEditedMessages: cumulative 70.18 | average: 3.19 | max: 9.73 | count: 22 ||| saveConversations: cumulative 51.88 | average: 51.88 | max: 51.88 | count: 1 ||| getConversationMessageStats: cumulative 25.17 | average: 2.52 | max: 7.3 | count: 10 ||| getAllKyberPreKeys: cumulative 22.17 | average: 22.17 | max: 22.17 | count: 1 ||| saveMessage: cumulative 18.27 | average: 4.57 | max: 5.79 | count: 4 ||| getAllConversations: cumulative 15.91 | average: 15.91 | max: 15.91 | count: 1; Total cumulative duration of all SQL queries during this epoch: 2238.02ms"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"App loaded - time: 88774"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"SQL init - time: 1309"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"Preload Compile - time: 249"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"Preload - time: 968"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"WebSocket connect - time: 72269"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"Processed count: 1"}
{"level":30,"time":"2025-05-25T10:13:14.361Z","msg":"Messages per second: 0.06436248954109546"}
{"level":30,"time":"2025-05-25T10:14:29.712Z","msg":"close event {\"readyForShutdown\":false,\"shouldQuit\":false}"}
{"level":30,"time":"2025-05-25T10:14:29.713Z","msg":"maybeRequestCloseConfirmation: Checking to see if close confirmation is needed"}
{"level":30,"time":"2025-05-25T10:14:29.717Z","msg":"maybeRequestCloseConfirmation: Response received"}
{"level":30,"time":"2025-05-25T10:14:29.719Z","msg":"System tray service: rendering no tray"}
{"level":30,"time":"2025-05-25T10:14:29.720Z","msg":"requestShutdown: Requesting close of mainWindow..."}
{"level":30,"time":"2025-05-25T10:14:30.018Z","msg":"requestShutdown: Response received"}
{"level":30,"time":"2025-05-25T10:14:30.671Z","msg":"before-quit event {\"readyForShutdown\":true,\"shouldQuit\":false,\"hasEventBeenPrevented\":false,\"windowCount\":1,\"mainWindowExists\":true,\"mainWindowIsFullScreen\":false}"}
{"level":30,"time":"2025-05-25T10:14:30.671Z","msg":"System tray service: markShouldQuit"}
{"level":30,"time":"2025-05-25T10:14:30.671Z","msg":"close event {\"readyForShutdown\":true,\"shouldQuit\":true}"}
{"level":30,"time":"2025-05-25T10:14:30.675Z","msg":"main window closed event"}
{"level":30,"time":"2025-05-25T10:14:30.675Z","msg":"System tray service: updating main window. Previously, there was a window, and now there is not"}
{"level":30,"time":"2025-05-25T10:14:30.675Z","msg":"System tray service: rendering no tray"}
{"level":30,"time":"2025-05-25T10:14:30.675Z","msg":"will-quit event {\"hasEventBeenPrevented\":false,\"windowCount\":0,\"mainWindowExists\":false}"}
{"level":30,"time":"2025-05-25T10:14:30.676Z","msg":"quit event {\"hasEventBeenPrevented\":false,\"windowCount\":0,\"mainWindowExists\":false}"}
