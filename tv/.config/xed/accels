; xed GtkAccelMap rc-file         -*- scheme -*-
; this file is an automated accelerator map dump
;
; (gtk_accel_path "<Actions>/FileBrowserWidgetSelectionActionGroup/FileMoveToTrash" "")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSelectionActionGroup/FileDelete" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewOverviewMap" "F12")
; (gtk_accel_path "<Actions>/XedWindowActions/SearchGoToLine" "<Primary>i")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewToolbar" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewFullscreen" "F11")
; (gtk_accel_path "<Actions>/XedWindowActions/FileSaveAll" "<Primary><Shift>l")
; (gtk_accel_path "<Actions>/XedTextSizePluginActions/NormalSizeAction" "<Primary>0")
; (gtk_accel_path "<Actions>/XedWindowPanesActions/ViewBottomPane" "<Primary>F9")
; (gtk_accel_path "<Actions>/XedWindowActions/DocumentsMoveToNewWindow" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/HelpAbout" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewStatusbar" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/FileRecentsMenu" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditToggleComment" "<Primary>slash")
; (gtk_accel_path "<Actions>/DocumentsListActions/Tab_0" "<Alt>1")
; (gtk_accel_path "<Actions>/XedCloseWindowActions/FileClose" "<Primary>w")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewWordWrap" "<Primary>r")
; (gtk_accel_path "<Actions>/XedWindowActions/FileSave" "<Primary>s")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/HelpContents" "F1")
; (gtk_accel_path "<Actions>/XedWindowActions/SearchReplace" "<Primary>h")
; (gtk_accel_path "<Actions>/XedWindowActions/FileRevert" "")
; (gtk_accel_path "<Actions>/XedJoinLinesPluginActions/JoinLinesAction" "<Primary>j")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSensitiveActionGroup/DirectoryOpen" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/Tools" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditPaste" "<Primary>v")
; (gtk_accel_path "<Actions>/FileBrowserWidgetActionGroup/FilterHidden" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditCut" "<Primary>x")
; (gtk_accel_path "<Actions>/XedWindowPanesActions/ViewSidePane" "F9")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/View" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/File" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditSelectAll" "<Primary>a")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/Help" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditDelete" "")
; (gtk_accel_path "<Actions>/XedSpellPluginActions/CheckSpell" "<Shift>F7")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/Documents" "")
; (gtk_accel_path "<Actions>/XedWindowActions/DocumentsNextDocument" "<Primary><Alt>Page_Down")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/ViewMenubar" "")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSingleMostSelectionActionGroup/DirectoryNew" "")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSensitiveActionGroup/DirectoryRefresh" "")
; (gtk_accel_path "<Actions>/XedWindowActions/FilePrint" "<Primary>p")
; (gtk_accel_path "<Actions>/XedTextSizePluginActions/LargerTextAction" "<Primary>equal")
; (gtk_accel_path "<Actions>/XedWindowActions/EditRedo" "<Primary>y")
; (gtk_accel_path "<Actions>/FileBrowserWidgetFileSelectionActionGroup/FileOpen" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditDuplicate" "<Primary><Shift>d")
; (gtk_accel_path "<Actions>/FileBrowserPluginExtra/SetActiveRoot" "")
; (gtk_accel_path "<Actions>/XedSpellPluginActions/ConfigSpell" "")
; (gtk_accel_path "<Actions>/XedDocinfoPluginActions/DocumentStatistics" "")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSingleMostSelectionActionGroup/FileNew" "")
; (gtk_accel_path "<Actions>/XedQuitWindowActions/FileQuit" "<Primary>q")
; (gtk_accel_path "<Actions>/XedWindowActions/EditUndo" "<Primary>z")
; (gtk_accel_path "<Actions>/XedTimePluginActions/InsertDateAndTime" "F5")
; (gtk_accel_path "<Actions>/FileBrowserWidgetSingleSelectionActionGroup/FileRename" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/HelpShortcuts" "")
; (gtk_accel_path "<Actions>/XedWindowActions/ViewHighlightMode" "<Primary><Shift>h")
; (gtk_accel_path "<Actions>/XedSortPluginActions/Sort" "F10")
; (gtk_accel_path "<Actions>/XedWindowActions/SearchFind" "<Primary>f")
; (gtk_accel_path "<Actions>/XedWindowActions/SearchFindPrevious" "<Primary><Shift>g")
; (gtk_accel_path "<Actions>/FileBrowserWidgetActionGroup/FilterBinary" "")
; (gtk_accel_path "<Actions>/XedWindowActions/FileCloseAll" "<Primary><Shift>w")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/Search" "")
; (gtk_accel_path "<Actions>/FileBrowserWidgetActionGroupToplevel/FilterMenuAction" "")
; (gtk_accel_path "<Actions>/XedSpellPluginActions/InlineSpellChecker" "")
; (gtk_accel_path "<Actions>/XedWindowActions/DocumentsPreviousDocument" "<Primary><Alt>Page_Up")
; (gtk_accel_path "<Actions>/XedWindowActions/EditToggleCommentBlock" "<Primary><Shift>question")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/XAppFavoritesMenu" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/Edit" "")
; (gtk_accel_path "<Actions>/XedTextSizePluginActions/SmallerTextAction" "<Primary>minus")
; (gtk_accel_path "<Actions>/XedWindowActions/FileSaveAs" "<Primary><Shift>s")
; (gtk_accel_path "<Actions>/FileBrowserWidgetBookmarkActionGroup/BookmarkOpen" "")
; (gtk_accel_path "<Actions>/XedWindowActions/EditCopy" "<Primary>c")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/EditPreferences" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/FileOpen" "<Primary>o")
; (gtk_accel_path "<Actions>/FileBrowserPluginSingleSelectionExtra/OpenTerminal" "")
; (gtk_accel_path "<Actions>/XedWindowAlwaysSensitiveActions/FileNew" "<Primary>n")
; (gtk_accel_path "<Actions>/XedWindowActions/SearchFindNext" "<Primary>g")
; (gtk_accel_path "<Actions>/XedWindowActions/FilePrintPreview" "<Primary><Shift>p")
